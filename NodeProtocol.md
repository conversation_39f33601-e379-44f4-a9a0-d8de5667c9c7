# RPCS M Controllers Interfaces

这个仓库包含RPCS系统中所有的ROS2接口定义，包括消息(msg)、服务(srv)和动作(action)。

### 节点使用协议:
| 协议名         | 协议名称              | 包名                  | 包描述                           | 可执行程序名         | 工程名                      | 节点描述    | 为什么节点需要此协议                  | 添加时间   | 修改时间 |
| -------------- | --------------------- | --------------------- | -------------------------------- | -------------------- | --------------------------- | ----------- | ------------------------------------- | ---------- | -------- |
| AgvGoPoint.srv | agv走点服务协议       | rpcs_s_interfaces_agv | agv专用协议包                    | agv_go_point_service | RPCS_S_agv_go_point_service | agv走点服务 | 此节点提供agv走点服务，使用此服务协议 | 2025/06/16 |          |
| AGVState.msg   | IDL形式的状态话题协议 | vda5050_msgs          | ROS2使用的VDA5050协议的IDL协议包 | agv_go_point_service | RPCS_S_agv_go_point_service | agv走点服务 | kuka的AGV小车状态话题上报             | 2025/07/02 |          |

### 话题列表:
| 话题                      | 话题名称                                       | 所用协议                               | 发布话题工程名              | 节点描述    | 发布频率          | 添加时间   | 修改时间 |
| ------------------------- | ---------------------------------------------- | -------------------------------------- | --------------------------- | ----------- | ----------------- | ---------- | -------- |
| /Robot1/KuKaAgvStateTopic | agv状态话题，uagv/v2/kuka/1001/state的完整透传 | vda5050_msgs/msg/AGVState.msg          | RPCS_S_agv_go_point_service | agv走点服务 | 每5秒发布一次话题 | 2025/07/04 |          |
| /Robot1/AgvStateTopic     | agv状态话题，研发自己总结的状态话题            | rpcs_s_interfaces_agv/msg/AGVState.msg | RPCS_S_agv_go_point_service | agv走点服务 | 每5秒发布一次话题 | 2025/07/04 |          |

### 服务列表:
| 服务                      | 服务名称        | 所用协议                                 | 发提供服务工程名            | 节点描述    | 为什么节点需要此协议                 | 添加时间   | 修改时间 |
| ------------------------- | --------------- | ---------------------------------------- | --------------------------- | ----------- | ------------------------------------ | ---------- | -------- |
| /Robot1/AgvGoPointService | agv走点服务     | rpcs_s_interfaces_agv/srv/AgvGoPoint.srv | RPCS_S_agv_go_point_service | agv走点服务 | 提供走点服务                         | 2025/07/04 |          |
| /Robot1/AgvStateService   | 获取AGV状态服务 | rpcs_s_interfaces_agv/srv/AGVState.srv   | RPCS_S_agv_go_point_service | agv走点服务 | 可用于开机自检，工艺中走点之前的检查 | 2025/07/04 |          |


