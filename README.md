# RPCS M Controllers Interfaces

这个仓库包含RPCS系统中所有的ROS2接口定义，包括消息(msg)、服务(srv)和动作(action)。

## 目录结构

```
RPCS_M_Controllers_interfaces/
├── src/
│   ├── rpcs_s_interfaces_behavior_tree/  # 行为树接口
│   ├── rpcs_s_interfaces_canopen/        # CANopen接口
│   ├── rpcs_s_interfaces_fexiv/          # Fexiv接口
│   ├── rpcs_s_interfaces_imu/            # IMU接口
│   ├── rpcs_s_interfaces_io_board/       # IO板接口
│   ├── rpcs_s_interfaces_screwdriver/    # 电动螺丝刀接口
│   └── rpcs_s_interfaces_agv/            # AGV接口
```

## 包命名规范

所有接口包必须遵循以下命名规范:

### 命名格式

```
rpcs_s_interfaces_<功能模块>
```

- `rpcs_s`: 表示RPCS系统S级别接口
- `interfaces`: 表示这是接口定义包
- `<功能模块>`: 具体的功能模块名称，如behavior_tree、canopen等

### 具体包介绍

- **rpcs_s_interfaces_behavior_tree**: 行为树相关的接口
- **rpcs_s_interfaces_canopen**: CANopen协议相关的接口
- **rpcs_s_interfaces_fexiv**: Fexiv电机控制相关的接口
- **rpcs_s_interfaces_imu**: 惯性测量单元相关的接口
- **rpcs_s_interfaces_io_board**: IO板控制相关的接口
- **rpcs_s_interfaces_screwdriver**: 电动螺丝刀控制相关的接口
- **rpcs_s_interfaces_agv**: AGV移动控制相关的接口

## 接口文件命名规范

### 消息(msg)命名

- 使用CamelCase(驼峰命名)
- 描述性名称，表明数据内容
- 例: `MotorStatus.msg`, `JointPosition.msg`

### 服务(srv)命名

- 使用动词+名词的CamelCase格式
- 清晰表达服务的功能
- 例: `GetNodeStatus.srv`, `SetParameters.srv`

### 动作(action)命名

- 使用动词的CamelCase格式
- 表示长时间运行的任务
- 例: `ExecuteTask.action`, `MoveTo.action`

## 多人协作指南

### 添加新接口

1. **确定合适的包**:
   - 根据接口的功能确定应放在哪个现有包中
   - 如果不属于任何现有包，可以创建新的包(遵循命名规范)

2. **创建接口文件**:
   ```bash
   # 在适当的包目录下创建接口文件
   mkdir -p src/rpcs_s_interfaces_<功能模块>/{msg,srv,action}
   touch src/rpcs_s_interfaces_<功能模块>/msg/YourMessage.msg
   ```

3. **更新CMakeLists.txt**:
   ```cmake
   # 在interface_files列表中添加新接口
   set(interface_files
     "msg/ExistingMsg.msg"
     "msg/YourMessage.msg"  # 新增的接口
   )
   ```

### 修改现有接口

**重要**: 接口一旦被使用，修改需谨慎！

1. **兼容性修改**:
   - 添加新字段(在末尾)
   - 扩展枚举值

2. **破坏性修改**:
   - 如需进行破坏性修改，创建新版本的接口
   - 例: `StatusV2.msg` 而不是修改 `Status.msg`

### 版本控制

1. **接口版本控制**:
   - 接口文件命名中可以包含版本，如 `MotorStatusV2.msg`
   - 在注释中注明版本和变更记录

2. **Git工作流**:
   - 使用Feature分支开发新功能
   - 提交前进行本地编译测试
   - 提交PR前确保不影响现有功能

### 编译和测试

```bash
# 编译单个包
colcon build --packages-select rpcs_s_interfaces_<功能模块>

# 编译所有包
colcon build

# 加载环境
source install/setup.bash

# 验证接口
ros2 interface show rpcs_s_interfaces_<功能模块>/msg/YourMessage
```

## 接口使用示例

### C++中使用接口

```cpp
#include "rpcs_s_interfaces_agv/srv/agv_go_point.hpp"
#include "rclcpp/rclcpp.hpp"

// 创建服务客户端
auto client = node->create_client<rpcs_s_interfaces_agv::srv::AgvGoPoint>("agv_go_point");

// 创建请求
auto request = std::make_shared<rpcs_s_interfaces_agv::srv::AgvGoPoint::Request>();
request->request = "{\"go_point_name\": \"station_1\"}";

// 发送请求
auto future = client->async_send_request(request);
```

### Python中使用接口

```python
from rpcs_s_interfaces_agv.srv import AgvGoPoint
import rclpy

# 创建服务客户端
client = node.create_client(AgvGoPoint, 'agv_go_point')

# 创建请求
request = AgvGoPoint.Request()
request.request = '{"go_point_name": "station_1"}'

# 发送请求
future = client.call_async(request)
```

## 最佳实践

1. **文档化**:
   - 每个接口文件都应有详细注释
   - 描述每个字段的含义、单位、范围等

2. **接口设计**:
   - 保持简单，仅包含必要字段
   - 使用标准类型
   - 考虑未来扩展性

3. **协作流程**:
   - 接口变更需团队评审
   - 所有接口需经过测试
   - 维护接口变更日志
