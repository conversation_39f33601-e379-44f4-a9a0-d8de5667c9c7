cmake_minimum_required(VERSION 3.8)
project(rpcs_s_interfaces_io_board)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)

# 定义接口文件列表
set(interface_files
  # 服务文件（使用相对路径）
  "srv/GetIOStatus.srv"
  "srv/ReadRegister.srv"
  "srv/SetOutput.srv"
  "srv/WriteRegister.srv"
  
  # 消息文件（使用相对路径）
  "msg/InputStatus.msg"
  "msg/OutputStatus.msg"
  "msg/RemoteIOStatus.msg"
)

# 只有当接口文件列表非空时才生成接口
if(interface_files)
  rosidl_generate_interfaces(${PROJECT_NAME}
    ${interface_files}
    DEPENDENCIES std_msgs
  )

  # 确保生成的接口被正确导出
  ament_export_dependencies(rosidl_default_runtime)
endif()

ament_package()
