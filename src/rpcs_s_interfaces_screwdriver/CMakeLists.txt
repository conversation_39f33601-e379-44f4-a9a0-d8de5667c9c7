cmake_minimum_required(VERSION 3.8)
project(rpcs_s_interfaces_screwdriver)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)

# 定义接口文件列表
set(interface_files
  # 添加你的自定义接口文件
  # "msg/YourMsg.msg"
  # "srv/YourSrv.srv"
  # "action/YourAction.action"
)

# 只有当接口文件列表非空时才生成接口
if(interface_files)
  rosidl_generate_interfaces(${PROJECT_NAME}
    ${interface_files}
  )
endif()

ament_package()
