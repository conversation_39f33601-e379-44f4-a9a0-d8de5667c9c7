cmake_minimum_required(VERSION 3.8)
project(rpcs_interfaces_common)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)

set(interface_files
  "msg/Info.msg"
  "msg/Error.msg"
  "msg/ErrorReference.msg"
  "msg/InfoReference.msg"
  "msg/ComposeState.msg"
)
rosidl_generate_interfaces(${PROJECT_NAME}
  ${interface_files}
)

ament_package()
