cmake_minimum_required(VERSION 3.8)
project(rpcs_s_interfaces_agv)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)

# 定义接口文件列表
set(interface_files
  "msg/AGVState.msg"
  "srv/AgvGoPoint.srv"
  "srv/AGVState.srv"
  # 添加其他自定义接口文件
  # "msg/YourMsg.msg"
  # "srv/YourSrv.srv"
  # "action/YourAction.action"
)

# 生成接口
rosidl_generate_interfaces(${PROJECT_NAME}
  ${interface_files}
)

ament_package()
