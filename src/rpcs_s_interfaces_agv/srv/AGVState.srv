---
# 由agv_base_state可以知道，是否需要人工干预
# 代表agv小车现在是否正常，true正常，false不正常
# 正常包含空闲、工作、不用人工干预
# 不正常包含，FATAL致命故障、小车mqtt断线、需要人工干预
bool agv_base_state


# 由agv_state可以知道，小车是否可以走点、是否需要人工干预
# 小车状态 IDLE、DRIVING、FATAL、INITIALIZING
# IDLE代表小车可以走点，此时小车没有故障、没有在走点、当然如果正在充电也算空闲。
# DRIVING小车正在走点，此时 go_point_name可以看到走点业务名称
# FATAL致命故障，需要人工干预解决故障
# INITIALIZING代表AGV小车刚开机，还没有初始化完毕
string agv_state

# 走点业务名称，当agv_state=DRIVING时，go_point_name有意义
string go_point_name
# 故障信息，当agv_state=FATAL时，fatal_msg有意义
string fatal_msg


# 电池电量，比如95
float64 battery_charge

# 电池是否正在充电中
bool battery_charging

# 电池错误级别，WARNING, FATAL
# 低于50%，WARNING警告，小车应该回充电点位进行充电，如果在取料点位停止作业，应该关闭电源
# 低于30%，FATAL致命故障，AGV开机后小车会在10分钟内关机
string battery_error_level


# 当前是几号小车，当然根据话题名称查看也可以。
int32 robot_number


#agv是否正在报警告，或者报过警告，不影响agv工作，给维护、研发用的，最好看一下有什么问题。
bool is_warning
#警告信息
string warning_msg

