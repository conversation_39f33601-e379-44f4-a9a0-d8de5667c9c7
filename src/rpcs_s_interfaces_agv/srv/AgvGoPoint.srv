# 京东方显示屏组装机器人，AGV走点服务的请求和响应格式
#
# 请求报文：
# json字符串，举例
# {
#     "go_point_name": "走点业务名称"
# }
#
string request
---
#
# 响应报文：
bool success   # indicate successful run of triggered service
string message # informational, e.g. for error messages
# 故障码专用响应报文，success为false时使用，json字符串，其中故障原因未必能给出来，比如丢失定位的原因
# {
#     "code": "英文大写描述的故障码",
#     "problem": "问题描述、或者问题现象",
#     "reason": "故障原因",
#     "instructions": "操作指引"
# }
string response

