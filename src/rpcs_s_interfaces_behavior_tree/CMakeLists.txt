cmake_minimum_required(VERSION 3.8)
project(rpcs_s_interfaces_behavior_tree)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(builtin_interfaces REQUIRED)

# 定义接口文件列表
set(interface_files
  "action/ExecuteProcessAction.action"

  "msg/Extend.msg"
  # 添加你的其他自定义接口文件
  # "msg/YourMsg.msg"
  # "srv/YourSrv.srv"
  # "action/YourAction.action"
)

# 生成接口
rosidl_generate_interfaces(${PROJECT_NAME}
  ${interface_files}
  DEPENDENCIES builtin_interfaces
)

ament_package()
