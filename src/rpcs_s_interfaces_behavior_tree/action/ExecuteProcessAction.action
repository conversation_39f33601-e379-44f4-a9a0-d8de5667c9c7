# Goal - 执行工艺动作的请求
string process_action_type    # 工艺动作类型，对应具体的工艺动作树文件
string robot_id              # 机器人ID (Robot1, Robot2, Robot3)
string[] process_parameters  # 工艺参数列表
int32 timeout_seconds        # 超时时间（秒），0表示无超时
bool preempt_current         # 是否抢占当前正在执行的工艺动作
string process_id            # 工艺流程ID，用于追踪
rpcs_s_interfaces_behavior_tree/Extend[] extends            #扩展信息
---
# Result - 工艺执行结果
bool success                 # 工艺执行是否成功
string result_message        # 结果描述信息
string final_status          # 最终状态 (SUCCESS, FAILURE, ABORTED)
float64 execution_time       # 工艺执行时间（秒）
string[] process_output_data # 工艺输出数据
string quality_status        # 质量状态 (OK, NG, UNKNOWN)
---
# Feedback - 工艺执行反馈
string current_process_step  # 当前执行的工艺步骤名称
string current_status        # 当前状态 (RUNNING, SUCCESS, FAILURE)
float64 progress_percent     # 工艺执行进度百分比 (0.0-100.0)
string status_message        # 状态描述信息
string current_operation     # 当前操作描述
builtin_interfaces/Time timestamp  # 时间戳 