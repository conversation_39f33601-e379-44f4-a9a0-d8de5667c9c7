cmake_minimum_required(VERSION 3.8)
project(vir_robot_interfaces)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)

# 添加服务文件
rosidl_generate_interfaces(${PROJECT_NAME}
  msg/SuckerKeyValue.msg
  srv/CorrectionPosition.srv
  srv/Conveyor.srv
  srv/PetStrip.srv
  srv/Sucker.srv
  srv/PressHoldBoard.srv
  srv/Workbench.srv
  srv/SemiFinishedProduct.srv
  srv/MaterialState.srv
  srv/MaterialUpload.srv
  srv/ProductPowerOnDetect.srv
  srv/FpcOperate.srv
  srv/ProtectShell.srv
  srv/AgvMaterial.srv
  srv/ImageDetection.srv
  
  action/AgvMaterial.action
  action/Conveyor.action
  action/CorrectionPosition.action
  action/FpcOperate.action
  action/MaterialState.action
  action/MaterialUpload.action
  action/PetStrip.action
  action/PressHoldBoard.action
  action/ProductPowerOnDetect.action
  action/ProtectShell.action
  action/SemiFinishedProduct.action
  action/Sucker.action
  action/Workbench.action
  action/CorrectionPositionV1.action
  action/CommandTest.action
  action/SystemCtrl.action

  msg/PlcFeedback.msg
  msg/BoardDetectionResult.msg
  msg/Command.msg
  msg/ImageDetectionResult.msg
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()

install(DIRECTORY action
  DESTINATION share/${PROJECT_NAME}/
)
