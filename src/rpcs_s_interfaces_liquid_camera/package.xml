<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rpcs_s_interfaces_liquid_camera</name>
  <version>0.0.0</version>
  <description>ROS2 interfaces package for liquid camera, containing action, service and message definitions</description>
  <maintainer email="<EMAIL>">robot</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <build_depend>action_msgs</build_depend>
  <exec_depend>action_msgs</exec_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>
  

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
