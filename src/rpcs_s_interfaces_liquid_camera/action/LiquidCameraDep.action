
# 目标请求 - Goal
string param_overrides     # 参数覆盖，JSON格式的键值对字符串，例如: {"threshold": "0.8", "max_detections": "100"}
---
# 结果 - Result
bool success               # 是否成功
string message             # 状态消息
bool is_finish             # 是否完成
Command[] commands  # 移动命令数组
---
# Feedback
string current_process_step   # 当前工序
string current_status         # 当前状态
string progress_percent       # 进度百分比
string status_message         # 状态描述