 cmake_minimum_required(VERSION 3.8)
project(algorithm_interface)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)

# 定义接口文件列表
set(interface_files
  "srv/BoardAlign.srv"
  "srv/RobotAlign.srv"
  "srv/CommonAlign.srv"
  "srv/DepthFusion.srv"
  "srv/VehiclesAlign.srv"

  "msg/MoveCommand.msg"
  "msg/FusionResult.msg"
  "msg/ImageData.msg"
)

if(interface_files)
  rosidl_generate_interfaces(${PROJECT_NAME}
    ${interface_files}
    DEPENDENCIES sensor_msgs
    DEPENDENCIES std_msgs
  )

  ament_export_dependencies(rosidl_default_runtime)
endif()

ament_package()