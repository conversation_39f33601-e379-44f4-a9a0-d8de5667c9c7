<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rpcs_interfaces_motor</name>
  <version>1.0.0</version>
  <description>ROS2 action interfaces for RPCS motor control system including position, velocity, torque and homing control</description>
  <maintainer email="<EMAIL>">robot</maintainer>
  <license>Apache-2.0</license>

  <!-- Build tool dependencies -->
  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <!-- Build dependencies -->
  <build_depend>rosidl_default_generators</build_depend>
  
  <!-- Runtime dependencies -->
  <exec_depend>rosidl_default_runtime</exec_depend>
  
  <!-- Interface package membership -->
  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
