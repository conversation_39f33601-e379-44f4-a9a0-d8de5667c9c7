# rpcs_interfaces_motor

## 概述

`rpcs_interfaces_motor` 是一个ROS2接口包，为RPCS电机控制系统提供标准化的Action接口定义。

## 包含的接口

### Action接口

1. **PositionControl.action** - 位置控制接口
   - 支持绝对位置和相对位置控制
   - 梯形速度曲线规划
   - 实时反馈和进度监控

2. **VelocityControl.action** - 速度控制接口
   - 恒定速度运行
   - 时间限制和位置边界保护
   - 动态速度调整

3. **TorqueControl.action** - 转矩控制接口
   - 精确转矩输出控制
   - 速度限制保护
   - 适用于力控制应用

4. **HomingControl.action** - 原点控制接口
   - 多种原点搜索方法
   - 限位开关和索引脉冲支持
   - 系统初始化和校准

## 构建和使用

### 构建

```bash
cd /path/to/your/workspace
colcon build --packages-select rpcs_interfaces_motor
source install/setup.bash
```

### 验证接口

```bash
# 查看所有生成的接口
ros2 interface list | grep rpcs_interfaces_motor

# 查看特定接口定义
ros2 interface show rpcs_interfaces_motor/action/PositionControl
ros2 interface show rpcs_interfaces_motor/action/VelocityControl
ros2 interface show rpcs_interfaces_motor/action/TorqueControl
ros2 interface show rpcs_interfaces_motor/action/HomingControl
```

### 在其他包中使用

在需要使用这些接口的包的 `package.xml` 中添加依赖：

```xml
<depend>rpcs_interfaces_motor</depend>
```

在 `CMakeLists.txt` 中添加：

```cmake
find_package(rpcs_interfaces_motor REQUIRED)

# 在生成executable时添加依赖
ament_target_dependencies(your_node
  rpcs_interfaces_motor
  # 其他依赖...
)
```

## 支持的电机品牌

- Kinco (步科) 电机
- UMot (优默特) 电机

## 版本信息

- **版本**: 1.0.0
- **兼容性**: ROS2 Humble及以上版本
- **许可证**: Apache-2.0

## 维护信息

- **维护者**: robot <<EMAIL>>
- **包描述**: ROS2 action interfaces for RPCS motor control system 