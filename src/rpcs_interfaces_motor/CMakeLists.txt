cmake_minimum_required(VERSION 3.8)
project(rpcs_interfaces_motor)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)

# 定义action接口文件
set(action_files
  "action/PositionControl.action"
  "action/VelocityControl.action"
  "action/TorqueControl.action"
  "action/HomingControl.action"
  "action/DigitalInputRead.action"
  "action/DigitalOutputWrite.action"
  "action/DigitalOutputRead.action"
  "action/BatchIOOperation.action"
  "action/RobotArmControl.action"
)

# 生成接口
rosidl_generate_interfaces(${PROJECT_NAME}
  ${action_files}
  DEPENDENCIES
    std_msgs
)

# 导出接口
ament_export_dependencies(rosidl_default_runtime)

ament_package() 