# BatchIOOperation.action
# 批量IO操作Action接口

# Goal - 目标参数
# 读取操作
uint16[] read_input_addresses    # 要读取的输入地址列表
uint16[] read_output_addresses   # 要读取的输出地址列表

# 写入操作
uint16[] write_addresses         # 要写入的输出地址列表
bool[] write_values             # 要写入的值列表，必须与write_addresses数量一致

# 操作选项
bool verify_writes              # 是否验证写入结果
bool atomic_operation           # 是否原子操作（失败时回滚）
float64 operation_timeout       # 操作超时时间(秒)，0使用默认超时

---

# Result - 执行结果
# 读取结果
bool[] input_values             # 读取到的输入值
bool[] output_values            # 读取到的输出值
uint16[] input_addresses        # 输入地址列表
uint16[] output_addresses       # 输出地址列表

# 写入结果
bool[] write_success            # 每个写入操作是否成功
uint16[] write_addresses        # 写入地址列表
bool[] final_write_values       # 最终写入值（验证后的值）

# 整体结果
bool success                    # 整体操作是否成功
int32 error_code               # 错误代码，0表示成功
string error_message           # 错误信息描述
uint32 total_operations        # 总操作数
float64 total_execution_time   # 总执行时间(毫秒)

---

# Feedback - 实时反馈
string current_operation        # 当前操作类型（"reading_inputs", "reading_outputs", "writing_outputs", "verifying_writes"）
uint32 completed_operations     # 已完成操作数
uint32 total_operations         # 总操作数
float64 progress               # 完成进度 (0.0-1.0)
string status_message          # 状态信息
float64 elapsed_time           # 已运行时间(秒) 