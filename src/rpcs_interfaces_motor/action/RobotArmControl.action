# Goal - 输入参数
int32 project_id                    # 工程ID
int32 speed_multiplier              # 速度倍率
float32 position_x                  # X坐标
float32 position_y                  # Y坐标  
float32 position_z                  # Z坐标
float32 rotation_rx                 # Rx旋转角度
float32 rotation_ry                 # Ry旋转角度
float32 rotation_rz                 # Rz旋转角度
int32[6] function_data             # 6个功能数据
float64 timeout_seconds            # 超时时间（秒）

---
# Result - 输出结果（基于图片中的输出参数）
bool success                        # 操作是否成功
string error_message               # 错误信息
float64 execution_time             # 执行时间（秒）
int32 error_code                   # 错误码 (IntOut端口1: int32_1)

# 程序状态输出
bool program_running               # 程序运行 (BitOut 1)
bool program_paused                # 程序已暂停 (BitOut 2) 
bool remote_mode_enabled           # 远程模式启用 (BitOut 3)
bool emergency_stop                # 急停 (BitOut 4)
bool program_clear_request         # 程序清求 (BitOut 5)
bool error_status                  # 错误状态 (BitOut 6)
bool motor_power_status            # 电机打开状态 (BitOut 7)

# 工程和状态信息
int32 project_id_status           # 工程ID状态 (IntOut 1)

---
# Feedback - 实时反馈
float64 progress_percentage        # 执行进度百分比
string current_status             # 当前状态描述
float64 elapsed_time              # 已用时间（秒）

# 实时状态反馈
bool program_running_feedback     # 程序运行状态反馈
bool program_paused_feedback      # 程序暂停状态反馈
bool remote_mode_feedback         # 远程模式状态反馈
bool emergency_stop_feedback      # 急停状态反馈
bool error_status_feedback        # 错误状态反馈
bool motor_power_feedback         # 电机电源状态反馈

# 当前位置反馈
float32 current_x                 # 当前X坐标
float32 current_y                 # 当前Y坐标
float32 current_z                 # 当前Z坐标
float32 current_rx                # 当前Rx角度
float32 current_ry                # 当前Ry角度
float32 current_rz                # 当前Rz角度 