# Goal
int8 homing_method # 寻找原点的方法(1-35), 对应对象索引0x6098
float32 speed_switch # 开关搜索速度(rpm), 内部自动转换为DEC单位
float32 speed_zero # 零点搜索速度(rpm), 内部自动转换为DEC单位
int32 home_offset # 原点偏移量(mm), 对应对象索引0x607C
uint32 position_window # 位置窗口(inc), 对应对象索引0x6067
uint16 position_window_time # 位置窗口时间(ms), 对应对象索引0x6068
float64 timeout # 超时时间（秒），0或负数表示使用服务器默认超时
---
# Result
bool success
string message
float32 final_position
---
# Feedback
uint16 status
string status_description
float32 current_position 