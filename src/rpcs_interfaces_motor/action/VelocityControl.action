# 目标速度（物理单位/秒）
float64 target_velocity
# 加速度（物理单位/秒^2）
float64 acceleration
# 减速度（物理单位/秒^2）
float64 deceleration
# 运行时间（秒，0表示持续运行直到取消）
float64 duration
# 目标电流限制（A）
float64 target_current_limit
# 位置限制（可选，物理单位）
bool use_position_limits
float64 min_position
float64 max_position
float64 timeout # 超时时间（秒），0或负数表示使用服务器默认超时
---
# 最终速度（物理单位/秒）
float64 final_velocity
# 最终位置（物理单位）
float64 final_position
# 是否成功完成
bool success
# 错误代码
int32 error_code
# 错误信息
string error_message
---
# 当前速度（物理单位/秒）
float64 current_velocity
# 当前位置（物理单位）
float64 current_position
# 当前转矩（物理单位）
float64 current_torque
# 已运行时间（秒）
float64 elapsed_time
# 剩余时间（秒，持续运行时为-1）
float64 time_remaining 