# DigitalOutputRead.action
# 数字输出读取Action接口

# Goal - 目标参数
uint16[] output_addresses   # 要读取的输出地址列表，空数组表示读取所有输出
bool continuous_read        # 是否连续读取模式
float64 read_interval      # 连续读取间隔(秒)，仅在continuous_read=true时有效
float64 duration           # 连续读取持续时间(秒)，0表示无限制，仅在continuous_read=true时有效

---

# Result - 执行结果
bool[] output_values       # 读取到的输出值
uint16[] addresses         # 对应的地址
bool success               # 操作是否成功
int32 error_code          # 错误代码，0表示成功
string error_message      # 错误信息描述
uint32 total_reads        # 总读取次数
float64 average_read_time  # 平均读取时间(毫秒)

---

# Feedback - 实时反馈
bool[] current_values      # 当前读取到的输出值
uint16[] addresses         # 对应的地址
uint32 read_count         # 已完成的读取次数
float64 elapsed_time      # 已运行时间(秒)
float64 average_read_time # 平均读取时间(毫秒)
string status_message     # 状态信息 