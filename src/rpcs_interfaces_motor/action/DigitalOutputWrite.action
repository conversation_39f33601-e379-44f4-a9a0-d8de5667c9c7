# DigitalOutputWrite.action
# 数字输出写入Action接口

# Goal - 目标参数
uint16[] output_addresses   # 要写入的输出地址列表
bool[] output_values       # 要写入的值列表，必须与addresses数量一致
bool verify_write          # 是否验证写入结果
bool batch_operation       # 是否使用批量操作模式
float64 write_timeout      # 写入超时时间(秒)，0使用默认超时

---

# Result - 执行结果
bool[] final_values        # 最终的输出值（验证后的值）
uint16[] addresses         # 对应的地址
bool[] write_success       # 每个地址的写入是否成功
bool success               # 整体操作是否成功
int32 error_code          # 错误代码，0表示成功
string error_message      # 错误信息描述
uint32 write_operations   # 总写入操作次数
float64 average_write_time # 平均写入时间(毫秒)

---

# Feedback - 实时反馈
uint16 current_address     # 当前处理的地址
bool current_value         # 当前写入的值
uint32 completed_writes    # 已完成的写入数量
uint32 total_writes        # 总写入数量
float64 progress          # 完成进度 (0.0-1.0)
string status_message     # 状态信息 