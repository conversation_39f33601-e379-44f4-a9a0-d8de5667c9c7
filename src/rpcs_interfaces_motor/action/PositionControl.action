# 目标位置（物理单位）(对象索引: 607A00)
float64 target_position
# 位置模式选择：true=绝对位置模式(0x2F->0x3F), false=相对位置模式(0x4F->0x5F)
bool absolute_position
# 梯形速度（物理单位/秒）(对象索引: 608100)
float64 max_velocity
# 梯形加速度（物理单位/秒^2）(对象索引: 608300)
float64 acceleration
# 梯形减速度（物理单位/秒^2）(对象索引: 608400)
float64 deceleration
# 运动完成后的停留时间（秒）
float64 dwell_time
float64 timeout # 超时时间（秒），0或负数表示使用服务器默认超时
---
# 最终位置（物理单位）
float64 final_position
# 位置误差（物理单位）
float64 position_error
# 是否成功到达目标
bool success
# 错误代码
int32 error_code
# 错误信息
string error_message
---
# 当前位置（物理单位）
float64 current_position
# 当前速度（物理单位/秒）
float64 current_velocity
# 当前转矩（物理单位）
float64 current_torque
# 完成百分比（0.0-1.0）
float64 progress
# 剩余时间（秒）
float64 time_remaining 