# RPCS_S_Controller_Motor API接口文档

## 概述

RPCS_S_Controller_Motor 是一个多品牌电机控制系统，提供统一的ROS2接口用于控制不同品牌的电机（Kinco、UMot等）。本文档详细介绍了系统提供的所有API接口，包括Action接口、服务接口、话题接口和统一电机接口。

### 设备ID与多设备支持

系统支持设备ID功能，允许在同一局域网中部署多个电机控制系统而不产生冲突。通过为每个设备分配唯一的ID前缀，所有的话题和服务名称都会自动添加设备前缀。

#### 话题命名规则

- **无设备ID时**（传统单设备模式）：`/motor_X/action_name`
- **有设备ID时**（多设备模式）：`/{device_id}/motor_X/action_name`

#### 示例对比

**单设备模式**：
```
/motor_5/position_control
/motor_5/velocity_control
/motor_6/position_control
```

**多设备模式**（设备ID为"Robot1"）：
```
/Robot1/motor_5/position_control
/Robot1/motor_5/velocity_control
/Robot1/motor_6/position_control
```

#### 节点命名

- **无设备ID时**：节点名为 `motor_controller`
- **有设备ID时**：节点名为 `{device_id}_motor_controller`（如：`Robot1_motor_controller`）

## 目录

1. [设备ID配置](#设备id配置)
2. [对象索引快速参考](#对象索引快速参考)
3. [Action接口](#action接口)
   - [位置控制Action](#位置控制action)
   - [速度控制Action](#速度控制action)
   - [转矩控制Action](#转矩控制action)
   - [原点控制Action](#原点控制action)
4. [服务接口](#服务接口)
5. [话题接口](#话题接口)
6. [统一电机接口](#统一电机接口)
7. [数据类型定义](#数据类型定义)
8. [错误代码](#错误代码)
9. [Action超时控制说明](#action超时控制说明)
10. [使用示例](#使用示例)

---

## 设备ID配置

### 配置方法

#### 方法1：配置文件中设置

```yaml
# 配置文件示例
device:
  id: "Robot1"                 # 设备唯一标识符
  description: "主控制器"       # 设备描述（可选）

motors:
  - node_id: 5
    brand: "Kinco"
    # ... 其他配置
```

#### 方法2：启动参数覆盖

```bash
# 使用启动参数指定设备ID
ros2 launch rpcs_s_control_motor kinco_motors.launch.py device_id:=Robot1

# 启动参数会覆盖配置文件中的设备ID
ros2 launch rpcs_s_control_motor motor_controller_with_device_id.launch.py device_id:=Station_A
```

### 启动示例

```bash
# 启动带设备ID的控制器
ros2 launch rpcs_s_control_motor motor_controller_with_device_id.launch.py device_id:=Robot1

# 查看生成的Action话题
ros2 action list | grep Robot1
```

---

## 对象索引快速参考

### 步科电机CANopen对象索引汇总表

本节提供步科电机控制中涉及的所有CANopen对象索引的快速参考。

#### 运动控制对象索引

| 对象索引 | 参数名称 | 数据类型 | 物理单位 | 转换系数 | 说明 |
|---------|----------|----------|----------|----------|------|
| **基础控制** |
| 0x6040 | 控制字 | Unsigned16 | DEC | 1.0 | 状态机控制序列 |
| 0x6041 | 状态字 | Unsigned16 | DEC | 1.0 | 设备状态反馈 |
| 0x6060 | 工作模式 | Integer8 | DEC | 1.0 | 1=位置, -3=立即速度, 4=力矩, 6=原点 |
| 0x6061 | 模式显示 | Integer8 | DEC | 1.0 | 当前实际工作模式 |
| **位置控制** |
| 0x607A | 目标位置 | Integer32 | mm | 编码器分辨率 | 绝对/相对位置目标 |
| 0x6064 | 实际位置 | Integer32 | inc | 编码器分辨率 | 当前位置反馈 |
| 0x6067 | 位置窗口 | Unsigned32 | inc | 1.0 | 到位判断窗口 |
| 0x6068 | 位置窗口时间 | Unsigned16 | ms | 1.0 | 到位判断时间 |
| **速度控制** |
| 0x6080 | 最大速度限制 | Unsigned16 | rpm | **1.0** | ⚠️无需转换 |
| 0x6081 | 梯形速度 | Unsigned32 | rpm | 17895.7 | 梯形运动最大速度 |
| 0x60FF | 目标速度 | Integer32 | rpm | 17895.7 | 速度模式目标速度 |
| 0x606C | 实际速度 | Integer32 | rpm | 17895.7 | 当前速度反馈 |
| 0x6073 | 目标电流限制 | Unsigned16 | A | **42.67** | ⚠️**步科实测确认转换系数** |
| **加速度控制** |
| 0x6083 | 梯形加速度 | Unsigned32 | rps/s | 1073.75 | 加速阶段加速度 |
| 0x6084 | 梯形减速度 | Unsigned32 | rps/s | 1073.75 | 减速阶段减速度 |
| **力矩控制** |
| 0x6071 | 目标力矩 | Integer16 | % | 10.0 | 额定力矩百分比 |
| 0x6078 | 实际电流值 | Integer16 | Ap | 42.67 | 步科特有，代替0x6077 |
| 0x60F608 | 目标电流 | Integer16 | Ap | 42.67 | 力矩模式电流指令 |
| **原点控制** |
| 0x6098 | 寻原点方法 | Integer8 | DEC | 1.0 | 原点搜索方法(1-35) |
| 0x6099.01 | 开关搜索速度 | Unsigned32 | rpm | 17895.7 | 第一阶段搜索速度 |
| 0x6099.02 | 零点搜索速度 | Unsigned32 | rpm | 17895.7 | 第二阶段搜索速度 |
| 0x607C | 原点偏移量 | Integer32 | inc | 编码器分辨率 | 原点位置偏移 |
| 0x607D | 软件位置限制 | Integer32 | inc | 编码器分辨率 | 正负向位置限制 |
| 0x607E | 极性 | Unsigned8 | DEC | 1.0 | 编码器和电机极性 |

#### 重要转换系数说明

| 转换系数 | 适用对象 | 转换示例 | 注意事项 |
|---------|----------|----------|----------|
| **1.0** | 0x6080(最大速度限制) | 30 rpm → 30 DEC | ⚠️特殊：无需转换 |
| **10.0** | 0x6071(目标力矩) | 26.70% → 267 DEC | 百分比转换 |
| **17895.7** | 0x6081,0x60FF,0x606C,0x6099 | 20.0 rpm → 357914 DEC | 标准速度转换 |
| **1073.75** | 0x6083,0x6084 | 20.0 rps/s → 21475 DEC | 加减速度转换 |
| **42.67** | 0x6073,0x6078,0x60F608 | 23.44 Ap → 1000 DEC | ⚠️**步科实测电流转换** |
| **编码器分辨率** | 位置相关对象 | 取决于具体配置 | 通常为4000-10000 |

#### 步科电机特殊性

| 特殊性 | 说明 | 标准DS402值 | 步科实际值 |
|--------|------|-------------|------------|
| 力矩模式值 | 工作模式设置 | 10 | **4** |
| 实际转矩对象 | 反馈对象索引 | 0x6077 | **0x6078** |
| 最大速度限制 | 转换关系 | 需要转换 | **无需转换** |

---

## Action接口

Action接口提供异步的电机运动控制功能，支持实时反馈和取消操作。

### 位置控制Action

**Action类型**: `rpcs_interfaces_motor::action::PositionControl`

位置控制Action用于精确的位置运动控制，支持绝对位置和相对位置两种模式。

#### Goal参数

| 参数名 | 类型 | 对象索引 | 数据类型 | 说明 |
|--------|------|----------|----------|------|
| `target_position` | `float64` | 0x607A | Integer32 | 目标位置（物理单位，如mm） |
| `absolute_position` | `bool` | 0x6040 | Unsigned16 | 位置模式选择：true=绝对位置(0x2F→0x3F)，false=相对位置(0x4F→0x5F) |
| `max_velocity` | `float64` | 0x6080 | Unsigned16 | 最大速度限制（rpm，无需转换） |
| `acceleration` | `float64` | 0x6083 | Unsigned32 | 梯形运动的加速度（rps/s，转换系数1073.75） |
| `deceleration` | `float64` | 0x6084 | Unsigned32 | 梯形运动的减速度（rps/s，转换系数1073.75） |
| `dwell_time` | `float64` | - | - | 到达目标位置后的停留时间（秒，软件实现） |
| `timeout` | `float64` | - | - | 超时时间（秒），0或负数表示使用服务器默认超时（10秒） |

**对象索引转换关系说明**：

- **目标位置 (0x607A)**: 转换系数由编码器分辨率决定，单位转换为inc
- **最大速度限制 (0x6080)**: 无需转换，直接使用rpm值
- **梯形加速度 (0x6083)**: 转换系数 1073.75，20.0 rps/s → 21475 DEC
- **梯形减速度 (0x6084)**: 转换系数 1073.75，20.0 rps/s → 21475 DEC
- **控制字 (0x6040)**: 绝对位置序列 0x2F→0x3F，相对位置序列 0x4F→0x5F

**其他相关对象索引**：

| 对象索引 | 参数名称 | 数据类型 | 单位 | 说明 |
|---------|----------|----------|------|------|
| 0x6081 | 梯形速度 | Unsigned32 | rpm | 梯形运动最大速度，转换系数 17895.7 |
| 0x6060 | 工作模式 | Integer8 | DEC | 位置模式值 1 |
| 0x6061 | 模式显示 | Integer8 | DEC | 显示当前实际工作模式 |
| 0x6064 | 实际位置 | Integer32 | inc | 当前位置反馈 |

**重要参数说明**：

- **`target_position`**: 
  - 绝对位置模式：表示目标坐标位置（例如移动到25mm位置）
  - 相对位置模式：表示从当前位置移动的距离（例如向前移动10mm）
  
- **`absolute_position`**: 控制位置控制的运动模式
  - `true`（绝对位置模式）：电机移动到指定的绝对坐标位置，系统使用控制字序列 0x2F→0x3F
  - `false`（相对位置模式）：电机从当前位置移动指定的距离，系统使用控制字序列 0x4F→0x5F

**运动模式示例**：

假设电机当前位置为 15mm：

| 模式 | target_position | absolute_position | 最终位置 | 说明 |
|------|-----------------|-------------------|----------|------|
| 绝对位置 | 25.0 | true | 25mm | 直接移动到25mm |
| 相对位置 | 10.0 | false | 25mm | 从15mm向前移动10mm |
| 相对位置 | -5.0 | false | 10mm | 从15mm向后移动5mm |

**注意事项**：
- 相对位置模式主要支持Kinco电机，其他品牌电机会自动使用绝对位置模式
- 使用相对位置模式前建议确认当前位置的准确性
- 设置合适的位置限制以避免超出机械限位

#### Feedback反馈

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `current_position` | `float64` | 当前位置（物理单位） |
| `current_velocity` | `float64` | 当前速度（物理单位/秒） |
| `current_torque` | `float64` | 当前转矩（物理单位） |
| `progress` | `float64` | 完成百分比（0.0-1.0） |
| `time_remaining` | `float64` | 预计剩余时间（秒） |

#### Result结果

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `final_position` | `float64` | 最终位置（物理单位） |
| `position_error` | `float64` | 位置误差（物理单位） |
| `success` | `bool` | 是否成功到达目标位置 |
| `error_code` | `int32` | 错误代码（成功时为0） |
| `error_message` | `string` | 错误信息描述 |

### 速度控制Action

**Action类型**: `rpcs_interfaces_motor::action::VelocityControl`

速度控制Action用于控制电机以指定速度运行，支持时间限制和位置限制。

#### Goal参数

| 参数名 | 类型 | 对象索引 | 数据类型 | 说明 |
|--------|------|----------|----------|------|
| `target_velocity` | `float64` | 0x60FF | Integer32 | 目标速度（rpm，转换系数17895.7） |
| `acceleration` | `float64` | 0x6083 | Unsigned32 | 加速度（rps/s，转换系数1073.75） |
| `deceleration` | `float64` | 0x6084 | Unsigned32 | 减速度（rps/s，转换系数1073.75） |
| `duration` | `float64` | - | - | 运行时间（秒），0表示持续运行直到取消 |
| `use_position_limits` | `bool` | - | - | 是否使用位置限制（软件限制） |
| `min_position` | `float64` | - | - | 最小位置限制（物理单位，软件限制） |
| `max_position` | `float64` | - | - | 最大位置限制（物理单位，软件限制） |
| `target_current_limit` | `float64` | 0x6073 | Unsigned16 | **目标电流限制（A，转换系数42.67，默认20A）**⚠️ |
| `timeout` | `float64` | - | - | 超时时间（秒），0或负数表示使用服务器默认超时（10秒） |

**对象索引转换关系说明**：

- **目标速度 (0x60FF)**: 转换系数 17895.7，30.0 rpm → 536871 DEC
- **加速度 (0x6083)**: 转换系数 1073.75，10.0 rps/s → 10737 DEC  
- **减速度 (0x6084)**: 转换系数 1073.75，10.0 rps/s → 10737 DEC
- **目标电流限制 (0x6073)**: **转换系数 42.67，20.0 A → 853 DEC**⚠️**（步科实测数据确认）**
- **控制字 (0x6040)**: **修正的状态切换序列**⚠️**（见下文控制流程说明）**
- **工作模式 (0x6060)**: 速度模式值 3

**其他相关对象索引**：

| 对象索引 | 参数名称 | 数据类型 | 单位 | 说明 |
|---------|----------|----------|------|------|
| 0x6080 | 最大速度限制 | Unsigned16 | rpm | 无需转换，直接使用rpm值 |
| 0x6073 | 目标电流限制 | Unsigned16 | A | **转换系数42.67，与实际电流值相同**⚠️ |
| 0x6061 | 模式显示 | Integer8 | DEC | 显示当前实际工作模式 |
| 0x606C | 实际速度 | Integer32 | rpm | 当前速度反馈，转换系数17895.7 |

#### ⚠️重要修正：速度模式控制流程

**修正的状态转换和参数写入顺序**：

经过步科电机实测验证，速度模式的正确控制流程如下：

**阶段1：电机状态准备**
1. 状态转换：`NOT_READY → SWITCH_ON_DISABLED → READY_TO_SWITCH_ON → SWITCHED_ON`
2. 控制字序列：`0x0006 → 0x0007 → 0x0003`

**阶段2：参数设置**
3. 设置操作模式：`0x6060 = -3`（立即速度模式）
4. 设置加速度：`0x6083 = value × 1073.75`
5. 设置减速度：`0x6084 = value × 1073.75`  
6. 设置目标电流限制：`0x6073 = value × 42.67`**（关键修正：转换系数确认）**

**阶段3：安全启动**⚠️**（关键修正：写入顺序）**
7. **先设置目标速度为0**：`0x60FF = 0`**（防止意外运动）**
8. **后进行状态转换**：`SWITCHED_ON → OPERATION_ENABLED`（控制字`0x000F`）

**阶段4：命令执行**
9. 设置实际目标速度：`0x60FF = target × 17895.7`
10. 监控执行：读取`0x606C`（实际速度）和`0x6063`（实际位置）

**转换系数修正说明**：
- **目标电流限制(0x6073)**：根据步科电机调试界面实测数据
  - 显示值：`1000 DEC ↔ 23.44 Ap`
  - 计算得出：转换系数 = `1000 ÷ 23.44 = 42.67`
  - 与实际电流值(0x6078)和目标电流(0x60F608)使用相同转换系数

**写入顺序修正的重要性**：
- **避免意外运动**：在电机使能前设置目标速度为0
- **参数稳定性**：确保所有控制参数在电机运行前已正确设置
- **安全保障**：遵循步科电机厂家推荐的启动时序

#### Feedback反馈

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `current_velocity` | `float64` | 当前速度（物理单位/秒） |
| `current_position` | `float64` | 当前位置（物理单位） |
| `current_torque` | `float64` | 当前转矩（物理单位） |
| `elapsed_time` | `float64` | 已运行时间（秒） |
| `time_remaining` | `float64` | 剩余时间（秒），持续运行时为-1 |

#### Result结果

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `final_velocity` | `float64` | 最终速度（物理单位/秒） |
| `final_position` | `float64` | 最终位置（物理单位） |
| `success` | `bool` | 是否成功完成 |
| `error_code` | `int32` | 错误代码（成功时为0） |
| `error_message` | `string` | 错误信息描述 |

### 转矩控制Action

**Action类型**: `rpcs_interfaces_motor::action::TorqueControl`

转矩控制Action用于控制电机输出指定的转矩，适用于力控制应用。

#### Goal参数

| 参数名 | 类型 | 对象索引 | 数据类型 | 说明 |
|--------|------|----------|----------|------|
| `target_torque` | `float64` | 0x6071 | Integer16 | 目标转矩（%，占额定转矩的百分比） |
| `velocity_limit` | `float64` | 0x6080 | Unsigned16 | 最大速度限制（rpm，无需转换） |
| `torque_slope` | `float64` | - | - | 转矩变化斜率（物理单位/秒，⚠️软件实现但未写入CANopen对象） |
| `duration` | `float64` | - | - | 运行时间（秒），0表示持续运行直到取消 |
| `use_position_limits` | `bool` | - | - | 是否使用位置限制（软件限制） |
| `min_position` | `float64` | - | - | 最小位置限制（物理单位，软件限制） |
| `max_position` | `float64` | - | - | 最大位置限制（物理单位，软件限制） |
| `timeout` | `float64` | - | - | 超时时间（秒），0或负数表示使用服务器默认超时（10秒） |

**对象索引转换关系说明**：

- **目标转矩 (0x6071)**: 转换系数 10.0，26.70% → 267 DEC
- **最大速度限制 (0x6080)**: 无需转换，30 rpm → 30 DEC
- **控制字 (0x6040)**: 力矩模式状态切换序列 0x6 → 0xF
- **工作模式 (0x6060)**: 力矩模式值 4（步科电机特定值）

**其他相关对象索引**：

| 对象索引 | 参数名称 | 数据类型 | 单位 | 说明 |
|---------|----------|----------|------|------|
| 0x6078 | 实际电流值 | Integer16 | Ap | 实际电流/转矩反馈，转换系数 42.67 |
| 0x60F608 | 目标电流 | Integer16 | Ap | 力矩模式下的电流指令 |
| 0x6061 | 模式显示 | Integer8 | DEC | 显示当前实际工作模式 |

#### Feedback反馈

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `current_torque` | `float64` | 当前转矩（物理单位） |
| `current_velocity` | `float64` | 当前速度（物理单位/秒） |
| `current_position` | `float64` | 当前位置（物理单位） |
| `elapsed_time` | `float64` | 已运行时间（秒） |
| `time_remaining` | `float64` | 剩余时间（秒），持续运行时为-1 |

#### Result结果

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `final_torque` | `float64` | 最终转矩（物理单位） |
| `final_velocity` | `float64` | 最终速度（物理单位/秒） |
| `final_position` | `float64` | 最终位置（物理单位） |
| `success` | `bool` | 是否成功完成 |
| `error_code` | `int32` | 错误代码（成功时为0） |
| `error_message` | `string` | 错误信息描述 |

### 原点控制Action

**Action类型**: `rpcs_interfaces_motor::action::HomingControl`

原点控制Action用于执行电机回零操作，建立位置基准。

#### Goal参数

| 参数名 | 类型 | 对象索引 | 数据类型 | 说明 |
|--------|------|----------|----------|------|
| `homing_method` | `int8` | 0x6098 | Integer8 | 寻找原点的方法（1-35） |
| `speed_switch` | `float32` | 0x6099子索引0x01 | Unsigned32 | 开关搜索速度（rpm，转换系数17895.7） |
| `speed_zero` | `float32` | 0x6099子索引0x02 | Unsigned32 | 零点搜索速度（rpm，转换系数17895.7） |
| `home_offset` | `int32` | 0x607C | Integer32 | 原点偏移量（inc，由编码器分辨率转换） |
| `position_window` | `uint32` | 0x6067 | Unsigned32 | 位置窗口（inc） |
| `position_window_time` | `uint16` | 0x6068 | Unsigned16 | 位置窗口时间（ms） |
| `timeout` | `float64` | - | - | 超时时间（秒），0或负数表示使用服务器默认超时（30秒） |

**对象索引转换关系说明**：

- **寻原点方法 (0x6098)**: 直接使用方法编号，无需转换
- **开关搜索速度 (0x6099.01)**: 转换系数 17895.7，10.0 rpm → 178957 DEC
- **零点搜索速度 (0x6099.02)**: 转换系数 17895.7，5.0 rpm → 89478 DEC
- **原点偏移量 (0x607C)**: 转换系数由编码器分辨率决定
- **控制字 (0x6040)**: 原点模式状态切换序列 0x6 → 0xF → 0x1F
- **工作模式 (0x6060)**: 原点模式值 6

**其他相关对象索引**：

| 对象索引 | 参数名称 | 数据类型 | 单位 | 说明 |
|---------|----------|----------|------|------|
| 0x6061 | 模式显示 | Integer8 | DEC | 显示当前实际工作模式 |
| 0x6041 | 状态字 | Unsigned16 | DEC | 原点搜索状态反馈 |
| 0x607D | 软件位置限制 | Integer32 | inc | 位置限制范围 |
| 0x607E | 极性 | Unsigned8 | DEC | 编码器和电机极性设置 |

#### Feedback反馈

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `status` | `uint16` | 状态字值 |
| `status_description` | `string` | 状态描述 |
| `current_position` | `float32` | 当前位置（mm） |

#### Result结果

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `success` | `bool` | 是否成功找到原点 |
| `message` | `string` | 结果消息 |
| `final_position` | `float32` | 最终位置（mm） |

### Action超时控制说明

**重要特性**：所有Action接口都支持客户端自定义超时控制，为不同的操作需求提供灵活的超时管理。

#### 超时参数使用说明

- **`timeout` 参数**：每个Action的Goal中都包含一个`timeout`字段，用于指定该次请求的最大执行时间
- **默认超时时间**：
  - 位置控制、速度控制、转矩控制：10秒
  - 原点控制：30秒（回零操作通常需要更长时间）
- **超时值设置**：
  - `> 0`：使用指定的超时时间（秒）
  - `≤ 0`：使用服务器的默认超时时间

#### 超时处理机制

1. **超时检测**：Action服务器会持续监控操作的执行时间
2. **超时响应**：当操作时间超过指定的`timeout`值时：
   - 立即中止当前操作
   - 执行安全停止程序
   - 返回超时错误结果
3. **安全保障**：确保电机在超时后安全停止，避免意外运动

#### 使用建议

- **根据操作复杂度设置超时**：复杂的运动（如长距离移动、多轴同步）应设置更长的超时时间
- **考虑机械特性**：不同负载和机械结构的响应时间不同，建议根据实际测试结果调整
- **安全第一**：宁可设置稍长的超时时间，也要确保正常操作不会被误判为超时

#### 示例

```cpp
// 设置20秒超时的位置控制
auto goal = PositionControl::Goal();
goal.target_position = 100.0;
goal.absolute_position = true;
goal.max_velocity = 50.0;
goal.timeout = 20.0;  // 20秒超时

// 使用默认超时（10秒）
auto goal2 = PositionControl::Goal();
goal2.target_position = 50.0;
goal2.timeout = 0.0;  // 使用默认超时
```

---

## 服务接口

服务接口提供同步的电机控制功能，适用于简单的电机状态控制。

### 电机使能服务

**服务类型**: `std_srvs::srv::Trigger`  
**服务名称**: `enable`

使能电机，使电机进入可控制状态。

#### 请求参数
无参数

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `success` | `bool` | 是否成功使能 |
| `message` | `string` | 结果信息 |

### 电机禁用服务

**服务类型**: `std_srvs::srv::Trigger`  
**服务名称**: `disable`

禁用电机，使电机进入不可控制状态。

#### 请求参数
无参数

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `success` | `bool` | 是否成功禁用 |
| `message` | `string` | 结果信息 |

### 电机重置服务

**服务类型**: `std_srvs::srv::Trigger`  
**服务名称**: `reset`

重置电机，清除错误状态并重新初始化。

#### 请求参数
无参数

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `success` | `bool` | 是否成功重置 |
| `message` | `string` | 结果信息 |

### 紧急停止服务

**服务类型**: `std_srvs::srv::Trigger`  
**服务名称**: `emergency_stop`

立即停止电机运动，用于紧急情况。

#### 请求参数
无参数

#### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `success` | `bool` | 是否成功执行紧急停止 |
| `message` | `string` | 结果信息 |

---

## 话题接口

话题接口提供实时的数据传输功能。

### 状态发布话题

**话题类型**: `std_msgs::msg::Float64MultiArray`  
**话题名称**: `state`

实时发布电机状态信息。

#### 数据格式
- `data[0]`: 当前位置（物理单位）
- `data[1]`: 当前速度（物理单位/秒）
- `data[2]`: 当前转矩（物理单位）
- `data[3]`: 电机状态码
- `data[4]`: 错误代码

### 命令订阅话题

**话题类型**: `std_msgs::msg::Float64MultiArray`  
**话题名称**: `command`

接收电机控制命令。

#### 数据格式
- `data[0]`: 命令类型（1=位置，2=速度，3=转矩）
- `data[1]`: 目标值
- `data[2]`: 参数1（如速度、加速度等）
- `data[3]`: 参数2（如减速度等）

---

## 统一电机接口

统一电机接口（UnifiedMotorInterface）提供了所有电机品牌的通用编程接口。

### 基础控制接口

#### enable()
```cpp
virtual bool enable() = 0;
```
**功能**: 使能电机  
**返回值**: 成功返回true，失败返回false

#### disable()
```cpp
virtual bool disable() = 0;
```
**功能**: 禁用电机  
**返回值**: 成功返回true，失败返回false

#### reset()
```cpp
virtual bool reset() = 0;
```
**功能**: 重置电机  
**返回值**: 成功返回true，失败返回false

#### emergencyStop()
```cpp
virtual bool emergencyStop() = 0;
```
**功能**: 紧急停止电机  
**返回值**: 成功返回true，失败返回false

### 模式设置接口

#### setOperationMode()
```cpp
virtual bool setOperationMode(OperationMode mode) = 0;
```
**功能**: 设置电机运行模式  
**参数**: 
- `mode`: 运行模式（见OperationMode枚举）  
**返回值**: 成功返回true，失败返回false

### 运动控制接口

#### setTargetPosition()
```cpp
virtual bool setTargetPosition(double position) = 0;
```
**功能**: 设置目标位置  
**参数**: 
- `position`: 目标位置（物理单位）  
**返回值**: 成功返回true，失败返回false

#### setTargetVelocity()
```cpp
virtual bool setTargetVelocity(double velocity) = 0;
```
**功能**: 设置目标速度  
**参数**: 
- `velocity`: 目标速度（物理单位/秒）  
**返回值**: 成功返回true，失败返回false

#### setTargetTorque()
```cpp
virtual bool setTargetTorque(double torque) = 0;
```
**功能**: 设置目标转矩  
**参数**: 
- `torque`: 目标转矩（物理单位）  
**返回值**: 成功返回true，失败返回false

### 参数设置接口

#### setAcceleration()
```cpp
virtual bool setAcceleration(double acceleration) = 0;
```
**功能**: 设置加速度  
**参数**: 
- `acceleration`: 加速度（物理单位/秒²）  
**返回值**: 成功返回true，失败返回false

#### setDeceleration()
```cpp
virtual bool setDeceleration(double deceleration) = 0;
```
**功能**: 设置减速度  
**参数**: 
- `deceleration`: 减速度（物理单位/秒²）  
**返回值**: 成功返回true，失败返回false

#### setMaxVelocity()
```cpp
virtual bool setMaxVelocity(double max_velocity) = 0;
```
**功能**: 设置最大速度  
**参数**: 
- `max_velocity`: 最大速度（物理单位/秒）  
**返回值**: 成功返回true，失败返回false

#### setProfileVelocity()
```cpp
virtual bool setProfileVelocity(double velocity) = 0;
```
**功能**: 设置梯形运动速度  
**参数**: 
- `velocity`: 梯形运动速度（物理单位/秒）  
**返回值**: 成功返回true，失败返回false

#### setProfileAcceleration()
```cpp
virtual bool setProfileAcceleration(double acceleration) = 0;
```
**功能**: 设置梯形运动加速度  
**参数**: 
- `acceleration`: 梯形运动加速度（物理单位/秒²）  
**返回值**: 成功返回true，失败返回false

#### setProfileDeceleration()
```cpp
virtual bool setProfileDeceleration(double deceleration) = 0;
```
**功能**: 设置梯形运动减速度  
**参数**: 
- `deceleration`: 梯形运动减速度（物理单位/秒²）  
**返回值**: 成功返回true，失败返回false

### 状态查询接口

#### getCurrentPosition()
```cpp
virtual double getCurrentPosition() = 0;
```
**功能**: 获取当前位置  
**返回值**: 当前位置（物理单位）

#### getCurrentVelocity()
```cpp
virtual double getCurrentVelocity() = 0;
```
**功能**: 获取当前速度  
**返回值**: 当前速度（物理单位/秒）

#### getCurrentTorque()
```cpp
virtual double getCurrentTorque() = 0;
```
**功能**: 获取当前转矩  
**返回值**: 当前转矩（物理单位）

#### getStatus()
```cpp
virtual MotorStatus getStatus() = 0;
```
**功能**: 获取电机状态  
**返回值**: MotorStatus结构体（见数据类型定义）

---

## 数据类型定义

### OperationMode枚举

电机运行模式枚举定义：

```cpp
enum class OperationMode
{
  PROFILE_POSITION = 1,     // 位置模式
  PROFILE_VELOCITY = -3,    // 立即速度模式
  PROFILE_TORQUE = 4,       // 转矩模式
  HOMING = 6,               // 回零模式
  INTERPOLATED_POSITION = 7, // 位置插补模式
  CYCLIC_SYNC_TORQUE = 10,  // 力矩模式
  PULSE_MODE = -4           // 脉冲模式
};
```

### MotorStatus结构体

电机状态结构体定义：

```cpp
struct MotorStatus
{
  bool enabled = false;           // 是否使能
  bool fault = false;             // 是否故障
  uint32_t error_code = 0;        // 错误码
  OperationMode operation_mode;   // 当前运行模式
  bool target_reached = false;    // 是否到达目标位置
  bool moving = false;            // 是否正在运动
};
```

### DS402State枚举

CANopen DS402状态机状态定义：

```cpp
enum class DS402State : uint16_t
{
  NOT_READY_TO_SWITCH_ON = 0x0000,  // 未就绪
  SWITCH_ON_DISABLED = 0x0040,      // 切换使能禁止
  READY_TO_SWITCH_ON = 0x0021,      // 就绪切换使能
  SWITCHED_ON = 0x0023,             // 已切换使能
  OPERATION_ENABLED = 0x0027,       // 运行使能
  QUICK_STOP_ACTIVE = 0x0007,       // 快速停止激活
  FAULT_REACTION_ACTIVE = 0x000F,   // 故障反应激活
  FAULT = 0x0008                    // 故障
};
```

---

## 错误代码

### 通用错误代码

| 错误代码 | 说明 |
|----------|------|
| 0 | 成功 |
| -1 | 通用错误 |
| -2 | 参数无效 |
| -3 | 电机未连接 |
| -4 | 电机未使能 |
| -5 | 电机故障 |
| -6 | 超时 |
| -7 | 位置超限 |
| -8 | 速度超限 |
| -9 | 转矩超限 |
| -10 | 通信错误 |

### CANopen错误代码

| 错误代码 | 说明 |
|----------|------|
| 0x05030000 | Toggle bit not alternated |
| 0x05040000 | SDO protocol timed out |
| 0x05040001 | Command specifier not valid or unknown |
| 0x05040005 | Out of memory |
| 0x06010000 | Unsupported access to an object |
| 0x06010001 | Attempt to read a write only object |
| 0x06010002 | Attempt to write a read only object |
| 0x06020000 | Object does not exist in the object dictionary |
| 0x06040041 | Object cannot be mapped to the PDO |
| 0x06040042 | The number and length of the objects to be mapped would exceed PDO length |
| 0x06040043 | General parameter incompatibility reason |
| 0x06040047 | General internal incompatibility in the device |
| 0x06060000 | Access failed due to an hardware error |
| 0x06070010 | Data type does not match, length of service parameter does not match |
| 0x06070012 | Data type does not match, length of service parameter too high |
| 0x06070013 | Data type does not match, length of service parameter too low |
| 0x06090011 | Sub-index does not exist |
| 0x06090030 | Value range of parameter exceeded |
| 0x06090031 | Value of parameter written too high |
| 0x06090032 | Value of parameter written too low |
| 0x06090036 | Maximum value is less than minimum value |
| 0x08000000 | General error |
| 0x08000020 | Data cannot be transferred or stored to the application |
| 0x08000021 | Data cannot be transferred or stored to the application because of local control |
| 0x08000022 | Data cannot be transferred or stored to the application because of the present device state |

---

## 使用示例

### Python示例

#### 位置控制示例

```python
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_interfaces_motor.action import PositionControl

class MotorControlExample(Node):
    def __init__(self):
        super().__init__('motor_control_example')
        self.action_client = ActionClient(
            self, 
            PositionControl, 
            'motor_1/position_control'
        )

    def send_position_goal(self, target_position):
        # 等待Action服务器可用
        self.action_client.wait_for_server()
        
        # 创建Goal
        goal_msg = PositionControl.Goal()
        goal_msg.target_position = target_position
        goal_msg.absolute_position = True
        goal_msg.max_velocity = 100.0  # mm/s
        goal_msg.acceleration = 500.0  # mm/s²
        goal_msg.deceleration = 500.0  # mm/s²
        goal_msg.dwell_time = 1.0      # s
        
        # 发送Goal
        future = self.action_client.send_goal_async(
            goal_msg,
            feedback_callback=self.feedback_callback
        )
        
        future.add_done_callback(self.goal_response_callback)

    def feedback_callback(self, feedback_msg):
        feedback = feedback_msg.feedback
        self.get_logger().info(
            f'Position: {feedback.current_position:.2f}, '
            f'Progress: {feedback.progress:.1%}'
        )

    def goal_response_callback(self, future):
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected')
            return
        
        self.get_logger().info('Goal accepted')
        get_result_future = goal_handle.get_result_async()
        get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        result = future.result().result
        if result.success:
            self.get_logger().info(
                f'Position control succeeded! '
                f'Final position: {result.final_position:.2f}'
            )
        else:
            self.get_logger().error(
                f'Position control failed: {result.error_message}'
            )

def main():
    rclpy.init()
    node = MotorControlExample()
    
    # 移动到位置100mm
    node.send_position_goal(100.0)
    
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()
```

#### 服务调用示例

```python
import rclpy
from rclpy.node import Node
from std_srvs.srv import Trigger

class MotorServiceExample(Node):
    def __init__(self):
        super().__init__('motor_service_example')
        
        # 创建服务客户端
        self.enable_client = self.create_client(Trigger, 'motor_1/enable')
        self.disable_client = self.create_client(Trigger, 'motor_1/disable')
        self.reset_client = self.create_client(Trigger, 'motor_1/reset')
        self.emergency_stop_client = self.create_client(Trigger, 'motor_1/emergency_stop')

    def enable_motor(self):
        request = Trigger.Request()
        future = self.enable_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        response = future.result()
        if response.success:
            self.get_logger().info('Motor enabled successfully')
        else:
            self.get_logger().error(f'Failed to enable motor: {response.message}')
        
        return response.success

    def disable_motor(self):
        request = Trigger.Request()
        future = self.disable_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        response = future.result()
        if response.success:
            self.get_logger().info('Motor disabled successfully')
        else:
            self.get_logger().error(f'Failed to disable motor: {response.message}')
        
        return response.success

def main():
    rclpy.init()
    node = MotorServiceExample()
    
    # 使能电机
    if node.enable_motor():
        # 进行其他操作...
        pass
    
    # 禁用电机
    node.disable_motor()
    
    rclpy.shutdown()

if __name__ == '__main__':
    main()
```

### C++示例

#### 位置控制示例

```cpp
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include "rpcs_interfaces_motor/action/position_control.hpp"

class MotorControlExample : public rclcpp::Node
{
public:
  using PositionControl = rpcs_interfaces_motor::action::PositionControl;
  using GoalHandlePositionControl = rclcpp_action::ClientGoalHandle<PositionControl>;

  MotorControlExample() : Node("motor_control_example")
  {
    action_client_ = rclcpp_action::create_client<PositionControl>(
      this, "motor_1/position_control");
  }

  void send_position_goal(double target_position)
  {
    if (!action_client_->wait_for_action_server()) {
      RCLCPP_ERROR(get_logger(), "Action server not available");
      return;
    }

    auto goal_msg = PositionControl::Goal();
    goal_msg.target_position = target_position;
    goal_msg.absolute_position = true;
    goal_msg.max_velocity = 100.0;     // mm/s
    goal_msg.acceleration = 500.0;     // mm/s²
    goal_msg.deceleration = 500.0;     // mm/s²
    goal_msg.dwell_time = 1.0;         // s

    auto send_goal_options = rclcpp_action::Client<PositionControl>::SendGoalOptions();
    
    send_goal_options.goal_response_callback =
      [this](std::shared_future<GoalHandlePositionControl::SharedPtr> future) {
        auto goal_handle = future.get();
        if (!goal_handle) {
          RCLCPP_ERROR(get_logger(), "Goal was rejected by server");
        } else {
          RCLCPP_INFO(get_logger(), "Goal accepted by server, waiting for result");
        }
      };

    send_goal_options.feedback_callback =
      [this](GoalHandlePositionControl::SharedPtr,
              const std::shared_ptr<const PositionControl::Feedback> feedback) {
        RCLCPP_INFO(get_logger(), 
          "Position: %.2f, Progress: %.1f%%", 
          feedback->current_position, 
          feedback->progress * 100.0);
      };

    send_goal_options.result_callback =
      [this](const GoalHandlePositionControl::WrappedResult & result) {
        switch (result.code) {
          case rclcpp_action::ResultCode::SUCCEEDED:
            if (result.result->success) {
              RCLCPP_INFO(get_logger(), 
                "Position control succeeded! Final position: %.2f", 
                result.result->final_position);
            } else {
              RCLCPP_ERROR(get_logger(), 
                "Position control failed: %s", 
                result.result->error_message.c_str());
            }
            break;
          case rclcpp_action::ResultCode::ABORTED:
            RCLCPP_ERROR(get_logger(), "Goal was aborted");
            break;
          case rclcpp_action::ResultCode::CANCELED:
            RCLCPP_ERROR(get_logger(), "Goal was canceled");
            break;
          default:
            RCLCPP_ERROR(get_logger(), "Unknown result code");
            break;
        }
      };

    action_client_->async_send_goal(goal_msg, send_goal_options);
  }

private:
  rclcpp_action::Client<PositionControl>::SharedPtr action_client_;
};

int main(int argc, char ** argv)
{
  rclcpp::init(argc, argv);
  auto node = std::make_shared<MotorControlExample>();
  
  // 移动到位置100mm
  node->send_position_goal(100.0);
  
  rclcpp::spin(node);
  rclcpp::shutdown();
  
  return 0;
}
```

#### 速度控制示例⚠️**（包含修正后的target_current_limit参数）**

```cpp
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include "rpcs_interfaces_motor/action/velocity_control.hpp"

class VelocityControlExample : public rclcpp::Node
{
public:
  using VelocityControl = rpcs_interfaces_motor::action::VelocityControl;
  using GoalHandleVelocityControl = rclcpp_action::ClientGoalHandle<VelocityControl>;

  VelocityControlExample() : Node("velocity_control_example")
  {
    action_client_ = rclcpp_action::create_client<VelocityControl>(
      this, "motor_1/velocity_control");
  }

  void send_velocity_goal(double target_velocity, double duration = 5.0, double current_limit = 20.0)
  {
    if (!action_client_->wait_for_action_server()) {
      RCLCPP_ERROR(get_logger(), "Action server not available");
      return;
    }

    auto goal_msg = VelocityControl::Goal();
    goal_msg.target_velocity = target_velocity;      // rpm
    goal_msg.acceleration = 10.0;                    // rps/s → 10737 DEC (转换系数1073.75)
    goal_msg.deceleration = 10.0;                    // rps/s → 10737 DEC
    goal_msg.duration = duration;                    // 运行时间（秒）
    goal_msg.target_current_limit = current_limit;   // A → 853 DEC (转换系数42.67，步科实测确认)
    goal_msg.use_position_limits = false;            // 不使用位置限制
    goal_msg.timeout = 15.0;                         // 超时时间

    auto send_goal_options = rclcpp_action::Client<VelocityControl>::SendGoalOptions();
    
    send_goal_options.goal_response_callback =
      [this](std::shared_future<GoalHandleVelocityControl::SharedPtr> future) {
        auto goal_handle = future.get();
        if (!goal_handle) {
          RCLCPP_ERROR(get_logger(), "Velocity goal was rejected by server");
        } else {
          RCLCPP_INFO(get_logger(), "Velocity goal accepted, motor will run with proper current limit");
        }
      };

    send_goal_options.feedback_callback =
      [this](GoalHandleVelocityControl::SharedPtr,
              const std::shared_ptr<const VelocityControl::Feedback> feedback) {
        RCLCPP_INFO(get_logger(), 
          "Current velocity: %.2f rpm, Position: %.2f, Time remaining: %.1f s", 
          feedback->current_velocity, 
          feedback->current_position,
          feedback->time_remaining);
      };

    send_goal_options.result_callback =
      [this](const GoalHandleVelocityControl::WrappedResult & result) {
        switch (result.code) {
          case rclcpp_action::ResultCode::SUCCEEDED:
            if (result.result->success) {
              RCLCPP_INFO(get_logger(), 
                "Velocity control succeeded! Final velocity: %.2f rpm", 
                result.result->final_velocity);
            } else {
              RCLCPP_ERROR(get_logger(), 
                "Velocity control failed: %s", 
                result.result->error_message.c_str());
            }
            break;
          case rclcpp_action::ResultCode::ABORTED:
            RCLCPP_ERROR(get_logger(), "Velocity goal was aborted");
            break;
          case rclcpp_action::ResultCode::CANCELED:
            RCLCPP_ERROR(get_logger(), "Velocity goal was canceled");
            break;
          default:
            RCLCPP_ERROR(get_logger(), "Unknown result code");
            break;
        }
      };

    RCLCPP_INFO(get_logger(), 
      "Sending velocity goal: %.2f rpm for %.1f seconds with %.1f A current limit", 
      target_velocity, duration, current_limit);
    
    action_client_->async_send_goal(goal_msg, send_goal_options);
  }

private:
  rclcpp_action::Client<VelocityControl>::SharedPtr action_client_;
};

int main(int argc, char ** argv)
{
  rclcpp::init(argc, argv);
  auto node = std::make_shared<VelocityControlExample>();
  
  // 以30rpm运行5秒，电流限制15A
  node->send_velocity_goal(30.0, 5.0, 15.0);
  
  rclcpp::spin(node);
  rclcpp::shutdown();
  
  return 0;
}
```

**重要参数说明**：
- `target_current_limit = 15.0`：将被转换为 `15.0 × 42.67 = 640 DEC` 写入 `0x6073`
- `acceleration = 10.0`：将被转换为 `10.0 × 1073.75 = 10737 DEC` 写入 `0x6083`
- `target_velocity = 30.0`：将被转换为 `30.0 × 17895.7 = 536871 DEC` 写入 `0x60FF`

**控制流程（修正后的时序）**：
1. 电机状态准备：`SWITCH_ON_DISABLED → READY_TO_SWITCH_ON → SWITCHED_ON`
2. 参数设置：设置模式、加减速度、**电流限制**
3. **安全启动**：先设置 `0x60FF = 0`，再转换到 `OPERATION_ENABLED`
4. 命令执行：设置实际目标速度

### 命令行示例

#### 单设备模式下的Action调用

```bash
# 位置控制（传统模式，无设备ID）
ros2 action send_goal /motor_1/position_control rpcs_interfaces_motor/action/PositionControl \
  "{target_position: 100.0, absolute_position: true, max_velocity: 50.0, acceleration: 200.0, deceleration: 200.0, dwell_time: 1.0, timeout: 15.0}"

# 速度控制（传统模式）⚠️包含修正后的target_current_limit参数
ros2 action send_goal /motor_1/velocity_control rpcs_interfaces_motor/action/VelocityControl \
  "{target_velocity: 30.0, acceleration: 10.0, deceleration: 10.0, duration: 5.0, target_current_limit: 15.0, use_position_limits: false, timeout: 10.0}"
```

#### 多设备模式下的Action调用

```bash
# 位置控制（Robot1设备）
ros2 action send_goal /Robot1/motor_5/position_control rpcs_interfaces_motor/action/PositionControl \
  "{target_position: 100.0, absolute_position: true, max_velocity: 50.0, acceleration: 200.0, deceleration: 200.0, dwell_time: 1.0, timeout: 15.0}"

# 速度控制（Robot1设备）⚠️包含修正后的target_current_limit参数
ros2 action send_goal /Robot1/motor_5/velocity_control rpcs_interfaces_motor/action/VelocityControl \
  "{target_velocity: 30.0, acceleration: 10.0, deceleration: 10.0, duration: 5.0, target_current_limit: 18.0, use_position_limits: false, timeout: 10.0}"

# 速度控制参数转换说明：
# target_velocity: 30.0 rpm → 30.0 × 17895.7 = 536871 DEC (写入0x60FF)
# acceleration: 10.0 rps/s → 10.0 × 1073.75 = 10737 DEC (写入0x6083)
# target_current_limit: 18.0 A → 18.0 × 42.67 = 768 DEC (写入0x6073，步科实测转换系数)

# 转矩控制（Robot1设备）- 使用修正后的参数
ros2 action send_goal /Robot1/motor_3/torque_control rpcs_interfaces_motor/action/TorqueControl \
  "{target_torque: 0.5, velocity_limit: 10.0, torque_slope: 1.0, duration: 3.0, use_position_limits: false, timeout: 12.0}"

# 重要转换说明：
# velocity_limit使用10rpm（对应0x6080，无需转换，直接写入10 DEC）
# target_torque使用0.5%（对应0x6071，转换系数10.0，写入5 DEC）

# 原点控制（Robot1设备）
ros2 action send_goal /Robot1/motor_5/homing_control rpcs_interfaces_motor/action/HomingControl \
  "{homing_method: 1, speed_switch: 10.0, speed_zero: 5.0, home_offset: 0, position_window: 100, position_window_time: 1000, timeout: 35.0}"

# 同时控制多个设备
# Robot1的电机5
ros2 action send_goal /Robot1/motor_5/position_control rpcs_interfaces_motor/action/PositionControl \
  "{target_position: 50.0, absolute_position: true, max_velocity: 30.0, acceleration: 100.0, deceleration: 100.0, dwell_time: 1.0, timeout: 15.0}" &

# Robot2的电机5（假设存在）
ros2 action send_goal /Robot2/motor_5/position_control rpcs_interfaces_motor/action/PositionControl \
  "{target_position: 75.0, absolute_position: true, max_velocity: 40.0, acceleration: 120.0, deceleration: 120.0, dwell_time: 1.0, timeout: 15.0}" &

# Station_A的电机3（假设存在）
ros2 action send_goal /Station_A/motor_3/position_control rpcs_interfaces_motor/action/PositionControl \
  "{target_position: 25.0, absolute_position: true, max_velocity: 20.0, acceleration: 80.0, deceleration: 80.0, dwell_time: 2.0, timeout: 20.0}" &
```

#### 多设备模式下的话题查看

```bash
# 查看所有设备的Action话题
ros2 action list

# 查看特定设备的Action话题
ros2 action list | grep Robot1
ros2 action list | grep Station_A

# 查看特定Action的详细信息
ros2 action info /Robot1/motor_5/position_control

# 监控不同设备的状态（如果有状态话题）
ros2 topic echo /Robot1/motor_5/state &
ros2 topic echo /Robot2/motor_5/state &
ros2 topic echo /Station_A/motor_3/state &
```

#### 多设备C++客户端示例

```cpp
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include "rpcs_interfaces_motor/action/position_control.hpp"

class MultiDeviceMotorController : public rclcpp::Node
{
public:
    MultiDeviceMotorController() : Node("multi_device_motor_controller")
    {
        // 为不同设备创建Action客户端
        robot1_motor5_client_ = rclcpp_action::create_client<rpcs_interfaces_motor::action::PositionControl>(
            this, "/Robot1/motor_5/position_control");
        
        robot2_motor5_client_ = rclcpp_action::create_client<rpcs_interfaces_motor::action::PositionControl>(
            this, "/Robot2/motor_5/position_control");
        
        station_a_motor3_client_ = rclcpp_action::create_client<rpcs_interfaces_motor::action::PositionControl>(
            this, "/Station_A/motor_3/position_control");
    }
    
    void sendPositionGoal(const std::string& device_name, int motor_id, double target_position)
    {
        auto goal_msg = rpcs_interfaces_motor::action::PositionControl::Goal();
        goal_msg.target_position = target_position;
        goal_msg.absolute_position = true;
        goal_msg.max_velocity = 50.0;
        goal_msg.acceleration = 200.0;
        goal_msg.deceleration = 200.0;
        goal_msg.dwell_time = 1.0;
        goal_msg.timeout = 15.0;
        
        // 根据设备名称选择对应的客户端
        if (device_name == "Robot1" && motor_id == 5) {
            robot1_motor5_client_->async_send_goal(goal_msg);
        } else if (device_name == "Robot2" && motor_id == 5) {
            robot2_motor5_client_->async_send_goal(goal_msg);
        } else if (device_name == "Station_A" && motor_id == 3) {
            station_a_motor3_client_->async_send_goal(goal_msg);
        }
        
        RCLCPP_INFO(this->get_logger(), 
                   "Sent position goal to %s/motor_%d: %.2f", 
                   device_name.c_str(), motor_id, target_position);
    }
    
    void coordinatedMovement()
    {
        // 协调多设备运动
        RCLCPP_INFO(this->get_logger(), "Starting coordinated movement...");
        
        sendPositionGoal("Robot1", 5, 50.0);
        sendPositionGoal("Robot2", 5, 75.0);
        sendPositionGoal("Station_A", 3, 25.0);
        
        RCLCPP_INFO(this->get_logger(), "All movement commands sent!");
    }

private:
    rclcpp_action::Client<rpcs_interfaces_motor::action::PositionControl>::SharedPtr robot1_motor5_client_;
    rclcpp_action::Client<rpcs_interfaces_motor::action::PositionControl>::SharedPtr robot2_motor5_client_;
    rclcpp_action::Client<rpcs_interfaces_motor::action::PositionControl>::SharedPtr station_a_motor3_client_;
};

int main(int argc, char ** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<MultiDeviceMotorController>();
    
    // 执行协调运动
    node->coordinatedMovement();
    
    rclcpp::spin(node);
    rclcpp::shutdown();
    
    return 0;
}
```

#### 多设备Python客户端示例

```python
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_interfaces_motor.action import PositionControl

class MultiDeviceMotorController(Node):
    def __init__(self):
        super().__init__('multi_device_motor_controller')
        
        # 为不同设备创建Action客户端
        self.robot1_motor5_client = ActionClient(
            self, PositionControl, '/Robot1/motor_5/position_control')
        self.robot2_motor5_client = ActionClient(
            self, PositionControl, '/Robot2/motor_5/position_control')
        self.station_a_motor3_client = ActionClient(
            self, PositionControl, '/Station_A/motor_3/position_control')
    
    def send_position_goal(self, device_name, motor_id, target_position):
        goal_msg = PositionControl.Goal()
        goal_msg.target_position = target_position
        goal_msg.absolute_position = True
        goal_msg.max_velocity = 50.0
        goal_msg.acceleration = 200.0
        goal_msg.deceleration = 200.0
        goal_msg.dwell_time = 1.0
        goal_msg.timeout = 15.0
        
        # 根据设备名称选择对应的客户端
        if device_name == "Robot1" and motor_id == 5:
            self.robot1_motor5_client.send_goal_async(goal_msg)
        elif device_name == "Robot2" and motor_id == 5:
            self.robot2_motor5_client.send_goal_async(goal_msg)
        elif device_name == "Station_A" and motor_id == 3:
            self.station_a_motor3_client.send_goal_async(goal_msg)
        
        self.get_logger().info(
            f'Sent position goal to {device_name}/motor_{motor_id}: {target_position}')
    
    def coordinated_movement(self):
        """执行协调运动"""
        self.get_logger().info("Starting coordinated movement...")
        
        self.send_position_goal("Robot1", 5, 50.0)
        self.send_position_goal("Robot2", 5, 75.0)
        self.send_position_goal("Station_A", 3, 25.0)
        
        self.get_logger().info("All movement commands sent!")

def main():
    rclpy.init()
    node = MultiDeviceMotorController()
    
    # 执行协调运动
    node.coordinated_movement()
    
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()
```

---

## 最佳实践

### 1. 使用前检查

在发送运动控制命令前，建议先检查电机状态：

```python
# 检查电机是否使能
if not motor_status.enabled:
    # 先使能电机
    enable_motor()

# 检查是否有故障
if motor_status.fault:
    # 先重置故障
    reset_motor()
```

### 2. 参数验证

在设置运动参数前，请确保参数在合理范围内：

```python
# 检查位置范围
if position < min_position or position > max_position:
    raise ValueError("Position out of range")

# 检查速度范围
if velocity > max_velocity:
    raise ValueError("Velocity too high")
```

### 3. 错误处理

始终检查函数返回值和Action结果：

```python
# Action结果检查
if not result.success:
    logger.error(f"Action failed: {result.error_message}")
    # 执行错误恢复逻辑

# 服务调用检查
if not response.success:
    logger.error(f"Service call failed: {response.message}")
```

### 4. 安全停止

在程序退出或发生异常时，确保电机安全停止：

```python
def cleanup():
    try:
        # 停止当前运动
        emergency_stop()
        # 禁用电机
        disable_motor()
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")

# 注册清理函数
atexit.register(cleanup)
```

### 5. 实时监控

对于关键应用，建议实时监控电机状态：

```python
def status_callback(msg):
    # 检查故障状态
    if motor_status.fault:
        logger.error(f"Motor fault detected: {motor_status.error_code}")
        # 执行故障处理逻辑
    
    # 检查位置限制
    if position > safety_limit:
        logger.warning("Position approaching safety limit")
        # 执行预防措施

# 订阅状态话题
status_subscriber = node.create_subscription(
    Float64MultiArray, 
    'motor_1/state', 
    status_callback, 
    10
)
```

---

## 故障排除

### 常见问题

1. **Action服务器未响应**
   - 检查节点是否正在运行
   - 确认Action名称拼写正确
   - 检查网络连接

2. **电机无法使能**
   - 检查电机硬件连接
   - 确认CAN总线通信正常
   - 检查电机驱动器状态

3. **位置控制精度不足**
   - 调整位置窗口参数
   - 检查编码器分辨率设置
   - 优化PID参数

4. **速度控制不稳定**
   - 调整加减速度参数
   - 检查负载惯量匹配
   - 优化速度环参数

### 调试工具

```bash
# 查看可用的Action
ros2 action list

# 查看Action接口定义
ros2 interface show rpcs_interfaces_motor/action/PositionControl

# 监控Action状态
ros2 action info /motor_1/position_control

# 查看话题数据
ros2 topic echo /motor_1/state

# 检查服务状态
ros2 service list | grep motor_1
```

---

本文档提供了RPCS_S_Controller_Motor系统的完整API参考。如有疑问或需要更多信息，请参考源代码注释或联系开发团队。 