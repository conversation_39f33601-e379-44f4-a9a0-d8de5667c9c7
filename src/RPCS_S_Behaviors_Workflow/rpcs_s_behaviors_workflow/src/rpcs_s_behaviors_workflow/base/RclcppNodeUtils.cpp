#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

// 定义插件的名称空间
namespace rpcs_s_behaviors_workflow
{
    // 创建带特定domain_id的节点
    // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样
    // 注意: 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node，一律使用create_node_with_domain函数创建
    //       如果不是与电机节点RPCS_S_Controller_Motor通讯, 就不要使用create_node_with_domain函数
    //       查看电机节点action list, 需要使用如下命令:
    //       export ROS_DOMAIN_ID=`hostname -I | grep -Eo '192\.168\.100\.[0-9]{1,3}' | awk -F '.' '{print $4}'`
    //       ros2 action list
    // 上下文隔离：
    //       每个自定义上下文拥有独立的通信域（比如默认使用ROS_DOMAIN_ID为10，但是2号工控机行为树与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node使用102）
    //       不同域ID的节点无法直接通信（隔离网络分区）
    // 使用限制：
    //       同一进程内不同域ID的节点不能直接通信
    //       适用于需要网络隔离的场景（如多环境模拟）
    rclcpp::Node::SharedPtr create_node_with_domain(const std::string& node_name, int domain_id)
    {
        // 1. 创建自定义上下文
        auto context = std::make_shared<rclcpp::Context>();

        // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样
        // 原因是三台小车机械臂、工控机、agv、路由器、交换机网络较为复杂，行为树调用IO、机械臂，概率性出现
        // [R03001110003.unified_controller.rclcpp_action]: Failed to send goal response 83dd9baf8b9bc3b7613667a736b27fd (timeout): client will not receive response, at ./src/rmw_response.cpp:154, at ./src/rcl/service.c:314
        // 分析：当 handleGoal 返回 ACCEPT_AND_EXECUTE 时，ROS 2 底层需要向客户端发送"目标已接受"的响应
        //    这个发送操作在 handleAccepted 被调用之前发生
        //    报错显示这个发送操作超时失败（timeout），导致客户端永远不会收到响应
        // 为什么发生在 handleGoal 和 handleAccepted 之间：
        // ROS2动作服务器的内部时序：
        //    客户端发送目标
        //    服务器调用 handleGoal
        //    服务器尝试发送目标接受响应（ROS 2 底层需要向客户端发送"目标已接受"的响应）
        //    服务器调用 handleAccepted
        // 日志显示服务器尝试发送目标接受响应失败，因此从未进入 handleAccepted
        // 根本原因分析：
        // 客户端提前断开：客户端在发送目标后立即断开连接，导致服务器无法发送响应
        // 网络延迟/不稳定：响应在传输过程中丢失或超时（因三台小车复杂的网络环境，这个的可能性较大，）
        // 客户端处理缓慢：客户端未及时处理响应，导致服务器端超时
        // 资源竞争：当多个客户端同时发送请求时可能发生
        // 
        // 针对复杂网络环境下，IO、机械臂调用通讯不稳定的解决方案：
        // 为某些rclcpp::Node创建自定义上下文，就可以有独立的通信域，就可以在自定义上下文中设置domain_id
        // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样，比如101、102、103
        // 其他rclcpp::Node使用.bashrc文件中配置的export ROS_DOMAIN_ID=10
        // 以此来实现通信域的隔离，有望解决复杂网络环境下IO、机械臂调用通讯不稳定。

        // 2. 配置初始化选项
        rclcpp::InitOptions init_options;
        init_options.set_domain_id(domain_id);  // 设置域ID
    
        // 3. 初始化上下文
        context->init(0, nullptr, init_options);  // 参数：argc, argv, options
    
        // 4. 创建节点选项并绑定上下文
        rclcpp::NodeOptions node_options;
        node_options.context(context);  // 关键：绑定自定义上下文
    
        // 5. 创建节点
        return std::make_shared<rclcpp::Node>(node_name, node_options);
    }
}


