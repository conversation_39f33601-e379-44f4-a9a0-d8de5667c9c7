/*****************************************************************************
 * @copyright Copyright (c) 2023.11 软件组
 * @file      util.h
 * @brief
 * @details   framework工具库对象集合
 *
 * <AUTHOR>
 * @date      2023/11/23
 * @version
 * @attention
 * @remark    
             【需求背景】
                   util工具库，函数默认返回0是成功，返回其他是发生错误
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "rpcs_s_behaviors_workflow/base/util.h"
#include "rpcs_s_behaviors_workflow/base/StringUtils.h"
#include <array>
#include <cctype>
#include <charconv>
#include <cstdio>
#include <cstdlib>
#include <cerrno>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>
#include <system_error>
#include <unordered_set>
#include <vector>
#include <unistd.h>
#include <climits>

namespace rpcs_s_behaviors_workflow
{
// std::string转为long long int的函数，转换失败会抛异常
long long int Utils::atoll(const std::string & str)
{
    long long int value = 0;
    auto [ptr, ec] = std::from_chars(str.data(), str.data() + str.size(), value);

    if (ec != std::errc())
    {
        std::cerr << "string转为long long int失败，请检查" << std::endl;
        // // SPDLOG_CRITICAL(UTIL TRY_CATCH"[{}] string转为long long int失败，请检查! str=[{}]", classFuncName, str);
        // 抛出异常 不再抛出异常，修改为只打印日志，保持系统稳定。
        // 业务逻辑上，应该先调用can_atoll，再调用本函数，所以不该有异常。
    }

    return value;
}

// std::string转为int的函数，转换失败会抛异常
int Utils::atoi(const std::string & str)
{
    int value = 0;
    auto [ptr, ec] = std::from_chars(str.data(), str.data() + str.size(), value);

    if (ec != std::errc())
    {
        std::cerr << "string转为int失败，请检查" << std::endl;
        // SPDLOG_CRITICAL(UTIL TRY_CATCH"[{}] string转为int失败，请检查! str=[{}]", classFuncName, str);
        // 抛出异常 不再抛出异常，修改为只打印日志，保持系统稳定。
        // 业务逻辑上，应该先调用can_atoll，再调用本函数，所以不该有异常。
    }

    return value;
}

// 判断字符串std::string是否可以转换为long long int类型
bool Utils::can_atoll(const std::string & str)
{
    for (char c : str)
    {
        if (!(::isspace(c) || ::isdigit(c)))
        {
            return false;
        }
    }

    long long int value;
    auto [ptr, ec] = std::from_chars(str.data(), str.data() + str.size(), value);
    return ec == std::errc();
}


// std::string转为double类型，转换失败会抛异常
double Utils::atof(const std::string & str)
{
    double value = 0.0;
    auto [ptr, ec] { std::from_chars(str.data(), str.data() + str.size(), value) };

    if (ec != std::errc())
    {
        std::cerr << "string转为double失败，请检查" << std::endl;
        // SPDLOG_ERROR(UTIL TRY_CATCH"[{}] string转为double失败，请检查! str=[{}]", classFuncName, str);
        // 抛出异常 不再抛出异常，修改为只打印日志，保持系统稳定。
        // 业务逻辑上，应该先调用can_atoll，再调用本函数，所以不该有异常。
    }

    return value;
}

// 判断字符串std::string是否可以转换为double类型，目前暂不支持科学计数法的浮点数转换
bool Utils::can_atof(const std::string & str)
{
    for (char c : str)
    {
        if (!(::isspace(c) || ::isdigit(c) || '.'==c || '-'==c))
        {
            return false;
        }
    }

    double value = 0.0;
    auto [ptr, ec] { std::from_chars(str.data(), str.data() + str.size(), value) };
    return ec == std::errc();
}


// 从文件路径中提取文件名，类似basename命令
std::string Utils::basename(const std::string& path)
{
    size_t lastSlash = path.find_last_of("/\\");
    return (lastSlash == std::string::npos) ? path : path.substr(lastSlash + 1);
}


// 检查文件是否存在， 目录存在时返回true 目录不存在时返回false
// 保险起见，建议此处使用全路径
bool Utils::IsFileExist(const std::string & path)
{
    return !path.empty() && std::filesystem::is_regular_file(path);
}


// 检查目录是否存在， 目录存在时返回true 目录不存在时返回false
// 保险起见，建议此处使用全路径
bool Utils::IsPathExist(const std::string & path)
{
    return !path.empty() && std::filesystem::exists(path) && std::filesystem::is_directory(path);
}


// 如果目录不存在则创建目录
bool Utils::CreateDirectories(const std::string & path)
{
    IF_TRUE_RETURNVALUE(path.empty(), false);
    IF_TRUE_RETURNVALUE(Utils::IsPathExist(path), true);
    // 目录不存在，创建目录
    std::filesystem::create_directories(path);
    IF_TRUE_RETURNVALUE(Utils::IsPathExist(path), true);
    std::string command("mkdir -p " + path);
    int result = std::system(command.c_str());
    if (0!=result || !Utils::IsPathExist(path))
    {
        // SPDLOG_ERROR(UTIL"创建目录[{}]失败，请检查！", classFuncName, path);
        std::cerr << "创建目录失败，一般是权限问题，请检查！" << std::endl;
        return false;
    }
    return true;
}


// 拷贝文件
bool Utils::CopyFile(const std::string & srcFullPath, const std::string & destFullPath)
{
    IF_TRUE_RETURNVALUE(srcFullPath.empty() || destFullPath.empty(), false);
    IF_TRUE_RETURNVALUE(srcFullPath == destFullPath, false);
    IF_TRUE_RETURNVALUE(!Utils::IsFileExist(srcFullPath), false);
    IF_TRUE_RETURNVALUE(!Utils::RemoveFile(destFullPath), false);
    std::filesystem::copy(srcFullPath, destFullPath);
    // 因为destFullPath可能是个文件全路径，也可能是个目录，所以不能直接根据destFullPath判断文件是否拷贝成功。
    std::string destFilePath = CASEWHEN(Utils::IsPathExist(destFullPath), destFullPath + "/" + Utils::basename(srcFullPath), destFullPath);
    if (!Utils::IsFileExist(destFilePath))
    {
        // SPDLOG_ERROR(UTIL"[{}] 文件拷贝失败，请检查！ srcFullPath=[{}] destFullPath=[{}]", classFuncName, srcFullPath, destFullPath);
        std::cerr << "文件拷贝失败，一般是权限问题，请检查！" << std::endl;
        return false;
    }

    return true;
}


// 移除文件
bool Utils::RemoveFile(const std::string & fileFullPath)
{
    IF_TRUE_RETURNVALUE(fileFullPath.empty(), false);
    IF_TRUE_RETURNVALUE(!Utils::IsFileExist(fileFullPath), true);
    std::filesystem::remove(fileFullPath);
    if (Utils::IsFileExist(fileFullPath))
    {
        // SPDLOG_ERROR(UTIL"[{}] 文件移除失败，请检查！ fileFullPath=[{}]", classFuncName, fileFullPath);
        std::cerr << "文件移除失败，一般是权限问题，请检查！" << std::endl;
        return false;
    }

    return true;
}


// 移动文件
bool Utils::MoveFile(const std::string & srcFullPath, const std::string & destFullPath)
{
    IF_TRUE_RETURNVALUE(srcFullPath.empty() || destFullPath.empty(), false);
    IF_TRUE_RETURNVALUE(srcFullPath == destFullPath, false);
    IF_TRUE_RETURNVALUE(!Utils::IsFileExist(srcFullPath), false);
    IF_TRUE_RETURNVALUE(!Utils::CopyFile(srcFullPath, destFullPath), false);
    IF_TRUE_RETURNVALUE(!Utils::RemoveFile(srcFullPath), false);
    return true;
}


// 读取文件大小
// 调用此函数前，最好检查并确保文件存在
long long int Utils::GetFileSize(const std::string & filename)
{
    IF_TRUE_RETURNVALUE(filename.empty(), 0);

    try {
        std::filesystem::path file_path = filename;
        // 检查文件是否存在并且是一个常规文件
        if (std::filesystem::exists(file_path) && std::filesystem::is_regular_file(file_path)) {
            return (long long int)std::filesystem::file_size(file_path);
        } else {
            // SPDLOG_ERROR(UTIL"[{}] 读取文件大小失败，File does not exist or is not a regular file. filename=[{}]", classFuncName, filename);
            std::cerr << "读取文件大小失败1，请检查！" << std::endl;
        }
    } catch (const std::filesystem::filesystem_error& e) {
		// SPDLOG_ERROR(UTIL"[{}] 读取文件大小失败，Filesystem error: exception=[{}] filename=[{}]", classFuncName, e.what(), filename);
        std::cerr << "读取文件大小失败2，请检查！" << std::endl;
    } catch (const std::exception& e) {
        // SPDLOG_ERROR(UTIL"[{}] 读取文件大小失败，exception=[{}] filename=[{}]", classFuncName, e.what(), filename);
        std::cerr << "读取文件大小失败3，请检查！" << std::endl;
    } catch (...) {
        // SPDLOG_ERROR(UTIL"[{}] 读取文件大小失败，系统故障，请检查！ filename=[{}]", classFuncName, filename);
        std::cerr << "读取文件大小失败4，请检查！" << std::endl;
    }

    return 0;
}


// 获取system函数的输出内容
std::string Utils::GetSystemEchoContent(const std::string & cmd)
{
    std::array<char, 128> buffer;
    std::string result;

    // 使用 popen 执行命令，并以只读模式打开管道
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.data(), "r"), pclose);
    if (!pipe) {
        // SPDLOG_ERROR(UTIL"[{}] 获取system函数的输出失败 cmd=[{}]", classFuncName, cmd);
        std::cout << "执行shell命令, 获取命令行输出: cmd = [   " << cmd << "   ]" << std::endl;
        std::cerr << "获取system函数的输出失败，请检查！" << std::endl;
        return result;
    }

    // 从管道中读取输出
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    std::string content = StringUtils::Trim(result);
    std::cout << "执行shell命令, 获取命令行输出: cmd = [   " << cmd << "   ],   result=[" << content << "]" << std::endl;
    return content;
}


// 读取文件内容，文件成功打开，返回true； 否则返回false； isTrim默认为true，因为大部分实际应用，还是需要trim的。
bool Utils::ReadFileContent(const std::string & fileFullPath, POUT std::string & fileContent, bool isTrim)
{
    IF_TRUE_RETURNVALUE(fileFullPath.empty(), false);
    std::ifstream file(fileFullPath);
    std::stringstream buffer;

    // 检查文件是否打开成功
    if (file)
    {
        try
        {
            // 读取文件内容到buffer
            buffer << file.rdbuf();
        }
        catch(const std::exception& e)
        {
            // SPDLOG_ERROR(UTIL"[{}] buffer << file.rdbuf() 出现异常，请检查！ fileFullPath=[{}] exception=[{}]", classFuncName, fileFullPath, e.what());
            std::cerr << "ReadFileContent error，请检查！" << std::endl;
            file.close();
            return true;
        }
        catch(...)
        {
            // SPDLOG_ERROR(UTIL"[{}] buffer << file.rdbuf() 出现系统异常，请检查！ fileFullPath=[{}]", classFuncName, fileFullPath);
            std::cerr << "ReadFileContent error222，请检查！" << std::endl;
            file.close();
            return true;
        }

        file.close();
        // isTrim默认为true，因为大部分实际应用，还是需要trim的。
        fileContent = CASEWHEN(isTrim, StringUtils::Trim(buffer.str()), buffer.str());
        return true;
    }

    // SPDLOG_ERROR(UTIL"[{}] 文件[{}]打开失败，请检查！", classFuncName, fileFullPath);
    std::cerr << "文件[" << fileFullPath << "]打开失败，请检查！" << std::endl;
    return false;
}


// 读取文件内容，文件成功打开 并且为整型，返回true； 否则返回false
bool Utils::ReadFileContent(const std::string & fileFullPath, POUT long long int & fileContent)
{
    IF_TRUE_RETURNVALUE(fileFullPath.empty(), false);
    std::string fileContentOrg;
    IF_FALSE_RETURNVALUE(Utils::ReadFileContent(fileFullPath, fileContentOrg, true), false);
    IF_FALSE_RETURNVALUE(Utils::can_atoll(fileContentOrg), false);
    fileContent = Utils::atoll(fileContentOrg);
    return true;
}


// 写入文件成功，返回true； 否则返回false
bool Utils::WriteFile(PIN const std::string & fileContent, const std::string & fileFullPath)
{
    IF_TRUE_RETURNVALUE(fileFullPath.empty(), false);
    // 创建输出文件流对象
    std::ofstream outfile(fileFullPath);

    // 检查文件是否成功打开
    if (outfile.is_open()) {
        // 将字符串写入文件
        outfile << fileContent;
        // 关闭文件
        outfile.close();
        // SPDLOG_INFO(UTIL"[{}] 文件[{}]写入成功.", classFuncName, fileFullPath);
    } else {
        // SPDLOG_ERROR(UTIL"[{}] 文件[{}]写入失败，请检查！", classFuncName, fileFullPath);
        std::cerr << "文件[" << fileFullPath << "]写入失败，请检查！" << std::endl;
        return false;
    }

    return true;
}


// 获取可执行文件全路径
std::string Utils::GetExeFileFullPath()
{
    char result[PATH_MAX] = { '\0' };
    ssize_t count = readlink("/proc/self/exe", result, PATH_MAX);
    if (count != -1) {
        return std::string(result, size_t(count));
    }

    return EMPTY_STRING;
}


// 获取可执行文件路径
std::string Utils::GetExeFilePath()
{
    std::string fullPath(Utils::GetExeFileFullPath());
    return StringUtils::RemoveLastChars(fullPath, (int)(Utils::basename(fullPath).size() + 1));
}

std::string Utils::ToString(const std::vector<std::string> & arr)
{
    IF_TRUE_RETURNVALUE(arr.empty(), "[]");
    std::stringstream ss;
    ss << "[";
    std::string seq(", ");
    // 这里必须保证arr.size()-1不能为负值，因为前面检查了arr.empty()，所以此处也安全
    size_t preSize = arr.size() - 1;
    for (size_t i=0; i<preSize; i++)
    {
        ss << "{\"item\":\"" << arr[i] << "\"}" << seq;
    }
    if (!arr.empty())
    {
        ss << "{\"item\":\"" << arr[arr.size()-1] << "\"}";
    }
    ss << "]";
    return ss.str();
}

std::string Utils::ToString(const std::vector<long long int> & arr)
{
    IF_TRUE_RETURNVALUE(arr.empty(), "[]");
    std::stringstream ss;
    ss << "[";
    std::string seq(", ");
    // 这里必须保证arr.size()-1不能为负值，因为前面检查了arr.empty()，所以此处也安全
    size_t preSize = arr.size() - 1;
    for (size_t i=0; i<preSize; i++)
    {
        ss << arr[i] << seq;
    }
    if (!arr.empty())
    {
        ss << arr[arr.size()-1];
    }
    ss << "]";
    return ss.str();
}


std::string Utils::ToString(const std::unordered_set<std::string> & set)
{
    IF_TRUE_RETURNVALUE(set.empty(), "[]");
    std::stringstream ss;
    ss << "[";
    std::string seq(", ");
    bool isFirst = true;
    for (const auto & item : set)
    {
        IF_TRUE_EXECUTE(isFirst, ss << item; isFirst = false; continue);
        ss << seq << item;
    }
    ss << "]";
    return ss.str();
}


// 获取行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id
// 获取RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID
// 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样
int Utils::GetControllerMotorNodeRosDomainId()
{
    // 获取RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID
    // 查询方式  hostname -I | grep -Eo '192\.168\.100\.[0-9]{1,3}' | awk -F '.' '{print $4}'
    std::string cmd = R"(hostname -I | grep -Eo '192\.168\.100\.[0-9]{1,3}' | awk -F '.' '{print $4}')";
    std::string strControllerMotorNodeRosDomainId = Utils::GetSystemEchoContent(cmd);
    if (strControllerMotorNodeRosDomainId.empty() || !Utils::can_atoll(strControllerMotorNodeRosDomainId))
    {
        // 如果不是在工控机上运行，返回ROS_DOMAIN_ID为10，因为研发测试使用的这个数据
        std::cout << "不是在工控机上运行，返回行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id为10，因为研发测试使用的这个数据" << std::endl;
        return 10;
    }

    // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node，一律使用create_node_with_domain函数创建、使用带特定domain_id的自定义上下文。
    return Utils::atoi(strControllerMotorNodeRosDomainId);
}

} // namespace rpcs_s_behaviors_workflow


