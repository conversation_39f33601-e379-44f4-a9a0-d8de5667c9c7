/*****************************************************************************
 * @copyright Copyright (c) 2023.11 软件组
 * @file      StringUtils.cpp
 * @brief
 * @details   framework工具库对象string_utils字符串工具
 *
 * <AUTHOR>
 * @date      2023/12/04
 * @version
 * @attention
 * @remark    
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "rpcs_s_behaviors_workflow/base/StringUtils.h"
#include "rpcs_s_behaviors_workflow/base/macros.h"
#include <algorithm>
#include <cctype>
#include <array>
#include <cstddef>
#include <cstring>
#include <exception>
#include <functional>
#include <iomanip>
#include <sstream>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_set>

namespace rpcs_s_behaviors_workflow
{
// 去除字符串的前导和尾随空白字符，返回值为“修剪”后的字符串
// 部分类型可以转换为string类型，却不能转为string_view类型，比如json[key]。所以保留了string类型的Trim函数
std::string StringUtils::Trim(const std::string & str)
{
    IF_TRUE_RETURNVALUE(str.empty(), EMPTY_STRING);
    std::string_view sv(str);
    return std::string(Trim(sv));
}

// 去除字符串的前导和尾随空白字符，返回值为“修剪”后的字符串
// 由于const char*可以同时转化为string、string_view类型，为了不报ambiguous错误，保留了const char*类型的Trim函数
std::string StringUtils::Trim(const char * str)
{
    IF_TRUE_RETURNVALUE(nullptr==str || '\0'==*str, EMPTY_STRING);
    std::string_view sv(str);
    return std::string(Trim(sv));
}

// 是否trim之后的字符串是空字符串，返回true，表示是空字符串，返回false表示不是空字符串。
bool StringUtils::IsTrimEmpty(const std::string & str)
{
    std::string_view sv(str);
    return Trim(sv).empty();
}

// 去除字符串的前导和尾随空白字符，返回值为“修剪”后的视图内容
std::string_view StringUtils::Trim(std::string_view sv)
{
    auto is_not_space = [](unsigned char ch) {
        // 如果传递给 std::isspace 的值超出了 unsigned char 范围，并且不等于 EOF，其行为是未定义的，因此，参数用了unsigned char类型。
        return !std::isspace(ch);
    };

    // 移除前导空格
    sv.remove_prefix(static_cast<std::string_view::size_type>(std::distance(sv.begin(), std::find_if(sv.begin(), sv.end(), is_not_space))));

    // 移除尾随空格
    sv.remove_suffix(static_cast<std::string_view::size_type>(std::distance(sv.rbegin(), std::find_if(sv.rbegin(), sv.rend(), is_not_space))));

    // 使用这个trim函数时，原始的std::string_view不会被修改，因为std::string_view不拥有其所指向的字符串数据。
    // 相反，trim函数返回一个新的std::string_view实例，该实例引用原始字符串的一个子串，从而提供了一个视图到“修剪”后的内容。
    return sv;
}

// 移除字符串中所有空白字符，返回新字符串
std::string StringUtils::RemoveWhitespace(const std::string& input)
{
    std::string result = input;  // 创建输入字符串的副本

    // 使用 std::remove_if 移除所有空白字符
    result.erase(std::remove_if(result.begin(), result.end(), [](unsigned char c) {
        return std::isspace(c); // 判断是否为空白字符
    }), result.end());

    return result; // 返回去掉空白字符后的字符串
}

// 移除源字符串中所有空白字符，修改源字符串
std::string StringUtils::RemoveOrgStrWhitespace(PINOUT std::string & input)
{
    // 使用 std::remove_if 移除所有空白字符
    input.erase(std::remove_if(input.begin(), input.end(), [](unsigned char c) {
        return std::isspace(c); // 判断是否为空白字符
    }), input.end());

    return input; // 返回去掉空白字符后的字符串
}

// 将std::array转化为string
std::string StringUtils::ArrayToString(const std::array<std::string, 5> & arr)
{
    std::string result;
    for (const auto& str : arr) {
        result += str + " "; // 在每个字符串后添加一个空格
    }

    // 删除最后一个多余的空格
    if (!result.empty()) {
        result.pop_back();
    }

    return result;
}


// 是否是有效的十六进制字符串
bool StringUtils::IsValidHexString(const std::string &str)
{
    // 使用此数组的代码段，需要自行保障下标不会超出，基本上先检查下范围再使用数组就好。(0<=ch && 128>ch)
    // 判断是否是十六进制字符使用，128大小的整数数组，当ASCII为0-9、a-z、A-Z时，数组下标内容为1，否则为0
    static int HexCharArr[128] = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
                                   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 
                                   0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0 };
    // 遗留代码GeneralUtil::isValidHexString的源代码是没有的，只能看法反汇编之后的汇编代码，虽然数据库中数据都是大写的十六进制
    // 因为无法确定是否百分百不包含小写的十六进制，或者小写的十六禁止的设备id是否错误，因无法确定，所以十六进制包含了小写的十六进制字母。
    // 应用场景：添加/修改设备名称
    for (const char ch : str)
    {
        int charInt = static_cast<int>(ch);
        if (!(0<=charInt && 128>charInt && HexCharArr[charInt]))
        {
            return false;
        }
    }

    return true;
}

//将字符串改为大写，但是不改变原有字符串
std::string StringUtils::ToUpperCopy(std::string src)
{
    if (src.empty())
    {
        return src;
    }

    transform(src.begin(), src.end(), src.begin(), ::toupper);
    return src;
}

// 如果sep刚好是两个字符，且字符串是以sep的两个字符开头和结尾，那么将原始字符串中的开头、结尾字符删除。
// 使用场景：有些https返回的报文是json数组，且数组中只有一组数据，那么把[]剔除后，直接用json解析更为方便。
std::string StringUtils::RemoveStartEndCharacters(const std::string & str, const std::string & sep)
{
    IF_FALSE_RETURNVALUE(2==sep.size() && 2<=str.size() && sep[0]==str[0] && sep[1]==str[str.size()-1], str);
    return std::string(str, 1, str.size()-2);
}

//判断一个字符串是否以指定字符串开头
bool StringUtils::CheckStrStart(const std::string & str, const char *comStr)
{
    return CheckStrStart(str.c_str(), comStr);
}

//判断一个字符串是否以指定字符串开头
bool StringUtils::CheckStrStart(const char *str, const char *comStr)
{
    if (IsEmptyString(str) || IsEmptyString(comStr))
    {
        // SPDLOG_WARN("{} 存在空字符串，请检查！ str=[{}] comStr=[{}]", classFuncName, PNRE(str), PNRE(comStr));
        return false;
    }

    const char *pStr = str;
    const char *pComStr = comStr;
    while ('\0' != *pStr && '\0' != *pComStr)
    {
        if (*pStr != *pComStr)
        {
            return false;
        }

        pStr++;
        pComStr++;
    }

    return '\0' == *pComStr;
}

//判断一个字符串是否以指定字符串结尾
bool StringUtils::CheckStrEnd(const std::string & str, const char *comStr)
{
    if (str.empty() || IsEmptyString(comStr))
    {
        // SPDLOG_WARN("{} 存在空字符串，请检查！ str=[{}] comStr=[{}]", classFuncName, str, PNRE(comStr));
        return false;
    }

    int lenStr = (int)str.length();
    int lenComStr = (int)strlen(comStr);
    if (lenStr < lenComStr)
    {
        return false;
    }

    const char *pStr = str.c_str() + lenStr - 1;
    const char *pComStr = comStr + lenComStr - 1;

    //此处while的结尾处有个;此处是故意这样写的，目的是找出第一个不等的comStr
    while (comStr <= pComStr && *pStr-- == *pComStr--)
        ;

    pStr++;
    pComStr++;

    return pComStr == comStr && *pStr == *comStr;
}


// 如果一个字符串是double转化的字符串，移除字符串小数点之后，结尾的0
// 比如转114.031600为114.0316
void StringUtils::RemoveDoubleStrTailZero(PINOUT std::string & str)
{
    const char * pStr = str.c_str();
    const char * p = pStr;
    for ( ; nullptr!=p && '\0'!=*p; p++)
    {
        IF_FALSE_RETURN(isdigit(*p) || '.'==*p || '-'==*p);
    }

    IF_TRUE_RETURN(str.empty());
    IF_TRUE_RETURN(nullptr == strchr(pStr, '.'));
    size_t tailLen = 0;
    for ( int i=(int)str.size()-1; 0<=i && '0'==str[(size_t)i]; i--)
    {
        tailLen++;
    }

    IF_TRUE_EXECUTE(str.size()>tailLen && '.'==str[str.size()-tailLen-1], tailLen++);
    if (0 < tailLen)
    {
        str = std::string(str, 0, str.size()-tailLen);
    }
}


// 获取最后一个分隔符最后的数据
// 比如，由 /upload/face/ranvoo 获取到 ranvoo
std::string StringUtils::SubstrEndSeq(const std::string & str, const char seq)
{
    IF_TRUE_RETURNVALUE(str.empty() || '\0'==seq, EMPTY_STRING);
    const char * data = strrchr(str.c_str(), seq);
    IF_TRUE_RETURNVALUE(nullptr == data, EMPTY_STRING);
    return data + 1;
}

// 从字符串中提取子串，比如从12345[678]9中根据[]可以提取到678子串
// 此函数一般结合StringUtils::IsFindSubstringMultiple一起使用，精确保障业务场景，避免多个子串出现的场景。
std::string StringUtils::ExtractContent(const std::string& str, const char* seq)
{
    IF_TRUE_RETURNVALUE(IsEmptyString(seq) || 2 != strlen(seq), EMPTY_STRING);
    size_t startPos = str.find(seq[0]);
    IF_TRUE_RETURNVALUE(std::string::npos == startPos, EMPTY_STRING); // 没有找到起始符号
    // 注意：确保函数内使用的下标seq[1]不会超过strlen(seq)的长度2
    size_t endPos = str.find(seq[1], startPos + 1);
    IF_TRUE_RETURNVALUE(std::string::npos == endPos, EMPTY_STRING); // 没有找到结束符号

    // 提取并返回内容
    return str.substr(startPos + 1, endPos - startPos - 1);
}

// 判断两个字符串是否相等
bool StringUtils::IsStringsEqual(const char* str1, const char* str2) {
    IF_TRUE_RETURNVALUE(nullptr == str1 || nullptr == str2, false);

    // strcmp返回0表示字符串相等
    return 0 == std::strcmp(str1, str2);
}

// 判断两个字符串是否相等，忽略大小写
bool StringUtils::IsStringsEqualIgnoreCase(const std::string & str1_org, const std::string & str2_org)
{
    std::string str1 = StringUtils::ToUpperCopy(str1_org);
    std::string str2 = StringUtils::ToUpperCopy(str2_org);
    return StringUtils::IsStringsEqual(str1.data(), str2.data());
}

// 判断字符串str1是否包含字符串str2
bool StringUtils::IsContainStr(const std::string & str1, const std::string & str2)
{
    return IsContainStr(str1, str2.data());
}


// 判断字符串str1是否包含字符串str2
bool StringUtils::IsContainStr(const std::string & str1, const char* str2)
{
    IF_TRUE_RETURNVALUE(IsEmptyString(str2), false);
    return std::string::npos != str1.find(str2);
}


// 判断字符串str1是否包含字符串str2，忽略大小写
bool StringUtils::IsContainStrIgnoreCase(const std::string & str1_org, std::string str2_org)
{
    std::string str1 = StringUtils::ToUpperCopy(str1_org);
    std::string str2 = StringUtils::ToUpperCopy(str2_org);
    return StringUtils::IsContainStr(str1, str2.data());
}


// 用于SQL语句拼接，比如VALUES(13)可以得到 VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) 
std::string StringUtils::SqlValuesConcat(int n)
{
    IF_TRUE_RETURNVALUE(0>=n || 1024<n, " VALUES() ");

    std::string result(" VALUES(");
    for (int i = 1; i <= n; ++i)
	{
        result += "?";
        if (i != n) result += ", ";
    }
	result += ") ";
    return result;
}

// 剔除字符串结尾的几个字符，number代表剔除几个字符
std::string StringUtils::RemoveLastChars(const std::string& input, int number)
{
    if ((int)input.length() <= number) {
        // 如果字符串长度小于等于number，返回空字符串
        return "";
    } else {
        // 否则，返回去掉最后number个字符的新字符串
        return input.substr(0, input.length() - (size_t)number);
    }
}


// 将一段内存转为十六进制字符串，方便spdlog打印，主要用于打印日志、问题定位。
std::string StringUtils::ToHexString(const void *content, const size_t len)
{
    return ToHexString(content, (int)len);
}

// 将一段内存转为十六进制字符串，方便spdlog打印，主要用于打印日志、问题定位。
std::string StringUtils::ToHexString(const void *content, const int len) {
    if (nullptr == content) {
        // SPDLOG_WARN("{} content为空, 请检查! ", classFuncName);
        return EMPTY_STRING;
    }

    if (0 >= len) {
        // SPDLOG_WARN("{} len <= 0, 请检查! len=[{}]", classFuncName, len);
        return EMPTY_STRING;
    }

    if (10485760 < len) {
        // SPDLOG_WARN("{} len > 10485760, 系统不支持超过10M的内存转十六进制字符串, 请检查业务层面原因, 如果确实内存偏大, 还需分成多段内存依次转十六进制, 请检查! len=[{}]", classFuncName, len);
        return EMPTY_STRING;
    }

    const char hex_chars[] = "0123456789ABCDEF";
    // 每个字节转为2个十六进制字符加3个说明字符，外加一个用于终止的空字符，减去最后的空格字符。
    std::string hexStr;
    hexStr.reserve((size_t)(len * 5));
    const unsigned char *byte_content = (const unsigned char *)content;
    const int preLen = len - 1;
    for (int i = 0; i < preLen; i++) {
        unsigned char byte = byte_content[size_t(i)];
        hexStr += '0';
        hexStr += 'x';
        hexStr += hex_chars[(byte >> 4) & 0xF];     // 高四位
        hexStr += hex_chars[byte & 0xF];        // 低四位
        hexStr += ' ';
    }

    if (0 <= preLen) {
        unsigned char byte = byte_content[size_t(preLen)];
        hexStr += '0';
        hexStr += 'x';
        hexStr += hex_chars[(byte >> 4) & 0xF];     // 高四位
        hexStr += hex_chars[byte & 0xF];        // 低四位
    }

    return hexStr;
}


// 将地址转为十六进制字符串
std::string StringUtils::ToHexAddress(const void * addr) {
    std::ostringstream oss;
    // Add the "0x" prefix
    oss << "0x";
    // Use uppercase letters and fill with '0' to make sure all hex digits are shown
    // 使用 reinterpret_cast<uintptr_t>(addr) 来确保地址被正确转换为整数类型进行处理。
    // 否则，实际是0x61DA6D22D070，却会打印出0x0x5a0bb4f53710，明显错误。
    // reinterpret_cast是一种类型转换运算符，用于在指针、引用和其他不同类型之间进行低级别的类型转换，而不进行任何实际的数据位改变。它常用于需要在不同类型之间强制转换的场景。
    oss << std::uppercase << std::hex << reinterpret_cast<uintptr_t>(addr);
    return oss.str();
}

} // namespace rpcs_s_behaviors_workflow


