#include "rpcs_s_behaviors_workflow/process_tree_path_resolver.hpp"

namespace rpcs_s_behaviors_workflow
{

ProcessTreePathResolver::ProcessTreePathResolver(
    rclcpp::Logger logger, 
    const std::string& config_path,
    const std::string& tree_base_path)
    : logger_(logger), 
      config_path_(config_path), 
      tree_base_path_(tree_base_path),
      using_new_structure_(false)
{
    if (!config_path_.empty()) {
        loadConfig(config_path_);
    }
}

bool ProcessTreePathResolver::loadConfig(const std::string& config_path)
{
    config_path_ = config_path;
    path_cache_.clear();

    if (!std::filesystem::exists(config_path_)) {
        RCLCPP_ERROR(logger_, "配置文件不存在: %s", config_path_.c_str());
        return false;
    }

    try {
        YAML::Node config = YAML::LoadFile(config_path_);

        // 直接加载新格式，不再尝试加载旧格式
        if (tryLoadNewFormat(config)) {
            using_new_structure_ = true;
            RCLCPP_INFO(logger_, "成功加载层次化目录结构配置");
            return true;
        }

        RCLCPP_ERROR(logger_, "配置文件格式无效，不是层次化目录结构格式: %s", config_path_.c_str());
        return false;

    } catch (const YAML::Exception& e) {
        RCLCPP_ERROR(logger_, "解析YAML配置文件失败: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        RCLCPP_ERROR(logger_, "加载配置文件时发生错误: %s", e.what());
        return false;
    }
}

bool ProcessTreePathResolver::tryLoadOldFormat(const YAML::Node& config)
{
    old_structure_mapping_.clear();
    
    try {
        // 检查所有节点是否符合旧格式
        bool has_valid_entries = false;

        // 遍历顶级节点，应该是机器人ID
        for (const auto& robot_entry : config) {
            std::string robot_id = robot_entry.first.as<std::string>();
            
            // 确保值是一个映射
            if (!robot_entry.second.IsMap()) {
                continue;
            }

            // 遍历每个机器人的动作映射
            for (const auto& action_entry : robot_entry.second) {
                std::string action_type = action_entry.first.as<std::string>();
                
                // 确保值是一个字符串（文件路径）
                if (!action_entry.second.IsScalar()) {
                    continue;
                }

                std::string file_path = action_entry.second.as<std::string>();
                old_structure_mapping_[robot_id][action_type] = file_path;
                has_valid_entries = true;
            }
        }

        return has_valid_entries;

    } catch (const YAML::Exception& e) {
        RCLCPP_DEBUG(logger_, "不是旧格式: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        RCLCPP_DEBUG(logger_, "加载旧格式失败: %s", e.what());
        return false;
    }
}

bool ProcessTreePathResolver::tryLoadNewFormat(const YAML::Node& config)
{
    new_structure_mapping_.clear();

    try {
        // 检查配置文件是否包含"mapping"节点
        if (!config["mapping"]) {
            return false;
        }

        YAML::Node mapping = config["mapping"];
        bool has_valid_entries = false;

        // 遍历机器人ID
        for (const auto& robot_entry : mapping) {
            std::string robot_id = robot_entry.first.as<std::string>();
            
            if (!robot_entry.second.IsMap()) {
                continue;
            }

            // 遍历工艺类型
            for (const auto& process_entry : robot_entry.second) {
                std::string process_type = process_entry.first.as<std::string>();
                
                if (!process_entry.second.IsMap()) {
                    continue;
                }

                // 遍历动作类型
                for (const auto& action_entry : process_entry.second) {
                    std::string action_type = action_entry.first.as<std::string>();
                    
                    if (!action_entry.second.IsScalar()) {
                        continue;
                    }

                    std::string file_path = action_entry.second.as<std::string>();
                    new_structure_mapping_[robot_id][process_type][action_type] = file_path;
                    has_valid_entries = true;
                }
            }
        }

        return has_valid_entries;

    } catch (const YAML::Exception& e) {
        RCLCPP_DEBUG(logger_, "不是新格式: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        RCLCPP_DEBUG(logger_, "加载新格式失败: %s", e.what());
        return false;
    }
}

std::string ProcessTreePathResolver::getTreePath(
    const std::string& robot_id, 
    const std::string& process_action_type)
{
    // 先查找缓存
    std::string cache_key = getCacheKey(robot_id, process_action_type);
    auto cache_it = path_cache_.find(cache_key);
    if (cache_it != path_cache_.end()) {
        return cache_it->second;
    }

    // 只使用新结构查找路径
    if (using_new_structure_) {
        std::string result = findPathInNewStructure(robot_id, process_action_type);
        if (!result.empty()) {
            path_cache_[cache_key] = result;
            return result;
        }
    }

    // 如果没有找到或者未启用新结构，返回空
    RCLCPP_ERROR(logger_, "找不到行为树文件: robot_id=%s, action_type=%s", 
                robot_id.c_str(), process_action_type.c_str());
    return "";
}

std::string ProcessTreePathResolver::findPathInOldStructure(
    const std::string& robot_id, 
    const std::string& process_action_type)
{
    auto robot_it = old_structure_mapping_.find(robot_id);
    if (robot_it == old_structure_mapping_.end()) {
        return "";
    }

    auto action_it = robot_it->second.find(process_action_type);
    if (action_it == robot_it->second.end()) {
        return "";
    }

    std::string file_name = action_it->second;
    std::string full_path = tree_base_path_ + "/" + file_name;

    if (!std::filesystem::exists(full_path)) {
        RCLCPP_ERROR(logger_, "行为树文件不存在(旧结构): %s", full_path.c_str());
        return "";
    }

    return full_path;
}

std::string ProcessTreePathResolver::findPathInNewStructure(
    const std::string& robot_id, 
    const std::string& process_action_type)
{
    auto robot_it = new_structure_mapping_.find(robot_id);
    if (robot_it == new_structure_mapping_.end()) {
        return "";
    }

    // 遍历所有工艺类型，查找指定的动作类型
    for (const auto& process_entry : robot_it->second) {
        const std::string& process_type = process_entry.first;
        const auto& action_map = process_entry.second;

        auto action_it = action_map.find(process_action_type);
        if (action_it != action_map.end()) {
            std::string file_name = action_it->second;
            std::string full_path = tree_base_path_ + "/" + robot_id + "/" + process_type + "/" + file_name;

            if (!std::filesystem::exists(full_path)) {
                RCLCPP_WARN(logger_, "行为树文件不存在(新结构): %s", full_path.c_str());
                continue;
            }

            return full_path;
        }
    }

    return "";
}

std::string ProcessTreePathResolver::getCacheKey(
    const std::string& robot_id, 
    const std::string& process_action_type) const
{
    return robot_id + "_" + process_action_type;
}

} // namespace rpcs_s_behaviors_workflow 