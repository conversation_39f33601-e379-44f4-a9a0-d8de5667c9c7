#include "behaviortree_cpp/bt_factory.h"
#include "behaviortree_cpp/loggers/groot2_publisher.h"
#include "behaviortree_cpp/utils/shared_library.h"
#include "behaviortree_ros2/plugins.hpp"
#include "rpcs_s_behaviors_workflow/base/version.h"
#include "rpcs_s_behaviors_workflow/bt_plugins_list.hpp"

int main(int argc, char ** argv)
{
  std::cout << "RPCS Behavior Tree Started" << std::endl;

  rclcpp::init(argc, argv);
  BT::BehaviorTreeFactory factory;

  std::string bt_xml_path;
  auto node = std::make_shared<rclcpp::Node>("rpcs_s_behaviors_workflow");
  node->declare_parameter<std::string>(
    "style", "./rm_decision_ws/rpcs_s_behaviors_workflow/rpcs_s_behaviors_workflow.xml");
  node->get_parameter_or<std::string>(
    "style", bt_xml_path, "./rm_decision_ws/rpcs_s_behaviors_workflow/config/hj_robot_test.xml");

  std::cout << "Start rpcs_s_behaviors_workflow" << '\n';
  RCLCPP_INFO(node->get_logger(), "Load bt_xml: \e[1;42m %s \e[0m", bt_xml_path.c_str());

  BT::RosNodeParams params_update_msg;
  params_update_msg.nh = std::make_shared<rclcpp::Node>("update_msg");

  // BT::RosNodeParams params_robot_control;
  // params_robot_control.nh = std::make_shared<rclcpp::Node>("robot_control");
  // params_robot_control.default_port_value = "robot_control";

  BT::RosNodeParams params_send_goal;
  params_send_goal.nh = std::make_shared<rclcpp::Node>("send_goal");
  params_send_goal.default_port_value = "goal_pose";

  // clang-format off
  const std::vector<std::string> msg_update_plugin_libs = {
    "SubRobotProperties",
    "PubPrintMessage",
    "SubMaterialStatus"
};

  // 使用共享头文件中定义的插件列表
  const std::vector<std::string>& bt_plugin_libs = rpcs_s_behaviors_workflow::get_bt_plugin_libs();
  // clang-format on

  for (const auto & p : msg_update_plugin_libs) {
    RegisterRosNode(factory, BT::SharedLibrary::getOSName(p), params_update_msg);
  }

  for (const auto & p : bt_plugin_libs) {
    factory.registerFromPlugin(BT::SharedLibrary::getOSName(p));
  }

  // RegisterRosNode(factory, BT::SharedLibrary::getOSName("robot_control"), params_robot_control);
  auto context = BT::Blackboard::create();
  auto tree = factory.createTreeFromFile(bt_xml_path, context);

  // Connect the Groot2Publisher. This will allow Groot2 to get the tree and poll status updates.
  const unsigned port = 1670;
  BT::Groot2Publisher publisher(tree, port);

  int times=0;
  while (rclcpp::ok()) {
    tree.tickWhileRunning(std::chrono::milliseconds(100));
    // times++;
    // if(times>1000){
    //   break;
    // }
  }

  rclcpp::shutdown();
  return 0;
}