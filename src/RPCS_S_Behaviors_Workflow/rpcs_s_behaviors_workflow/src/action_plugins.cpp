#include <memory>
#include "behaviortree_cpp/bt_factory.h"

// 包含行为树节点头文件
#include "action/SetProgress.hpp"
#include "action/PubPrintMessage.hpp"
#include "action/PubProcessFeedback.hpp"
#include "action/AgvGoPoint.hpp"
#include "action/ImageDetectionToZero.hpp"
#include "action/CleanStatus.hpp"
#include "action/Wait.hpp"
#include "action/FPCAdapterNumber.hpp"
#include "action/ImageDetectionConversion.hpp"
#include "action/CorrectionPosition.hpp"
#include "action/BoardAlign.hpp"
#include "action/CommonAlign.hpp"
#include "action/RobotAlign.hpp"
#include "action/DepthFusion.hpp"
#include "action/CameraCapture.hpp"
#include "action/VehiclesAlign.hpp"

namespace rpcs_s_behaviors_workflow
{

void RegisterActionNodes(BT::BehaviorTreeFactory& factory)
{
  // 注册节点
  factory.registerNodeType<rpcs_s_behaviors_workflow::SetProgress>("SetProgress");
  factory.registerNodeType<rpcs_s_behaviors_workflow::PubPrintMessage>("PubPrintMessage");
  factory.registerNodeType<rpcs_s_behaviors_workflow::PubProcessFeedback>("PubProcessFeedback");
  factory.registerNodeType<rpcs_s_behaviors_workflow::AgvGoPoint>("AgvGoPoint");
  factory.registerNodeType<rpcs_s_behaviors_workflow::ImageDetectionToZero>("ImageDetectionToZero");
  factory.registerNodeType<rpcs_s_behaviors_workflow::CleanStatus>("CleanStatus");
  factory.registerNodeType<rpcs_s_behaviors_workflow::Wait>("Wait");
  factory.registerNodeType<rpcs_s_behaviors_workflow::FPCAdapterNumber>("FPCAdapterNumber");
  factory.registerNodeType<rpcs_s_behaviors_workflow::ImageDetectionConversion>("ImageDetectionConversion");
  factory.registerNodeType<rpcs_s_behaviors_workflow::CorrectionPosition>("CorrectionPosition");
  factory.registerNodeType<rpcs_s_behaviors_workflow::BoardAlign>("BoardAlign");
  factory.registerNodeType<rpcs_s_behaviors_workflow::CommonAlign>("CommonAlign");
  factory.registerNodeType<rpcs_s_behaviors_workflow::RobotAlign>("RobotAlign");
  factory.registerNodeType<rpcs_s_behaviors_workflow::DepthFusion>("DepthFusion");
  factory.registerNodeType<rpcs_s_behaviors_workflow::CameraCapture>("CameraCapture");
  factory.registerNodeType<rpcs_s_behaviors_workflow::VehiclesAlign>("VehiclesAlign");
}

}  // namespace rpcs_s_behaviors_workflow 

