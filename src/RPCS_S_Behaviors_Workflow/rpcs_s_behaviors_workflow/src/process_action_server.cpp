#include "rpcs_s_behaviors_workflow/process_action_server.hpp"
#include "rpcs_s_behaviors_workflow/base/version.h"
#include "rpcs_s_behaviors_workflow/bt_plugins_list.hpp"
#include <filesystem>
#include <fstream>

namespace rpcs_s_behaviors_workflow
{

// 定义状态常量
const std::string STATUS_WAITING = "WAITING";
const std::string STATUS_INITIALIZING = "INITIALIZING";
const std::string STATUS_RUNNING = "RUNNING";
const std::string STATUS_PAUSED = "PAUSED";
const std::string STATUS_FINISHED = "FINISHED";
const std::string STATUS_FAILED = "FAILED";

ProcessActionServer::ProcessActionServer(const std::string& robot_id, const rclcpp::NodeOptions& options)
    : Node("process_action_server", options),
      robot_id_(robot_id),
      is_executing_(false),
      preempt_requested_(false),
      groot2_port_(1670)
{
    RCLCPP_INFO(this->get_logger(), "Initializing ProcessActionServer for robot: %s", robot_id_.c_str());
    RCLCPP_INFO(this->get_logger(), "Current version: %s", RPCS_VERSION_STR);

    // 声明和获取参数
    declare_parameters();
    get_parameters();

    // 加载配置
    if (!load_configuration()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to load configuration");
        throw std::runtime_error("Configuration loading failed");
    }

    // 设置行为树工厂
    if (!setup_behavior_tree_factory()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to setup behavior tree factory");
        throw std::runtime_error("BehaviorTree factory setup failed");
    }

    // 创建 Action Server，使用robot_id作为前缀
    std::string action_name = robot_id_ + "/ExecuteProcessAction";
    action_server_ = rclcpp_action::create_server<ExecuteProcessAction>(
        this,
        action_name,
        std::bind(&ProcessActionServer::handle_goal, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&ProcessActionServer::handle_cancel, this, std::placeholders::_1),
        std::bind(&ProcessActionServer::handle_accepted, this, std::placeholders::_1));
    
    RCLCPP_INFO(this->get_logger(), "Action server created with name: %s", action_name.c_str());

    RCLCPP_INFO(this->get_logger(), "ProcessActionServer initialized successfully");
}

ProcessActionServer::~ProcessActionServer()
{
    cleanup_current_execution();
    if (execution_thread_.joinable()) {
        execution_thread_.join();
    }
}

void ProcessActionServer::declare_parameters()
{
    // 声明参数
    this->declare_parameter("config_file", "");
    this->declare_parameter("hierarchical_config_file", "");  // 新增：层次化配置文件路径
    this->declare_parameter("tree_file_path", "");
    this->declare_parameter("max_concurrent_actions", 1);
    this->declare_parameter("default_timeout", 300);
    this->declare_parameter("groot2_port", 1671);
}

void ProcessActionServer::get_parameters()
{
    // 获取参数值
    config_file_path_ = this->get_parameter("config_file").as_string();
    hierarchical_config_file_path_ = this->get_parameter("hierarchical_config_file").as_string();  // 新增：获取层次化配置文件路径
    tree_file_path_ = this->get_parameter("tree_file_path").as_string();
    max_concurrent_actions_ = this->get_parameter("max_concurrent_actions").as_int();
    default_timeout_ = this->get_parameter("default_timeout").as_int();
    groot2_port_ = static_cast<unsigned int>(this->get_parameter("groot2_port").as_int());
    
    // 打印参数
    RCLCPP_INFO(this->get_logger(), "配置文件路径: %s", config_file_path_.c_str());
    if (!hierarchical_config_file_path_.empty()) {
        RCLCPP_INFO(this->get_logger(), "层次化配置文件路径: %s", hierarchical_config_file_path_.c_str());
    }
    RCLCPP_INFO(this->get_logger(), "工艺树文件路径: %s", tree_file_path_.c_str());
    RCLCPP_INFO(this->get_logger(), "最大并发动作数: %d", max_concurrent_actions_);
    RCLCPP_INFO(this->get_logger(), "默认超时时间: %d秒", default_timeout_);
    RCLCPP_INFO(this->get_logger(), "Groot2端口: %u", groot2_port_);
}

bool ProcessActionServer::load_configuration()
{
    // 初始化行为树路径解析器
    path_resolver_ = std::make_unique<ProcessTreePathResolver>(
        this->get_logger(),
        "",  // 先不设置配置文件路径
        tree_file_path_
    );
    
    // 如果提供了层次化配置文件，则优先使用
    if (!hierarchical_config_file_path_.empty()) {
        if (std::filesystem::exists(hierarchical_config_file_path_)) {
            RCLCPP_INFO(this->get_logger(), "尝试加载层次化配置文件: %s", hierarchical_config_file_path_.c_str());
            if (path_resolver_->loadConfig(hierarchical_config_file_path_)) {
                RCLCPP_INFO(this->get_logger(), "成功加载层次化配置文件");
                return true;  // 成功加载层次化配置后直接返回，不再加载旧配置
            } else {
                RCLCPP_WARN(this->get_logger(), "加载层次化配置文件失败，将使用传统配置");
            }
        } else {
            RCLCPP_WARN(this->get_logger(), "层次化配置文件不存在: %s", hierarchical_config_file_path_.c_str());
        }
    }
    
    // 如果没有层次化配置或加载失败，则加载旧的配置
    if (!load_process_action_mapping()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to load process action mapping");
        return false;
    }
    
    return true;
}

bool ProcessActionServer::load_process_action_mapping()
{
    if (config_file_path_.empty()) {
        RCLCPP_ERROR(this->get_logger(), "Config file path is empty");
        return false;
    }

    if (!std::filesystem::exists(config_file_path_)) {
        RCLCPP_ERROR(this->get_logger(), "Config file does not exist: %s", config_file_path_.c_str());
        return false;
    }

    try {
        YAML::Node config = YAML::LoadFile(config_file_path_);
        
        if (!config[robot_id_]) {
            RCLCPP_ERROR(this->get_logger(), "Robot ID '%s' not found in config file", robot_id_.c_str());
            return false;
        }

        YAML::Node robot_config = config[robot_id_];
        process_action_mapping_.clear();

        for (const auto& item : robot_config) {
            std::string action_type = item.first.as<std::string>();
            std::string tree_file = item.second.as<std::string>();
            process_action_mapping_[action_type] = tree_file;
            
            RCLCPP_INFO(this->get_logger(), "Loaded mapping: %s -> %s", action_type.c_str(), tree_file.c_str());
        }

        RCLCPP_INFO(this->get_logger(), "Successfully loaded %zu process action mappings", process_action_mapping_.size());
        return true;

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Error loading config file: %s", e.what());
        return false;
    }
}

std::string ProcessActionServer::get_process_tree_file_path(const std::string& process_action_type)
{
    // 使用新的路径解析器
    if (path_resolver_ && path_resolver_->isUsingNewStructure()) {
        std::string path = path_resolver_->getTreePath(robot_id_, process_action_type);
        if (!path.empty()) {
            RCLCPP_INFO(this->get_logger(), "使用层次化目录结构找到行为树: %s", path.c_str());
            return path;
        }
        
        // 如果找不到路径，直接返回空，不再尝试旧方法
        RCLCPP_ERROR(this->get_logger(), "在层次化目录结构中找不到行为树: robot_id=%s, action_type=%s", 
                    robot_id_.c_str(), process_action_type.c_str());
        return "";
    }

    // 如果没有使用新结构，则使用旧方法
    auto it = process_action_mapping_.find(process_action_type);
    if (it == process_action_mapping_.end()) {
        RCLCPP_ERROR(this->get_logger(), "Unknown process action type: %s", process_action_type.c_str());
        return "";
    }

    std::string full_path = tree_file_path_ + "/" + it->second;
    
    if (!std::filesystem::exists(full_path)) {
        RCLCPP_ERROR(this->get_logger(), "Process tree file does not exist: %s", full_path.c_str());
        return "";
    }

    RCLCPP_INFO(this->get_logger(), "使用传统目录结构找到行为树: %s", full_path.c_str());
    return full_path;
}

bool ProcessActionServer::setup_behavior_tree_factory()
{
    register_ros_nodes();
    register_behavior_tree_plugins();
    return true;
}

void ProcessActionServer::register_ros_nodes()
{
    // 注册真正的ROS节点插件（如果有的话）
    // 目前这些插件都是普通的行为树插件，不是ROS节点插件
    // 所以这个函数暂时为空，所有插件都在register_behavior_tree_plugins中注册
    
    RCLCPP_DEBUG(this->get_logger(), "ROS node plugins registration completed");
}

void ProcessActionServer::register_behavior_tree_plugins()
{
    // 使用共享头文件中定义的插件列表
    const std::vector<std::string>& bt_plugin_libs = rpcs_s_behaviors_workflow::get_bt_plugin_libs();

    // 先检查环境变量
    const char* library_path = std::getenv("LD_LIBRARY_PATH");
    RCLCPP_INFO(this->get_logger(), "当前LD_LIBRARY_PATH: %s", library_path ? library_path : "未设置");

    for (const auto& plugin : bt_plugin_libs) {
        try {
            // 使用完整的库文件名称，而不是BT::SharedLibrary::getOSName
            // 因为我们已经将库路径添加到LD_LIBRARY_PATH中
            std::string lib_name = "lib" + plugin + ".so";
            factory_.registerFromPlugin(lib_name);
            RCLCPP_DEBUG(this->get_logger(), "已注册插件: %s", plugin.c_str());
        } catch (const std::exception& e) {
            RCLCPP_WARN(this->get_logger(), "Failed to register plugin %s: %s", plugin.c_str(), e.what());
        }
    }
}

bool ProcessActionServer::load_and_setup_tree(const std::string& tree_file_path,
                                                   const std::shared_ptr<const ExecuteProcessAction::Goal>& goal)
{
    try {
        auto context = BT::Blackboard::create();

        // 将 action goal 参数写入黑板
        if (goal) {
            // 写入基本信息
            context->set("action_robot_id", goal->robot_id);
            context->set("action_process_id", goal->process_id);
            context->set("action_process_type", goal->process_action_type);
            context->set("action_timeout_seconds", goal->timeout_seconds);
            context->set("action_preempt_current", goal->preempt_current);

            // 写入工艺参数列表
            context->set("action_process_parameters", goal->process_parameters);

            // 为了方便访问，也将参数按索引单独存储
            for (size_t i = 0; i < goal->process_parameters.size(); ++i) {
                std::string key = "action_param_" + std::to_string(i);
                context->set(key, goal->process_parameters[i]);
            }

            // 存储参数数量
            context->set("action_param_count", static_cast<int>(goal->process_parameters.size()));

            // 写入扩展信息 (Extend[] extends)
            context->set("action_extends", goal->extends);
            context->set("action_extends_count", static_cast<int>(goal->extends.size()));

            // 将每个扩展键值对写入黑板，使用键名作为黑板变量名
            for (size_t i = 0; i < goal->extends.size(); ++i) {
                const auto& extend = goal->extends[i];
                // 直接使用 key 作为黑板变量名，添加前缀避免冲突
                std::string blackboard_key = "extend_" + extend.key;
                context->set(blackboard_key, extend.value);

                // 同时按索引存储，方便遍历
                std::string index_key = "action_extend_" + std::to_string(i) + "_key";
                std::string index_value = "action_extend_" + std::to_string(i) + "_value";
                context->set(index_key, extend.key);
                context->set(index_value, extend.value);
            }

            RCLCPP_INFO(this->get_logger(), "已将 %zu 个工艺参数写入黑板", goal->process_parameters.size());
            for (size_t i = 0; i < goal->process_parameters.size(); ++i) {
                RCLCPP_INFO(this->get_logger(), "  黑板参数[%zu]: action_param_%zu = %s",
                           i, i, goal->process_parameters[i].c_str());
            }

            RCLCPP_INFO(this->get_logger(), "已将 %zu 个扩展信息写入黑板", goal->extends.size());
            for (size_t i = 0; i < goal->extends.size(); ++i) {
                const auto& extend = goal->extends[i];
                RCLCPP_INFO(this->get_logger(), "  扩展信息[%zu]: extend_%s = %s",
                           i, extend.key.c_str(), extend.value.c_str());
            }
        }

        current_process_tree_ = std::make_unique<BT::Tree>(factory_.createTreeFromFile(tree_file_path, context));

        // 暂时禁用 Groot2 发布器，避免端口冲突问题
        // TODO: 修复端口冲突后重新启用
        /*
        if (!groot2_publisher_) {
            groot2_publisher_ = std::make_unique<BT::Groot2Publisher>(*current_process_tree_, groot2_port_);
            RCLCPP_INFO(this->get_logger(), "Groot2 publisher created on port: %u", groot2_port_);
        }
        */

        RCLCPP_INFO(this->get_logger(), "Successfully loaded process tree: %s", tree_file_path.c_str());
        return true;
        
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Failed to load process tree %s: %s", tree_file_path.c_str(), e.what());
        return false;
    }
}

rclcpp_action::GoalResponse ProcessActionServer::handle_goal(
    const rclcpp_action::GoalUUID & uuid,
    std::shared_ptr<const ExecuteProcessAction::Goal> goal)
{
    (void)uuid;
    
    // 打印完整的请求信息
    RCLCPP_INFO(this->get_logger(), "收到工艺动作请求:");
    RCLCPP_INFO(this->get_logger(), "  工艺动作类型: %s", goal->process_action_type.c_str());
    RCLCPP_INFO(this->get_logger(), "  机器人ID: %s", goal->robot_id.c_str());
    RCLCPP_INFO(this->get_logger(), "  工艺流程ID: %s", goal->process_id.c_str());
    RCLCPP_INFO(this->get_logger(), "  超时时间: %d秒", goal->timeout_seconds);
    RCLCPP_INFO(this->get_logger(), "  是否抢占当前任务: %s", goal->preempt_current ? "是" : "否");
    
    // 打印工艺参数列表
    if(goal->process_parameters.empty()) {
        RCLCPP_INFO(this->get_logger(), "  工艺参数: 无");
    } else {
        RCLCPP_INFO(this->get_logger(), "  工艺参数列表:");
        for(size_t i = 0; i < goal->process_parameters.size(); i++) {
            RCLCPP_INFO(this->get_logger(), "    参数[%zu]: %s", i, goal->process_parameters[i].c_str());
        }
    }

    // 打印扩展信息列表
    if(goal->extends.empty()) {
        RCLCPP_INFO(this->get_logger(), "  扩展信息: 无");
    } else {
        RCLCPP_INFO(this->get_logger(), "  扩展信息列表:");
        for(size_t i = 0; i < goal->extends.size(); i++) {
            RCLCPP_INFO(this->get_logger(), "    扩展[%zu]: %s = %s",
                       i, goal->extends[i].key.c_str(), goal->extends[i].value.c_str());
        }
    }
    RCLCPP_INFO(this->get_logger(), "Received goal request for process action: %s", goal->process_action_type.c_str());

    // 获取锁，使用不同策略根据是否需要抢占
    std::unique_lock<std::mutex> lock;
    
    if (goal->preempt_current) {
        // 如果是抢占模式，则一直等待锁成功获取
        RCLCPP_INFO(this->get_logger(), "抢占模式：等待获取执行锁...");
        
        // 先设置抢占请求标志，通知正在执行的线程
        preempt_requested_.store(true);
        
        // 使用阻塞式锁获取，等待执行线程释放锁
        lock = std::unique_lock<std::mutex>(execution_mutex_);
        RCLCPP_INFO(this->get_logger(), "抢占模式：已获取执行锁");
    } else {
        // 非抢占模式，尝试获取锁，如果失败则直接拒绝
        lock = std::unique_lock<std::mutex>(execution_mutex_, std::try_to_lock);
        if (!lock.owns_lock()) {
            RCLCPP_WARN(this->get_logger(), "无法获取执行锁，另一个线程正在操作。由于非抢占模式，请求被拒绝。");
            return rclcpp_action::GoalResponse::REJECT;
        }
    }

    // 检查是否正在执行
    if (is_executing_.load()) {
        if (!goal->preempt_current) {
            RCLCPP_WARN(this->get_logger(), "已有任务在执行中。由于非抢占模式，请求被拒绝。");
            return rclcpp_action::GoalResponse::REJECT;
        } else {
            RCLCPP_INFO(this->get_logger(), "执行抢占操作：终止当前任务，准备执行新任务");
            cleanup_current_execution();
            
            // 确保清理已经完成并且不再执行任何操作
            if (execution_thread_.joinable()) {
                // 解锁以避免死锁，因为execution_thread_可能持有同一把锁
                lock.unlock();
                RCLCPP_INFO(this->get_logger(), "等待执行线程结束...");
                execution_thread_.join();
                RCLCPP_INFO(this->get_logger(), "执行线程已结束");
                // 重新获取锁
                lock.lock();
            }
            
            // 重置抢占请求标志
            preempt_requested_.store(false);
        }
    }

    // // 验证机器人ID
    // if (goal->robot_id != robot_id_) {
    //     RCLCPP_ERROR(this->get_logger(), "目标机器人ID '%s' 与服务器机器人ID '%s' 不匹配", 
    //                  goal->robot_id.c_str(), robot_id_.c_str());
    //     return rclcpp_action::GoalResponse::REJECT;
    // }

    // 检查工艺动作类型是否存在
    std::string tree_file_path = get_process_tree_file_path(goal->process_action_type);
    if (tree_file_path.empty()) {
        RCLCPP_ERROR(this->get_logger(), "无效的工艺动作类型: %s", goal->process_action_type.c_str());
        return rclcpp_action::GoalResponse::REJECT;
    }

    RCLCPP_INFO(this->get_logger(), "接受工艺动作请求: %s", goal->process_action_type.c_str());
    return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse ProcessActionServer::handle_cancel(
    const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "Received request to cancel goal");
    
    std::lock_guard<std::mutex> lock(execution_mutex_);
    if (current_goal_handle_ && current_goal_handle_ == goal_handle) {
        cleanup_current_execution();
        RCLCPP_INFO(this->get_logger(), "Goal canceled successfully");
        return rclcpp_action::CancelResponse::ACCEPT;
    }
    
    return rclcpp_action::CancelResponse::REJECT;
}

void ProcessActionServer::handle_accepted(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle)
{
    RCLCPP_INFO(this->get_logger(), "处理接受的任务请求...");
    
    // 先安全地等待任何现有的执行线程
    if (execution_thread_.joinable()) {
        try {
            RCLCPP_INFO(this->get_logger(), "等待之前的执行线程结束...");
            execution_thread_.join();
            RCLCPP_INFO(this->get_logger(), "之前的执行线程已结束");
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "等待执行线程时发生异常: %s", e.what());
        }
    }
    
    // 启动新线程执行任务
    try {
        RCLCPP_INFO(this->get_logger(), "启动新的执行线程...");
        execution_thread_ = std::thread(&ProcessActionServer::execute_process_action, this, goal_handle);
        RCLCPP_INFO(this->get_logger(), "新执行线程已启动");
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "创建执行线程时发生异常: %s", e.what());
        auto result = std::make_shared<ExecuteProcessAction::Result>();
        result->success = false;
        result->result_message = "Failed to start execution thread";
        result->final_status = STATUS_FAILED;
        goal_handle->abort(result);
    }
}

void ProcessActionServer::execute_process_action(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle)
{
    std::shared_ptr<ExecuteProcessAction::Result> result;
    
    try {
        std::lock_guard<std::mutex> lock(execution_mutex_);
        
        result = std::make_shared<ExecuteProcessAction::Result>();
        const auto goal = goal_handle->get_goal();
        
        current_goal_handle_ = goal_handle;
        is_executing_.store(true);
        preempt_requested_.store(false);  // 重置抢占标志
        
        auto start_time = std::chrono::steady_clock::now();
        
        RCLCPP_INFO(this->get_logger(), "Starting execution of process action: %s (ID: %s)", 
                    goal->process_action_type.c_str(), goal->process_id.c_str());

        // 发送初始反馈
        publish_feedback(goal_handle, "初始化", "加载工艺流程", 0.0, "开始执行工艺树", STATUS_INITIALIZING);

        // 获取并加载工艺动作树文件
        std::string tree_file_path = get_process_tree_file_path(goal->process_action_type);
        if (tree_file_path.empty() || !load_and_setup_tree(tree_file_path, goal)) {
            result->success = false;
            result->result_message = "Failed to load process tree file";
            result->final_status = STATUS_FAILED;
            goal_handle->abort(result);
            cleanup_current_execution();
            return;
        }

        publish_feedback(goal_handle, "Tree Loaded", "Executing process tree", 10.0, "Process tree loaded successfully", STATUS_RUNNING);

        // 执行工艺动作树
        BT::NodeStatus tree_status = BT::NodeStatus::RUNNING;
        int tick_count = 0;
        const int max_ticks = (goal->timeout_seconds > 0) ? goal->timeout_seconds * 10 : default_timeout_ * 10; // 100ms per tick

        // 用于跟踪反馈信息的变化和控制发送频率
        std::string prev_process_step = "";
        std::string prev_status = "";
        double prev_progress = 0.0;
        std::string prev_operation = "";
        std::string prev_message = "";
        
        // 存储上次发送PubProcessFeedback信息的时间
        auto last_feedback_time = std::chrono::steady_clock::now();
        bool has_sent_first_custom_feedback = false;

        try {
            while (rclcpp::ok() && tree_status == BT::NodeStatus::RUNNING && tick_count < max_ticks && is_executing_.load()) {
                // 检查是否被取消
                if (!goal_handle || goal_handle->is_canceling()) {
                    RCLCPP_INFO(this->get_logger(), "Goal canceled during execution");
                    result->success = false;
                    result->result_message = "Execution canceled by request";
                    result->final_status = STATUS_FAILED;
                    if (goal_handle) {
                        goal_handle->canceled(result);
                    }
                    cleanup_current_execution();
                    return;
                }
                
                // 检查是否收到抢占请求
                if (preempt_requested_.load()) {
                    RCLCPP_INFO(this->get_logger(), "收到抢占请求，中断当前执行");
                    result->success = false;
                    result->result_message = "Execution preempted by new request";
                    result->final_status = STATUS_FAILED;
                    if (goal_handle) {
                        goal_handle->canceled(result);
                    }
                    
                    // 释放执行锁，以便新任务可以获取
                    is_executing_.store(false);
                    
                    // 清理资源
                    cleanup_current_execution();
                    return;
                }

                // Tick 行为树
                if (current_process_tree_) {
                    tree_status = current_process_tree_->tickOnce();
                } else {
                    RCLCPP_ERROR(this->get_logger(), "行为树已经为空，停止执行");
                    break;
                }
                
                // 检查执行状态是否被其他线程更新
                if (!is_executing_.load()) {
                    RCLCPP_INFO(this->get_logger(), "执行状态已被更新为非运行，终止执行");
                    break;
                }
                
                // 定义一个标记是否有有效自定义反馈的变量
                bool has_valid_custom_feedback = false;

                // 临时存储从黑板获取的信息
                std::string current_step = "";
                std::string current_status = "";
                std::string current_operation = "";
                std::string status_message = "";
                double progress = prev_progress;
                
                // 尝试从黑板获取PubProcessFeedback写入的信息
                auto blackboard = current_process_tree_ ? current_process_tree_->rootBlackboard() : nullptr;
                if (blackboard) {
                    // 获取进度
                    double blackboard_progress;
                    if (blackboard->get("current_progress", blackboard_progress)) {
                        progress = blackboard_progress;
                        has_valid_custom_feedback = true;
                    } else if (blackboard->get("progress_percent", blackboard_progress)) {
                        progress = blackboard_progress;
                        has_valid_custom_feedback = true;
                    }
                    
                    // 获取其他信息
                    if (blackboard->get("current_process_step", current_step)) {
                        has_valid_custom_feedback = true;
                    }
                    
                    if (blackboard->get("current_status", current_status)) {
                        has_valid_custom_feedback = true;
                    }
                    
                    if (blackboard->get("current_operation", current_operation)) {
                        has_valid_custom_feedback = true;
                    }
                    
                    if (blackboard->get("status_message", status_message)) {
                        has_valid_custom_feedback = true;
                    }
                }
                
                // 检查是否需要发送反馈
                bool should_send_feedback = false;
                
                // 判断是否有新信息
                bool has_new_info = (current_step != prev_process_step) || 
                                  (current_status != prev_status) || 
                                  (std::abs(progress - prev_progress) > 0.1) || 
                                  (current_operation != prev_operation) || 
                                  (status_message != prev_message);

                // 如果是首次收到有效的自定义反馈，立即发送
                if (has_valid_custom_feedback && !has_sent_first_custom_feedback) {
                    should_send_feedback = true;
                    has_sent_first_custom_feedback = true;
                }
                // 当有新信息且有效时发送反馈
                else if (has_valid_custom_feedback && has_new_info) {
                    should_send_feedback = true;
                }
                // 定期发送最新状态（即使没有变化），避免客户端超时
                else if (has_valid_custom_feedback && 
                        (std::chrono::duration_cast<std::chrono::seconds>(
                            std::chrono::steady_clock::now() - last_feedback_time).count() >= 5)) {
                    should_send_feedback = true;
                }
                
                // 只有在应该发送反馈且有有效的自定义反馈时才发送
                if (should_send_feedback && has_valid_custom_feedback && goal_handle) {
                    // 确保所有必要字段都有值
                    if (current_step.empty()) current_step = prev_process_step.empty() ? "执行中" : prev_process_step;
                    if (current_status.empty()) current_status = prev_status.empty() ? STATUS_RUNNING : prev_status;
                    if (current_operation.empty()) current_operation = prev_operation.empty() ? "执行工艺流程" : prev_operation;
                    if (status_message.empty()) status_message = prev_message.empty() ? "工艺进行中" : prev_message;
                    
                    try {
                        publish_feedback(goal_handle, current_step, current_operation, progress, status_message, current_status);
                        last_feedback_time = std::chrono::steady_clock::now();
                    } catch (const std::exception& e) {
                        RCLCPP_ERROR(this->get_logger(), "发送反馈时发生异常: %s", e.what());
                    }
                    
                    // 更新上一次的值
                    prev_process_step = current_step;
                    prev_status = current_status;
                    prev_progress = progress;
                    prev_operation = current_operation;
                    prev_message = status_message;
                }
                
                // 如果工艺完成，发送一个最终的100%完成反馈
                if (tree_status != BT::NodeStatus::RUNNING && progress >= 95.0 && goal_handle) {
                    std::string final_step = current_step.empty() ? "完成" : current_step;
                    std::string final_status = (tree_status == BT::NodeStatus::SUCCESS) ? STATUS_FINISHED : STATUS_FAILED;
                    std::string final_operation = "工艺流程结束";
                    std::string final_message = (tree_status == BT::NodeStatus::SUCCESS) ? "工艺执行成功" : "工艺执行失败";
                    
                    try {
                        publish_feedback(goal_handle, final_step, final_operation, 100.0, final_message, final_status);
                    } catch (const std::exception& e) {
                        RCLCPP_ERROR(this->get_logger(), "发送最终反馈时发生异常: %s", e.what());
                    }
                }

                tick_count++;
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }

        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Exception during tree execution: %s", e.what());
            result->success = false;
            result->result_message = "Exception during execution: " + std::string(e.what());
            result->final_status = STATUS_FAILED;
            if (goal_handle) {
                goal_handle->abort(result);
            }
            cleanup_current_execution();
            return;
        }

        // 处理执行结果
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        result->execution_time = duration.count() / 1000.0;

        // 确保goal_handle仍然有效
        if (!goal_handle) {
            RCLCPP_ERROR(this->get_logger(), "Goal handle无效，无法返回结果");
            cleanup_current_execution();
            return;
        }

        if (tree_status == BT::NodeStatus::SUCCESS) {
            result->success = true;
            result->result_message = "Process action completed successfully";
            result->final_status = STATUS_FINISHED;
            result->quality_status = "OK";
            
            try {
                publish_feedback(goal_handle, "完成", "工艺流程结束", 100.0, "工艺执行成功", STATUS_FINISHED);
                goal_handle->succeed(result);
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "发送成功结果时发生异常: %s", e.what());
            }
            
            RCLCPP_INFO(this->get_logger(), "Process action '%s' completed successfully in %.2f seconds", 
                        goal->process_action_type.c_str(), result->execution_time);
        } else if (tree_status == BT::NodeStatus::FAILURE) {
            result->success = false;
            result->result_message = "Process action failed";
            result->final_status = STATUS_FAILED;
            result->quality_status = "NG";
            
            try {
                publish_feedback(goal_handle, "失败", "工艺流程失败", 100.0, "工艺执行失败", STATUS_FAILED);
                goal_handle->abort(result);
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "发送失败结果时发生异常: %s", e.what());
            }
            
            RCLCPP_ERROR(this->get_logger(), "Process action '%s' failed after %.2f seconds", 
                         goal->process_action_type.c_str(), result->execution_time);
        } else {
            // 超时或被中断
            result->success = false;
            result->result_message = "Process action timed out or interrupted";
            result->final_status = STATUS_FAILED;
            result->quality_status = "UNKNOWN";
            
            try {
                publish_feedback(goal_handle, "终止", "工艺流程中断", 100.0, "工艺执行终止", STATUS_FAILED);
                goal_handle->abort(result);
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "发送终止结果时发生异常: %s", e.what());
            }
            
            RCLCPP_WARN(this->get_logger(), "Process action '%s' timed out or interrupted after %.2f seconds", 
                        goal->process_action_type.c_str(), result->execution_time);
        }

        cleanup_current_execution();
    } catch (std::exception& e) {
        // 捕获任何未被内部try-catch捕获的异常
        RCLCPP_ERROR(this->get_logger(), "严重异常: %s", e.what());
        
        // 尝试清理资源和通知客户端
        try {
            if (!result) {
                result = std::make_shared<ExecuteProcessAction::Result>();
            }
            result->success = false;
            result->result_message = "Unhandled exception: " + std::string(e.what());
            result->final_status = STATUS_FAILED;
            
            if (goal_handle) {
                goal_handle->abort(result);
            }
        } catch (...) {
            RCLCPP_ERROR(this->get_logger(), "在清理期间发生了另一个异常");
        }
        
        // 无论如何都要清理执行资源
        cleanup_current_execution();
    }
}

void ProcessActionServer::publish_feedback(
    const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle,
    const std::string& current_step,
    const std::string& current_operation,
    double progress_percent,
    const std::string& status_message,
    const std::string& current_status)
{
    auto feedback = std::make_shared<ExecuteProcessAction::Feedback>();
    
    // 直接使用传入的参数
    feedback->current_process_step = current_step;
    feedback->current_operation = current_operation;
    feedback->progress_percent = progress_percent;
    feedback->status_message = status_message;
    feedback->current_status = current_status;
    
    // 设置时间戳
    feedback->timestamp = this->now();
    
    RCLCPP_DEBUG(this->get_logger(), "Publishing feedback: [Step: %s] [Status: %s] [Progress: %.1f%%] [Operation: %s] [Message: %s]", 
               feedback->current_process_step.c_str(), 
               feedback->current_status.c_str(), 
               feedback->progress_percent, 
               feedback->current_operation.c_str(), 
               feedback->status_message.c_str());
    
    goal_handle->publish_feedback(feedback);
}

void ProcessActionServer::cleanup_current_execution()
{
    RCLCPP_INFO(this->get_logger(), "正在清理当前执行的任务...");
    
    // 先标记执行状态为false，防止新tick
    is_executing_.store(false);
    
    // 安全停止行为树
    if (current_process_tree_) {
        try {
            RCLCPP_INFO(this->get_logger(), "正在停止并释放行为树...");
            
            // 主动停止行为树中的所有节点，触发所有节点的onHalted回调
            current_process_tree_->haltTree();
            
            // 给予足够时间让节点完成清理
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            
            // 释放行为树对象
            current_process_tree_.reset();
            RCLCPP_INFO(this->get_logger(), "行为树已停止和释放");
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "停止行为树时发生异常: %s", e.what());
        }
    }
    
    // 释放其他资源
    groot2_publisher_.reset();
    current_goal_handle_.reset();
    
    RCLCPP_INFO(this->get_logger(), "任务清理完成");
}

} // namespace rpcs_s_behaviors_workflow 