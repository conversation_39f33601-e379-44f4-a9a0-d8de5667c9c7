#include <memory>
#include "behaviortree_cpp/bt_factory.h"
#include "condition/SensorValueCheck.hpp"
#include "condition/SensorValueCheckInt.hpp"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::SensorValueCheck>("SensorValueCheck");
    factory.registerNodeType<rpcs_s_behaviors_workflow::SensorValueCheckInt>("SensorValueCheckInt");
}

// 包含行为树节点头文件
// 注意：DiGetIoStatus已经丢弃

namespace rpcs_s_behaviors_workflow
{

void RegisterConditionNodes(BT::BehaviorTreeFactory& factory)
{
  // 注册节点
  factory.registerNodeType<SensorValueCheck>("SensorValueCheck");
  factory.registerNodeType<SensorValueCheckInt>("SensorValueCheckInt");
}

}  // namespace rpcs_s_behaviors_workflow 