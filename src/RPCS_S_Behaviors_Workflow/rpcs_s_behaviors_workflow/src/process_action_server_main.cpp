#include "rpcs_s_behaviors_workflow/process_action_server.hpp"
#include <rclcpp/rclcpp.hpp>
#include <signal.h>

std::shared_ptr<rpcs_s_behaviors_workflow::ProcessActionServer> g_server;

void signal_handler(int signum) {
    (void)signum;
    RCLCPP_INFO(rclcpp::get_logger("process_action_server_main"), "Received signal, shutting down...");
    if (g_server) {
        rclcpp::shutdown();
    }
}

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    
    // 注册信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建临时节点来获取参数
    std::string robot_id = "Robot1";
    std::string config_file = "";
    std::string tree_file_path = "";
    
    {
        // 使用作用域限制临时节点的生命周期
        auto temp_node = rclcpp::Node::make_shared("temp_param_node");
        
        // 声明并获取机器人ID参数
        temp_node->declare_parameter("robot_id", "Robot1");
        robot_id = temp_node->get_parameter("robot_id").as_string();
        
        // 声明并获取配置文件路径参数
        temp_node->declare_parameter("config_file", "");
        config_file = temp_node->get_parameter("config_file").as_string();
        
        // 声明并获取树文件路径参数
        temp_node->declare_parameter("tree_file_path", "");
        tree_file_path = temp_node->get_parameter("tree_file_path").as_string();
        
        // 临时节点在此作用域结束时会被销毁
    }
    
    // 如果配置文件路径为空，使用默认路径
    if (config_file.empty()) {
        config_file = "/home/<USER>/workspaces/home_git/AutoCreateRPS_home/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_action_mapping.yaml";
    }
    
    // 如果树文件路径为空，使用默认路径
    if (tree_file_path.empty()) {
        tree_file_path = "/home/<USER>/workspaces/home_git/AutoCreateRPS_home/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_trees";
    }
    
    // 也支持环境变量作为备选
    if (robot_id == "Robot1") {
        const char* env_robot_id = getenv("ROBOT_ID");
        if (env_robot_id != nullptr) {
            robot_id = env_robot_id;
        }
    }
    
    RCLCPP_INFO(rclcpp::get_logger("process_action_server_main"), 
                "Starting ProcessActionServer for robot: %s", robot_id.c_str());
    
    try {
        // 创建 ProcessActionServer 的节点选项并设置参数
        rclcpp::NodeOptions options;
        options.parameter_overrides({
            {"config_file", config_file},
            {"tree_file_path", tree_file_path}
        });
        
        // 创建 ProcessActionServer
        g_server = std::make_shared<rpcs_s_behaviors_workflow::ProcessActionServer>(robot_id, options);
        
        // 创建多线程执行器
        rclcpp::executors::MultiThreadedExecutor executor;
        executor.add_node(g_server);
        
        RCLCPP_INFO(rclcpp::get_logger("process_action_server_main"), 
                    "ProcessActionServer started successfully for robot: %s", robot_id.c_str());
        
        // 运行执行器
        executor.spin();
        
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("process_action_server_main"), 
                     "Failed to start ProcessActionServer: %s", e.what());
        return 1;
    }
    
    RCLCPP_INFO(rclcpp::get_logger("process_action_server_main"), "ProcessActionServer shutdown complete");
    return 0;
} 