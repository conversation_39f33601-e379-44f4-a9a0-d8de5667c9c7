#!/bin/bash

# 脚本用于更新源文件中的include路径
# 将"rpcs_s_behaviors_workflow/plugins/..."替换为直接引用"..."

# 定义项目根目录
PROJECT_ROOT=$(pwd)
PLUGINS_SRC_DIR="${PROJECT_ROOT}/plugins/src"
PLUGINS_INCLUDE_DIR="${PROJECT_ROOT}/plugins/include"

echo "开始更新源文件中的include路径..."

# 更新plugins/src目录下的所有.cpp文件
find ${PLUGINS_SRC_DIR} -name "*.cpp" -type f -exec sed -i 's|#include "rpcs_s_behaviors_workflow/plugins/|#include "|g' {} \;
echo "已更新 .cpp 文件中的include路径"

# 更新plugins/include目录下的所有.hpp文件中的交叉引用
find ${PLUGINS_INCLUDE_DIR} -name "*.hpp" -type f -exec sed -i 's|#include "rpcs_s_behaviors_workflow/plugins/|#include "|g' {} \;
echo "已更新 .hpp 文件中的include路径"

# 更新src目录下的所有.cpp文件
find ${PROJECT_ROOT}/src -name "*.cpp" -type f -exec sed -i 's|#include "rpcs_s_behaviors_workflow/plugins/|#include "plugins/|g' {} \;
echo "已更新主源文件中的include路径"

echo "include路径更新完成!" 