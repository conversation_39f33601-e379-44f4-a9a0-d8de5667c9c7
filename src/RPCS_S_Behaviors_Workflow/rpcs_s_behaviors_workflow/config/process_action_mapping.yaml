mapping:
  R03001010001:
    6_StackBoards:
      StackLightBoards: stack_light_boards.xml
    LightPanelSplicing:
      LightBoardCompleteWorkflow: complete_workflow.xml
      LightBoardPickSendStock: pick_send_stock.xml
      LightBoardResetBinPosition: motor_homing_jack.xml
      LightBoardSelfCheck: self_check.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    Testing:
      IoTest: io_test.xml
      Motor5IO4Linkage: motor5_io4_linkage.xml
      MotorHomingAll: motor_homing_all.xml
      MotorHomingAndJack: motor_homing_jack.xml
      MotorHomingOnly: motor_homing.xml
      Robot1ArmTest: arm_test.xml
      TestMotorHoming: test_motor_homing.xml
    VisionAlignment:
      BoardAlignExample: board_align_example.xml
      CameraCaptureTest: camera_capture_test.xml
      CommonAlignExample: common_align_example.xml
      DepthFusionExample: depth_fusion_example.xml
      ImageDetectionTest: image_detection_test.xml
      RobotAlignExample: robot_align_example.xml
  R03001010002:
    LightPanelSplicing:
      LightBoardPickSendStock: pick_send_stock.xml
      LightBoardSelfCheck: self_check.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    MaterialTransfer:
      MaterialTransferExecution: material_transfer_execution.xml
      MaterialTransferMain: material_transfer_main.xml
      MaterialTransferPreparation: material_transfer_preparation.xml
    Other:
      PcbaPickSendStock: pick_send_stock.xml

    Testing:
      R03001010002TestFlexiv: test_flexiv.xml

    WorkInAdvance:
      Robot2ReturnOrigin: robot2_return_origin.xml

    PowerDetect:
      PlaceProductNG: place_product_ng.xml
      PlaceProductOK: place_product_ok.xml
      TransferFixture: transfer_fixture.xml

  R03001110003:
    LightPanelSplicing:
      LightBoardPickSendStock: light_board_pick_send_stock.xml
      LightBoardPickSendStockAlt: pick_send_stock.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    ProtectiveCase:
      ProCasePickSendStock: pick_send_stock.xml
      FastenProCase: fasten_pro_case.xml

  Test:
    Test:
      MaterialTransferPreparationTest: material_transfer_preparation_test.xml
