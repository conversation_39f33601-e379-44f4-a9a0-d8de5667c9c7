mapping:
  R03001010001:
    6_StackBoards:
      StackLightBoards: stack_light_boards.xml
    LightPanelSplicing:
      LightBoardCompleteWorkflow: complete_workflow.xml
      LightBoardPickSendStock: pick_send_stock.xml
      LightBoardResetBinPosition: motor_homing_jack.xml
      LightBoardSelfCheck: self_check.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    # 物料转移
    MaterialTransfer:
      MaterialTransferExecution: material_transfer_execution.xml
      MaterialTransferMain: material_transfer_main.xml
      MaterialTransferPreparation: material_transfer_preparation.xml
    Testing:
      IoTest: io_test.xml
      Motor5IO4Linkage: motor5_io4_linkage.xml
      MotorHomingAll: motor_homing_all.xml
      MotorHomingAndJack: motor_homing_jack.xml
      MotorHomingOnly: motor_homing.xml
      Robot1ArmTest: arm_test.xml
      TestMotorHoming: test_motor_homing.xml
    VisionAlignment:
      BoardAlignExample: board_align_example.xml
      CameraCaptureTest: camera_capture_test.xml
      CommonAlignExample: common_align_example.xml
      DepthFusionExample: depth_fusion_example.xml
      ImageDetectionTest: image_detection_test.xml
      RobotAlignExample: robot_align_example.xml

    # 载板贴合转移
    8_CarrierBonding:
      # 载板贴合
      BondCarrier: bond_carrier.xml
      # 半成品转移
      MoveWIP: semi_finished_product_transfer.xml

  R03001010002:
    LightPanelSplicing:
      LightBoardPickSendStock: pick_send_stock.xml
      LightBoardSelfCheck: self_check.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    # 物料转移
    MaterialTransfer:
      MaterialTransferExecution: material_transfer_execution.xml
      MaterialTransferMain: material_transfer_main.xml
      MaterialTransferPreparation: material_transfer_preparation.xml
    Other:
      PcbaPickSendStock: pick_send_stock.xml

    Testing:
      R03001010002TestFlexiv: test_flexiv.xml
      Test: action_extends_simple_test.xml

    WorkInAdvance:
      Robot2ReturnOrigin: robot2_return_origin.xml

    PowerDetect:
      PlaceProductNG: place_product_ng.xml
      PlaceProductOK: place_product_ok.xml
      TransferFixture: transfer_fixture.xml

    # PCBA固定+FPC插接
    PCBA_fixation_FPC_insertion:
      # 半成品摆放
      PlaceWIP: SemiFinishedProductPlacement.xml
      # PCBA摆放
      PlacePCBA: PCBA_Placement.xml
      # FPC转接线粘贴
      DoFPCAdapterPaste: FPC_Adapter_Paste.xml
      # FPC开盖
      OpenFPC: FPC_CoverOpening.xml
      # FPC转接线开盖
      OpenFPCAdapter: FPC_Adapter_CoverOpening.xml
      # FPC插接
      ConnectFPC: FPC_Insertion.xml
      # FPC转接线插接
      ConnectFPCAdapter: FPC_Adapter_Insertion.xml


  R03001110003:
    LightPanelSplicing:
      LightBoardPickSendStock: light_board_pick_send_stock.xml
      LightBoardPickSendStockAlt: pick_send_stock.xml
      LightBoardToRacks: light_board_to_racks.xml
      LightBoardToSafePoint: light_board_to_safe_point.xml
    ProtectiveCase:
      ProCasePickSendStock: pick_send_stock.xml
      FastenProCase: fasten_pro_case.xml
    # 物料转移
    MaterialTransfer:
      MaterialTransferExecution: material_transfer_execution.xml
      MaterialTransferMain: material_transfer_main.xml
      MaterialTransferPreparation: material_transfer_preparation.xml
  Test:
    Test:
      MaterialTransferPreparationTest: material_transfer_preparation_test.xml
      ActionParametersTest: action_parameters_test.xml
