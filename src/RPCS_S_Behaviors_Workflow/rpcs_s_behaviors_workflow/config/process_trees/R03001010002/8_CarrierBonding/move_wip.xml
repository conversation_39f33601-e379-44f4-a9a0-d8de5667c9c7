<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="MoveWIP">
        <Sequence name="MainSequence">
            <PubProcessFeedback
                strProcessStep="半成品转移"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="开始半成品转移任务"
                strOperation="开始半成品转移任务" />

            <PubProcessFeedback
                strProcessStep="半成品转移"
                strStatus="RUNNING"
                doubleProgress="0"
                strMessage="步骤(1/2)： 移动半成品至保压区"
                strOperation="步骤(1/2)： 移动半成品至保压区" />
            <!-- 1. 移动半成品至保压区-->
            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0803"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />

            <PubProcessFeedback
                strProcessStep="半成品转移"
                strStatus="RUNNING"
                doubleProgress="50"
                strMessage="步骤(2/2)： 机械臂回零"
                strOperation="步骤(2/2)： 机械臂回零" />

            <!-- 2. 机械臂回零 -->
            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0804"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />

            <PubProcessFeedback
                strProcessStep="半成品转移"
                strStatus="RUNNING"
                doubleProgress="100"
                strMessage="半成品转移任务完成"
                strOperation="半成品转移任务完成" />

        </Sequence>
    </BehaviorTree>
</root>