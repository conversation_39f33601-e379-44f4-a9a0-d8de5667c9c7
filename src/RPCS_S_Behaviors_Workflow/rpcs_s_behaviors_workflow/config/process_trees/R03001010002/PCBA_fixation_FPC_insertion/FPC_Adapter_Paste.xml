<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC插接 - FPC转接线粘贴 -->
	<!-- FPC转接线粘贴的起始位是PCBA摆放以及FPC转接线tray上料之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - 取FPC转接线（1个tray盘8个）+ 离型膜剥离 + FPC转接线粘贴（1个载板4次）-->
	<BehaviorTree ID="PCBA_FPC_FPC_AdapterPaste">
		<Sequence name="PCBA_FPC_FPC_AdapterPaste">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="FPC转接线粘贴"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="FPC转接线粘贴"
				strOperation="FPC转接线粘贴" />

			<!-- FPC转接线粘贴 -->
			<Fallback name="FPC_AdapterPasteFallback">
				<Sequence name="FPC_AdapterPasteSequence">
					<!-- FPC转接线粘贴 -->
					<!-- 2号小车 - 机械臂操作 - 取FPC转接线（1个tray盘8个）+ 离型膜剥离 + FPC转接线粘贴（1个载板4次）-->
					<PubProcessFeedback
						strProcessStep="FPC转接线粘贴"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="FPC转接线粘贴"
						strOperation="FPC转接线粘贴" />

					<!-- 1个载板，需要粘贴4次FPC转接线，因此循环4次 -->
					<Repeat num_cycles="4" name="FPC_AdapterPasteLoop">
						<Sequence name="FPC_AdapterPasteLoopCount">
							<PubProcessFeedback
								strProcessStep="FPC转接线粘贴-循环中"
								strStatus="RUNNING"
								doubleProgress="0.0"
								strMessage="FPC转接线粘贴-循环中"
								strOperation="FPC转接线粘贴" />
	
							<PubProcessFeedback
								strProcessStep="需要从tray盘获取时，到第几个了"
								strStatus="RUNNING"
								doubleProgress="20.0"
								strMessage="需要从tray盘获取时，到第几个了"
								strOperation="FPC转接线粘贴" />
	
							<!-- 需要从tray盘获取时，到第几个了，返回当前数量，并递增1，第8个之后自动变为1 -->
							<FPCAdapterNumber strType="TRAY" intFPCAdapterNumber="{intFPCAdapterNumber_TRAY}"/>
	
							<PubProcessFeedback
								strProcessStep="调用机械臂，移动到tray盘吸取FPC转接线"
								strStatus="RUNNING"
								doubleProgress="30.0"
								strMessage="调用机械臂，移动到tray盘吸取FPC转接线"
								strOperation="FPC转接线粘贴" />
	
							<!-- 需要修改任务id、速度
								其中 intFunctionData0="{intFPCAdapterNumber_TRAY}" 代表需要从tray盘获取时，到第几个了
							<RobotArmControl
								strDeviceName="/R03001010002"
								intProjectId="1"
								intSpeedMultiplier="25"
								intTimeoutMs="60000"
								intFunctionData0="{intFPCAdapterNumber_TRAY}"
								intFunctionData1="{intFPCAdapterNumber_TRAY}"
								/>
		
							<CommonAlign strCameraNamespaces="FPC"
								strModelPath="/home/<USER>/vision/templates/fpc-0725.stp"
								doublePixelDimensions="0.055"
								doubleTimeout="60.0"
								boolOutputSuccess="{boolOutputSuccess}"
								strOutputMessage="{strOutputMessage}"
								doubleX="{doubleX}"
								doubleY="{doubleY}"
								doubleRZ="{doubleRZ}"/>
		
							<ImageDetectionConversion strType="-1,0,-1"
								doubleX="{doubleX}"
								doubleY="{doubleY}"
								doubleRZ="{doubleRZ}"
								image_detection_x="{image_detection_x1}"
								image_detection_y="{image_detection_y1}"
								image_detection_rz="{image_detection_rz1}"
								boolOutputSuccess="{boolOutputSuccess}"
								strOutputMessage="{strOutputMessage}"
								strOutputResponse="{strOutputResponse}"/>
		
							<RobotArmControl
								strDeviceName="/R03001010002"
								intProjectId="2"
								intSpeedMultiplier="25"
								intTimeoutMs="60000"
								intFunctionData0="{intFPCAdapterNumber_TRAY}"
								intFunctionData1="{intFPCAdapterNumber_TRAY}"
								floatRotationRz="{image_detection_rz1}" />
		
							<CommonAlign strCameraNamespaces="FPC"
								strModelPath="/home/<USER>/vision/templates/fpc-0725.stp"
								doublePixelDimensions="0.055"
								doubleTimeout="60.0"
								boolOutputSuccess="{boolOutputSuccess}"
								strOutputMessage="{strOutputMessage}"
								doubleX="{doubleX}"
								doubleY="{doubleY}"
								doubleRZ="{doubleRZ}"/>
		
							<ImageDetectionConversion strType="-1,0,-1"
								doubleX="{doubleX}"
								doubleY="{doubleY}"
								doubleRZ="{doubleRZ}"
								image_detection_x="{image_detection_x2}"
								image_detection_y="{image_detection_y2}"
								image_detection_rz="{image_detection_rz2}"
								boolOutputSuccess="{boolOutputSuccess}"
								strOutputMessage="{strOutputMessage}"
								strOutputResponse="{strOutputResponse}"/>
		
							<RobotArmControl
								strDeviceName="/R03001010002"
								intProjectId="3"
								intSpeedMultiplier="25"
								intTimeoutMs="60000"
								intFunctionData0="{intFPCAdapterNumber_TRAY}"
								intFunctionData1="{intFPCAdapterNumber_TRAY}"
								floatPositionX="{image_detection_x2}"
								floatPositionY="{image_detection_y2}" />
		
							<RobotArmControl
								strDeviceName="/R03001010002"
								intProjectId="4"
								intSpeedMultiplier="25"
								intTimeoutMs="60000"
								intFunctionData0="{intFPCAdapterNumber_TRAY}"
								intFunctionData1="{intFPCAdapterNumber_TRAY}"/>
							-->


							<!-- 调用PLC执行离型膜剥离 -->
							<PubProcessFeedback
								strProcessStep="调用PLC执行离型膜剥离"
								strStatus="RUNNING"
								doubleProgress="40.0"
								strMessage="调用PLC执行离型膜剥离"
								strOperation="FPC转接线粘贴" />
	
							<!-- 调用PLC执行离型膜剥离
							<PetStrip
								strOperation="B"
								doubleTimeout="60.0"/>
							-->


							<PubProcessFeedback
								strProcessStep="FPC转接线粘贴到载板时，到第几个了"
								strStatus="RUNNING"
								doubleProgress="50.0"
								strMessage="FPC转接线粘贴到载板时，到第几个了"
								strOperation="FPC转接线粘贴" />
	
							<!-- FPC转接线粘贴到载板时，到第几个了，返回当前数量，并递增1，第4个之后自动变为1 -->
							<FPCAdapterNumber strType="BOARD" intFPCAdapterNumber="{intFPCAdapterNumber_BOARD}"/>
	
							<PubProcessFeedback
								strProcessStep="调用机械臂，执行FPC转接线粘贴"
								strStatus="RUNNING"
								doubleProgress="60.0"
								strMessage="调用机械臂，执行FPC转接线粘贴"
								strOperation="FPC转接线粘贴" />
	
							<!-- 需要修改任务id、速度
								其中 intFunctionData0="{intFPCAdapterNumber_BOARD}" 代表FPC转接线粘贴到载板时，到第几个了
							<RobotArmControl
								strDeviceName="/R03001010002"
								intProjectId="100000"
								intSpeedMultiplier="10"
								intFunctionData0="{intFPCAdapterNumber_BOARD}" />
							-->
	
							<PubProcessFeedback
								strProcessStep="FPC转接线粘贴-循环中-粘贴完一个"
								strStatus="RUNNING"
								doubleProgress="70.0"
								strMessage="FPC转接线粘贴-循环中-粘贴完一个"
								strOperation="FPC转接线粘贴" />
						</Sequence>
					</Repeat>
				</Sequence>

				<!-- FPC转接线粘贴失败处理 -->
				<Sequence name="FPC_AdapterPasteFailureHandler">
					<PubProcessFeedback
						strProcessStep="FPC转接线粘贴"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="FPC转接线粘贴失败"
						strOperation="FPC转接线粘贴失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- FPC转接线粘贴完成，已粘贴完4个 -->
			<PubProcessFeedback
				strProcessStep="FPC转接线粘贴"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="FPC转接线粘贴完成"
				strOperation="FPC转接线粘贴结束" />
		</Sequence>
	</BehaviorTree>
</root>
