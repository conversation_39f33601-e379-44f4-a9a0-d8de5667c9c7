<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC插接 - PCBA摆放 -->
	<!-- PCBA摆放的起始位是FPC挑起之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - 移动到tray盘 + PCBA摆放（2次）-->
	<BehaviorTree ID="PCBA_FPC_PCBA_Placement">
		<Sequence name="PCBA_FPC_PCBA_Placement">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="PCBA摆放"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="PCBA摆放"
				strOperation="PCBA摆放" />

			<!-- PCBA摆放 -->
			<Fallback name="PCBA_PlacementFallback">
				<Sequence name="PCBA_PlacementSequence">
					<!-- PCBA摆放 -->
					<!-- 2号小车 - 机械臂操作 - 移动到tray盘 + PCBA摆放（2次）-->
					<PubProcessFeedback
						strProcessStep="PCBA摆放第一块"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="PCBA摆放第一块"
						strOperation="PCBA摆放第一块" />

					<!-- 需要修改任务id、速度
					其中 intFunctionData0="1" 代表PCBA摆放第一块
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="1"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="1"
						intFunctionData1="1"
						/>

					<CommonAlign strCameraNamespaces="PCBA"
						strModelPath="/home/<USER>/vision/templates/pcba-pick-0716-2.stp"
						doublePixelDimensions="0.048"
						doubleTimeout="60.0"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"/>

					<ImageDetectionConversion strType="-1,0,-1"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"
						image_detection_x="{image_detection_x1}"
						image_detection_y="{image_detection_y1}"
						image_detection_rz="{image_detection_rz1}"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						strOutputResponse="{strOutputResponse}"/>

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="2"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="1"
						intFunctionData1="1"
						floatRotationRz="{image_detection_rz1}" />

					<CommonAlign strCameraNamespaces="PCBA"
						strModelPath="/home/<USER>/vision/templates/pcba-pick-0716-2.stp"
						doublePixelDimensions="0.048"
						doubleTimeout="60.0"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"/>

					<ImageDetectionConversion strType="-1,0,-1"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"
						image_detection_x="{image_detection_x2}"
						image_detection_y="{image_detection_y2}"
						image_detection_rz="{image_detection_rz2}"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						strOutputResponse="{strOutputResponse}"/>

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="3"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="1"
						intFunctionData1="1"
						floatPositionX="{image_detection_x2}"
						floatPositionY="{image_detection_y2}" />

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="4"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="1"
						intFunctionData1="1"
						intFunctionData2="1"/>
					-->


					<PubProcessFeedback
						strProcessStep="PCBA摆放第二块"
						strStatus="RUNNING"
						doubleProgress="50.0"
						strMessage="PCBA摆放第二块"
						strOperation="PCBA摆放第二块" />

					<!-- 需要修改任务id、速度
					其中 intFunctionData0="2" 代表PCBA摆放第二块
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="1"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="2"
						intFunctionData1="2"
						/>

					<CommonAlign strCameraNamespaces="PCBA"
						strModelPath="/home/<USER>/vision/templates/pcba-pick-0716-2.stp"
						doublePixelDimensions="0.048"
						doubleTimeout="60.0"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"/>

					<ImageDetectionConversion strType="-1,0,-1"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"
						image_detection_x="{image_detection_x1}"
						image_detection_y="{image_detection_y1}"
						image_detection_rz="{image_detection_rz1}"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						strOutputResponse="{strOutputResponse}"/>

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="2"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="2"
						intFunctionData1="2"
						floatRotationRz="{image_detection_rz1}" />

					<CommonAlign strCameraNamespaces="PCBA"
						strModelPath="/home/<USER>/vision/templates/pcba-pick-0716-2.stp"
						doublePixelDimensions="0.048"
						doubleTimeout="60.0"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"/>

					<ImageDetectionConversion strType="-1,0,-1"
						doubleX="{doubleX}"
						doubleY="{doubleY}"
						doubleRZ="{doubleRZ}"
						image_detection_x="{image_detection_x2}"
						image_detection_y="{image_detection_y2}"
						image_detection_rz="{image_detection_rz2}"
						boolOutputSuccess="{boolOutputSuccess}"
						strOutputMessage="{strOutputMessage}"
						strOutputResponse="{strOutputResponse}"/>

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="3"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="2"
						intFunctionData1="2"
						floatPositionX="{image_detection_x2}"
						floatPositionY="{image_detection_y2}" />

					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="4"
						intSpeedMultiplier="25"
						intTimeoutMs="60000"
						intFunctionData0="2"
						intFunctionData1="2"
						intFunctionData2="2"/>
					-->

					<PubProcessFeedback
						strProcessStep="PCBA摆放"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="PCBA摆放完成"
						strOperation="PCBA摆放" />
				</Sequence>

				<!-- PCBA摆放失败处理 -->
				<Sequence name="PCBA_PlacementFailureHandler">
					<PubProcessFeedback
						strProcessStep="PCBA摆放"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="PCBA摆放失败"
						strOperation="PCBA摆放失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- PCBA摆放完成 -->
			<PubProcessFeedback
				strProcessStep="PCBA摆放"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="PCBA摆放完成"
				strOperation="PCBA摆放结束" />
		</Sequence>
	</BehaviorTree>
</root>
