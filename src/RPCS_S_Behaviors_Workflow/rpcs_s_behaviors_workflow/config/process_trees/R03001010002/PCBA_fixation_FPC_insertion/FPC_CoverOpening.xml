<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC插接 - FPC开盖 -->
	<!-- FPC开盖的起始位是PCBA锁付之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - FPC开盖（12个正向） -->
	<BehaviorTree ID="PCBA_FPC_FPC_CoverOpening">
		<Sequence name="PCBA_FPC_FPC_CoverOpeningSequence">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="FPC开盖"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="FPC开盖"
				strOperation="FPC开盖" />

			<!-- FPC开盖 -->
			<Fallback name="FPC_CoverOpeningFallback">
				<Sequence name="FPC_CoverOpeningSequence">
					<!-- FPC开盖 -->
					<!-- 2号小车 - 机械臂操作 - FPC开盖（12个正向） -->
					<PubProcessFeedback
						strProcessStep="FPC开盖"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="FPC开盖"
						strOperation="FPC开盖" />

					<!-- 需要修改任务id、速度
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="100000"
						intSpeedMultiplier="10"/>
					-->

					<PubProcessFeedback
						strProcessStep="FPC开盖"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="FPC开盖完成"
						strOperation="FPC开盖" />
				</Sequence>

				<!-- FPC开盖失败处理 -->
				<Sequence name="FPC_CoverOpeningFailureHandler">
					<PubProcessFeedback
						strProcessStep="FPC开盖"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="FPC开盖失败"
						strOperation="FPC开盖失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- FPC开盖完成 -->
			<PubProcessFeedback
				strProcessStep="FPC开盖"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="FPC开盖完成"
				strOperation="FPC开盖结束" />

		</Sequence>
	</BehaviorTree>
</root>
