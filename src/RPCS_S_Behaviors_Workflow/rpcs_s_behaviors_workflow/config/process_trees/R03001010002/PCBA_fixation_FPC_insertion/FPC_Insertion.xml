<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC插接 - FPC插接 -->
	<!-- FPC插接的起始位是FPC开盖之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - FPC插接（12个正向） -->
	<BehaviorTree ID="PCBA_FPC_FPC_Insertion">
		<Sequence name="PCBA_FPC_FPC_InsertionSequence">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="FPC插接"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="FPC插接"
				strOperation="FPC插接" />

			<!-- FPC插接 -->
			<Fallback name="FPC_InsertionFallback">
				<Sequence name="FPC_InsertionSequence">
					<!-- FPC插接 -->
					<!-- 2号小车 - 机械臂操作 - FPC插接（12个正向） -->
					<PubProcessFeedback
						strProcessStep="FPC插接"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="FPC插接"
						strOperation="FPC插接" />

					<!-- 需要修改任务id、速度
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="100000"
						intSpeedMultiplier="10"/>
					-->

					<PubProcessFeedback
						strProcessStep="FPC插接"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="FPC插接完成"
						strOperation="FPC插接" />
				</Sequence>

				<!-- FPC插接失败处理 -->
				<Sequence name="FPC_InsertionFailureHandler">
					<PubProcessFeedback
						strProcessStep="FPC插接"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="FPC插接失败"
						strOperation="FPC插接失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- FPC插接完成 -->
			<PubProcessFeedback
				strProcessStep="FPC插接"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="FPC插接完成"
				strOperation="FPC插接结束" />

		</Sequence>
	</BehaviorTree>
</root>
