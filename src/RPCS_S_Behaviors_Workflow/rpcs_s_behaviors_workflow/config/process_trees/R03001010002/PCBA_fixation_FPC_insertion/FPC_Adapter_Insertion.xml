<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC转接线插接 - FPC转接线插接 -->
	<!-- FPC转接线插接的起始位是FPC转接线开盖之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - FPC转接线插接（4个反向） -->
	<BehaviorTree ID="PCBA_FPC_FPC_Adapter_Insertion">
		<Sequence name="PCBA_FPC_FPC_Adapter_InsertionSequence">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="FPC转接线插接"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="FPC转接线插接"
				strOperation="FPC转接线插接" />

			<!-- FPC转接线插接 -->
			<Fallback name="FPC_Adapter_InsertionFallback">
				<Sequence name="FPC_Adapter_InsertionSequence">
					<!-- FPC转接线插接 -->
					<!-- 2号小车 - 机械臂操作 - FPC转接线插接（4个反向） -->
					<PubProcessFeedback
						strProcessStep="FPC转接线插接"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="FPC转接线插接"
						strOperation="FPC转接线插接" />

					<!-- 需要修改任务id、速度
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="100000"
						intSpeedMultiplier="10"/>
					-->

					<PubProcessFeedback
						strProcessStep="FPC转接线插接"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="FPC转接线插接完成"
						strOperation="FPC转接线插接" />
				</Sequence>

				<!-- FPC转接线插接失败处理 -->
				<Sequence name="FPC_Adapter_InsertionFailureHandler">
					<PubProcessFeedback
						strProcessStep="FPC转接线插接"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="FPC转接线插接失败"
						strOperation="FPC转接线插接失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- FPC转接线插接完成 -->
			<PubProcessFeedback
				strProcessStep="FPC转接线插接"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="FPC转接线插接完成"
				strOperation="FPC转接线插接结束" />

		</Sequence>
	</BehaviorTree>
</root>
