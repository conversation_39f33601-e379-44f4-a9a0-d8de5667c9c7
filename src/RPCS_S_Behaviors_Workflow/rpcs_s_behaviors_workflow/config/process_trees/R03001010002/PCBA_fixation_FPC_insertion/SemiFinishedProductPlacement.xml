<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- PCBA固定+FPC插接 - 半成品摆放 -->
	<!-- 半成品摆放的起始位是半成品保压之后 -->
	<!-- 几号小车工艺: 2号小车 - 机械臂操作 - 移动半成品至FPC插接模组 -->
	<BehaviorTree ID="PCBA_FPC_SemiFinishedProductPlacement">
		<Sequence name="PCBA_FPC_SemiFinishedProductPlacementSequence">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="半成品摆放"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="半成品摆放"
				strOperation="半成品摆放" />

			<!-- 半成品摆放 -->
			<Fallback name="SemiFinishedProductPlacementFallback">
				<Sequence name="SemiFinishedProductPlacementSequence">
					<!-- 半成品摆放 -->
					<!-- 2号小车 - 机械臂操作 - 移动半成品至FPC插接模组 -->
					<PubProcessFeedback
						strProcessStep="半成品摆放"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="半成品摆放"
						strOperation="半成品摆放" />

					<!-- 需要修改任务id、速度
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="100000"
						intSpeedMultiplier="10"/>
					-->

					<PubProcessFeedback
						strProcessStep="半成品摆放"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="半成品摆放完成"
						strOperation="半成品摆放" />
				</Sequence>

				<!-- 半成品摆放失败处理 -->
				<Sequence name="SemiFinishedProductPlacementFailureHandler">
					<PubProcessFeedback
						strProcessStep="半成品摆放"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="半成品摆放失败"
						strOperation="半成品摆放失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- 半成品摆放完成 -->
			<PubProcessFeedback
				strProcessStep="半成品摆放"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="半成品摆放完成"
				strOperation="半成品摆放结束" />

		</Sequence>
	</BehaviorTree>
</root>
