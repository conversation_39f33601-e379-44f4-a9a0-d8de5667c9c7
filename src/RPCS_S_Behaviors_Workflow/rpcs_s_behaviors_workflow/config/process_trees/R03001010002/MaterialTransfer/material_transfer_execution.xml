<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 物料转移执行行为树 -->
    <BehaviorTree ID="MaterialTransferExecution">
        <Sequence name="MaterialTransferExecutionSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移执行"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始物料转移执行流程"
                strOperation="检查前置条件" />

            <!-- 打开真空泵 -->
            <DigitalOutputWrite
                    strDeviceId="R03001010002"
                    intOutputAddresses="12,13,14"
                    boolOutputValues="1,1,1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{vacuum_pump1_success}"
                    strOutputMessage="{vacuum_pump1_message}" />

            <!-- 第二步：转移操作序列 -->
            <Sequence name="TransferOperationSequence">
                
                <!-- 2.1 存储池转移气缸原位 -->
                <!-- 存储池转移气缸原位 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001010002"
                    intOutputAddresses="4,5"
                    boolOutputValues="0,1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{transfer_cylinder_origin_success}"
                    strOutputMessage="{transfer_cylinder_origin_message}" />

                <!-- 检查转移气缸原位传感器 -->
                <Sequence name="检查原位传感器">
                    <DigitalOutputRead
                        strDeviceId="R03001010002"
                        intOutputAddresses="4,5"
                        boolExpectedValues="0,1"
                        boolContinuousRead="true" 
                        doubleReadInterval="0.5"
                        doubleDuration="8.0"
                        intTimeoutMs="50000"
                        boolOutputSuccess="{transfer_cylinder_origin_read_success}"
                        boolOutputValues="{transfer_cylinder_origin_values}"
                        strOutputMessage="{transfer_cylinder_origin_read_message}" />

                    <!-- 检查传感器结果 -->
                    <Sequence name="检查传感器并执行后续操作">
                        <!-- 检查传感器 -->
                        <Fallback name="检查传感器值或打印错误">
                            <SensorValueCheck sensor_value="{transfer_cylinder_origin_read_success}" expected_value="true" />
                            <Sequence name="打印错误并确保失败">
                                <PubPrintMessage strPrintMessage="❌ 错误: 转移气缸未到原位" />
                                <PubProcessFeedback 
                                    strProcessStep="转移气缸原位检查"
                                    strStatus="FAILED"
                                    doubleProgress="99.0"
                                    strMessage="转移气缸未到原位检测失败"
                                    strOperation="检测转移气缸原位" />
                                <AlwaysFailure />
                            </Sequence>
                        </Fallback>
                        
                        <PubPrintMessage strPrintMessage="✅ 转移气缸已到原位" />
                    </Sequence>
                </Sequence>

                <PubProcessFeedback 
                    strProcessStep="物料转移执行"
                    strStatus="RUNNING"
                    doubleProgress="20.0"
                    strMessage="转移气缸原位完成"
                    strOperation="电机7移动到100位置" />

                <!-- 2.2 电机7位置模式100 -->
                <MotorPositionControl 
                    strDeviceId="R03001010002"
                    intMotorId="7"
                    doubleTargetPosition="0.0"
                    boolAbsolutePosition="true"
                    doubleMaxVelocity="100.0"
                    doubleAcceleration="100.0"
                    doubleDeceleration="100.0"
                    doubleDwellTime="0.5"
                    doubleTimeout="30.0"
                    boolOutputSuccess="{motor7_position100_success}"
                    strOutputMessage="{motor7_position100_message}" />

                <PubProcessFeedback 
                    strProcessStep="物料转移执行"
                    strStatus="RUNNING"
                    doubleProgress="35.0"
                    strMessage="电机7移动完成，开始转移序列"
                    strOperation="执行转移动作序列" />

                <!-- 打开真空泵 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001010002"
                    intOutputAddresses="12,13,14"
                    boolOutputValues="1,1,1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{vacuum_pump1_success}"
                    strOutputMessage="{vacuum_pump1_message}" />

                <Wait name="等待1秒" intMsec="1000"/>

                <!-- 2.3 转移动作序列 -->
                <Sequence name="TransferActionSequence">
                    <!-- 存储池转移气缸动作 -->
                    <Sequence name="转移气缸动作序列">
                        <DigitalOutputWrite 
                            strDeviceId="R03001010002"
                            intOutputAddresses="4,5"
                            boolOutputValues="1,0"
                            boolVerifyWrite="true"
                            intTimeoutMs="50000"
                            boolOutputSuccess="{transfer_cylinder_origin_success}"
                            strOutputMessage="{transfer_cylinder_origin_message}" />
                        
                        <!-- 等待1秒 -->
                        <Wait name="等待1秒" intMsec="1000"/>
                        
                        <!-- 检查Tray转移气缸下降位 -->
                        <Sequence name="检查下降位传感器">
                            <DigitalInputRead 
                                strDeviceId="R03001010002"
                                intInputAddresses="8,9"
                                boolExpectedValues="1,0"
                                intTimeoutMs="50000"
                                boolContinuousRead="true"
                                doubleReadInterval="0.5"
                                doubleDuration="8.0"
                                boolOutputSuccess="{tray_transfer_down_success}"
                                boolCurrentValues="{tray_transfer_down_values}"
                                strOutputMessage="{tray_transfer_down_message}" />
                            
                            <!-- 检查传感器结果 -->
                            <Sequence name="检查传感器并执行后续操作">
                                <!-- 检查传感器 -->
                                <Fallback name="检查传感器值或打印错误">
                                    <SensorValueCheck sensor_value="{tray_transfer_down_success}" expected_value="true" />
                                    <Sequence name="打印错误并确保失败">
                                        <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到下降位" />
                                        <PubProcessFeedback 
                                            strProcessStep="Tray转移气缸下降位检查"
                                            strStatus="FAILED"
                                            doubleProgress="99.0"
                                            strMessage="Tray转移气缸未到下降位检测失败"
                                            strOperation="检测Tray转移气缸下降位" />
                                        <AlwaysFailure />
                                    </Sequence>
                                </Fallback>
                                
                                <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到下降位" />
                            </Sequence>
                        </Sequence>
                    </Sequence>

                    <Wait name="等待1秒" intMsec="1000"/>
                    <!-- 转移吸真空 -->
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="6,7"
                        boolOutputValues="0,1"
                        boolVerifyWrite="true"
                        intTimeoutMs="50000"
                        boolOutputSuccess="{transfer_vacuum1_success}"
                        strOutputMessage="{transfer_vacuum1_message}" />

                    <Wait name="等待1秒" intMsec="1000"/>

                    <!-- 存储池转移气缸原位 -->
                    <Sequence name="转移气缸原位序列">
                        <DigitalOutputWrite 
                            strDeviceId="R03001010002"
                            intOutputAddresses="4,5"
                            boolOutputValues="0,1"
                            boolVerifyWrite="true"
                            intTimeoutMs="5000"
                            boolOutputSuccess="{transfer_cylinder_origin_success}"
                            strOutputMessage="{transfer_cylinder_origin_message}" />
                        
                        <!-- 等待1秒 -->
                        <Wait name="等待1秒" intMsec="1000"/>
                        
                        <!-- 检查Tray转移气缸上升位 -->
                        <Sequence name="检查上升位传感器">
                            <DigitalInputRead 
                                strDeviceId="R03001010002"
                                intInputAddresses="9,8"
                                boolExpectedValues="1,0"
                                intTimeoutMs="50000"
                                boolContinuousRead="true"
                                doubleReadInterval="0.5"
                                doubleDuration="8.0"
                                boolOutputSuccess="{tray_transfer_up_success}"
                                boolCurrentValues="{tray_transfer_up_values}"
                                strOutputMessage="{tray_transfer_up_message}" />
                            
                            <!-- 检查传感器结果 -->
                            <Sequence name="检查传感器并执行后续操作">
                                <!-- 检查传感器 -->
                                <Fallback name="检查传感器值或打印错误">
                                    <SensorValueCheck sensor_value="{tray_transfer_up_success}" expected_value="true" />
                                    <Sequence name="打印错误并确保失败">
                                        <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到上升位" />
                                        <PubProcessFeedback 
                                            strProcessStep="Tray转移气缸上升位检查"
                                            strStatus="FAILED"
                                            doubleProgress="99.0"
                                            strMessage="Tray转移气缸未到上升位检测失败"
                                            strOperation="检测Tray转移气缸上升位" />
                                        <AlwaysFailure />
                                    </Sequence>
                                </Fallback>
                                
                                <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到上升位" />
                            </Sequence>
                        </Sequence>
                    </Sequence>
                    
                    <!-- 电机7位置模式330 -->
                    <MotorPositionControl 
                        strDeviceId="R03001010002"
                        intMotorId="7"
                        doubleTargetPosition="320.0"
                        boolAbsolutePosition="true"
                        doubleMaxVelocity="100.0"
                        doubleAcceleration="100.0"
                        doubleDeceleration="100.0"
                        doubleDwellTime="0.5"
                        doubleTimeout="30.0"
                        boolOutputSuccess="{motor7_position330_success}"
                        strOutputMessage="{motor7_position330_message}" />
                </Sequence>

                <PubProcessFeedback 
                    strProcessStep="物料转移执行"
                    strStatus="RUNNING"
                    doubleProgress="60.0"
                    strMessage="第一次转移序列完成，开始第二次转移"
                    strOperation="执行第二次转移" />

                <!-- 2.4 第二次转移序列 -->
                <Sequence name="SecondTransferSequence">
                    <!-- 存储池转移气缸动作 -->
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="4,5"
                        boolOutputValues="1,0"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{transfer_cylinder_origin_success}"
                        strOutputMessage="{transfer_cylinder_origin_message}" />

                    <Wait name="等待1秒" intMsec="1000"/>
                    <!-- 转移破真空 -->
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="6,7"
                        boolOutputValues="1,0"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{transfer_vacuum2_success}"
                        strOutputMessage="{transfer_vacuum2_message}" />
                    
                    <!-- 关闭真空泵 -->
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="12,13"
                        boolOutputValues="0,0"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{storage_cylinder2_action_success}"
                        strOutputMessage="{storage_cylinder2_action_message}" />

                    <!-- 存储池转移气缸原位 -->
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="4,5"
                        boolOutputValues="0,1"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{transfer_cylinder_origin_success}"
                        strOutputMessage="{transfer_cylinder_origin_message}" />

                </Sequence>

                <PubProcessFeedback 
                    strProcessStep="物料转移执行"
                    strStatus="RUNNING"
                    doubleProgress="80.0"
                    strMessage="转移序列完成，开始最终位置调整"
                    strOperation="最终位置调整" />

            </Sequence>

                    <!-- 电机7位置模式400 -->
                    <MotorPositionControl 
                        strDeviceId="R03001010002"
                        intMotorId="7"
                        doubleTargetPosition="400.0"
                        boolAbsolutePosition="true"
                        doubleMaxVelocity="100.0"
                        doubleAcceleration="100.0"
                        doubleDeceleration="100.0"
                        doubleDwellTime="0.5"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor7_position330_success}"
                        strOutputMessage="{motor7_position330_message}" />

            <!-- 第三步：并行执行最终位置调整 -->
            <Parallel success_count="2" failure_count="1" name="FinalPositionAdjustment">

                <!-- 电机6回零 -->
                <MotorHoming
                        strDeviceId="R03001010002"
                        intMotorId="6"
                        intHomingMethod="17"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="100.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor6_homing_success}"
                        strOutputMessage="{motor6_homing_message}" />
                
                <!-- 条件检查序列 -->
                <Sequence name="ConditionalMotor5Movement">
                    <!-- 检查Tray盘到位感应2(DI5)和感应1(DI0) -->

                        <!-- 读取Tray盘到位感应2和感应1 -->
                        <DigitalInputRead 
                            strDeviceId="R03001010002"
                            intInputAddresses="4"
                            boolExpectedValues="1"
                            intTimeoutMs="30000"
                            boolContinuousRead="true"
                            doubleReadInterval="0.5"
                            doubleDuration="8.0"
                            boolOutputSuccess="{tray_sensors_read_success}"
                            boolCurrentValues="{tray_sensors_current_values}"
                            boolValueMatched="{tray_sensors_matched}"
                            strOutputMessage="{tray_sensors_read_message}" />
                    
                    <!-- 检查传感器结果 -->
                    <Sequence name="检查传感器并执行后续操作">
                        <!-- 检查传感器 -->
                        <Fallback name="检查传感器值或打印错误">
                            <SensorValueCheck sensor_value="{tray_sensors_read_success}" expected_value="false" />
                            <Sequence name="成功并移动电机5">
                                <MotorPositionControl
                                        strDeviceId="R03001010002"
                                        intMotorId="5"
                                        doubleTargetPosition="30.0"
                                        boolAbsolutePosition="false"
                                        doubleMaxVelocity="20.0"
                                        doubleAcceleration="50.0"
                                        doubleDeceleration="50.0"
                                        doubleDwellTime="0.5"
                                        doubleTimeout="10.0"
                                        boolOutputSuccess="{motor5_relative_success}"
                                        strOutputMessage="{motor5_relative_message}" />

                                <!-- 电机5回零 -->
                                <MotorHoming
                                        strDeviceId="R03001010002"
                                        intMotorId="5"
                                        intHomingMethod="17"
                                        floatSpeedSwitch="50.0"
                                        floatSpeedZero="50.0"
                                        intHomeOffset="0"
                                        intPositionWindow="10"
                                        intPositionWindowTime="100"
                                        doubleTimeout="50.0"
                                        boolOutputSuccess="{motor5_homing_success}"
                                        strOutputMessage="{motor5_homing_message}" />
                            </Sequence>
                        </Fallback>

                    </Sequence>
                </Sequence>
            </Parallel>

            <!-- 完成反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移执行"
                strStatus="COMPLETED"
                doubleProgress="100.0"
                strMessage="物料转移执行完成"
                strOperation="转移完成" />
            
            <PubPrintMessage strPrintMessage="✅ 物料转移执行完成，所有操作已完成" />
            
        </Sequence>
    </BehaviorTree>
</root> 