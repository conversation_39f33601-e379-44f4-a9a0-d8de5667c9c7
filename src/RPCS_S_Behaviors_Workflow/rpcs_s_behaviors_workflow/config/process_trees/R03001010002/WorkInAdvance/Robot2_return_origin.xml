<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 机械臂回零行为树 -->
    <BehaviorTree ID="Robot2ReturnOrigin">
        <Sequence name="机械臂回零流程">
            <!-- 步骤1: 机械臂移动到零位（工程ID=100） -->
            <Sequence name="机械臂移动到零位">
                <RobotArmControl 
                    strDeviceName="/R03001010002"
                    intProjectId="100"
                    intSpeedMultiplier="50"
                    floatPositionX="0.0"
                    floatPositionY="0.0"
                    floatPositionZ="0.0"
                    floatRotationRx="0.0"
                    floatRotationRy="0.0"
                    floatRotationRz="0.0"
                    intFunctionData0="0"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{move_to_zero_success}"
                    strOutputErrorMessage="{move_to_zero_error}"
                    doubleExecutionTime="{move_to_zero_time}"
                    strCurrentStatus="{move_to_zero_status}" />
                
                <!-- 检查移动到零位是否成功 -->
                <ForceSuccess>
                    <Precondition if="move_to_zero_success == false">
                        <Sequence>
                            <LogMessage message="机械臂移动到零位失败: {move_to_zero_error}" />
                            <Failure />
                        </Sequence>
                    </Precondition>
                </ForceSuccess>
                
                <!-- 短暂延时，等待机械臂稳定 -->
                <Wait name="等待机械臂稳定" intMsec="1000"/>
            </Sequence>
            
            <!-- 步骤2: 调用视觉识别，获取偏差值 -->
            <Sequence name="视觉识别计算偏差">
                <ImageDetection 
                    strCameraGroup="robot2"
                    strDetectType="ROBOT_ALIGN"
                    strNamespace=""
                    intTimeoutMs="10000"
                    boolOutputSuccess="{vision_success}"
                    strOutputErrorCode="{vision_error_code}"
                    strOutputMessage="{vision_message}"
                    strResultCameraIp="{camera_ip}"
                    doubleResultX="{offset_x}"
                    doubleResultY="{offset_y}"
                    doubleResultRz="{offset_rz}"
                    strDetectionSummary="{vision_summary}" />
                
                <!-- 检查视觉识别是否成功 -->
                <ForceSuccess>
                    <Precondition if="vision_success == false">
                        <Sequence>
                            <LogMessage message="视觉识别失败: {vision_message}, 错误码: {vision_error_code}" />
                            <Failure />
                        </Sequence>
                    </Precondition>
                </ForceSuccess>
                
                <!-- 输出视觉识别结果 -->
                <LogMessage message="视觉识别结果: {vision_summary}" />
            </Sequence>
            
            <!-- 步骤3: 根据偏差值进行微调（工程ID=200） -->
            <Sequence name="根据偏差值微调位置">
                <RobotArmControl 
                    strDeviceName="/R03001010002"
                    intProjectId="200"
                    intSpeedMultiplier="50"
                    floatPositionX="{offset_x}"
                    floatPositionY="{offset_y}"
                    floatPositionZ="0.0"
                    floatRotationRx="0.0"
                    floatRotationRy="0.0"
                    floatRotationRz="{offset_rz}"
                    intFunctionData0="0"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{adjust_success}"
                    strOutputErrorMessage="{adjust_error}"
                    doubleExecutionTime="{adjust_time}"
                    strCurrentStatus="{adjust_status}" />
                
                <!-- 检查微调是否成功 -->
                <ForceSuccess>
                    <Precondition if="adjust_success == false">
                        <Sequence>
                            <LogMessage message="位置微调失败: {adjust_error}" />
                            <Failure />
                        </Sequence>
                    </Precondition>
                </ForceSuccess>
                
                <!-- 输出微调结果 -->
                <LogMessage message="机械臂回零完成，微调偏差值 X:{offset_x} Y:{offset_y} Rz:{offset_rz}" />
            </Sequence>
        </Sequence>
    </BehaviorTree>
</root>
