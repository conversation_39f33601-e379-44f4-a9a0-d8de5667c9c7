<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 回安全位 -->
	<!-- 安全位-也是退出取料点位-即做物料规正的点位 -->
	<!-- AGV小车走点动作-从取料点位到物料规正的点位 -->
	<!-- 按照产品经理、软件组长要求，从取料点位到机器人作业点位中间设置一个停顿点，安全位，用于物料归正，预防小车走点时物料惯性滑动 -->
	<!-- 但是从机器人作业点到取料点位时，也就是去取料不会管这个安全点位  -->
	<!-- 其中2号小车安全点位是单独的，但是1号、3号小车安全点位和机器人作业点位完全重合 -->
	<!-- 不同小车差异说明-回安全位，3个小车回安全位工艺树配置完全相同，唯一的区别AgvGoPoint需要传递/RobotX -->
	<BehaviorTree ID="LightBoardToSafePoint">
		<Sequence name="LightBoardToSafePointSequence">
			<PubProcessFeedback
				strProcessStep="AGV移动到安全位"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="AGV移动到安全位"
				strOperation="开始移动到安全位任务" />

			<!-- 回安全位 -->
			<Fallback name="MoveToSafePointFallback">
				<Sequence name="MoveToSafePointSequence">
					<!-- 从取料点位 到 安全位-即到物料规正的点位 -->
					<PubProcessFeedback
						strProcessStep="AGV移动到安全位"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="AGV移动到安全位"
						strOperation="AGV移动到安全位" />

					<!-- 调用AGV走点服务移动到安全位-即从取料点位到物料规正的点位 -->
					<AgvGoPoint strGoPointName="ToSafePoint"
								strNamespace="/Robot2" />

					<PubProcessFeedback
						strProcessStep="AGV移动到安全位"
						strStatus="RUNNING"
						doubleProgress="100.0"
						strMessage="AGV已经移动到安全位"
						strOperation="AGV移动到安全位" />
				</Sequence>

				<!-- AGV移动失败处理 -->
				<Sequence name="MoveToSafePointFailureHandler">
					<PubProcessFeedback
						strProcessStep="AGV移动到安全位"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="AGV移动失败"
						strOperation="处理AGV移动失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- AGV移动到安全位完成 -->
			<PubProcessFeedback
				strProcessStep="AGV移动到安全位"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="AGV移动到安全位完成"
				strOperation="AGV移动到安全位任务结束" />
		</Sequence>
	</BehaviorTree>
</root>
