<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="Robot2SelfCheck">
        <ReactiveSequence name="SelfCheckSequence">
            <PubPrintMessage strMessage="Robot2 开始自检..." />
            <CleanStatus/>
            <Fallback name="SelfCheckFallback">
                <Sequence name="SelfCheckSteps">
                    <PubPrintMessage strMessage="检查PCBA处理设备..." />
                    <Delay delay_msec="1000"/>
                    <PubPrintMessage strMessage="检查贴片设备..." />
                    <Delay delay_msec="1000"/>
                    <PubPrintMessage strMessage="检查FPC连接器..." />
                    <Delay delay_msec="1000"/>
                    <PubPrintMessage strMessage="检查夹具系统..." />
                    <Delay delay_msec="1000"/>
                    <PubPrintMessage strMessage="Robot2 自检完成!" />
                </Sequence>
                <PubPrintMessage strMessage="Robot2 自检失败!" />
            </Fallback>
        </ReactiveSequence>
    </BehaviorTree>
</root> 