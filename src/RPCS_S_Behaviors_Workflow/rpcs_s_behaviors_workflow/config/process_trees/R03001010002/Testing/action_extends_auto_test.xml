<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- Action扩展信息自动识别验证行为树 -->
    <BehaviorTree ID="ActionExtendsAutoTest">
        <Sequence name="ActionExtendsAutoTestSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息自动识别测试"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始Action扩展信息自动识别测试流程"
                strOperation="初始化系统" />
            
            <!-- 获取扩展信息总数 -->
            <GetActionParameters 
                intExtendCount="{extend_count}"
                boolOutputSuccess="{get_extend_count_success}"
                strOutputMessage="{get_extend_count_message}" />
            
            <PubPrintMessage strPrintMessage="=== Action扩展信息自动识别测试 ===" />
            <PubPrintMessage strPrintMessage="检测到扩展信息数量: {extend_count}" />
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息自动识别测试"
                strStatus="RUNNING"
                doubleProgress="10.0"
                strMessage="开始遍历所有扩展信息"
                strOperation="遍历扩展信息" />
            
            <!-- 动态遍历所有扩展信息 -->
            <Fallback name="遍历扩展信息">
                <Sequence name="有扩展信息时遍历">
                    <!-- 检查是否有扩展信息 - 使用Fallback来实现大于0的检查 -->
                    <Fallback name="检查扩展信息数量">
                        <Sequence name="有扩展信息">
                            <!-- 尝试获取第0个扩展信息来验证是否有扩展信息 -->
                            <GetActionParameters
                                intExtendIndex="0"
                                strExtendValue="{temp_extend_0_value}"
                                boolOutputSuccess="{temp_has_extends}" />
                            <SensorValueCheck sensor_value="{temp_has_extends}" expected_value="true" />
                        </Sequence>
                        <!-- 如果没有扩展信息，直接失败，让外层Fallback处理 -->
                        <ReturnFailure />
                    </Fallback>

                    <PubPrintMessage strPrintMessage="--- 开始遍历扩展信息 ---" />
                    
                    <!-- 遍历第0个扩展信息 -->
                    <Fallback name="处理扩展信息0">
                        <Sequence name="获取扩展信息0">
                            <GetActionParameters
                                intExtendIndex="0"
                                strExtendValue="{extend_0_value}"
                                strExtendKeyOut="{extend_0_key}"
                                boolOutputSuccess="{get_extend_0_success}"
                                strOutputMessage="{get_extend_0_message}" />
                            
                            <SensorValueCheck sensor_value="{get_extend_0_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="扩展信息[0]: {extend_0_key} = {extend_0_value}" />
                            
                            <!-- 验证直接访问方式 - 尝试通过键名获取 -->
                            <Fallback name="验证直接访问0">
                                <Sequence name="直接访问成功0">
                                    <GetActionParameters
                                        strExtendKey="{extend_0_key}"
                                        strExtendValue="{direct_access_0_value}"
                                        boolOutputSuccess="{direct_access_0_found}" />
                                    <SensorValueCheck sensor_value="{direct_access_0_found}" expected_value="true" />
                                    <PubPrintMessage strPrintMessage="  → 直接访问验证: extend_{extend_0_key} = {direct_access_0_value}" />
                                </Sequence>
                                <PubPrintMessage strPrintMessage="  → 直接访问验证失败: extend_{extend_0_key}" />
                            </Fallback>
                        </Sequence>
                        <PubPrintMessage strPrintMessage="扩展信息[0]: 不存在或获取失败" />
                    </Fallback>
                    
                    <!-- 遍历第1个扩展信息 -->
                    <Fallback name="处理扩展信息1">
                        <Sequence name="获取扩展信息1">
                            <GetActionParameters
                                intExtendIndex="1"
                                strExtendValue="{extend_1_value}"
                                strExtendKeyOut="{extend_1_key}"
                                boolOutputSuccess="{get_extend_1_success}"
                                strOutputMessage="{get_extend_1_message}" />
                            
                            <SensorValueCheck sensor_value="{get_extend_1_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="扩展信息[1]: {extend_1_key} = {extend_1_value}" />
                            
                            <!-- 验证直接访问方式 - 尝试通过键名获取 -->
                            <Fallback name="验证直接访问1">
                                <Sequence name="直接访问成功1">
                                    <GetActionParameters
                                        strExtendKey="{extend_1_key}"
                                        strExtendValue="{direct_access_1_value}"
                                        boolOutputSuccess="{direct_access_1_found}" />
                                    <SensorValueCheck sensor_value="{direct_access_1_found}" expected_value="true" />
                                    <PubPrintMessage strPrintMessage="  → 直接访问验证: extend_{extend_1_key} = {direct_access_1_value}" />
                                </Sequence>
                                <PubPrintMessage strPrintMessage="  → 直接访问验证失败: extend_{extend_1_key}" />
                            </Fallback>
                        </Sequence>
                        <PubPrintMessage strPrintMessage="扩展信息[1]: 不存在" />
                    </Fallback>
                    
                    <!-- 遍历第2个扩展信息 -->
                    <Fallback name="处理扩展信息2">
                        <Sequence name="获取扩展信息2">
                            <GetActionParameters
                                intExtendIndex="2"
                                strExtendValue="{extend_2_value}"
                                strExtendKeyOut="{extend_2_key}"
                                boolOutputSuccess="{get_extend_2_success}"
                                strOutputMessage="{get_extend_2_message}" />
                            
                            <SensorValueCheck sensor_value="{get_extend_2_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="扩展信息[2]: {extend_2_key} = {extend_2_value}" />
                            
                            <!-- 验证直接访问方式 - 尝试通过键名获取 -->
                            <Fallback name="验证直接访问2">
                                <Sequence name="直接访问成功2">
                                    <GetActionParameters
                                        strExtendKey="{extend_2_key}"
                                        strExtendValue="{direct_access_2_value}"
                                        boolOutputSuccess="{direct_access_2_found}" />
                                    <SensorValueCheck sensor_value="{direct_access_2_found}" expected_value="true" />
                                    <PubPrintMessage strPrintMessage="  → 直接访问验证: extend_{extend_2_key} = {direct_access_2_value}" />
                                </Sequence>
                                <PubPrintMessage strPrintMessage="  → 直接访问验证失败: extend_{extend_2_key}" />
                            </Fallback>
                        </Sequence>
                        <PubPrintMessage strPrintMessage="扩展信息[2]: 不存在" />
                    </Fallback>
                    
                    <!-- 遍历第3个扩展信息 -->
                    <Fallback name="处理扩展信息3">
                        <Sequence name="获取扩展信息3">
                            <GetActionParameters
                                intExtendIndex="3"
                                strExtendValue="{extend_3_value}"
                                strExtendKeyOut="{extend_3_key}"
                                boolOutputSuccess="{get_extend_3_success}"
                                strOutputMessage="{get_extend_3_message}" />
                            
                            <SensorValueCheck sensor_value="{get_extend_3_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="扩展信息[3]: {extend_3_key} = {extend_3_value}" />
                            
                            <!-- 验证直接访问方式 - 尝试通过键名获取 -->
                            <Fallback name="验证直接访问3">
                                <Sequence name="直接访问成功3">
                                    <GetActionParameters
                                        strExtendKey="{extend_3_key}"
                                        strExtendValue="{direct_access_3_value}"
                                        boolOutputSuccess="{direct_access_3_found}" />
                                    <SensorValueCheck sensor_value="{direct_access_3_found}" expected_value="true" />
                                    <PubPrintMessage strPrintMessage="  → 直接访问验证: extend_{extend_3_key} = {direct_access_3_value}" />
                                </Sequence>
                                <PubPrintMessage strPrintMessage="  → 直接访问验证失败: extend_{extend_3_key}" />
                            </Fallback>
                        </Sequence>
                        <PubPrintMessage strPrintMessage="扩展信息[3]: 不存在" />
                    </Fallback>
                    
                    <!-- 遍历第4个扩展信息 -->
                    <Fallback name="处理扩展信息4">
                        <Sequence name="获取扩展信息4">
                            <GetActionParameters
                                intExtendIndex="4"
                                strExtendValue="{extend_4_value}"
                                strExtendKeyOut="{extend_4_key}"
                                boolOutputSuccess="{get_extend_4_success}"
                                strOutputMessage="{get_extend_4_message}" />
                            
                            <SensorValueCheck sensor_value="{get_extend_4_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="扩展信息[4]: {extend_4_key} = {extend_4_value}" />
                            
                            <!-- 验证直接访问方式 - 尝试通过键名获取 -->
                            <Fallback name="验证直接访问4">
                                <Sequence name="直接访问成功4">
                                    <GetActionParameters
                                        strExtendKey="{extend_4_key}"
                                        strExtendValue="{direct_access_4_value}"
                                        boolOutputSuccess="{direct_access_4_found}" />
                                    <SensorValueCheck sensor_value="{direct_access_4_found}" expected_value="true" />
                                    <PubPrintMessage strPrintMessage="  → 直接访问验证: extend_{extend_4_key} = {direct_access_4_value}" />
                                </Sequence>
                                <PubPrintMessage strPrintMessage="  → 直接访问验证失败: extend_{extend_4_key}" />
                            </Fallback>
                        </Sequence>
                        <PubPrintMessage strPrintMessage="扩展信息[4]: 不存在" />
                    </Fallback>
                    
                </Sequence>
                <PubPrintMessage strPrintMessage="没有检测到任何扩展信息" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息自动识别测试"
                strStatus="RUNNING"
                doubleProgress="50.0"
                strMessage="扩展信息遍历完成，开始验证读取方式"
                strOperation="验证读取方式" />
            
            <!-- 验证多种读取方式 -->
            <PubPrintMessage strPrintMessage="=== 验证多种扩展信息读取方式 ===" />
            
            <!-- 方式1: 通过索引访问黑板变量 -->
            <PubPrintMessage strPrintMessage="--- 方式1: 索引访问黑板变量 ---" />
            <PubPrintMessage strPrintMessage="action_extend_0_key: {action_extend_0_key}" />
            <PubPrintMessage strPrintMessage="action_extend_0_value: {action_extend_0_value}" />
            <PubPrintMessage strPrintMessage="action_extend_1_key: {action_extend_1_key}" />
            <PubPrintMessage strPrintMessage="action_extend_1_value: {action_extend_1_value}" />
            
            <!-- 方式2: 直接访问黑板变量（如果知道key名称） -->
            <PubPrintMessage strPrintMessage="--- 方式2: 直接访问黑板变量 ---" />
            <PubPrintMessage strPrintMessage="extend_product_type: {extend_product_type}" />
            <PubPrintMessage strPrintMessage="extend_quality_level: {extend_quality_level}" />
            <PubPrintMessage strPrintMessage="extend_station: {extend_station}" />
            <PubPrintMessage strPrintMessage="extend_batch_id: {extend_batch_id}" />
            <PubPrintMessage strPrintMessage="extend_operator: {extend_operator}" />
            
            <!-- 方式3: 通过GetActionParameters节点按键名获取 -->
            <PubPrintMessage strPrintMessage="--- 方式3: GetActionParameters按键名获取 ---" />
            
            <!-- 尝试获取常见的扩展信息键 -->
            <Fallback name="尝试获取product_type">
                <Sequence name="获取product_type">
                    <GetActionParameters
                        strExtendKey="product_type"
                        strExtendValue="{retrieved_product_type}"
                        boolOutputSuccess="{get_product_type_success}" />
                    <SensorValueCheck sensor_value="{get_product_type_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="通过键名获取 product_type: {retrieved_product_type}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="未找到 product_type 扩展信息" />
            </Fallback>
            
            <Fallback name="尝试获取quality_level">
                <Sequence name="获取quality_level">
                    <GetActionParameters
                        strExtendKey="quality_level"
                        strExtendValue="{retrieved_quality_level}"
                        boolOutputSuccess="{get_quality_level_success}" />
                    <SensorValueCheck sensor_value="{get_quality_level_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="通过键名获取 quality_level: {retrieved_quality_level}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="未找到 quality_level 扩展信息" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息自动识别测试"
                strStatus="RUNNING"
                doubleProgress="80.0"
                strMessage="读取方式验证完成，生成测试报告"
                strOperation="生成测试报告" />
            
            <!-- 生成测试报告 -->
            <PubPrintMessage strPrintMessage="=== 扩展信息自动识别测试报告 ===" />
            <PubPrintMessage strPrintMessage="✓ 扩展信息总数: {extend_count}" />
            <PubPrintMessage strPrintMessage="✓ 索引访问方式: 已验证" />
            <PubPrintMessage strPrintMessage="✓ 直接访问方式: 已验证" />
            <PubPrintMessage strPrintMessage="✓ GetActionParameters节点: 已验证" />
            <PubPrintMessage strPrintMessage="✓ 自动识别功能: 正常工作" />
            
            <!-- 最终反馈 -->
            <PubProcessFeedback
                strProcessStep="Action扩展信息自动识别测试"
                strStatus="SUCCESS"
                doubleProgress="100.0"
                strMessage="Action扩展信息自动识别测试完成"
                strOperation="测试完成" />
            
        </Sequence>
    </BehaviorTree>
</root>
