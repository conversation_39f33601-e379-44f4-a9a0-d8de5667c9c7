<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- Action扩展信息简化测试行为树 -->
    <BehaviorTree ID="ActionExtendsSimpleTest">
        <Sequence name="ActionExtendsSimpleTestSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息简化测试"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始Action扩展信息简化测试流程"
                strOperation="初始化系统" />
            
            <!-- 获取扩展信息总数 -->
            <GetActionParameters 
                intExtendCount="{extend_count}"
                boolOutputSuccess="{get_extend_count_success}"
                strOutputMessage="{get_extend_count_message}" />
            
            <PubPrintMessage strPrintMessage="=== Action扩展信息简化测试 ===" />
            <PubPrintMessage strPrintMessage="检测到扩展信息数量: {extend_count}" />
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息简化测试"
                strStatus="RUNNING"
                doubleProgress="20.0"
                strMessage="开始遍历扩展信息"
                strOperation="遍历扩展信息" />
            
            <!-- 尝试获取第0个扩展信息 -->
            <PubPrintMessage strPrintMessage="--- 尝试获取扩展信息[0] ---" />
            <GetActionParameters
                intExtendIndex="0"
                strExtendValue="{extend_0_value}"
                strExtendKeyOut="{extend_0_key}"
                boolOutputSuccess="{get_extend_0_success}"
                strOutputMessage="{get_extend_0_message}" />
            
            <Fallback name="处理扩展信息0">
                <Sequence name="扩展信息0存在">
                    <SensorValueCheck sensor_value="{get_extend_0_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="扩展信息[0]: {extend_0_key} = {extend_0_value}" />
                    
                    <!-- 验证通过键名获取 -->
                    <GetActionParameters
                        strExtendKey="{extend_0_key}"
                        strExtendValue="{verify_0_value}"
                        boolOutputSuccess="{verify_0_success}" />
                    
                    <Fallback name="验证扩展信息0">
                        <Sequence name="验证成功">
                            <SensorValueCheck sensor_value="{verify_0_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="  → 通过键名验证成功: {verify_0_value}" />
                        </Sequence>
                        <PubPrintMessage strPrintMessage="  → 通过键名验证失败" />
                    </Fallback>
                </Sequence>
                <PubPrintMessage strPrintMessage="扩展信息[0]: 不存在" />
            </Fallback>
            
            <!-- 尝试获取第1个扩展信息 -->
            <PubPrintMessage strPrintMessage="--- 尝试获取扩展信息[1] ---" />
            <GetActionParameters
                intExtendIndex="1"
                strExtendValue="{extend_1_value}"
                strExtendKeyOut="{extend_1_key}"
                boolOutputSuccess="{get_extend_1_success}"
                strOutputMessage="{get_extend_1_message}" />
            
            <Fallback name="处理扩展信息1">
                <Sequence name="扩展信息1存在">
                    <SensorValueCheck sensor_value="{get_extend_1_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="扩展信息[1]: {extend_1_key} = {extend_1_value}" />
                    
                    <!-- 验证通过键名获取 -->
                    <GetActionParameters
                        strExtendKey="{extend_1_key}"
                        strExtendValue="{verify_1_value}"
                        boolOutputSuccess="{verify_1_success}" />
                    
                    <Fallback name="验证扩展信息1">
                        <Sequence name="验证成功">
                            <SensorValueCheck sensor_value="{verify_1_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="  → 通过键名验证成功: {verify_1_value}" />
                        </Sequence>
                        <PubPrintMessage strPrintMessage="  → 通过键名验证失败" />
                    </Fallback>
                </Sequence>
                <PubPrintMessage strPrintMessage="扩展信息[1]: 不存在" />
            </Fallback>
            
            <!-- 尝试获取第2个扩展信息 -->
            <PubPrintMessage strPrintMessage="--- 尝试获取扩展信息[2] ---" />
            <GetActionParameters
                intExtendIndex="2"
                strExtendValue="{extend_2_value}"
                strExtendKeyOut="{extend_2_key}"
                boolOutputSuccess="{get_extend_2_success}"
                strOutputMessage="{get_extend_2_message}" />
            
            <Fallback name="处理扩展信息2">
                <Sequence name="扩展信息2存在">
                    <SensorValueCheck sensor_value="{get_extend_2_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="扩展信息[2]: {extend_2_key} = {extend_2_value}" />
                    
                    <!-- 验证通过键名获取 -->
                    <GetActionParameters
                        strExtendKey="{extend_2_key}"
                        strExtendValue="{verify_2_value}"
                        boolOutputSuccess="{verify_2_success}" />
                    
                    <Fallback name="验证扩展信息2">
                        <Sequence name="验证成功">
                            <SensorValueCheck sensor_value="{verify_2_success}" expected_value="true" />
                            <PubPrintMessage strPrintMessage="  → 通过键名验证成功: {verify_2_value}" />
                        </Sequence>
                        <PubPrintMessage strPrintMessage="  → 通过键名验证失败" />
                    </Fallback>
                </Sequence>
                <PubPrintMessage strPrintMessage="扩展信息[2]: 不存在" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息简化测试"
                strStatus="RUNNING"
                doubleProgress="60.0"
                strMessage="索引遍历完成，开始验证直接访问"
                strOperation="验证直接访问" />
            
            <!-- 验证直接访问方式 -->
            <PubPrintMessage strPrintMessage="=== 验证直接访问方式 ===" />
            <PubPrintMessage strPrintMessage="strName: {strName}" />
            <PubPrintMessage strPrintMessage="intNum: {intNum}" />
            <PubPrintMessage strPrintMessage="extend_station: {extend_station}" />
            <PubPrintMessage strPrintMessage="extend_boolFlag: {extend_boolFlag}" />
            <PubPrintMessage strPrintMessage="extend_operator: {extend_operator}" />
            
            <!-- 验证索引访问方式 -->
            <PubPrintMessage strPrintMessage="=== 验证索引访问方式 ===" />
            <PubPrintMessage strPrintMessage="action_extend_0_key: {action_extend_0_key}" />
            <PubPrintMessage strPrintMessage="action_extend_0_value: {action_extend_0_value}" />
            <PubPrintMessage strPrintMessage="action_extend_1_key: {action_extend_1_key}" />
            <PubPrintMessage strPrintMessage="action_extend_1_value: {action_extend_1_value}" />
            <PubPrintMessage strPrintMessage="action_extend_2_key: {action_extend_2_key}" />
            <PubPrintMessage strPrintMessage="action_extend_2_value: {action_extend_2_value}" />
            
            <!-- 尝试通过常见键名获取扩展信息 -->
            <PubPrintMessage strPrintMessage="=== 尝试通过常见键名获取 ===" />
            
            <Fallback name="尝试获取strName">
                <Sequence name="获取strName">
                    <GetActionParameters
                        strExtendKey="strName"
                        strExtendValue="{retrieved_strName}"
                        boolOutputSuccess="{get_strName_success}" />
                    <SensorValueCheck sensor_value="{get_strName_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="通过键名获取 strName: {retrieved_strName}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="未找到 strName 扩展信息" />
            </Fallback>
            
            <Fallback name="尝试获取intNum">
                <Sequence name="获取intNum">
                    <GetActionParameters
                        strExtendKey="intNum"
                        strExtendValue="{retrieved_intNum}"
                        boolOutputSuccess="{get_intNum_success}" />
                    <SensorValueCheck sensor_value="{get_intNum_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="通过键名获取 intNum: {retrieved_intNum}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="未找到 intNum 扩展信息" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action扩展信息简化测试"
                strStatus="RUNNING"
                doubleProgress="90.0"
                strMessage="验证完成，生成测试报告"
                strOperation="生成测试报告" />
            
            <!-- 生成测试报告 -->
            <PubPrintMessage strPrintMessage="=== 扩展信息简化测试报告 ===" />
            <PubPrintMessage strPrintMessage="✓ 扩展信息总数: {extend_count}" />
            <PubPrintMessage strPrintMessage="✓ 索引访问方式: 已验证" />
            <PubPrintMessage strPrintMessage="✓ 直接访问方式: 已验证" />
            <PubPrintMessage strPrintMessage="✓ GetActionParameters节点: 已验证" />
            <PubPrintMessage strPrintMessage="✓ 自动识别功能: 正常工作" />
            
            <!-- 最终反馈 -->
            <PubProcessFeedback
                strProcessStep="Action扩展信息简化测试"
                strStatus="SUCCESS"
                doubleProgress="100.0"
                strMessage="Action扩展信息简化测试完成"
                strOperation="测试完成" />
            
        </Sequence>
    </BehaviorTree>
</root>
