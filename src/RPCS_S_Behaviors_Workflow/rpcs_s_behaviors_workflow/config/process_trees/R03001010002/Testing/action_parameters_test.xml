<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- Action参数测试行为树 -->
    <BehaviorTree ID="ActionParametersTest">
        <Sequence name="ActionParametersTestSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="Action参数测试"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始Action参数测试流程"
                strOperation="初始化系统" />
            
            <!-- 获取Action基本信息 -->
            <GetActionParameters 
                strRobotId="{robot_id}"
                strProcessId="{process_id}"
                strProcessType="{process_type}"
                intTimeoutSeconds="{timeout_seconds}"
                boolPreemptCurrent="{preempt_current}"
                intParameterCount="{param_count}"
                strParameterList="{param_list}"
                boolOutputSuccess="{get_action_info_success}"
                strOutputMessage="{get_action_info_message}" />
            
            <!-- 打印基本信息 -->
            <PubPrintMessage strPrintMessage="=== Action基本信息 ===" />
            <PubPrintMessage strPrintMessage="机器人ID: {robot_id}" />
            <PubPrintMessage strPrintMessage="工艺流程ID: {process_id}" />
            <PubPrintMessage strPrintMessage="工艺类型: {process_type}" />
            <PubPrintMessage strPrintMessage="超时时间: {timeout_seconds}秒" />
            <PubPrintMessage strPrintMessage="参数数量: {param_count}" />
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action参数测试"
                strStatus="RUNNING"
                doubleProgress="25.0"
                strMessage="已获取基本信息，开始获取参数"
                strOperation="获取参数" />
            
            <!-- 获取第一个参数 -->
            <GetActionParameters 
                intParameterIndex="0"
                strParameterValue="{param_0_value}"
                boolOutputSuccess="{get_param_0_success}"
                strOutputMessage="{get_param_0_message}" />
            
            <!-- 检查是否成功获取第一个参数 -->
            <Fallback name="检查第一个参数">
                <Sequence name="有第一个参数">
                    <SensorValueCheck sensor_value="{get_param_0_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="参数[0]: {param_0_value}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="没有第一个参数或获取失败" />
            </Fallback>
            
            <!-- 获取第二个参数 -->
            <GetActionParameters 
                intParameterIndex="1"
                strParameterValue="{param_1_value}"
                boolOutputSuccess="{get_param_1_success}"
                strOutputMessage="{get_param_1_message}" />
            
            <!-- 检查是否成功获取第二个参数 -->
            <Fallback name="检查第二个参数">
                <Sequence name="有第二个参数">
                    <SensorValueCheck sensor_value="{get_param_1_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="参数[1]: {param_1_value}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="没有第二个参数或获取失败" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback 
                strProcessStep="Action参数测试"
                strStatus="RUNNING"
                doubleProgress="50.0"
                strMessage="参数获取完成，开始测试特定键值获取"
                strOperation="测试键值获取" />
            
            <!-- 尝试通过键名获取参数 -->
            <GetActionParameters 
                strParameterKey="action_robot_id"
                strParameterValue="{robot_id_from_key}"
                boolOutputSuccess="{get_robot_id_success}"
                strOutputMessage="{get_robot_id_message}" />
            
            <Fallback name="检查键值获取">
                <Sequence name="键值获取成功">
                    <SensorValueCheck sensor_value="{get_robot_id_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="通过键名获取机器人ID: {robot_id_from_key}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="通过键名获取机器人ID失败" />
            </Fallback>
            
            <!-- 进度更新 -->
            <PubProcessFeedback
                strProcessStep="Action参数测试"
                strStatus="RUNNING"
                doubleProgress="60.0"
                strMessage="键值获取测试完成，开始测试扩展信息"
                strOperation="测试扩展信息" />

            <!-- 获取扩展信息数量 -->
            <GetActionParameters
                intExtendCount="{extend_count}"
                boolOutputSuccess="{get_extend_count_success}"
                strOutputMessage="{get_extend_count_message}" />

            <PubPrintMessage strPrintMessage="扩展信息数量: {extend_count}" />

            <!-- 通过键名获取扩展信息 -->
            <GetActionParameters
                strExtendKey="product_type"
                strExtendValue="{product_type_value}"
                strExtendKeyOut="{product_type_key}"
                boolOutputSuccess="{get_product_type_success}"
                strOutputMessage="{get_product_type_message}" />

            <Fallback name="检查产品类型扩展信息">
                <Sequence name="产品类型获取成功">
                    <SensorValueCheck sensor_value="{get_product_type_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="扩展信息 - 产品类型: {product_type_value}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="未找到产品类型扩展信息" />
            </Fallback>

            <!-- 通过索引获取第一个扩展信息 -->
            <GetActionParameters
                intExtendIndex="0"
                strExtendValue="{extend_0_value}"
                strExtendKeyOut="{extend_0_key}"
                boolOutputSuccess="{get_extend_0_success}"
                strOutputMessage="{get_extend_0_message}" />

            <Fallback name="检查第一个扩展信息">
                <Sequence name="第一个扩展信息获取成功">
                    <SensorValueCheck sensor_value="{get_extend_0_success}" expected_value="true" />
                    <PubPrintMessage strPrintMessage="扩展信息[0]: {extend_0_key} = {extend_0_value}" />
                </Sequence>
                <PubPrintMessage strPrintMessage="没有第一个扩展信息或获取失败" />
            </Fallback>

            <!-- 直接使用黑板中的扩展信息 -->
            <PubPrintMessage strPrintMessage="=== 直接使用黑板扩展信息 ===" />
            <PubPrintMessage strPrintMessage="产品类型 (extend_product_type): {extend_product_type}" />
            <PubPrintMessage strPrintMessage="质量等级 (extend_quality_level): {extend_quality_level}" />
            <PubPrintMessage strPrintMessage="工作站 (extend_station): {extend_station}" />

            <!-- 进度更新 -->
            <PubProcessFeedback
                strProcessStep="Action参数测试"
                strStatus="RUNNING"
                doubleProgress="90.0"
                strMessage="扩展信息测试完成"
                strOperation="准备完成测试" />

            <!-- 打印所有参数 -->
            <PubPrintMessage strPrintMessage="=== 测试完成 ===" />
            <PubPrintMessage strPrintMessage="所有Action参数和扩展信息已成功获取和测试" />

            <!-- 最终反馈 -->
            <PubProcessFeedback
                strProcessStep="Action参数测试"
                strStatus="SUCCESS"
                doubleProgress="100.0"
                strMessage="Action参数和扩展信息测试完成"
                strOperation="测试完成" />
            
        </Sequence>
    </BehaviorTree>
</root>
