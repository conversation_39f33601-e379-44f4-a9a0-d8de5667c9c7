<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 数字IO测试行为树 -->
    <BehaviorTree ID="DigitalIOTest">
        <Sequence name="DigitalIOTestSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="数字IO测试"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始数字IO测试流程"
                strOperation="初始化" />

            <!-- 第一步：写入数字输出 -->
            <Sequence name="WriteDigitalOutputSequence">
                <PubPrintMessage strPrintMessage="开始写入数字输出 [8,9,6,7,10] = [false,false,true,false,false]" />
                
                <PubProcessFeedback 
                    strProcessStep="数字IO测试"
                    strStatus="RUNNING"
                    doubleProgress="20.0"
                    strMessage="写入数字输出"
                    strOperation="写入输出" />
                
                <!-- 写入数字输出 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001010002"
                    intOutputAddresses="8,9,6,7,10"
                    boolOutputValues="0,0,1,0,0"
                    boolVerifyWrite="true"
                    intTimeoutMs="5000"
                    boolOutputSuccess="{write_output_success}"
                    boolFinalValues="{final_output_values}"
                    strOutputMessage="{write_output_message}" />

                <!-- 检查写入结果 -->
                <Fallback name="检查写入是否成功">
                    <SensorValueCheck sensor_value="{write_output_success}" expected_value="true" />
                    <Sequence name="写入失败处理">
                        <PubPrintMessage strPrintMessage="❌ 错误: 写入数字输出失败: {write_output_message}" />
                        <PubProcessFeedback 
                            strProcessStep="数字IO测试"
                            strStatus="FAILED"
                            doubleProgress="30.0"
                            strMessage="写入数字输出失败"
                            strOperation="写入输出" />
                        <AlwaysFailure />
                    </Sequence>
                </Fallback>
                
                <PubPrintMessage strPrintMessage="✅ 写入数字输出成功" />
            </Sequence>

            <!-- 第二步：读取数字输出并验证 -->
            <Sequence name="ReadDigitalOutputSequence">
                <PubPrintMessage strPrintMessage="开始读取数字输出验证 [8,9,6,7,10]，期望值 [false,false,true,false,false]" />
                
                <PubProcessFeedback 
                    strProcessStep="数字IO测试"
                    strStatus="RUNNING"
                    doubleProgress="50.0"
                    strMessage="验证数字输出"
                    strOperation="读取并验证输出" />

                <!-- 读取数字输出 -->
                <DigitalOutputRead
                    strDeviceId="R03001010002"
                    intOutputAddresses="8,9,6,7,10"
                    boolExpectedValues="0,0,1,0,0"
                    boolContinuousRead="true" 
                    doubleReadInterval="0.5"
                    doubleDuration="3.0"
                    intTimeoutMs="5000"
                    boolOutputSuccess="{read_output_success}"
                    boolOutputValues="{read_output_values}"
                    boolValueMatched="{output_value_matched}"
                    strOutputMessage="{read_output_message}" />

                <!-- 检查读取结果 -->
                <Fallback name="检查输出读取结果">
                    <SensorValueCheck sensor_value="{read_output_success}" expected_value="true" />
                    <Sequence name="输出读取失败处理">
                        <PubPrintMessage strPrintMessage="❌ 错误: 读取数字输出失败: {read_output_message}" />
                        <PubProcessFeedback 
                            strProcessStep="数字IO测试"
                            strStatus="FAILED"
                            doubleProgress="60.0"
                            strMessage="读取数字输出失败"
                            strOperation="读取输出" />
                        <AlwaysFailure />
                    </Sequence>
                </Fallback>

                <PubPrintMessage strPrintMessage="✅ 读取数字输出成功，输出值验证结果:" />
                
                <!-- 打印每个输出的验证结果 -->
                <PubPrintMessage strPrintMessage="输出8: 期望值[false], 读取值[{read_output_values[0]==1?'true':'false'}], 匹配[{output_value_matched[0]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输出9: 期望值[false], 读取值[{read_output_values[1]==1?'true':'false'}], 匹配[{output_value_matched[1]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输出6: 期望值[true], 读取值[{read_output_values[2]==1?'true':'false'}], 匹配[{output_value_matched[2]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输出7: 期望值[false], 读取值[{read_output_values[3]==1?'true':'false'}], 匹配[{output_value_matched[3]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输出10: 期望值[false], 读取值[{read_output_values[4]==1?'true':'false'}], 匹配[{output_value_matched[4]==1?'成功':'失败'}]" />
            </Sequence>

            <!-- 第三步：读取数字输入 -->
            <Sequence name="ReadDigitalInputSequence">
                <PubPrintMessage strPrintMessage="开始读取数字输入 [1,2,3,4,5,6]，期望值全为0" />
                
                <PubProcessFeedback 
                    strProcessStep="数字IO测试"
                    strStatus="RUNNING"
                    doubleProgress="80.0"
                    strMessage="读取数字输入"
                    strOperation="读取输入" />

                <!-- 读取数字输入 -->
                <DigitalInputRead 
                    strDeviceId="R03001010002"
                    intInputAddresses="1,2,3,4,5,6"
                    boolExpectedValues="0,0,1,0,1,0"
                    intTimeoutMs="5000"
                    boolContinuousRead="true"
                    doubleReadInterval="0.5"
                    doubleDuration="3.0"
                    boolOutputSuccess="{read_input_success}"
                    boolCurrentValues="{read_input_values}"
                    boolValueMatched="{input_value_matched}"
                    strOutputMessage="{read_input_message}" />

                <!-- 检查读取结果 -->
                <Fallback name="检查输入读取结果">
                    <SensorValueCheck sensor_value="{read_input_success}" expected_value="true" />
                    <Sequence name="输入读取失败处理">
                        <PubPrintMessage strPrintMessage="❌ 错误: 读取数字输入失败: {read_input_message}" />
                        <PubProcessFeedback 
                            strProcessStep="数字IO测试"
                            strStatus="FAILED"
                            doubleProgress="90.0"
                            strMessage="读取数字输入失败"
                            strOperation="读取输入" />
                        <AlwaysFailure />
                    </Sequence>
                </Fallback>

                <PubPrintMessage strPrintMessage="✅ 读取数字输入成功，输入值验证结果:" />
                
                <!-- 打印每个输入的验证结果 -->
                <PubPrintMessage strPrintMessage="输入1: 期望值[false], 读取值[{read_input_values[0]==1?'true':'false'}], 匹配[{input_value_matched[0]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输入2: 期望值[false], 读取值[{read_input_values[1]==1?'true':'false'}], 匹配[{input_value_matched[1]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输入3: 期望值[false], 读取值[{read_input_values[2]==1?'true':'false'}], 匹配[{input_value_matched[2]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输入4: 期望值[false], 读取值[{read_input_values[3]==1?'true':'false'}], 匹配[{input_value_matched[3]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输入5: 期望值[false], 读取值[{read_input_values[4]==1?'true':'false'}], 匹配[{input_value_matched[4]==1?'成功':'失败'}]" />
                <PubPrintMessage strPrintMessage="输入6: 期望值[false], 读取值[{read_input_values[5]==1?'true':'false'}], 匹配[{input_value_matched[5]==1?'成功':'失败'}]" />
            </Sequence>

            <!-- 完成反馈 -->
            <PubProcessFeedback 
                strProcessStep="数字IO测试"
                strStatus="COMPLETED"
                doubleProgress="100.0"
                strMessage="数字IO测试完成"
                strOperation="测试完成" />
            
            <PubPrintMessage strPrintMessage="✅ 数字IO测试完成，所有操作已执行" />
            
        </Sequence>
    </BehaviorTree>
</root>
