<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 机械臂连续3次请求测试 -->
    <BehaviorTree ID="R03001010002TestFlexiv">
        <Sequence name="连续3次机械臂控制">

            <Sequence name="第一次请求_工程ID1">
                                <!-- 气泵启动 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001010002"
                    intOutputAddresses="14"
                    boolOutputValues="1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{storage_cylinder2_action_success}"
                    strOutputMessage="{storage_cylinder2_action_message}" />

                <Wait name="等待1秒" intMsec="1000"/>
            </Sequence>



            <!-- 第一次请求：工程ID=1 -->
            <Sequence name="第一次请求_工程ID1">
                <RobotArmControl 
                    strDeviceName="/R03001010002"
                    intProjectId="22"
                    intSpeedMultiplier="80"
                    floatPositionX="10.1"
                    floatPositionY="20.1"
                    floatPositionZ="30.1"
                    floatRotationRx="40.1"
                    floatRotationRy="50.1"
                    floatRotationRz="60.1"
                    intFunctionData0="11"
                    intFunctionData1="22222"
                    intFunctionData2="65534"
                    intFunctionData3="44"
                    intFunctionData4="55"
                    intFunctionData5="66"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{success1}"
                    strOutputErrorMessage="{error1}"
                    doubleExecutionTime="{time1}"
                    strCurrentStatus="{status1}" />
                
                <!-- 短暂延时 -->
                <Wait name="等待5秒" intMsec="5000"/>
            </Sequence>
            
            <!-- 第二次请求：工程ID=2 -->
            <Sequence name="第二次请求_工程ID2">
                <RobotArmControl 
                    strDeviceName="/R03001010002"
                    intProjectId="23"
                     intSpeedMultiplier="80"
                    floatPositionX="10.2"
                    floatPositionY="20.2"
                    floatPositionZ="30.2"
                    floatRotationRx="40.2"
                    floatRotationRy="50.2"
                    floatRotationRz="60.2"
                    intFunctionData0="1"
                    intFunctionData1="2"
                    intFunctionData2="3"
                    intFunctionData3="4"
                    intFunctionData4="5"
                    intFunctionData5="6"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{success2}"
                    strOutputErrorMessage="{error2}"
                    doubleExecutionTime="{time2}"
                    strCurrentStatus="{status2}" />
                
                <!-- 短暂延时 -->
                <Wait name="等待0.2秒" intMsec="200"/>
            </Sequence>
            
        </Sequence>
    </BehaviorTree>
</root> 