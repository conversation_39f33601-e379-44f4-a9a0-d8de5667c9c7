<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 上电检测-成品转移 -->
	<BehaviorTree ID="PlaceProduct">
        <Fallback name="PlaceProductFallback">
            <Sequence name="成品摆放">
                <PubProcessFeedback 
                strProcessStep="检测台成品摆放"
                strStatus="RUNNING"
                doubleProgress="0.0 "
                strMessage="开始执行检测台成品摆放"
                strOperation="开始执行检测台成品摆放" />
<!-- 
                <RobotArmControl
                strDeviceName="/R03001010002"
                intProjectId="131"
                intSpeedMultiplier="20"
                intTimeoutMs="120"/> -->

                <PubProcessFeedback
                strProcessStep="检测台成品摆放"
                strStatus="RUNNING"
                doubleProgress="100.0"
                strMessage="检测台成品摆放成功"
                strOperation="检测台成品摆放任务结束" />

                <AlwaysSuccess>
                    <!-- <RobotArmControl
                    strDeviceName="/R03001010002"
                    intProjectId="132"
                    intSpeedMultiplier="20"
                    intTimeoutMs="120"/> -->

                 <PubProcessFeedback
                    strProcessStep="机械臂工程清除"
                    strStatus="RUNNING"
                    doubleProgress="100.0"
                    strMessage="机械臂工程清除完成"
                    strOperation="机械臂工程清除完成" />
                </AlwaysSuccess>
			</Sequence>
        
            <Sequence name="成品摆放失败">
                <PubProcessFeedback strProcessStep="成品摆放" strStatus="ERROR" doubleProgress="0.0" strMessage="成品摆放失败" strOperation="成品摆放任务结束" />
            </Sequence>
		</Fallback>
  </BehaviorTree>
</root>
