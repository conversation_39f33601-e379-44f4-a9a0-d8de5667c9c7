<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 上电检测-OK品下料 -->
	<BehaviorTree ID="PlaceProductOK">
        <Fallback name="PlaceProductOKFallback">
            <Sequence name="转移OK品">
                <PubProcessFeedback 
                strProcessStep="OK品转移任务"
                strStatus="RUNNING"
                doubleProgress="0.0 "
                strMessage="开始执行OK品转移任务"
                strOperation="开始执行OK品转移任务" />

                <!-- <RobotArmControl
                strDeviceName="/R03001010002"
                intProjectId="133"
                intFunctionData0="{shell_count}"
                intSpeedMultiplier="20"
                intTimeoutMs="120"/> -->

                <Wait name="等待10秒" intMsec="10000"/>

                <PubProcessFeedback
                    strProcessStep="OK品转移任务"
                    strStatus="RUNNING"
                    doubleProgress="100.0"
                    strMessage="OK品转移成功"
                    strOperation="OK品转移任务结束" />

                <AlwaysSuccess>
                       <!-- <RobotArmControl
                            strDeviceName="/R03001010002"
                            intSpeedMultiplier="20"
                            intProjectId="134"
                            intTimeoutMs="120"/> -->
                            
                <PubProcessFeedback
                    strProcessStep="机械臂工程清除"
                    strStatus="RUNNING"
                    doubleProgress="100.0"
                    strMessage="机械臂工程清除完成"
                    strOperation="机械臂工程清除完成" />
                </AlwaysSuccess>
            </Sequence>
			</Sequence>
        
            <Sequence name="转移OK品失败">
                <PubProcessFeedback strProcessStep="转移OK品" strStatus="ERROR" doubleProgress="0.0" strMessage="转移OK品失败" strOperation="转移OK品任务结束" />
     
            </Sequence>
		</Fallback>
  </BehaviorTree>
</root>
