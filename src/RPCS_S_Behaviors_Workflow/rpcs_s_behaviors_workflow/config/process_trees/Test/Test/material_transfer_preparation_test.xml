<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 物料转移前置准备行为树 -->
    <BehaviorTree ID="MaterialTransferPreparationTest">
        <Sequence name="MaterialTransferPreparationSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始物料转移前置准备流程"
                strOperation="初始化系统" />
            

                <!-- 气泵启动 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001010002"
                    intOutputAddresses="14"
                    boolOutputValues="1"
                    boolVerifyWrite="true"
                    intTimeoutMs="5000"
                    boolOutputSuccess="{storage_cylinder2_action_success}"
                    strOutputMessage="{storage_cylinder2_action_message}" />

            
                <!-- 存储池转移气缸原位 -->
                <Sequence name="转移气缸原位序列">
                    <DigitalOutputWrite 
                        strDeviceId="R03001010002"
                        intOutputAddresses="5,4" 
                        boolOutputValues="1,0"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{transfer_cylinder_origin_success}"
                        strOutputMessage="{transfer_cylinder_origin_message}" />
                    

                    <!-- 检查转移气缸原位传感器 -->
                    <Sequence name="检查原位传感器">
                        <DigitalOutputRead
                            strDeviceId="R03001010002"
                            intOutputAddresses="5,4"
                            boolExpectedValues="1,0"
                            boolContinuousRead="true" 
                            doubleReadInterval="0.5"
                            doubleDuration="8.0"
                            intTimeoutMs="5000"
                            boolOutputSuccess="{transfer_cylinder_origin_read_success}"
                            boolOutputValues="{transfer_cylinder_origin_values}"
                            strOutputMessage="{transfer_cylinder_origin_read_message}" />

                        <!-- 检查传感器结果 -->
                        <Sequence name="检查传感器并执行后续操作">
                            <!-- 检查传感器 -->
                            <Fallback name="检查传感器值或打印错误">
                                <SensorValueCheck sensor_value="{transfer_cylinder_origin_read_success}" expected_value="true" />
                                <Sequence name="打印错误并确保失败">
                                    <PubPrintMessage strPrintMessage="❌ 错误: 转移气缸未到原位" />
                                    <PubProcessFeedback 
                                        strProcessStep="打印错误并确保失败"
                                        strStatus="FALSE"
                                        doubleProgress="99.0"
                                        strMessage="转移气缸未到原位检测失败"
                                        strOperation="检测转移气缸原位" />
                                    <AlwaysFailure />
                                </Sequence>
                            </Fallback>
                            
                            <PubPrintMessage strPrintMessage="✅ 转移气缸已到原位" />
                        </Sequence>
                    </Sequence>




                                        <!-- 检查Tray转移气缸上升位 -->
                    <Sequence name="检查上升位传感器">
                        <DigitalInputRead 
                            strDeviceId="R03001010002"
                            intInputAddresses="8,9"
                            boolExpectedValues="0,1"
                            intTimeoutMs="5000"
                            boolContinuousRead="true"
                            doubleReadInterval="0.5"
                            doubleDuration="8.0"
                            boolOutputSuccess="{tray_transfer_up_success}"
                            boolCurrentValues="{tray_transfer_up_values}"
                            strOutputMessage="{tray_transfer_up_message}" />
                        
                <!-- 检查传感器结果 -->
                <Sequence name="检查传感器并执行后续操作">
                    <!-- 检查传感器 -->
                    <Fallback name="检查传感器值或打印错误">
                        <SensorValueCheck sensor_value="{tray_transfer_up_success}" expected_value="true" />
                        <Sequence name="打印错误并确保失败">
                            <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到上升位" />
                                <PubProcessFeedback 
                                strProcessStep="打印错误并确保失败"
                                strStatus="FALSE"
                                doubleProgress="99.0"
                                strMessage="打印错误并确保失败1"
                                strOperation="打印错误并确保失2" />
                            <AlwaysFailure />
                        </Sequence>
                    </Fallback>
                    
                    <!-- 后续操作只有在传感器值符合预期时才会执行 -->
                </Sequence>
                        
                        <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到上升位" />
                    </Sequence>

                </Sequence>


            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="RUNNING"
                doubleProgress="25.0"
                strMessage="回零和初始化完成，开始气缸动作"
                strOperation="执行气缸动作" />

            
        </Sequence>
    </BehaviorTree>
</root> 