<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="BondCarrier">
        <Sequence name="MainSequence">
            <PubProcessFeedback
                strProcessStep="载板贴合"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="开始载板贴合任务"
                strOperation="开始载板贴合任务" />
            <!-- 0. 对位台回零-->

            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                <CorrectionPosition
                    strCommandType="RESET"
                    doubleTimeout="60.0"
                    boolOutputSuccess="{success}"
                    strOutputMessage="{message}"
                />
            </RetryUntilSuccessful>

            <!-- 夹爪电机下降-->
            <MotorPositionControl
                strDeviceId="R03001010001"
                intMotorId="11"
                doubleTargetPosition="30.0"
                doubleTimeout="60.0"
                boolOutputSuccess="{motor6_position_success}"
                doubleFinalPosition="{motor6_position_final_position}"
                strOutputMessage="{motor6_position_message}"
                intErrorCode="{motor6_position_error_code}"
                doubleCurrentPosition="{motor6_position_current_position}" />

            <!-- 1. 机械臂吸取载板至离型膜剥离位置 -->
            <PubProcessFeedback
                strProcessStep="载板贴合"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="步骤(1/3)： 机械臂吸取载板至载板贴合位置"
                strOperation="步骤(1/3)： 机械臂吸取载板至载板贴合位置" />
            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0801"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />
            <!-- <PubPrintMessage strTopicName="开始载板贴合" strPrintMessage="开始载板贴合" /> -->


            <!-- 初始化是否完成标志 -->
            <Script code="is_finish := false" />
            <!-- 初始化位移计算结果 -->
            <Script code=" total_move_x := 0.0" />
            <Script code=" total_move_y := 0.0" />
            <Script code=" total_move_r := 0.0" />
            <!-- 初始化最大迭代次数 -->
            <Script code=" max_iterations := 20" />

            <RetryUntilSuccessful name="MaxIterations" num_attempts="{max_iterations}">
                <Sequence>

                    <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                        <CorrectionPosition
                            strCommandType="ABS"
                            doubleAbsZ="1.0"
                            doubleTimeout="60.0"
                            boolOutputSuccess="{success}"
                            strOutputMessage="{message}"
                        />
                    </RetryUntilSuccessful>

                    <PubProcessFeedback
                        strProcessStep="灯板码放"
                        strStatus="RUNNING"
                        doubleProgress="{progress}"
                        strMessage="对位台移动到Z=1.0位置完成"
                        strOperation="对位台移动到Z=1.0位置完成" />
                    <!-- 2. 调用相机执行拍照动作 -->
                    <PubProcessFeedback
                        strProcessStep="载板贴合"
                        strStatus="RUNNING"
                        doubleProgress="20"
                        strMessage="步骤(2/3)： 相机拍照计算偏移"
                        strOperation="步骤(2/3)： 相机拍照计算偏移" />

                    <!-- 3. 调用算法进行视觉融合 -->
                    <RetryUntilSuccessful name="RetryDepthFusion" num_attempts="3">
                        <DepthFusion
                            strInputDirectory="/tmp/depth_fusion_a"
                            strOutputPath="/tmp/depth_fusion_a/a.bmp"
                            doubleTimeout="30.0"
                            boolOutputSuccess="{success}"
                            strOutputMessage="{message}"
                        />
                    </RetryUntilSuccessful>

                    <RetryUntilSuccessful name="RetryDepthFusion" num_attempts="3">
                        <DepthFusion
                            strInputDirectory="/tmp/depth_fusion_b"
                            strOutputPath="/tmp/depth_fusion_b/b.bmp"
                            doubleTimeout="30.0"
                            boolOutputSuccess="{success}"
                            strOutputMessage="{message}"
                        />
                    </RetryUntilSuccessful>
                    <!-- 3. 调用算法进行位移计算 -->
                    <!-- 载板贴合算法执行 -->
                    <VehiclesAlign name="执行载板贴合算法"
                        strImagePathA="/tmp/depth_fusion_a/a.bmp"
                        strImagePathB="/tmp/depth_fusion_b/b.bmp"
                        doubleTimeout="300.0"
                        boolOutputSuccess="{success}"
                        strOutputMessage="{message}"
                        boolIsFinish="{is_finish}"
                        strMoveCommands="{move_commands}"
                        intMoveCommandsCount="{move_count}"
                        doubleOutputX="{x_value}"
                        doubleOutputY="{y_value}"
                        doubleOutputR="{r_value}" />

                    <!-- 3. 调用平台进行位移 -->
                    <!-- 累加位移计算结果: 这是因为平台的相对运动API计算结果不正确 -->
                    <Script code="total_move_x := total_move_x + move_x" name="AddMoveX" />
                    <Script code="total_move_y := total_move_y + move_y" name="AddMoveY" />
                    <Script code="total_move_r := total_move_r + move_r" name="AddMoveR" />


                    <PubProcessFeedback
                        strProcessStep="载板贴合"
                        strStatus="RUNNING"
                        doubleProgress="20"
                        strMessage="{move_commands}"
                        strOperation="{move_commands}" />
                    <!-- 对位台下降到3mm安全位 -->
                    <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                        <!-- 防止上一个请求还没跑完，这里延时3秒-->
                        <CorrectionPosition
                            strCommandType="ASB"
                            doubleAbsZ="2.0"
                            doubleTimeout="60.0"
                            boolOutputSuccess="{success}"
                            strOutputMessage="{message}"
                        />
                    </RetryUntilSuccessful>

                    <PubProcessFeedback
                        strProcessStep="灯板码放"
                        strStatus="RUNNING"
                        doubleProgress="{progress}"
                        strMessage="对位台下降到2mm安全位完成"
                        strOperation="对位台下降到2mm安全位完成" />

                    <!-- 根据算法返回封装运行参数（相对运动转绝对运动）,执行对位平台移动-->
                    <!-- 因为对位平台有限位,不能移动太多,因此,当算法计算需要位移较大时,先用机械臂移动到中间位置,再让对位平台移动-->
                    <Precondition if="move_x_enable==1" else="SUCCESS">
                        <Fallback>
                            <Precondition if="total_move_x < 4.5" else="FAILURE">
                                <Sequence>
                                    <RetryUntilSuccessful name="RetryCorrectionPosition"
                                        num_attempts="3">
                                        <CorrectionPosition
                                            strCommandType="ASB"
                                            doubleAbsX="{total_move_x}"
                                            doubleTimeout="300.0"
                                            boolOutputSuccess="{success}"
                                            strOutputMessage="{message}"
                                        />
                                    </RetryUntilSuccessful>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="对位平台X轴移动完成"
                                        strOperation="对位平台X轴移动完成" />
                                </Sequence>
                            </Precondition>

                            <ForceFailure>
                                <Sequence>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="超出对位台X轴移动范围"
                                        strOperation="超出对位台X轴移动范围" />
                                </Sequence>
                            </ForceFailure>
                        </Fallback>
                    </Precondition>

                    <Precondition if="move_y_enable==1" else="SUCCESS">
                        <Fallback>
                            <Precondition if="total_move_y < 4.5" else="FAILURE">
                                <Sequence>
                                    <RetryUntilSuccessful name="RetryCorrectionPosition"
                                        num_attempts="3">
                                        <CorrectionPosition
                                            strCommandType="ASB"
                                            doubleAbsY="{total_move_y}"
                                            doubleTimeout="300.0"
                                            boolOutputSuccess="{success}"
                                            strOutputMessage="{message}"
                                        />
                                    </RetryUntilSuccessful>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="对位平台Y轴移动完成"
                                        strOperation="对位平台Y轴移动完成" />
                                </Sequence>
                            </Precondition>
                            <ForceFailure>
                                <Sequence>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="超出对位台Y轴移动范围"
                                        strOperation="超出对位台Y轴移动范围" />
                                </Sequence>
                            </ForceFailure>
                        </Fallback>
                    </Precondition>

                    <Precondition if="move_r_enable==1" else="SUCCESS">
                        <Fallback>
                            <Precondition if="total_move_r < 2.5" else="FAILURE">
                                <Sequence>
                                    <RetryUntilSuccessful name="RetryCorrectionPosition"
                                        num_attempts="3">
                                        <CorrectionPosition
                                            strCommandType="ASB"
                                            doubleAbsR="{total_move_r}"
                                            doubleTimeout="300.0"
                                            boolOutputSuccess="{success}"
                                            strOutputMessage="{message}"
                                        />
                                    </RetryUntilSuccessful>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="对位平台R轴移动完成"
                                        strOperation="对位平台R轴移动完成" />
                                </Sequence>
                            </Precondition>
                            <ForceFailure>
                                <Sequence>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="超出对位台R轴移动范围"
                                        strOperation="超出对位台R轴移动范围" />
                                </Sequence>
                            </ForceFailure>
                        </Fallback>
                    </Precondition>

                    <!-- 如果算法没有完成，则认为对齐没有成功，继续迭代-->
                    <Precondition if="is_finish != true">
                        <AlwaysFailure />
                    </Precondition>
                </Sequence>
            </RetryUntilSuccessful>
            <!-- 4. 平台上升 -->
            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                <CorrectionPosition
                    strCommandType="ABS"
                    doubleAbsZ="0"
                    doubleTimeout="30.0"
                    boolOutputSuccess="{success}"
                    strOutputMessage="{message}"
                />
            </RetryUntilSuccessful>
            <!-- 5. 对位台破真空 -->
            <RetryUntilSuccessful name="RetrySucker" num_attempts="3">
                <Sucker
                    strSuckerConfig="[1,2,3,4,5,6,7,8,9,10,11,12]"
                    boolSuckerState="false"
                    doubleTimeout="30.0"
                    boolOutputSuccess="{success}"
                    strOutputMessage="{message}"
                    strPlcFeedback="{plc_feedback}"
                />
            </RetryUntilSuccessful>
            <!-- 5. 机械臂破真空并回零 -->
            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0802"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />

            <PubProcessFeedback
                strProcessStep="载板贴合"
                strStatus="FINISHED"
                doubleProgress="100"
                strMessage="载板贴合工艺完成"
                strOperation="载板贴合工艺完成" />
        </Sequence>
    </BehaviorTree>
</root>