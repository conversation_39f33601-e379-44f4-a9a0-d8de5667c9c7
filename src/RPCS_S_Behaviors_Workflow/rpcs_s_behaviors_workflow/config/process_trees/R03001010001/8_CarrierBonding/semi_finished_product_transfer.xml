<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 载板贴合转移 - 半成品转移 -->
	<!-- 半成品转移的起始位是载板贴合之后 -->
	<!-- 几号小车工艺: 1号小车 - 机械臂操作 - 半成品转移 -->
	<BehaviorTree ID="CBBAT_SemiFinishedProductTransfer">
		<Sequence name="CBBAT_SemiFinishedProductTransferSequence">
			<!-- 初始化反馈 -->
			<PubProcessFeedback 
				strProcessStep="半成品转移"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="半成品转移"
				strOperation="半成品转移" />

			<!-- 半成品转移 -->
			<Fallback name="SemiFinishedProductTransferFallback">
				<Sequence name="SemiFinishedProductTransferSequence">
					<!-- 半成品转移 -->
					<!-- 2号小车 - 机械臂操作 - 半成品转移 -->
					<PubProcessFeedback
						strProcessStep="半成品转移"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="半成品转移"
						strOperation="半成品转移" />

					<!-- 需要修改任务id、速度
					<RobotArmControl
						strDeviceName="/R03001010002"
						intProjectId="100000"
						intSpeedMultiplier="10"/>
					-->

					<PubProcessFeedback
						strProcessStep="半成品转移"
						strStatus="RUNNING"
						doubleProgress="99.0"
						strMessage="半成品转移完成"
						strOperation="半成品转移" />
				</Sequence>

				<!-- 半成品转移失败处理 -->
				<Sequence name="SemiFinishedProductTransferFailureHandler">
					<PubProcessFeedback
						strProcessStep="半成品转移"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="半成品转移失败"
						strOperation="半成品转移失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- 半成品转移完成 -->
			<PubProcessFeedback
				strProcessStep="半成品转移"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="半成品转移完成"
				strOperation="半成品转移结束" />

		</Sequence>
	</BehaviorTree>
</root>
