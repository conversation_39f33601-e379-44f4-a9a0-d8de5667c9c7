<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="StackLightBoards">
        <Sequence name="MainSequence">

            <PubProcessFeedback
                strProcessStep="灯板码放"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="开始灯板码放任务"
                strOperation="开始灯板码放任务" />

            <!-- 初始化循环计数器，代表第一次开始贴第几块灯板，默认从1开始 -->
            <Script code="current_board := 0" name="InitLoopCount" />
            <Script code="loop_count := board_num" name="InitLoopCount" />
            <!-- 初始化进度 -->
            <Script code="progress := 0.0" name="InitProgress" />
            <!-- 初始化灯板数量，要贴多少块 -->
            <Script code="boards := board_count" name="InitBoards" />

            <Repeat num_cycles="{boards}" name="LoopController">
                <Sequence name="LoopSequence">
                    <!-- 0. 对位台回零-->
                    <Script code="current_board := loop_count" name="IncrementCurrentBoard" />
                    <Precondition if="loop_count==4" else="SUCCESS">
                        <Sequence>
                            <Script code="current_board := 6" />
                        </Sequence>
                    </Precondition>
                    <Precondition if="loop_count==6" else="SUCCESS">
                        <Sequence>
                            <Script code="current_board := 4" />
                        </Sequence>
                    </Precondition>
                    <Precondition if="loop_count==10" else="SUCCESS">
                        <Sequence>
                            <Script code="current_board := 12" />
                        </Sequence>
                    </Precondition>
                    <Precondition if="loop_count==12" else="SUCCESS">
                        <Sequence>
                            <Script code="current_board := 10" />
                        </Sequence>
                    </Precondition>
                    <Script code=" progress :=progress+ (boards / 12.0) * 90.0 " />

                    <!-- 并行操作 -->
                    <Parallel>
                        <Sequence>
                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="对位台回零"
                                strOperation="对位台回零" />

                            <!-- 对位台回零，重试3次 -->
                            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                                <CorrectionPosition
                                    strCommandType="RESET"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="对位台回零完成"
                                strOperation="对位台回零完成" />

                            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                                <CorrectionPosition
                                    strCommandType="ABS"
                                    doubleAbsZ="1.0"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="对位台移动到Z=1.0位置完成"
                                strOperation="对位台移动到Z=1.0位置完成" />
                        </Sequence>
                        <!-- 2. 机械臂开始吸取灯板 -->
                        <Sequence>
                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="机械臂开始吸取灯板"
                                strOperation="机械臂开始吸取灯板" />

                            <!-- 夹爪电机上升-->
                            <MotorPositionControl
                                strDeviceId="R03001010001"
                                intMotorId="11"
                                doubleTargetPosition="0.0"
                                boolAbsolutePosition="true"
                                doubleTimeout="60.0"
                                boolOutputSuccess="{motor6_position_success}"
                                doubleFinalPosition="{motor6_position_final_position}"
                                strOutputMessage="{motor6_position_message}"
                                intErrorCode="{motor6_position_error_code}"
                                doubleCurrentPosition="{motor6_position_current_position}" />

                            <RobotArmControl
                                strDeviceName="/R03001010001"
                                intProjectId="61"
                                intFunctionData0="{current_board}"
                                intTimeoutMs="300000"
                                boolOutputSuccess="{success}"
                                strOutputErrorMessage="{error}"
                                doubleExecutionTime="{time}"
                                strCurrentStatus="{status}" />

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="机械臂吸取灯板完成"
                                strOperation="机械臂吸取灯板完成" />
                        </Sequence>
                    </Parallel>
                    <!-- 4. 调用算法进行位移计算 -->
                    <!-- 初始化是否完成标志 -->
                    <Script code="is_finish := false" />
                    <!-- 初始化位移计算结果 -->
                    <Script code=" total_move_x := 0.0" />
                    <Script code=" total_move_y := 0.0" />
                    <Script code=" total_move_r := 0.0" />
                    <!-- 初始化最大迭代次数 -->
                    <Script code=" max_iterations := 30" />

                    <!-- 开始调用算法进行灯板贴合 -->
                    <RetryUntilSuccessful name="MaxIterations" num_attempts="{max_iterations}">
                        <Sequence>
                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="调用算法进行位移计算"
                                strOperation="调用算法进行位移计算" />

                            <!-- 3. 对位平台Z=1拍照位 -->
                            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                                <CorrectionPosition
                                    strCommandType="ASB"
                                    doubleAbsZ="1.0"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>
                            <!-- 调用算法进行位移计算 -->
                            <Script code="move_x_enable := false" />
                            <Script code="move_y_enable := false" />
                            <Script code="move_r_enable := false" />
                            <BoardAlign
                                intBoardNum="{current_board}"
                                doubleTimeout="60.0"
                                boolOutputSuccess="{success}"
                                strOutputMessage="{message}"
                                boolIsFinish="{is_finish}"
                                strMoveCommands="{move_commands}"
                                intMoveCommandsCount="{move_commands_count}"
                                doubleOutputX="{move_x}"
                                doubleOutputY="{move_y}"
                                doubleOutputR="{move_r}"
                                boolOutputX="{move_x_enable}"
                                boolOutputY="{move_y_enable}"
                                boolOutputR="{move_r_enable}"
                            />

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="{move_commands}"
                                strOperation="{move_commands}" />

                            <!-- 累加位移计算结果: 这是因为平台的相对运动API计算结果不正确 -->
                            <Script code="total_move_x := total_move_x + move_x" name="AddMoveX" />
                            <Script code="total_move_y := total_move_y + move_y" name="AddMoveY" />
                            <Script code="total_move_r := total_move_r + move_r" name="AddMoveR" />

                            <!-- 对位台下降到3mm安全位 -->
                            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                                <!-- 防止上一个请求还没跑完，这里延时3秒-->
                                <CorrectionPosition
                                    strCommandType="ASB"
                                    doubleAbsZ="2.0"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="对位台下降到2mm安全位完成"
                                strOperation="对位台下降到2mm安全位完成" />

                            <!-- 根据算法返回封装运行参数（相对运动转绝对运动）,执行对位平台移动-->
                            <!-- 因为对位平台有限位,不能移动太多,因此,当算法计算需要位移较大时,先用机械臂移动到中间位置,再让对位平台移动-->
                            <Precondition if="move_x_enable==1" else="SUCCESS">
                                <Fallback>
                                    <Precondition if="total_move_x < 4.5" else="FAILURE">
                                        <Sequence>
                                            <RetryUntilSuccessful name="RetryCorrectionPosition"
                                                num_attempts="3">
                                                <CorrectionPosition
                                                    strCommandType="ASB"
                                                    doubleAbsX="{total_move_x}"
                                                    doubleTimeout="300.0"
                                                    boolOutputSuccess="{success}"
                                                    strOutputMessage="{message}"
                                                />
                                            </RetryUntilSuccessful>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="对位平台X轴移动完成"
                                                strOperation="对位平台X轴移动完成" />
                                        </Sequence>
                                    </Precondition>

                                    <ForceFailure>
                                        <Sequence>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="超出对位台X轴移动范围"
                                                strOperation="超出对位台X轴移动范围" />
                                        </Sequence>
                                    </ForceFailure>
                                </Fallback>
                            </Precondition>

                            <Precondition if="move_y_enable==1" else="SUCCESS">
                                <Fallback>
                                    <Precondition if="total_move_y < 4.5" else="FAILURE">
                                        <Sequence>
                                            <RetryUntilSuccessful name="RetryCorrectionPosition"
                                                num_attempts="3">
                                                <CorrectionPosition
                                                    strCommandType="ASB"
                                                    doubleAbsY="{total_move_y}"
                                                    doubleTimeout="300.0"
                                                    boolOutputSuccess="{success}"
                                                    strOutputMessage="{message}"
                                                />
                                            </RetryUntilSuccessful>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="对位平台Y轴移动完成"
                                                strOperation="对位平台Y轴移动完成" />
                                        </Sequence>
                                    </Precondition>
                                    <ForceFailure>
                                        <Sequence>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="超出对位台Y轴移动范围"
                                                strOperation="超出对位台Y轴移动范围" />
                                        </Sequence>
                                    </ForceFailure>
                                </Fallback>
                            </Precondition>

                            <Precondition if="move_r_enable==1" else="SUCCESS">
                                <Fallback>
                                    <Precondition if="total_move_r < 2.5" else="FAILURE">
                                        <Sequence>
                                            <RetryUntilSuccessful name="RetryCorrectionPosition"
                                                num_attempts="3">
                                                <CorrectionPosition
                                                    strCommandType="ASB"
                                                    doubleAbsR="{total_move_r}"
                                                    doubleTimeout="300.0"
                                                    boolOutputSuccess="{success}"
                                                    strOutputMessage="{message}"
                                                />
                                            </RetryUntilSuccessful>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="对位平台R轴移动完成"
                                                strOperation="对位平台R轴移动完成" />
                                        </Sequence>
                                    </Precondition>
                                    <ForceFailure>
                                        <Sequence>
                                            <PubProcessFeedback
                                                strProcessStep="灯板码放"
                                                strStatus="RUNNING"
                                                doubleProgress="{progress}"
                                                strMessage="超出对位台R轴移动范围"
                                                strOperation="超出对位台R轴移动范围" />
                                        </Sequence>
                                    </ForceFailure>
                                </Fallback>
                            </Precondition>

                            <!-- 如果算法没有完成，则认为对齐没有成功，继续迭代-->
                            <Precondition if="is_finish != true" else="SUCCESS">
                                <AlwaysFailure>
                                    <Sequence>
                                        <PubProcessFeedback
                                            strProcessStep="灯板码放"
                                            strStatus="RUNNING"
                                            doubleProgress="{progress}"
                                            strMessage="贴合未完成，继续下一轮迭代"
                                            strOperation="贴合未完成，继续下一轮迭代" />
                                    </Sequence>
                                </AlwaysFailure>
                            </Precondition>
                        </Sequence>
                    </RetryUntilSuccessful>

                    <!-- 如果算法对齐完成，则执行对位平台移动-->
                    <Precondition if="is_finish == true" else="FAILURE">
                        <Sequence>
                            <!--贴合完成，平台上升-->
                            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                                <CorrectionPosition
                                    strCommandType="ASB"
                                    doubleAbsZ="0.0"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="平台上升贴合完成"
                                strOperation="平台上升贴合完成" />

                            <!--对位平台真空吸盘开启-->
                            <RetryUntilSuccessful name="RetrySucker" num_attempts="3">
                                <Sucker
                                    intSuckerId="{current_board}"
                                    boolSuckerState="true"
                                    doubleTimeout="60.0"
                                    boolOutputSuccess="{success}"
                                    strOutputMessage="{message}"
                                />
                            </RetryUntilSuccessful>
                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="吸盘开启完成"
                                strOperation="吸盘开启完成" />

                            <!-- 6. 机械臂破真空、移动安全位 -->
                            <!-- 目前机械臂Control有问题，这里为了调试强制成功了！TODO:等机械臂Control修复后删除-->
                            <ForceSuccess>
                                <RobotArmControl
                                    strDeviceName="/R03001010001"
                                    intProjectId="62"
                                    intTimeoutMs="300000"
                                    boolOutputSuccess="{success}"
                                    strOutputErrorMessage="{error}"
                                    doubleExecutionTime="{time}"
                                    strCurrentStatus="{status}" />
                            </ForceSuccess>

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="机械臂破真空移动安全位完成"
                                strOperation="机械臂破真空移动安全位完成" />

                            <PubProcessFeedback
                                strProcessStep="灯板码放"
                                strStatus="RUNNING"
                                doubleProgress="{progress}"
                                strMessage="码放灯板完成"
                                strOperation="码放灯板完成" />
                            <!-- 循环计数器自增 -->
                            <Script code="loop_count := loop_count + 1" name="IncrementLoopCount" />

                            <!-- 如果第6片贴合完成后，需要进行更换料操作 -->
                            <Precondition if="loop_count == 6" else="SUCCESS">
                                <Sequence>
                                    <PubProcessFeedback
                                        strProcessStep="灯板码放"
                                        strStatus="RUNNING"
                                        doubleProgress="{progress}"
                                        strMessage="更换料完成"
                                        strOperation="更换料完成" />
                                </Sequence>
                            </Precondition>
                        </Sequence>
                    </Precondition>
                </Sequence>
            </Repeat>


            <!-- 对位台回零，重试3次 -->
            <RetryUntilSuccessful name="RetryCorrectionPosition" num_attempts="3">
                <CorrectionPosition
                    strCommandType="RESET"
                    doubleTimeout="60.0"
                    boolOutputSuccess="{success}"
                    strOutputMessage="{message}"
                />
            </RetryUntilSuccessful>

            <PubProcessFeedback
                strProcessStep="灯板码放"
                strStatus="FINISHED"
                doubleProgress="100.0"
                strMessage="灯板码放全部完成"
                strOperation="灯板码放全部完成" />
        </Sequence>
    </BehaviorTree>
</root>