<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 到取料点 -->
	<!-- 到取料点的起始位是充电点位，即机器人作业点位 -->
	<!-- 左料仓电机动作、电缸动作、机械臂动作、AGV走点动作 -->
	<!-- 从机器人作业点位到取料点位不会有任何停顿，至于安全点位去取料时不会停顿，只是AGV小车路过安全点位的二维码时，会用于自身纠正 -->
	<!-- 其中2号小车安全点位是单独的，但是1号、3号小车安全点位和机器人作业点位完全重合 -->
	<!-- 不同小车差异说明-到取料点，3个小车到取料点工艺完全相同，唯一的区别AgvGoPoint需要传递/RobotX -->
	<BehaviorTree ID="LightBoardToRacks">
		<Sequence name="LightBoardToRacksSequence">
			<PubProcessFeedback
				strProcessStep="到取料点"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="到取料点"
				strOperation="开始到取料点任务" />

			<!-- 到取料点 -->
			<Fallback name="MoveToRacksFallback">
				<Sequence name="MoveToRacksSequence">
					<!-- 到取料点第一阶段 -->
					<!-- 机器人取料前置，这是相对于机器人作业前置来的 -->
					<PubProcessFeedback
						strProcessStep="机器人取料前置"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="机器人取料前置"
						strOperation="机器人取料前置" />

					<PubProcessFeedback
						strProcessStep="机器人取料前置"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="AGV小车结束充电"
						strOperation="AGV小车结束充电" />

					<!-- 调用AGV服务-结束充电 -->
 					<!-- 如果AGV结束充电之后立马发送走点报文，小车会有几秒钟的时间在原地等待，不报错就是等待，因为KuKa的AGV内部正在结束充电 -->
					<!-- 如果先结束充电，再机械臂规零、电缸收起，那么发送AGV走点报文时，就避免了时AGV原地等待几秒钟 -->
					<AgvGoPoint strGoPointName="StopCharging"
								strNamespace="/Robot2" />
	
					<!-- 左料仓电机规零-左料仓电机从最上面下降到原点位 -->
					<!-- 右料仓电机不动 -->
					<!-- 电机6位置控制 -->
					<Sequence name="电机6位置归零">
						<PubProcessFeedback
							strProcessStep="机器人取料前置"
							strStatus="RUNNING"
							doubleProgress="20.0"
							strMessage="左料仓电机规零"
							strOperation="左料仓电机规零" />
						<MotorHoming
							strDeviceId="Robot1"
							intMotorId="6"
							intHomingMethod="18"
							floatSpeedSwitch="30.0"
							floatSpeedZero="100.0"
							intHomeOffset="0"
							intPositionWindow="10"
							intPositionWindowTime="200"
							doubleTimeout="200.0"
							boolOutputSuccess="{motor6_homing_success}"
							floatFinalPosition="{motor6_final_position}"
							strOutputMessage="{motor6_message}"
							intCurrentStatus="{motor6_status}"
							strStatusDescription="{motor6_status_desc}"
							floatCurrentPosition="{motor6_current_pos}" />
					</Sequence>
					
					<!-- 机械臂规零 -->
					<!-- 机器人取料前置中的机械臂归零，不同于机器人作业前置中的机械臂归零 -->
					<!-- 机器人作业前置中的机械臂归零有精度要求 -->
					<!-- 而机械臂取料前置中的机械臂归零，有碰撞要求，就是小车从作业点位到取料点位，包括从取料点位到作业点位，机械臂全程不能碰到其他设备、物料 -->
					<!-- 这里的其他设备并不是固定不动的，传送带会动，物料会移动，其他小车的机械臂会移动 -->
					<!-- 所以这里的碰撞归零要求是，其他所有，无论怎样动，小车移动过程中，这个归零后的机械臂，都不会发生碰撞 -->
					<PubProcessFeedback
						strProcessStep="机器人取料前置"
						strStatus="RUNNING"
						doubleProgress="40.0"
						strMessage="机械臂规零"
						strOperation="机械臂规零" />

					<Delay delay_msec="500">
						<AlwaysSuccess/>
					</Delay>

					<!-- 电缸收起 -->
					<!-- 电缸收起时，四个电缸都要收起，只要有1个电缸没有收起，那么这个电缸就是刹车片，AGV小车移动时，直接损坏电缸，间接损坏AGV电机引发过载、以及上装电缸承载设备 -->
					<PubProcessFeedback
						strProcessStep="机器人取料前置"
						strStatus="RUNNING"
						doubleProgress="60.0"
						strMessage="电缸收起"
						strOperation="电缸收起" />

					<Parallel success_count="4" failure_count="1" name="并行回零操作">
						<!-- 电机1回零 -->
						<Sequence name="电机1回零">
							<MotorHoming 
								strDeviceId="Robot1"
								intMotorId="1"
								intHomingMethod="17"
								floatSpeedSwitch="20.0"
								floatSpeedZero="40.0"
								intHomeOffset="0"
								intPositionWindow="10"
								intPositionWindowTime="100"
								doubleTimeout="60.0"
								boolOutputSuccess="{motor1_homing_success}"
								floatFinalPosition="{motor1_final_position}"
								strOutputMessage="{motor1_message}"
								intCurrentStatus="{motor1_status}"
								strStatusDescription="{motor1_status_desc}"
								floatCurrentPosition="{motor1_current_pos}" />
						</Sequence>
						
						<!-- 电机2回零 -->
						<Sequence name="电机2回零">
							<MotorHoming 
								strDeviceId="Robot1"
								intMotorId="2"
								intHomingMethod="17"
								floatSpeedSwitch="20.0"
								floatSpeedZero="40.0"
								intHomeOffset="0"
								intPositionWindow="10"
								intPositionWindowTime="100"
								doubleTimeout="60.0"
								boolOutputSuccess="{motor2_homing_success}"
								floatFinalPosition="{motor2_final_position}"
								strOutputMessage="{motor2_message}"
								intCurrentStatus="{motor2_status}"
								strStatusDescription="{motor2_status_desc}"
								floatCurrentPosition="{motor2_current_pos}" />
						</Sequence>
						
						<!-- 电机3回零 -->
						<Sequence name="电机3回零">
							<MotorHoming 
								strDeviceId="Robot1"
								intMotorId="3"
								intHomingMethod="17"
								floatSpeedSwitch="20.0"
								floatSpeedZero="40.0"
								intHomeOffset="0"
								intPositionWindow="10"
								intPositionWindowTime="100"
								doubleTimeout="60.0"
								boolOutputSuccess="{motor3_homing_success}"
								floatFinalPosition="{motor3_final_position}"
								strOutputMessage="{motor3_message}"
								intCurrentStatus="{motor3_status}"
								strStatusDescription="{motor3_status_desc}"
								floatCurrentPosition="{motor3_current_pos}" />
						</Sequence>
						
						<!-- 电机4回零 -->
						<Sequence name="电机4回零">
							<MotorHoming 
								strDeviceId="Robot1"
								intMotorId="4"
								intHomingMethod="17"
								floatSpeedSwitch="20.0"
								floatSpeedZero="40.0"
								intHomeOffset="0"
								intPositionWindow="10"
								intPositionWindowTime="100"
								doubleTimeout="60.0"
								boolOutputSuccess="{motor4_homing_success}"
								floatFinalPosition="{motor4_final_position}"
								strOutputMessage="{motor4_message}"
								intCurrentStatus="{motor4_status}"
								strStatusDescription="{motor4_status_desc}"
								floatCurrentPosition="{motor4_current_pos}" />
						</Sequence>
					</Parallel>
					<PubProcessFeedback
						strProcessStep="机器人取料前置"
						strStatus="RUNNING"
						doubleProgress="80.0"
						strMessage="机器人取料前置完毕"
						strOperation="机器人取料前置" />

					<PubProcessFeedback
						strProcessStep="AGV走点前人工检查四个电缸是否全部收起，等待20秒"
						strStatus="RUNNING"
						doubleProgress="80.0"
						strMessage="AGV走点前人工检查四个电缸是否全部收起，等待20秒"
						strOperation="AGV走点前人工检查四个电缸是否全部收起，等待20秒" />

					<Wait name="等待20秒" intMsec="20000"/>

					<!-- 到取料点第二阶段 -->
					<!-- AGV小车走点动作，从充电点位即机器人作业点位 到 取料点 -->
					<PubProcessFeedback
						strProcessStep="AGV移动到取料点"
						strStatus="RUNNING"
						doubleProgress="80.0"
						strMessage="AGV移动到取料点"
						strOperation="AGV移动到取料点" />

					<!-- 调用AGV走点服务移动到取料点-即从充电点位到取料点 -->
					<AgvGoPoint strGoPointName="ToRacks"
								strNamespace="/Robot2"/>

					<PubProcessFeedback
						strProcessStep="AGV移动到到取料点"
						strStatus="RUNNING"
						doubleProgress="100.0"
						strMessage="AGV已经移动到到取料点"
						strOperation="AGV移动到到取料点" />
				</Sequence>

				<!-- 到取料点失败处理 -->
				<Sequence name="MoveToRacksFailureHandler">
					<PubProcessFeedback
						strProcessStep="到取料点"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="到取料点失败"
						strOperation="处理到取料点失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- 到取料点完成 -->
			<PubProcessFeedback
				strProcessStep="到取料点"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="到取料点完成"
				strOperation="到取料点任务结束" />
		</Sequence>
	</BehaviorTree>
</root>
