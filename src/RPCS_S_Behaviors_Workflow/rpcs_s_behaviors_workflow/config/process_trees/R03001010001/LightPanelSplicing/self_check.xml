<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="LightBoardSelfCheck">
        <Sequence name="SelfCheckSequence">
            <!-- 初始化 (0%) -->
            <PubPrintMessage strPrintMessage="🚀 Robot1自检开始: 基本状态→作业状态→AGV状态→机械臂状态" />
            <PubProcessFeedback 
                strProcessStep="初始化" 
                strStatus="INITIALIZING" 
                doubleProgress="0.0" 
                strMessage="自检流程已启动" 
                strOperation="初始化自检参数" />
            
            <!-- 1、基本状态检查 (0-25%) -->
            <Sequence name="BasicStatusCheck">
                <PubProcessFeedback 
                    strProcessStep="基本状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="5.0" 
                    strMessage="开始基本状态检查" 
                    strOperation="系统诊断启动" />
                <PubPrintMessage strPrintMessage="🔍 基本状态检查开始..." />
                <Sequence name="SystemBasicCheck">
                    <PubPrintMessage strPrintMessage="💻 检查系统运行状态..." />
                    <PubProcessFeedback 
                        strProcessStep="基本状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="7.5" 
                        strMessage="检查系统运行状态" 
                        strOperation="验证系统进程" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🔋 检查电源状态..." />
                    <PubProcessFeedback 
                        strProcessStep="基本状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="12.5" 
                        strMessage="检查电源状态" 
                        strOperation="验证电源电压" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🌡️ 检查温度状态..." />
                    <PubProcessFeedback 
                        strProcessStep="基本状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="17.5" 
                        strMessage="检查温度状态" 
                        strOperation="读取温度传感器" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="📡 检查网络连接..." />
                    <PubProcessFeedback 
                        strProcessStep="基本状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="22.5" 
                        strMessage="检查网络连接" 
                        strOperation="验证网络可达性" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                </Sequence>
                <PubPrintMessage strPrintMessage="✅ 基本状态检查完成" />
                <PubProcessFeedback 
                    strProcessStep="基本状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="25.0" 
                    strMessage="基本状态检查完成" 
                    strOperation="完成系统诊断" />
            </Sequence>

            <!-- 2、作业状态检查 (25-50%) -->
            <Sequence name="WorkStatusCheck">
                <PubProcessFeedback 
                    strProcessStep="作业状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="30.0" 
                    strMessage="开始作业状态检查" 
                    strOperation="读取作业参数" />
                <PubPrintMessage strPrintMessage="📋 作业状态检查开始..." />
                <Sequence name="WorkStatusTests">
                    <PubPrintMessage strPrintMessage="🔄 检查当前工作模式..." />
                    <PubProcessFeedback 
                        strProcessStep="作业状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="33.75" 
                        strMessage="检查当前工作模式" 
                        strOperation="读取模式配置" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="📊 检查任务队列状态..." />
                    <PubProcessFeedback 
                        strProcessStep="作业状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="38.75" 
                        strMessage="检查任务队列状态" 
                        strOperation="读取任务队列" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="⏱️ 检查作业计时器..." />
                    <PubProcessFeedback 
                        strProcessStep="作业状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="43.75" 
                        strMessage="检查作业计时器" 
                        strOperation="读取计时器状态" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="📈 检查生产效率指标..." />
                    <PubProcessFeedback 
                        strProcessStep="作业状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="48.75" 
                        strMessage="检查生产效率指标" 
                        strOperation="计算效率数据" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                </Sequence>
                <PubPrintMessage strPrintMessage="✅ 作业状态检查完成" />
                <PubProcessFeedback 
                    strProcessStep="作业状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="50.0" 
                    strMessage="作业状态检查完成" 
                    strOperation="完成作业诊断" />
            </Sequence>

            <!-- 3、读取AGV状态 (50-75%) -->
            <Sequence name="AgvStatusCheck">
                <PubProcessFeedback 
                    strProcessStep="AGV状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="55.0" 
                    strMessage="开始AGV状态检查" 
                    strOperation="连接AGV控制器" />
                <PubPrintMessage strPrintMessage="🚗 AGV状态检查开始..." />
                <Sequence name="AgvStatusTests">
                    <PubPrintMessage strPrintMessage="🔋 检查AGV电池电量..." />
                    <PubProcessFeedback 
                        strProcessStep="AGV状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="58.75" 
                        strMessage="检查AGV电池电量" 
                        strOperation="读取电池状态" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🧭 检查AGV定位状态..." />
                    <PubProcessFeedback 
                        strProcessStep="AGV状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="63.75" 
                        strMessage="检查AGV定位状态" 
                        strOperation="读取定位数据" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🛑 检查AGV急停状态..." />
                    <PubProcessFeedback 
                        strProcessStep="AGV状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="68.75" 
                        strMessage="检查AGV急停状态" 
                        strOperation="验证安全系统" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🔄 检查AGV运动状态..." />
                    <PubProcessFeedback 
                        strProcessStep="AGV状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="73.75" 
                        strMessage="检查AGV运动状态" 
                        strOperation="读取驱动系统" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                </Sequence>
                <PubPrintMessage strPrintMessage="✅ AGV状态检查完成" />
                <PubProcessFeedback 
                    strProcessStep="AGV状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="75.0" 
                    strMessage="AGV状态检查完成" 
                    strOperation="完成AGV诊断" />
            </Sequence>

            <!-- 4、读取机械臂状态 (75-100%) -->
            <Sequence name="RobotArmStatusCheck">
                <PubProcessFeedback 
                    strProcessStep="机械臂状态检查" 
                    strStatus="RUNNING" 
                    doubleProgress="80.0" 
                    strMessage="开始机械臂状态检查" 
                    strOperation="连接机械臂控制器" />
                <PubPrintMessage strPrintMessage="🦾 机械臂状态检查开始..." />
                <Sequence name="ArmStatusTests">
                    <PubPrintMessage strPrintMessage="🔌 检查机械臂电源状态..." />
                    <PubProcessFeedback 
                        strProcessStep="机械臂状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="83.75" 
                        strMessage="检查机械臂电源状态" 
                        strOperation="读取供电参数" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="⚙️ 检查机械臂关节状态..." />
                    <PubProcessFeedback 
                        strProcessStep="机械臂状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="88.75" 
                        strMessage="检查机械臂关节状态" 
                        strOperation="读取关节参数" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="🛑 检查机械臂急停状态..." />
                    <PubProcessFeedback 
                        strProcessStep="机械臂状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="93.75" 
                        strMessage="检查机械臂急停状态" 
                        strOperation="验证安全回路" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                    <PubPrintMessage strPrintMessage="✋ 检查末端执行器状态..." />
                    <PubProcessFeedback 
                        strProcessStep="机械臂状态检查" 
                        strStatus="RUNNING" 
                        doubleProgress="98.75" 
                        strMessage="检查末端执行器状态" 
                        strOperation="读取末端数据" />
                    <Delay delay_msec="800">
                        <AlwaysSuccess/>
                    </Delay>
                </Sequence>
                <PubPrintMessage strPrintMessage="✅ 机械臂状态检查完成" />
                <PubProcessFeedback 
                    strProcessStep="机械臂状态检查" 
                    strStatus="FINISHED" 
                    doubleProgress="100.0" 
                    strMessage="机械臂状态检查完成" 
                    strOperation="完成机械臂诊断" />
            </Sequence>

            <!-- 最终报告 -->
            <PubPrintMessage strPrintMessage="🎉 Robot1自检完成 - 所有系统正常! 
- 基本状态: ✓ 正常
- 作业状态: ✓ 正常
- AGV状态: ✓ 正常
- 机械臂状态: ✓ 正常" />
        </Sequence>
    </BehaviorTree>
</root> 