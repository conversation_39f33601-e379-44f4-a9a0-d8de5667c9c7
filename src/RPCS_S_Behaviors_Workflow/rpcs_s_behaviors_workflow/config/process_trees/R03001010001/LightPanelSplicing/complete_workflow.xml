<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="LightBoardCompleteWorkflow">
        <Sequence name="CompleteWorkflowSequence">
            <!-- 初始化 (0%) -->
            <PubPrintMessage strPrintMessage="🚀 Robot1完整工作流程开始执行" />
            <PubProcessFeedback 
                strProcessStep="初始化" 
                strStatus="INITIALIZING" 
                doubleProgress="0.0" 
                strMessage="开始执行完整工作流程" 
                strOperation="初始化工作流程参数" />
            
            <!-- 1. 自检阶段 (0-20%) -->
            <Fallback name="SelfCheckFallback">
                <!-- 正常自检流程 -->
                <Sequence name="SelfCheckSequence">
                    <PubProcessFeedback 
                        strProcessStep="自检阶段" 
                        strStatus="RUNNING" 
                        doubleProgress="5.0" 
                        strMessage="开始执行系统自检" 
                        strOperation="启动自检流程" />
                    <PubPrintMessage strPrintMessage="🔍 开始执行系统自检..." />
                    
                    <!-- 调用自检子树 -->
                    <SubTree ID="LightBoardSelfCheck"/>
                    
                    <PubProcessFeedback 
                        strProcessStep="自检阶段" 
                        strStatus="RUNNING" 
                        doubleProgress="20.0" 
                        strMessage="系统自检完成" 
                        strOperation="自检流程结束" />
                    <PubPrintMessage strPrintMessage="✅ 系统自检完成" />
                </Sequence>
                
                <!-- 自检失败处理 -->
                <Sequence name="SelfCheckFailureHandler">
                    <PubProcessFeedback 
                        strProcessStep="自检阶段" 
                        strStatus="ERROR" 
                        doubleProgress="10.0" 
                        strMessage="系统自检失败" 
                        strOperation="处理自检失败" />
                    <PubPrintMessage strPrintMessage="❌ 系统自检失败" />
                    <PubPrintMessage strPrintMessage="⚠️ 错误日志: 系统自检流程失败" />
                    <ReturnFailure>
                        <AlwaysSuccess/>
                    </ReturnFailure>
                </Sequence>
            </Fallback>
            
            <!-- 2. 移动到料架阶段 (20-40%) -->
            <Fallback name="ToRacksFallback">
                <!-- 正常移动流程 -->
                <Sequence name="ToRacksSequence">
                    <PubProcessFeedback 
                        strProcessStep="移动到料架" 
                        strStatus="RUNNING" 
                        doubleProgress="25.0" 
                        strMessage="开始执行移动到料架任务" 
                        strOperation="启动移动流程" />
                    <PubPrintMessage strPrintMessage="🚗 开始执行移动到料架任务..." />
                    
                    <!-- 调用移动到料架子树 -->
                    <SubTree ID="LightBoardToRacks"/>
                    
                    <PubProcessFeedback 
                        strProcessStep="移动到料架" 
                        strStatus="RUNNING" 
                        doubleProgress="40.0" 
                        strMessage="移动到料架完成" 
                        strOperation="移动流程结束" />
                    <PubPrintMessage strPrintMessage="✅ 移动到料架完成" />
                </Sequence>
                
                <!-- 移动失败处理 -->
                <Sequence name="ToRacksFailureHandler">
                    <PubProcessFeedback 
                        strProcessStep="移动到料架" 
                        strStatus="ERROR" 
                        doubleProgress="30.0" 
                        strMessage="移动到料架失败" 
                        strOperation="处理移动失败" />
                    <PubPrintMessage strPrintMessage="❌ 移动到料架失败" />
                    <PubPrintMessage strPrintMessage="⚠️ 错误日志: 移动到料架流程失败" />
                    <ReturnFailure>
                        <AlwaysSuccess/>
                    </ReturnFailure>
                </Sequence>
            </Fallback>
            
            <!-- 3. 拾取送料阶段 (40-60%) -->
            <Fallback name="PickSendStockFallback">
                <!-- 正常拾取送料流程 -->
                <Sequence name="PickSendStockSequence">
                    <PubProcessFeedback 
                        strProcessStep="拾取送料" 
                        strStatus="RUNNING" 
                        doubleProgress="45.0" 
                        strMessage="开始执行拾取送料任务" 
                        strOperation="启动拾取送料流程" />
                    <PubPrintMessage strPrintMessage="🦾 开始执行拾取送料任务..." />
                    
                    <!-- 调用拾取送料子树 -->
                    <SubTree ID="LightBoardPickSendStock"/>
                    
                    <PubProcessFeedback 
                        strProcessStep="拾取送料" 
                        strStatus="RUNNING" 
                        doubleProgress="60.0" 
                        strMessage="拾取送料完成" 
                        strOperation="拾取送料流程结束" />
                    <PubPrintMessage strPrintMessage="✅ 拾取送料完成" />
                </Sequence>
                
                <!-- 拾取送料失败处理 -->
                <Sequence name="PickSendStockFailureHandler">
                    <PubProcessFeedback 
                        strProcessStep="拾取送料" 
                        strStatus="ERROR" 
                        doubleProgress="50.0" 
                        strMessage="拾取送料失败" 
                        strOperation="处理拾取送料失败" />
                    <PubPrintMessage strPrintMessage="❌ 拾取送料失败" />
                    <PubPrintMessage strPrintMessage="⚠️ 错误日志: 拾取送料流程失败" />
                    <ReturnFailure>
                        <AlwaysSuccess/>
                    </ReturnFailure>
                </Sequence>
            </Fallback>
            
            <!-- 4. 返回安全位置阶段 (60-80%) -->
            <Fallback name="ToSafePointFallback">
                <!-- 正常返回安全位置流程 -->
                <Sequence name="ToSafePointSequence">
                    <PubProcessFeedback 
                        strProcessStep="返回安全位置" 
                        strStatus="RUNNING" 
                        doubleProgress="65.0" 
                        strMessage="开始执行返回安全位置任务" 
                        strOperation="启动返回安全位置流程" />
                    <PubPrintMessage strPrintMessage="🏠 开始执行返回安全位置任务..." />
                    
                    <!-- 调用返回安全位置子树 -->
                    <SubTree ID="LightBoardToSafePoint"/>
                    
                    <PubProcessFeedback 
                        strProcessStep="返回安全位置" 
                        strStatus="RUNNING" 
                        doubleProgress="80.0" 
                        strMessage="返回安全位置完成" 
                        strOperation="返回安全位置流程结束" />
                    <PubPrintMessage strPrintMessage="✅ 返回安全位置完成" />
                </Sequence>
                
                <!-- 返回安全位置失败处理 -->
                <Sequence name="ToSafePointFailureHandler">
                    <PubProcessFeedback 
                        strProcessStep="返回安全位置" 
                        strStatus="ERROR" 
                        doubleProgress="70.0" 
                        strMessage="返回安全位置失败" 
                        strOperation="处理返回安全位置失败" />
                    <PubPrintMessage strPrintMessage="❌ 返回安全位置失败" />
                    <PubPrintMessage strPrintMessage="⚠️ 错误日志: 返回安全位置流程失败" />
                    <ReturnFailure>
                        <AlwaysSuccess/>
                    </ReturnFailure>
                </Sequence>
            </Fallback>
            
            <!-- 5. 重置料仓位置阶段 (80-100%) -->
            <Fallback name="ResetBinPositionFallback">
                <!-- 正常重置料仓位置流程 -->
                <Sequence name="ResetBinPositionSequence">
                    <PubProcessFeedback 
                        strProcessStep="重置料仓位置" 
                        strStatus="RUNNING" 
                        doubleProgress="85.0" 
                        strMessage="开始执行重置料仓位置任务" 
                        strOperation="启动重置料仓位置流程" />
                    <PubPrintMessage strPrintMessage="🔄 开始执行重置料仓位置任务..." />
                    
                    <!-- 调用重置料仓位置子树 -->
                    <SubTree ID="LightBoardResetBinPosition"/>
                    
                    <PubProcessFeedback 
                        strProcessStep="重置料仓位置" 
                        strStatus="FINISHED" 
                        doubleProgress="100.0" 
                        strMessage="重置料仓位置完成，整体工作流程结束" 
                        strOperation="完成所有任务" />
                    <PubPrintMessage strPrintMessage="✅ 重置料仓位置完成" />
                </Sequence>
                
                <!-- 重置料仓位置失败处理 -->
                <Sequence name="ResetBinPositionFailureHandler">
                    <PubProcessFeedback 
                        strProcessStep="重置料仓位置" 
                        strStatus="ERROR" 
                        doubleProgress="90.0" 
                        strMessage="重置料仓位置失败" 
                        strOperation="处理重置料仓位置失败" />
                    <PubPrintMessage strPrintMessage="❌ 重置料仓位置失败" />
                    <PubPrintMessage strPrintMessage="⚠️ 错误日志: 重置料仓位置流程失败" />
                    <ReturnFailure>
                        <AlwaysSuccess/>
                    </ReturnFailure>
                </Sequence>
            </Fallback>
            
            <!-- 最终报告 -->
            <PubPrintMessage strPrintMessage="🎉 Robot1完整工作流程执行完成! 
- 系统自检: ✓ 成功
- 移动到料架: ✓ 成功
- 拾取送料: ✓ 成功
- 返回安全位置: ✓ 成功
- 重置料仓位置: ✓ 成功
- 任务状态: ✓ 完成" />
        </Sequence>
    </BehaviorTree>
</root> 