<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="RobotAlignExample">
        <Sequence name="RobotAlignExample">
          
            <!-- 机器人对齐算法节点示例 -->
            <RobotAlign
                strCameraNamespaces="HJGC"
                strParamOverrides=""
                doubleTimeout="30.0"
                boolOutputSuccess="{robot_align_success}"
                strOutputMessage="{robot_align_message}"
                doubleX="{robot_align_x}"
                doubleY="{robot_align_y}"
                doubleRZ="{robot_align_rz}" />
        </Sequence>
    </BehaviorTree>
</root> 