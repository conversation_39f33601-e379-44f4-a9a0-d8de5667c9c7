<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="BoardAlignExample ">
        <Sequence name="BoardAlignExample">
            <!-- BoardAlign节点 -->
            <BoardAlign
                strCameraNamespaces="HJGC,HJGC"
                intBoardNum="1"
                doubleTimeout="30.0"
                boolOutputSuccess="{board_align_success}"
                strOutputMessage="{board_align_message}"
                boolIsFinish="{board_align_finish}"
                strMoveCommands="{board_align_commands}"
                intMoveCommandsCount="{board_align_commands_count}" />

        </Sequence>
    </BehaviorTree>
</root>