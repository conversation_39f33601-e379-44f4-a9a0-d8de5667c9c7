<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="CommonAlignExample">
        <Sequence name="CommonAlignExample">
          
            <!-- 通用对齐算法节点示例 -->
            <CommonAlign
                strModelPath="/home/<USER>/vision/templates/pcba-pick-0716-2.stp"
                doublePixelDimensions="0.1"
                strHWC=""
                strParamOverrides="camera_device_name=BoardA"
                doubleTimeout="30.0"
                boolOutputSuccess="{common_align_success}"
                strOutputMessage="{common_align_message}"
                doubleX="{common_align_x}"
                doubleY="{common_align_y}"
                doubleRZ="{common_align_rz}" />
        </Sequence>
    </BehaviorTree>
</root> 