<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="ImageDetectionTest">
        <Sequence name="视觉识别测试">
            <!-- 基本机械臂归零识别 -->
            <ImageDetection 
                strDetectType="ROBOT_ALIGN"
                strCameraGroup="workbench-2"
                strNamespace=""
                intTimeoutMs="5000"
                boolOutputSuccess="{detection_success}"
                strOutputErrorCode="{error_code}"
                strOutputMessage="{message}"
                doubleResultX="{offset_x}"
                doubleResultY="{offset_y}"
                doubleResultRz="{offset_rz}"
                strDetectionSummary="{summary}" />
            
            <!-- 检查识别结果 -->
            <Fallback>
                <!-- 识别成功分支 -->
                <Sequence>
                    <Script code="detection_success == true" />
                    <PubPrintMessage strTopicName="✅ 识别成功: {summary}" />
                    <PubPrintMessage strTopicName="📊 偏差值: X={offset_x}, Y={offset_y}, Rz={offset_rz}" />
                </Sequence>
                
                <!-- 识别失败分支 -->
                <Sequence>
                    <PubPrintMessage strTopicName="❌ 识别失败: {error_code} - {message}" />
                    <!-- 使用Script节点替代ReturnFailure -->
                    <Script code="false" />
                </Sequence>
            </Fallback>
        </Sequence>
    </BehaviorTree>
</root> 