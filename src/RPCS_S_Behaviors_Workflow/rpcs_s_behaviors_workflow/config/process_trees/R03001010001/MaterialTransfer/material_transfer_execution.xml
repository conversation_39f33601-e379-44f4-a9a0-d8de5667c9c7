<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="MaterialTransferExecution">
    <Sequence name="MaterialTransferExecutionSequence">
      <PubProcessFeedback doubleProgress="0.0"
                          strMessage="开始物料转移执行流程"
                          strOperation="检查前置条件"
                          strProcessStep="物料转移执行"
                          strStatus="INITIALIZING"/>
      <DigitalOutputWrite boolBatchOperation="false"
                          boolOutputValues="1,1,1"
                          boolVerifyWrite="true"
                          intOutputAddresses="12,13,14"
                          intTimeoutMs="50000"
                          strDeviceId="R03001010001"
                          boolOutputSuccess="{vacuum_pump1_success}"
                          boolFinalValues="{vacuum_pump1_final_values}"
                          boolWriteSuccesses="{vacuum_pump1_write_successes}"
                          intErrorCode="{vacuum_pump1_error_code}"
                          strOutputMessage="{vacuum_pump1_message}"/>
      <Sequence name="TransferOperationSequence">
        <DigitalOutputWrite boolBatchOperation="false"
                            boolOutputValues="0,1"
                            boolVerifyWrite="true"
                            intOutputAddresses="4,5"
                            intTimeoutMs="50000"
                            strDeviceId="R03001010001"
                            boolOutputSuccess="{transfer_cylinder_origin_success}"
                            boolFinalValues="{transfer_cylinder_origin_final_values}"
                            boolWriteSuccesses="{transfer_cylinder_origin_write_successes}"
                            intErrorCode="{transfer_cylinder_origin_error_code}"
                            strOutputMessage="{transfer_cylinder_origin_message}"/>
        <Sequence name="检查原位传感器">
          <DigitalOutputRead boolContinuousRead="true"
                             boolExpectedValues="0,1"
                             doubleDuration="8.0"
                             doubleReadInterval="0.5"
                             intOutputAddresses="4,5"
                             intTimeoutMs="50000"
                             strBlackboardKey=""
                             strDeviceId="R03001010001"
                             boolOutputSuccess="{transfer_cylinder_origin_read_success}"
                             boolOutputValues="{transfer_cylinder_origin_values}"
                             boolValueMatched="{transfer_cylinder_origin_matched}"
                             intErrorCode="{transfer_cylinder_origin_read_error_code}"
                             strOutputMessage="{transfer_cylinder_origin_read_message}"/>
          <Sequence name="检查传感器并执行后续操作">
            <Fallback name="检查传感器值或打印错误">
              <SensorValueCheck expected_value="true"
                                sensor_value="{transfer_cylinder_origin_read_success}"/>
              <Sequence name="打印错误并确保失败">
                <PubPrintMessage strPrintMessage="❌ 错误: 转移气缸未到原位"
                                 strTopicName=""/>
                <PubProcessFeedback doubleProgress="99.0"
                                    strMessage="转移气缸未到原位检测失败"
                                    strOperation="检测转移气缸原位"
                                    strProcessStep="转移气缸原位检查"
                                    strStatus="FAILED"/>
                <AlwaysFailure/>
              </Sequence>
            </Fallback>
            <PubPrintMessage strPrintMessage="✅ 转移气缸已到原位"
                             strTopicName=""/>
          </Sequence>
        </Sequence>
        <PubProcessFeedback doubleProgress="10.0"
                            strMessage="转移气缸原位完成"
                            strOperation="电机7移动到0位置"
                            strProcessStep="物料转移执行"
                            strStatus="RUNNING"/>
        <MotorPositionControl boolAbsolutePosition="true"
                              doubleAcceleration="100.0"
                              doubleDeceleration="100.0"
                              doubleDwellTime="0.5"
                              doubleMaxVelocity="150.0"
                              doubleTargetPosition="-376.0"
                              doubleTimeout="30.0"
                              intMotorId="7"
                              strDeviceId="R03001010001"
                              boolOutputSuccess="{motor7_position100_success}"
                              doubleCurrentPosition="0.0"
                              doubleFinalPosition="0.0"
                              doublePositionError="0.0"
                              doubleProgress="0.0"
                              intErrorCode="0"
                              strOutputMessage="{motor7_position100_message}"/>
        <PubProcessFeedback doubleProgress="15.0"
                            strMessage="电机7移动到0位完成"
                            strOperation="执行转移动作序列"
                            strProcessStep="物料转移执行"
                            strStatus="RUNNING"/>

        <Sequence name="TransferActionSequence">
          <Sequence name="转移气缸动作序列">
            <DigitalOutputWrite boolBatchOperation="false"
                                boolOutputValues="1,0"
                                boolVerifyWrite="true"
                                intOutputAddresses="4,5"
                                intTimeoutMs="50000"
                                strDeviceId="R03001010001"
                                boolOutputSuccess="{transfer_cylinder_down_success}"
                                boolFinalValues="{transfer_cylinder_down_final_values}"
                                boolWriteSuccesses="{transfer_cylinder_down_write_successes}"
                                intErrorCode="{transfer_cylinder_down_error_code}"
                                strOutputMessage="{transfer_cylinder_down_message}"/>
            <PubProcessFeedback doubleProgress="20.0"
                                strMessage="下降转移气缸"
                                strOperation="气缸转移动作"
                                strProcessStep="物料转移执行"
                                strStatus="RUNNING"/>
            <Sequence name="检查下降位传感器">
              <DigitalInputRead boolContinuousRead="true"
                                boolExpectedValues="1,0"
                                doubleDuration="8.0"
                                doubleReadInterval="0.5"
                                intInputAddresses="8,9"
                                intTimeoutMs="50000"
                                strBlackboardKey=""
                                strDeviceId="R03001010001"
                                boolCurrentValues="{tray_transfer_down_values}"
                                boolOutputSuccess="{tray_transfer_down_success}"
                                boolValueMatched="{tray_transfer_down_matched}"
                                intErrorCode="{tray_transfer_down_error_code}"
                                strOutputMessage="{tray_transfer_down_message}"/>
              <PubProcessFeedback doubleProgress="25.0"
                                  strMessage="确认转移气缸下降位"
                                  strOperation="检查气缸转移动作"
                                  strProcessStep="物料转移执行"
                                  strStatus="RUNNING"/>
              <Sequence name="检查传感器并执行后续操作">
                <Fallback name="检查传感器值或打印错误">
                  <SensorValueCheck expected_value="true"
                                    sensor_value="{tray_transfer_down_success}"/>
                  <Sequence name="打印错误并确保失败">
                    <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到下降位"
                                     strTopicName=""/>
                    <PubProcessFeedback doubleProgress="99.0"
                                        strMessage="Tray转移气缸未到下降位检测失败"
                                        strOperation="检测Tray转移气缸下降位"
                                        strProcessStep="Tray转移气缸下降位检查"
                                        strStatus="FAILED"/>
                    <AlwaysFailure/>
                  </Sequence>
                </Fallback>
                <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到下降位"
                                 strTopicName=""/>
              </Sequence>
            </Sequence>
          </Sequence>
          <Wait name="等待1秒"
                intMsec="1000"/>
          <DigitalOutputWrite boolBatchOperation="false"
                              boolOutputValues="0,1"
                              boolVerifyWrite="true"
                              intOutputAddresses="6,7"
                              intTimeoutMs="50000"
                              strDeviceId="R03001010001"
                              boolOutputSuccess="{transfer_vacuum1_success}"
                              boolFinalValues="{transfer_vacuum1_final_values}"
                              boolWriteSuccesses="{transfer_vacuum1_write_successes}"
                              intErrorCode="{transfer_vacuum1_error_code}"
                              strOutputMessage="{transfer_vacuum1_message}"/>
          <PubProcessFeedback doubleProgress="30.0"
                              strMessage="吸真空"
                              strOperation="真空吸取"
                              strProcessStep="物料转移执行"
                              strStatus="RUNNING"/>
          <Sequence name="转移气缸原位序列">
            <DigitalOutputWrite boolBatchOperation="false"
                                boolOutputValues="0,1"
                                boolVerifyWrite="true"
                                intOutputAddresses="4,5"
                                intTimeoutMs="5000"
                                strDeviceId="R03001010001"
                                boolOutputSuccess="{transfer_cylinder_up_success}"
                                boolFinalValues="{transfer_cylinder_up_final_values}"
                                boolWriteSuccesses="{transfer_cylinder_up_write_successes}"
                                intErrorCode="{transfer_cylinder_up_error_code}"
                                strOutputMessage="{transfer_cylinder_up_message}"/>
            <PubProcessFeedback doubleProgress="35.0"
                                strMessage="转移气缸上升"
                                strOperation="转移气缸原位"
                                strProcessStep="物料转移执行"
                                strStatus="RUNNING"/>
            <Wait name="等待1秒"
                  intMsec="1000"/>
            <Sequence name="检查上升位传感器">
              <DigitalInputRead boolContinuousRead="true"
                                boolExpectedValues="1,0"
                                doubleDuration="8.0"
                                doubleReadInterval="0.5"
                                intInputAddresses="9,8"
                                intTimeoutMs="50000"
                                strBlackboardKey=""
                                strDeviceId="R03001010001"
                                boolCurrentValues="{tray_transfer_up_values}"
                                boolOutputSuccess="{tray_transfer_up_success}"
                                boolValueMatched="{tray_transfer_up_matched}"
                                intErrorCode="{tray_transfer_up_error_code}"
                                strOutputMessage="{tray_transfer_up_message}"/>
              <PubProcessFeedback doubleProgress="40.0"
                                  strMessage="确认转移气缸上升位"
                                  strOperation="转移气缸原位检查"
                                  strProcessStep="物料转移执行"
                                  strStatus="RUNNING"/>
              <Sequence name="检查传感器并执行后续操作">
                <Fallback name="检查传感器值或打印错误">
                  <SensorValueCheck expected_value="true"
                                    sensor_value="{tray_transfer_up_success}"/>
                  <Sequence name="打印错误并确保失败">
                    <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到上升位"
                                     strTopicName=""/>
                    <PubProcessFeedback doubleProgress="99.0"
                                        strMessage="Tray转移气缸未到上升位检测失败"
                                        strOperation="检测Tray转移气缸上升位"
                                        strProcessStep="Tray转移气缸上升位检查"
                                        strStatus="FAILED"/>
                    <AlwaysFailure/>
                  </Sequence>
                </Fallback>
                <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到上升位"
                                 strTopicName=""/>
              </Sequence>
            </Sequence>
          </Sequence>
          <MotorPositionControl boolAbsolutePosition="true"
                                doubleAcceleration="100.0"
                                doubleDeceleration="100.0"
                                doubleDwellTime="0.5"
                                doubleMaxVelocity="150.0"
                                doubleTargetPosition="-45.0"
                                doubleTimeout="30.0"
                                intMotorId="7"
                                strDeviceId="R03001010001"
                                boolOutputSuccess="{motor7_position330_success}"
                                doubleCurrentPosition="0.0"
                                doubleFinalPosition="0.0"
                                doublePositionError="0.0"
                                doubleProgress="0.0"
                                intErrorCode="0"
                                strOutputMessage="{motor7_position330_message}"/>
          <PubProcessFeedback doubleProgress="45.0"
                              strMessage="电机7移动到320位置"
                              strOperation="电机定位"
                              strProcessStep="物料转移执行"
                              strStatus="RUNNING"/>


        </Sequence>
        <PubProcessFeedback doubleProgress="50.0"
                            strMessage="第一次转移序列完成，开始第二次转移"
                            strOperation="执行第二次转移"
                            strProcessStep="物料转移执行"
                            strStatus="RUNNING"/>
        <Sequence name="SecondTransferSequence">
          <DigitalOutputWrite boolBatchOperation="false"
                              boolOutputValues="1,0"
                              boolVerifyWrite="true"
                              intOutputAddresses="4,5"
                              intTimeoutMs="5000"
                              strDeviceId="R03001010001"
                              boolOutputSuccess="{transfer_cylinder_second_down_success}"
                              boolFinalValues="{transfer_cylinder_second_down_final_values}"
                              boolWriteSuccesses="{transfer_cylinder_second_down_write_successes}"
                              intErrorCode="{transfer_cylinder_second_down_error_code}"
                              strOutputMessage="{transfer_cylinder_second_down_message}"/>
          <PubProcessFeedback doubleProgress="55.0"
                              strMessage="第二次转移气缸下降"
                              strOperation="第二次转移"
                              strProcessStep="物料转移执行"
                              strStatus="RUNNING"/>
          <Wait name="等待1秒"
                intMsec="1000"/>
          <DigitalOutputWrite boolBatchOperation="false"
                              boolOutputValues="1,0"
                              boolVerifyWrite="true"
                              intOutputAddresses="6,7"
                              intTimeoutMs="5000"
                              strDeviceId="R03001010001"
                              boolOutputSuccess="{transfer_vacuum2_success}"
                              boolFinalValues="{transfer_vacuum2_final_values}"
                              boolWriteSuccesses="{transfer_vacuum2_write_successes}"
                              intErrorCode="{transfer_vacuum2_error_code}"
                              strOutputMessage="{transfer_vacuum2_message}"/>
          <PubProcessFeedback doubleProgress="60.0"
                              strMessage="破真空释放物料"
                              strOperation="真空释放"
                              strProcessStep="物料转移执行"
                              strStatus="RUNNING"/>
          <DigitalOutputWrite boolBatchOperation="false"
                              boolOutputValues="0,1"
                              boolVerifyWrite="true"
                              intOutputAddresses="4,5"
                              intTimeoutMs="5000"
                              strDeviceId="R03001010001"
                              boolOutputSuccess="{transfer_cylinder_final_up_success}"
                              boolFinalValues="{transfer_cylinder_final_up_final_values}"
                              boolWriteSuccesses="{transfer_cylinder_final_up_write_successes}"
                              intErrorCode="{transfer_cylinder_final_up_error_code}"
                              strOutputMessage="{transfer_cylinder_final_up_message}"/>
          <PubProcessFeedback doubleProgress="65.0"
                              strMessage="第二次转移气缸上升"
                              strOperation="第二次转移完成"
                              strProcessStep="物料转移执行"
                              strStatus="RUNNING"/>
        </Sequence>
        <PubProcessFeedback doubleProgress="70.0"
                            strMessage="转移序列完成，开始最终位置调整"
                            strOperation="最终位置调整"
                            strProcessStep="物料转移执行"
                            strStatus="RUNNING"/>
      </Sequence>

        <DigitalOutputWrite boolBatchOperation="false"
                            boolOutputValues="0,0"
                            boolVerifyWrite="true"
                            intOutputAddresses="12,13"
                            intTimeoutMs="50000"
                            strDeviceId="R03001010001"
                            boolOutputSuccess="{vacuum_pump1_success}"
                            boolFinalValues="{vacuum_pump1_final_values}"
                            boolWriteSuccesses="{vacuum_pump1_write_successes}"
                            intErrorCode="{vacuum_pump1_error_code}"
                            strOutputMessage="{vacuum_pump1_message}"/>


      <Parallel name="FinalPositionAdjustment"
                failure_count="1"
                success_count="3">
        <MotorHoming doubleTimeout="50.0"
                     floatSpeedSwitch="100.0"
                     floatSpeedZero="100.0"
                     intHomeOffset="0"
                     intHomingMethod="17"
                     intMotorId="6"
                     intPositionWindow="10"
                     intPositionWindowTime="100"
                     strDeviceId="R03001010001"
                     boolOutputSuccess="{motor6_homing_success}"
                     floatCurrentPosition="0.0"
                     floatFinalPosition="0.0"
                     intCurrentStatus="0"
                     strOutputMessage="{motor6_homing_message}"
                     strStatusDescription=""/>



          <!-- 电机7回零 -->
          <MotorHoming
                  strDeviceId="R03001010001"
                  intMotorId="7"
                  intHomingMethod="18"
                  floatSpeedSwitch="100.0"
                  floatSpeedZero="150.0"
                  intHomeOffset="0"
                  intPositionWindow="10"
                  intPositionWindowTime="100"
                  doubleTimeout="50.0"
                  boolOutputSuccess="{motor7_homing_success}"
                  strOutputMessage="{motor7_homing_message}" />

          <Sequence name="成功并移动电机5">
              <MotorPositionControl boolAbsolutePosition="true"
                                    doubleAcceleration="50.0"
                                    doubleDeceleration="50.0"
                                    doubleDwellTime="0.5"
                                    doubleMaxVelocity="20.0"
                                    doubleTargetPosition="20.0"
                                    doubleTimeout="30.0"
                                    intMotorId="5"
                                    strDeviceId="R03001010001"
                                    boolOutputSuccess="{motor5_relative_success}"
                                    doubleCurrentPosition="0.0"
                                    doubleFinalPosition="0.0"
                                    doublePositionError="0.0"
                                    doubleProgress="0.0"
                                    intErrorCode="0"
                                    strOutputMessage="{motor5_relative_message}"/>
              <PubProcessFeedback doubleProgress="98.0"
                                  strMessage="移动电机5"
                                  strOperation="转移完成"
                                  strProcessStep="物料转移执行"
                                  strStatus="COMPLETED"/>
              <Wait name="等待1秒"
                    intMsec="2000"/>
              <MotorHoming doubleTimeout="50.0"
                           floatSpeedSwitch="50.0"
                           floatSpeedZero="50.0"
                           intHomeOffset="0"
                           intHomingMethod="17"
                           intMotorId="5"
                           intPositionWindow="10"
                           intPositionWindowTime="100"
                           strDeviceId="R03001010001"
                           boolOutputSuccess="{motor5_homing_success}"
                           floatCurrentPosition="0.0"
                           floatFinalPosition="0.0"
                           intCurrentStatus="0"
                           strOutputMessage="{motor5_homing_message}"
                           strStatusDescription=""/>
          </Sequence>

      </Parallel>





      <PubProcessFeedback doubleProgress="100.0"
                          strMessage="物料转移执行完成"
                          strOperation="转移完成"
                          strProcessStep="物料转移执行"
                          strStatus="COMPLETED"/>
      <PubPrintMessage strPrintMessage="✅ 物料转移执行完成，所有操作已完成"
                       strTopicName=""/>
    </Sequence>
  </BehaviorTree>

</root>
