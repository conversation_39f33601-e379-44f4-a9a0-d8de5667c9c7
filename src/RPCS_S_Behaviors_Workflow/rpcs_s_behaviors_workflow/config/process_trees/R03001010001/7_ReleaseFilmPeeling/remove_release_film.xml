<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="RemoveReleaseFilm">
        <Sequence name="MainSequence">
            <PubProcessFeedback
                strProcessStep="离型膜剥离"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="开始移除离型膜任务"
                strOperation="开始移除离型膜任务" />
            <!-- 1. 机械臂吸取载板至离型膜剥离位置 -->
            <PubProcessFeedback
                strProcessStep="离型膜剥离"
                strStatus="RUNNING"
                doubleProgress="0.0"
                strMessage="步骤(1/3)： 机械臂吸取载板至离型膜剥离位置"
                strOperation="步骤(1/3)： 机械臂吸取载板至离型膜剥离位置" />
            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0701"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />
            <!-- 初始化循环计数器 -->
            <Script code="loop_count := 1" name="InitLoopCount" />
            <Script code="progress := 10" name="InitLoopCount" />
            <PubPrintMessage strTopicName="定义循环计数器{loop_count}"
                strPrintMessage="定义循环计数器{loop_count}" />
            <PubProcessFeedback
                strProcessStep="离型膜剥离"
                strStatus="RUNNING"
                doubleProgress="{progress}"
                strMessage="步骤(2/3)： 执行离型膜剥离"
                strOperation="步骤(2/3)： 执行离型膜剥离" />
            <!-- 循环48次（使用标准Sequence+Condition实现带计数传递的循环） -->
            <Repeat num_cycles="48" name="LoopController">
                <Sequence name="PeelActionsWithCount">
                    <!-- 2. 机械臂执行离型膜剥离（传入当前循环次数） -->

                    <Script code="current_msg := '步骤(2/3)： 开始执行离型膜剥离({loop_count}/48)'" />
                    <Script code="progress :=progress+ (loop_count / 48.0) * 80.0"
                        name="CalcProgress" />

                    <PubProcessFeedback
                        strProcessStep="离型膜剥离"
                        strStatus="RUNNING"
                        doubleProgress="{progress}"
                        strMessage="{current_msg}"
                        strOperation="{current_msg}" />

                    <RobotArmControl
                        strDeviceName="/robot1"
                        intProjectId="0702"
                        intFunctionData0="{loop_count}"
                        intTimeoutMs="30000"
                        boolOutputSuccess="{success}"
                        strOutputErrorMessage="{error}"
                        doubleExecutionTime="{time}"
                        strCurrentStatus="{status}" />

                    <RetryUntilSuccess num_retries="3">
                        <PetStrip
                            strOperation="A"
                            doubleTimeout="30.0"
                            boolOutputSuccess="{success}"
                            strErrorCode="{error}"
                            strOutputMessage="{message}"
                            strPlcFeedback="{plc_feedback}"
                        />
                    </RetryUntilSuccess>
                    <!-- 循环计数器自增 -->
                    <Script code="loop_count := loop_count + 1" name="IncrementLoopCount" />
                </Sequence>
            </Repeat>

            <!-- 4. 机械臂回零 -->
            <PubProcessFeedback
                strProcessStep="离型膜剥离"
                strStatus="RUNNING"
                doubleProgress="{progress}"
                strMessage="步骤(3/3)： 机械臂回零"
                strOperation="步骤(3/3)： 机械臂回零" />

            <RobotArmControl
                strDeviceName="/robot1"
                intProjectId="0703"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error}"
                doubleExecutionTime="{time}"
                strCurrentStatus="{status}" />

            <PubProcessFeedback
                strProcessStep="离型膜剥离"
                strStatus="RUNNING"
                doubleProgress="100"
                strMessage="离型膜剥离工艺完成"
                strOperation="离型膜剥离工艺完成" />

        </Sequence>
    </BehaviorTree>
</root>