<?xml version="1.0"?>
<root BTCPP_format="4" main_tree_to_execute="MotorHomingOnly">
    
    <!-- 
    robot1电机回零和速度控制测试
    功能：让robot1的motor_1到motor_4同时执行回零操作，然后进行速度控制
    参数：回零方法17，搜索速度20/20 rpm，速度控制20rpm，电流限制4A
    -->
    
    <BehaviorTree ID="MotorHomingOnly">
        <Sequence name="电机回零测试流程">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="robot1电机回零测试"
                strStatus="开始"
                strMessage="准备执行4个电机同时回零操作"
                doubleProgress="0.0" 
                strOperation="系统初始化" />
            
            <!-- 并行执行6个电机回零 -->
            <Parallel success_count="4" failure_count="1" name="并行回零操作">
                
                <!-- 电机1回零 -->
                <Sequence name="电机1回零">
                    <MotorHoming 
                        strDeviceId="robot1"
                        intMotorId="1"
                        intHomingMethod="17"
                        floatSpeedSwitch="20.0"
                        floatSpeedZero="40.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="60.0"
                        boolOutputSuccess="{motor1_homing_success}"
                        floatFinalPosition="{motor1_final_position}"
                        strOutputMessage="{motor1_message}"
                        intCurrentStatus="{motor1_status}"
                        strStatusDescription="{motor1_status_desc}"
                        floatCurrentPosition="{motor1_current_pos}" />
                    
                    <PubProcessFeedback 
                        strProcessStep="robot1电机回零测试"
                        strStatus="进行中"
                        strMessage="电机1回零完成: {motor1_message}"
                        doubleProgress="8.3" 
                        strOperation="电机1回零操作" />
                </Sequence>
                
                <!-- 电机2回零 -->
                <Sequence name="电机2回零">
                    <MotorHoming 
                        strDeviceId="robot1"
                        intMotorId="2"
                        intHomingMethod="17"
                        floatSpeedSwitch="20.0"
                        floatSpeedZero="40.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="60.0"
                        boolOutputSuccess="{motor2_homing_success}"
                        floatFinalPosition="{motor2_final_position}"
                        strOutputMessage="{motor2_message}"
                        intCurrentStatus="{motor2_status}"
                        strStatusDescription="{motor2_status_desc}"
                        floatCurrentPosition="{motor2_current_pos}" />
                    
                    <PubProcessFeedback 
                        strProcessStep="robot1电机回零测试"
                        strStatus="进行中"
                        strMessage="电机2回零完成: {motor2_message}"
                        doubleProgress="16.7" 
                        strOperation="电机2回零操作" />
                </Sequence>
                
                <!-- 电机3回零 -->
                <Sequence name="电机3回零">
                    <MotorHoming 
                        strDeviceId="robot1"
                        intMotorId="3"
                        intHomingMethod="17"
                        floatSpeedSwitch="20.0"
                        floatSpeedZero="40.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="60.0"
                        boolOutputSuccess="{motor3_homing_success}"
                        floatFinalPosition="{motor3_final_position}"
                        strOutputMessage="{motor3_message}"
                        intCurrentStatus="{motor3_status}"
                        strStatusDescription="{motor3_status_desc}"
                        floatCurrentPosition="{motor3_current_pos}" />
                    
                    <PubProcessFeedback 
                        strProcessStep="robot1电机回零测试"
                        strStatus="进行中"
                        strMessage="电机3回零完成: {motor3_message}"
                        doubleProgress="25.0" 
                        strOperation="电机3回零操作" />

                </Sequence>
                
                <!-- 电机4回零 -->
                <Sequence name="电机4回零">
                    <MotorHoming 
                        strDeviceId="robot1"
                        intMotorId="4"
                        intHomingMethod="17"
                        floatSpeedSwitch="20.0"
                        floatSpeedZero="40.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="60.0"
                        boolOutputSuccess="{motor4_homing_success}"
                        floatFinalPosition="{motor4_final_position}"
                        strOutputMessage="{motor4_message}"
                        intCurrentStatus="{motor4_status}"
                        strStatusDescription="{motor4_status_desc}"
                        floatCurrentPosition="{motor4_current_pos}" />
                    
                    <PubProcessFeedback 
                        strProcessStep="robot1电机回零测试"
                        strStatus="进行中"
                        strMessage="电机4回零完成: {motor4_message}"
                        doubleProgress="33.3" 
                        strOperation="电机4回零操作" />
                </Sequence>
             
            </Parallel>
            
            <!-- 回零完成反馈 -->
            <PubProcessFeedback 
                strProcessStep="robot1电机回零测试"
                strStatus="进行中"
                strMessage="所有4个电机回零操作完成，开始电机1-4速度控制"
                doubleProgress="50.0" 
                strOperation="回零完成，准备速度控制" />

            <!-- 发布最终结果反馈 -->
            <PubProcessFeedback 
                strProcessStep="robot1电机回零测试"
                strStatus="成功"
                strMessage="所有4个电机回零和速度控制操作完成"
                doubleProgress="100.0" 
                strOperation="任务完成" />
            
        </Sequence>
    </BehaviorTree>
    

    
</root> 