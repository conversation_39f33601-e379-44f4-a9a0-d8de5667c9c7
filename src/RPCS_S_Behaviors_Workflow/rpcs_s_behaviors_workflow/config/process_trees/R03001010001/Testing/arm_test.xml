<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 机械臂连续3次请求测试 -->
    <BehaviorTree ID="Robot1ArmTest">
        <Sequence name="连续3次机械臂控制">
            <!-- 第一次请求：工程ID=1 -->
            <Sequence name="第一次请求_工程ID1">
                <RobotArmControl 
                    strDeviceName="/robot1"
                    intProjectId="1"
                    intSpeedMultiplier="80"
                    floatPositionX="100.0"
                    floatPositionY="200.0"
                    floatPositionZ="300.0"
                    floatRotationRx="0.0"
                    floatRotationRy="0.0"
                    floatRotationRz="0.0"
                    intFunctionData0="1"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{success1}"
                    strOutputErrorMessage="{error1}"
                    doubleExecutionTime="{time1}"
                    strCurrentStatus="{status1}" />
                
                <!-- 短暂延时 -->
                <Wait name="等待0.2秒" intMsec="200"/>
            </Sequence>
            
            <!-- 第二次请求：工程ID=2 -->
            <Sequence name="第二次请求_工程ID2">
                <RobotArmControl 
                    strDeviceName="/robot1"
                    intProjectId="2"
                    intSpeedMultiplier="60"
                    floatPositionX="150.0"
                    floatPositionY="250.0"
                    floatPositionZ="350.0"
                    floatRotationRx="10.0"
                    floatRotationRy="20.0"
                    floatRotationRz="30.0"
                    intFunctionData0="0"
                    intFunctionData1="1"
                    intFunctionData2="0"
                    intFunctionData3="1"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="25000"
                    boolOutputSuccess="{success2}"
                    strOutputErrorMessage="{error2}"
                    doubleExecutionTime="{time2}"
                    strCurrentStatus="{status2}" />
                
                <!-- 短暂延时 -->
                <Wait name="等待0.2秒" intMsec="200"/>
            </Sequence>
            
            <!-- 第三次请求：工程ID=3 -->
            <Sequence name="第三次请求_工程ID3">
                <RobotArmControl 
                    strDeviceName="/robot1"
                    intProjectId="3"
                    intSpeedMultiplier="90"
                    floatPositionX="200.0"
                    floatPositionY="300.0"
                    floatPositionZ="400.0"
                    floatRotationRx="45.0"
                    floatRotationRy="90.0"
                    floatRotationRz="180.0"
                    intFunctionData0="1"
                    intFunctionData1="1"
                    intFunctionData2="1"
                    intFunctionData3="0"
                    intFunctionData4="1"
                    intFunctionData5="1"
                    intTimeoutMs="35000"
                    boolOutputSuccess="{success3}"
                    strOutputErrorMessage="{error3}"
                    doubleExecutionTime="{time3}"
                    strCurrentStatus="{status3}" />
            </Sequence>
        </Sequence>
    </BehaviorTree>
</root> 