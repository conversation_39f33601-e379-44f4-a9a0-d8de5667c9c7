<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- IO操作测试行为树 -->
    <!-- 测试数字输入读取、数字输出读取和写入功能 -->
    <BehaviorTree ID="IoTest">
        <Sequence name="IOTestSequence">
            <!-- 测试开始反馈 -->
            <PubProcessFeedback
                strProcessStep="IO测试"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始IO操作测试"
                strOperation="初始化IO测试" />

            <!-- IO操作测试序列 -->
            <Sequence name="IOOperationsSequence">
                
                <!-- 第一步：单次读取数字输入状态 -->
                <Sequence name="DigitalInputSingleReadTestSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输入单次读取测试"
                        strStatus="RUNNING"
                        doubleProgress="5.0"
                        strMessage="正在进行单次数字输入读取"
                        strOperation="读取输入地址0" />
                        
                    <DigitalInputRead 
                        strDeviceId="robot1"
                        intInputAddress="0"
                        intTimeoutMs="5000"
                        boolContinuousRead="false"
                        boolExpectedValue="false"
                        boolOutputSuccess="{input_single_read_success}"
                        boolInputValue="{input_0_single_value}"
                        boolCurrentValue="{input_0_current_value}"
                        strOutputMessage="{input_single_read_message}"
                        intErrorCode="{input_single_read_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输入单次读取结果"
                        strStatus="SUCCESS"
                        doubleProgress="10.0"
                        strMessage="{input_single_read_message}"
                        strOperation="单次输入读取完成" />
                </Sequence>

                <!-- 第二步：连续读取数字输入直到匹配期望值LOW -->
                <Sequence name="DigitalInputContinuousReadLowSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输入连续读取测试(期望LOW)"
                        strStatus="RUNNING"
                        doubleProgress="15.0"
                        strMessage="连续读取输入直到匹配期望值LOW"
                        strOperation="连续读取输入地址0期望LOW" />
                        
                    <DigitalInputRead 
                        strDeviceId="robot1"
                        intInputAddress="0"
                        intTimeoutMs="10000"
                        boolContinuousRead="true"
                        doubleReadInterval="0.2"
                        doubleDuration="0.0"
                        boolExpectedValue="false"
                        boolOutputSuccess="{input_continuous_low_success}"
                        boolInputValue="{input_0_continuous_low_value}"
                        boolCurrentValue="{input_0_current_low_value}"
                        strOutputMessage="{input_continuous_low_message}"
                        intErrorCode="{input_continuous_low_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输入连续读取LOW结果"
                        strStatus="SUCCESS"
                        doubleProgress="20.0"
                        strMessage="{input_continuous_low_message}"
                        strOperation="连续读取LOW完成" />
                </Sequence>

                <!-- 第三步：读取数字输出状态 -->
                <Sequence name="DigitalOutputReadTestSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输出读取测试"
                        strStatus="RUNNING"
                        doubleProgress="25.0"
                        strMessage="正在读取数字输出"
                        strOperation="读取输出地址0" />
                        
                    <DigitalOutputRead 
                        strDeviceId="robot1"
                        intOutputAddress="0"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_read_success}"
                        boolOutputValue="{output_0_value}"
                        strOutputMessage="{output_read_message}"
                        intErrorCode="{output_read_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输出读取结果"
                        strStatus="SUCCESS"
                        doubleProgress="30.0"
                        strMessage="{output_read_message}"
                        strOperation="输出读取完成" />
                </Sequence>

                <!-- 第四步：设置数字输出为HIGH -->
                <Sequence name="DigitalOutputWriteHighSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输出写入测试(HIGH)"
                        strStatus="RUNNING"
                        doubleProgress="35.0"
                        strMessage="正在设置数字输出为HIGH"
                        strOperation="写入输出地址0为HIGH" />
                        
                    <DigitalOutputWrite 
                        strDeviceId="robot1"
                        intOutputAddress="0"
                        boolOutputValue="true"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_write_high_success}"
                        boolFinalValue="{output_0_final_value_high}"
                        strOutputMessage="{output_write_high_message}"
                        intErrorCode="{output_write_high_error}" />

                    <!-- 设置其他输出为LOW -->
                    <DigitalOutputWrite 
                        strDeviceId="robot1"
                        intOutputAddress="11"
                        boolOutputValue="false"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_write_11_success}"
                        boolFinalValue="{output_11_final_value}"
                        strOutputMessage="{output_write_11_message}"
                        intErrorCode="{output_write_11_error}" />

                    <DigitalOutputWrite 
                        strDeviceId="robot1"
                        intOutputAddress="13"
                        boolOutputValue="false"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_write_13_success}"
                        boolFinalValue="{output_13_final_value}"
                        strOutputMessage="{output_write_13_message}"
                        intErrorCode="{output_write_13_error}" />

                    <DigitalOutputWrite 
                        strDeviceId="robot1"
                        intOutputAddress="15"
                        boolOutputValue="false"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_write_15_success}"
                        boolFinalValue="{output_15_final_value}"
                        strOutputMessage="{output_write_15_message}"
                        intErrorCode="{output_write_15_error}" />

                        
                    <PubProcessFeedback
                        strProcessStep="数字输出写入HIGH结果"
                        strStatus="SUCCESS"
                        doubleProgress="45.0"
                        strMessage="{output_write_high_message}"
                        strOperation="输出写入HIGH完成" />
                </Sequence>

                <!-- 第五步：连续读取数字输入直到匹配期望值HIGH -->
                <Sequence name="DigitalInputContinuousReadHighSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输入连续读取测试(期望HIGH)"
                        strStatus="RUNNING"
                        doubleProgress="50.0"
                        strMessage="连续读取输入直到匹配期望值HIGH"
                        strOperation="连续读取输入地址0期望HIGH" />
                        
                    <DigitalInputRead 
                        strDeviceId="robot1"
                        intInputAddress="0"
                        intTimeoutMs="15000"
                        boolContinuousRead="true"
                        doubleReadInterval="0.1"
                        doubleDuration="0.0"
                        boolExpectedValue="true"
                        boolOutputSuccess="{input_continuous_high_success}"
                        boolInputValue="{input_0_continuous_high_value}"
                        boolCurrentValue="{input_0_current_high_value}"
                        strOutputMessage="{input_continuous_high_message}"
                        intErrorCode="{input_continuous_high_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输入连续读取HIGH结果"
                        strStatus="SUCCESS"
                        doubleProgress="60.0"
                        strMessage="{input_continuous_high_message}"
                        strOperation="连续读取HIGH完成" />
                </Sequence>

                <!-- 第六步：连续读取数字输入指定时间（5秒） -->
                <Sequence name="DigitalInputTimedReadSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输入定时连续读取测试"
                        strStatus="RUNNING"
                        doubleProgress="65.0"
                        strMessage="连续读取输入5秒"
                        strOperation="定时连续读取输入地址0" />
                        
                    <DigitalInputRead 
                        strDeviceId="robot1"
                        intInputAddress="0"
                        intTimeoutMs="10000"
                        boolContinuousRead="true"
                        doubleReadInterval="0.5"
                        doubleDuration="5.0"
                        boolExpectedValue="false"
                        boolOutputSuccess="{input_timed_read_success}"
                        boolInputValue="{input_0_timed_value}"
                        boolCurrentValue="{input_0_current_timed_value}"
                        strOutputMessage="{input_timed_read_message}"
                        intErrorCode="{input_timed_read_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输入定时连续读取结果"
                        strStatus="SUCCESS"
                        doubleProgress="75.0"
                        strMessage="{input_timed_read_message}"
                        strOperation="定时连续读取完成" />
                </Sequence>

                <!-- 第七步：等待一段时间 -->
                <Sequence name="WaitSequence">
                    <PubProcessFeedback
                        strProcessStep="等待"
                        strStatus="RUNNING"
                        doubleProgress="80.0"
                        strMessage="等待2秒"
                        strOperation="延时等待" />
                        
                    <Wait name="等待2秒" intMsec="2000"/>
                        
                    <PubProcessFeedback
                        strProcessStep="等待完成"
                        strStatus="SUCCESS"
                        doubleProgress="85.0"
                        strMessage="等待完成"
                        strOperation="延时结束" />
                </Sequence>

                <!-- 第八步：设置数字输出为LOW -->
                <Sequence name="DigitalOutputWriteLowSequence">
                    <PubProcessFeedback
                        strProcessStep="数字输出写入测试(LOW)"
                        strStatus="RUNNING"
                        doubleProgress="90.0"
                        strMessage="正在设置数字输出为LOW"
                        strOperation="写入输出地址0为LOW" />
                        
                    <DigitalOutputWrite 
                        strDeviceId="robot1"
                        intOutputAddress="0"
                        boolOutputValue="false"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{output_write_low_success}"
                        boolFinalValue="{output_0_final_value_low}"
                        strOutputMessage="{output_write_low_message}"
                        intErrorCode="{output_write_low_error}" />
                        
                    <PubProcessFeedback
                        strProcessStep="数字输出写入LOW结果"
                        strStatus="SUCCESS"
                        doubleProgress="95.0"
                        strMessage="{output_write_low_message}"
                        strOperation="输出写入LOW完成" />
                </Sequence>

            </Sequence>

            <!-- 测试完成反馈 -->
            <PubProcessFeedback
                strProcessStep="IO测试完成"
                strStatus="SUCCESS"
                doubleProgress="100.0"
                strMessage="IO操作测试全部完成"
                strOperation="测试结束" />

        </Sequence>
    </BehaviorTree>
</root> 