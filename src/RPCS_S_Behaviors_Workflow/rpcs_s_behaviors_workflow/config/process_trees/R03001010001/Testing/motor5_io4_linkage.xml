<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="Motor5IO4Linkage">
        <Sequence name="Main">
            <!-- 直接进行速度控制和IO监控 -->
            <!-- 移动到安全位 -->
            <Sequence name="移动到原点">
                <!-- 电机5回零 -->
                <Sequence name="电机5回零">
                    <MotorHoming 
                        strDeviceId="robot1"
                        intMotorId="5"
                        intHomingMethod="18"
                        floatSpeedSwitch="30.0"
                        floatSpeedZero="100.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="60.0"
                        boolOutputSuccess="{motor5_homing_success}"
                        floatFinalPosition="{motor5_final_position}"
                        strOutputMessage="{motor5_message}"
                        intCurrentStatus="{motor5_status}"
                        strStatusDescription="{motor5_status_desc}"
                        floatCurrentPosition="{motor5_current_pos}" />
                    
                    <PubProcessFeedback 
                        strProcessStep="Robot1电机回零测试"
                        strStatus="进行中"
                        strMessage="电机5回零完成: {motor5_message}"
                        doubleProgress="35.7" 
                        strOperation="电机5回零操作" />
                </Sequence>
            </Sequence>
            <Parallel success_count="1" failure_count="1">
                                
                <!-- IO输入4监控任务 -->
                <DigitalInputRead
                    strDeviceId="robot1"
                    intInputAddress="4"
                    intTimeoutMs="10000"
                    boolContinuousRead="true"
                    doubleReadInterval="0.1"
                    doubleDuration="30.0"
                    boolExpectedValue="true"
                    strBlackboardKey="motor5_stop_signal"
                    boolOutputSuccess="{io4_success}"
                    boolCurrentValue="{io4_current_value}"
                    strOutputMessage="{io4_message}"
                    intErrorCode="{io4_error_code}"
                />
                <!-- 电机5速度控制任务 -->
                <MotorVelocityControl
                    strDeviceId="robot1"
                    intMotorId="5"
                    doubleTargetVelocity="-10.0"
                    doubleAcceleration="30.0"
                    doubleDeceleration="30.0"
                    doubleDuration="20.0"
                    doubleTargetCurrentLimit="15.0"
                    boolUsePositionLimits="false"
                    doubleMinPosition="-200.0"
                    doubleMaxPosition="200.0"
                    doubleTimeout="100.0"
                    strStopSignalKey="motor5_stop_signal"
                    boolOutputSuccess="{motor5_success}"
                    strOutputMessage="{motor5_message}"
                    intErrorCode="{motor5_error_code}"
                    doubleCurrentVelocity="{current_velocity}"
                    doubleElapsedTime="{elapsed_time}"
                />

            </Parallel>
        </Sequence>
    </BehaviorTree>
</root>
