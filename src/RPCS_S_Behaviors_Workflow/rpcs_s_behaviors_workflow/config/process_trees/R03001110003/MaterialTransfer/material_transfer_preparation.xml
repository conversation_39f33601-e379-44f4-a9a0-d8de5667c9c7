<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 物料转移前置准备行为树 -->
    <BehaviorTree ID="MaterialTransferPreparation">
        <Sequence name="MaterialTransferPreparationSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始物料转移前置准备流程"
                strOperation="初始化系统" />
            

                <!-- 气泵启动 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001110003"
                    intOutputAddresses="14"
                    boolOutputValues="1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{storage_cylinder2_action_success}"
                    strOutputMessage="{storage_cylinder2_action_message}" />


            
                <!-- 存储池转移气缸原位 -->
                <Sequence name="转移气缸原位序列">
                    <DigitalOutputWrite 
                        strDeviceId="R03001110003"
                        intOutputAddresses="5,4"
                        boolOutputValues="1,0"
                        boolVerifyWrite="true"
                        intTimeoutMs="5000"
                        boolOutputSuccess="{transfer_cylinder_origin_success}"
                        strOutputMessage="{transfer_cylinder_origin_message}" />
                    
                    <!-- 检查转移气缸原位传感器 -->
                    <Sequence name="检查原位传感器">
                        <DigitalOutputRead
                            strDeviceId="R03001110003"
                            intOutputAddresses="5,4"
                            boolExpectedValues="1,0"
                            boolContinuousRead="true" 
                            doubleReadInterval="0.5"
                            doubleDuration="8.0"
                            intTimeoutMs="5000"
                            boolOutputSuccess="{transfer_cylinder_origin_read_success}"
                            boolOutputValues="{transfer_cylinder_origin_values}"
                            strOutputMessage="{transfer_cylinder_origin_read_message}" />

                        <!-- 检查传感器结果 -->
                        <Sequence name="检查传感器并执行后续操作">
                            <!-- 检查传感器 -->
                            <Fallback name="检查传感器值或打印错误">
                                <SensorValueCheck sensor_value="{transfer_cylinder_origin_read_success}" expected_value="true" />
                                <Sequence name="打印错误并确保失败">
                                    <PubPrintMessage strPrintMessage="❌ 错误: 转移气缸未到原位" />
                                    <PubProcessFeedback 
                                        strProcessStep="转移气缸原位检查"
                                        strStatus="FAILED"
                                        doubleProgress="99.0"
                                        strMessage="转移气缸未到原位检测失败"
                                        strOperation="检测转移气缸原位" />
                                    <AlwaysFailure />
                                </Sequence>
                            </Fallback>
                            
                            <PubPrintMessage strPrintMessage="✅ 转移气缸已到原位" />
                        </Sequence>
                    </Sequence>
                    
                    <!-- 检查Tray转移气缸上升位 -->
                    <Sequence name="检查上升位传感器">
                        <DigitalInputRead 
                            strDeviceId="R03001110003"
                            intInputAddresses="8,9"
                            boolExpectedValues="0,1"
                            intTimeoutMs="5000"
                            boolContinuousRead="true"
                            doubleReadInterval="0.5"
                            doubleDuration="20.0"
                            boolOutputSuccess="{tray_transfer_up_success}"
                            boolCurrentValues="{tray_transfer_up_values}"
                            strOutputMessage="{tray_transfer_up_message}" />
                        
                        <!-- 检查传感器结果 -->
                        <Sequence name="检查传感器并执行后续操作">
                            <!-- 检查传感器 -->
                            <Fallback name="检查传感器值或打印错误">
                                <SensorValueCheck sensor_value="{tray_transfer_up_success}" expected_value="true" />
                                <Sequence name="打印错误并确保失败">
                                    <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到上升位" />
                                    <PubProcessFeedback 
                                        strProcessStep="Tray转移气缸上升位检查"
                                        strStatus="FAILED"
                                        doubleProgress="99.0"
                                        strMessage="Tray转移气缸未到上升位检测失败"
                                        strOperation="检测Tray转移气缸上升位" />
                                    <AlwaysFailure />
                                </Sequence>
                            </Fallback>
                            
                            <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到上升位" />
                        </Sequence>
                    </Sequence>
                </Sequence>


            <!-- 第一步：并行执行电机回零和气缸原位 -->
            <Parallel success_count="1" failure_count="1" name="初始化并行操作">
                <!-- 存储池气缸1\2回原 -->
                <!-- 电机5、6、7回零 -->
                <Parallel success_count="4" failure_count="1" name="电机回零组">

                    <DigitalOutputWrite
                            strDeviceId="R03001110003"
                            intOutputAddresses="0,1,2,3"
                            boolOutputValues="0,1,0,1"
                            boolVerifyWrite="true"
                            intTimeoutMs="50000"
                            boolOutputSuccess="{storage_cylinder1_action_success}"
                            strOutputMessage="{storage_cylinder1_action_message}" />

                    <!-- 电机5回零 -->
                    <MotorHoming 
                        strDeviceId="R03001110003"
                        intMotorId="5"
                        intHomingMethod="18"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="150.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor5_homing_success}"
                        strOutputMessage="{motor5_homing_message}" />
                    
                    <!-- 电机6回零 -->
                    <MotorHoming 
                        strDeviceId="R03001110003"
                        intMotorId="6"
                        intHomingMethod="18"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="150.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor6_homing_success}"
                        strOutputMessage="{motor6_homing_message}" />
                    
                    <!-- 电机7回零 -->
                    <MotorHoming 
                        strDeviceId="R03001110003"
                        intMotorId="7"
                        intHomingMethod="18"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="150.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor7_homing_success}"
                        strOutputMessage="{motor7_homing_message}" />
                </Parallel>
                

            </Parallel>

            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="RUNNING"
                doubleProgress="25.0"
                strMessage="回零和初始化完成，开始气缸动作"
                strOperation="执行气缸动作" />

            <!-- 第二步：串行执行存储池气缸动作 -->
            <Sequence name="存储池气缸动作序列">
                
                <!-- 存储池气缸1动作 -->
                <Sequence name="转移气缸动作序列">
                    <DigitalOutputWrite 
                        strDeviceId="R03001110003"
                        intOutputAddresses="4,5"
                        boolOutputValues="0,1"
                        boolVerifyWrite="true"
                        intTimeoutMs="100000"
                        boolOutputSuccess="{transfer_cylinder_action_success}"
                        strOutputMessage="{transfer_cylinder_action_message}" />
                    
                    <!-- 检查Tray转移气缸下降位 -->
                    <Sequence name="检查下降位传感器">
                        <DigitalInputRead 
                            strDeviceId="R03001110003"
                            intInputAddresses="8,9"
                            boolExpectedValues="0,1"
                            intTimeoutMs="10000"
                            boolContinuousRead="true"
                            doubleReadInterval="0.5"
                            doubleDuration="8.0"
                            boolOutputSuccess="{tray_transfer_down_success}"
                            boolCurrentValues="{tray_transfer_down_values}"
                            strOutputMessage="{tray_transfer_down_message}" />
                        
                        <!-- 检查传感器结果 -->
                        <Sequence name="检查传感器并执行后续操作">
                            <!-- 检查传感器 -->
                            <Fallback name="检查传感器值或打印错误">
                                <SensorValueCheck sensor_value="{tray_transfer_down_success}" expected_value="true" />
                                <Sequence name="打印错误并确保失败">
                                    <PubPrintMessage strPrintMessage="❌ 错误: Tray转移气缸未到下降位" />
                                    <PubProcessFeedback 
                                        strProcessStep="Tray转移气缸下降位检查"
                                        strStatus="FAILED"
                                        doubleProgress="99.0"
                                        strMessage="Tray转移气缸未到下降位检测失败"
                                        strOperation="检测Tray转移气缸下降位" />
                                    <AlwaysFailure />
                                </Sequence>
                            </Fallback>
                            
                            <PubPrintMessage strPrintMessage="✅ Tray转移气缸已到下降位" />
                        </Sequence>
                    </Sequence>
                </Sequence>

                <!-- 存储池气缸1\2动作 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001110003"
                    intOutputAddresses="0,1,2,3"
                    boolOutputValues="1,0,1,0"
                    boolVerifyWrite="true"
                    intTimeoutMs="100000"
                    boolOutputSuccess="{storage_cylinder1_action_success}"
                    strOutputMessage="{storage_cylinder1_action_message}" />

            </Sequence>

            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="RUNNING"
                doubleProgress="50.0"
                strMessage="气缸动作完成，开始气缸复位"
                strOperation="气缸复位操作" />

            <!-- 第三步：串行执行存储池气缸原位 -->
            <Sequence name="存储池气缸动作序列">
                
                <!-- 存储池气缸1\2回原 -->
                <DigitalOutputWrite 
                    strDeviceId="R03001110003"
                    intOutputAddresses="0,1,2,3"
                    boolOutputValues="0,1,0,1"
                    boolVerifyWrite="true"
                    intTimeoutMs="50000"
                    boolOutputSuccess="{storage_cylinder1_action_success}"
                    strOutputMessage="{storage_cylinder1_action_message}" />
                

            </Sequence>

            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="RUNNING"
                doubleProgress="75.0"
                strMessage="气缸复位完成，开始电机位置控制"
                strOperation="电机位置控制" />

 

            <!-- 第五步：并行执行电机回零 -->
            <Parallel success_count="3" failure_count="1" name="电机回零组">
                    <MotorHoming 
                        strDeviceId="R03001110003"
                        intMotorId="5"
                        intHomingMethod="17"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="150.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor5_homing_success}"
                        strOutputMessage="{motor5_homing_message}" />
                    
                    <!-- 电机6回零 -->
                    <MotorHoming 
                        strDeviceId="R03001110003"
                        intMotorId="6"
                        intHomingMethod="17"
                        floatSpeedSwitch="100.0"
                        floatSpeedZero="150.0"
                        intHomeOffset="0"
                        intPositionWindow="10"
                        intPositionWindowTime="100"
                        doubleTimeout="50.0"
                        boolOutputSuccess="{motor6_homing_success}"
                        strOutputMessage="{motor6_homing_message}" />

                        
                <!-- 电机7位置控制 -->
                <MotorPositionControl 
                    strDeviceId="R03001110003"
                    intMotorId="7"
                    doubleTargetPosition="-38.0"
                    boolAbsolutePosition="true"
                    doubleMaxVelocity="150.0"
                    doubleAcceleration="100.0"
                    doubleDeceleration="100.0"
                    doubleDwellTime="0.5"
                    doubleTimeout="40.0"
                    boolOutputSuccess="{motor7_position_success}"
                    strOutputMessage="{motor7_position_message}" />
            </Parallel>

            <!-- 完成反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移前置准备"
                strStatus="COMPLETED"
                doubleProgress="100.0"
                strMessage="物料转移前置准备完成"
                strOperation="准备完成" />
            
            <PubPrintMessage strPrintMessage="✅ 物料转移前置准备完成，系统已就绪" />
            
        </Sequence>
    </BehaviorTree>
</root> 