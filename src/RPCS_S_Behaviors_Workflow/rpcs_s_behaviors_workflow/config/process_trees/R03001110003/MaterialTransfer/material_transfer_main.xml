<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 物料转移主行为树 -->
    <BehaviorTree ID="MaterialTransferMain">
        <Sequence name="MaterialTransferMainSequence">
            
            <!-- 初始化反馈 -->
            <PubProcessFeedback 
                strProcessStep="物料转移主流程"
                strStatus="INITIALIZING"
                doubleProgress="0.0"
                strMessage="开始物料转移主流程"
                strOperation="初始化系统" />
            
            <!-- 第一阶段：物料转移前置准备 -->
            <Sequence name="PreparationPhase">
                <PubPrintMessage strPrintMessage="🔄 开始物料转移前置准备阶段" />
                
                <!-- 修改所有设备ID为R03001110003 -->
                <SubTree ID="MaterialTransferPreparation">
                    <Remap From="robot1" To="R03001110003" />
                </SubTree>
                
                <PubProcessFeedback 
                    strProcessStep="物料转移主流程"
                    strStatus="RUNNING"
                    doubleProgress="33.0"
                    strMessage="前置准备完成，开始机械臂操作"
                    strOperation="机械臂操作" />
            </Sequence>
            
            <!-- 第二阶段：机械臂操作 -->
            <Sequence name="RobotArmPhase">
                <PubPrintMessage strPrintMessage="🤖 开始机械臂操作阶段" />
                
                <!-- 使用机械臂控制节点，ID=16 -->
                <RobotArmControl 
                    strDeviceName="R03001110003"
                    intProjectId="4"
                    intSpeedMultiplier="50"
                    floatPositionX="0.0"
                    floatPositionY="0.0"
                    floatRotationRz="0.0"
                    intFunctionData0="0"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="60000"
                    boolOutputSuccess="{arm_success}"
                    strOutputMessage="{arm_message}" />
                
                <!-- 检查机械臂操作结果 -->
                <Fallback>
                    <Sequence>
                        <SensorValueCheck sensor_value="{arm_success}" expected_value="1" />
                        <PubPrintMessage strPrintMessage="✅ 机械臂操作成功完成" />
                    </Sequence>
                    <Sequence>
                        <PubPrintMessage strPrintMessage="❌ 机械臂操作失败: {arm_message}" />
                        <!-- 使用ReturnFailure替代Script节点 -->
                        <ReturnFailure />
                    </Sequence>
                </Fallback>
                
                <PubProcessFeedback 
                    strProcessStep="物料转移主流程"
                    strStatus="RUNNING"
                    doubleProgress="66.0"
                    strMessage="机械臂操作完成，开始执行阶段"
                    strOperation="执行转移" />
            </Sequence>
            
            <!-- 第三阶段：物料转移执行 -->
            <Sequence name="ExecutionPhase">
                <PubPrintMessage strPrintMessage="🔄 开始物料转移执行阶段" />
                
                <!-- 修改所有设备ID为R03001110003 -->
                <SubTree ID="MaterialTransferExecution">
                    <Remap From="robot1" To="R03001110003" />
                </SubTree>
                
                <PubProcessFeedback 
                    strProcessStep="物料转移主流程"
                    strStatus="COMPLETED"
                    doubleProgress="100.0"
                    strMessage="物料转移全部完成"
                    strOperation="流程完成" />
            </Sequence>
            
            <PubPrintMessage strPrintMessage="✅ 物料转移主流程全部完成" />
            
        </Sequence>
    </BehaviorTree>
</root> 