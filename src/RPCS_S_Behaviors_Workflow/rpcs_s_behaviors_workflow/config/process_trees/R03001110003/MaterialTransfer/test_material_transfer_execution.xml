<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="TestMaterialTransferExecution">
    <Sequence name="TestSequence">
      <DigitalInputRead boolContinuousRead="true"
                        boolExpectedValues="1"
                        doubleDuration="5"
                        doubleReadInterval="0.5"
                        intInputAddresses="3"
                        intTimeoutMs="5000"
                        strBlackboardKey=""
                        strDeviceId="R03001110003"
                        boolCurrentValues="{air_pressure_output_values}"
                        boolOutputSuccess="{air_pressure_output_success}"
                        boolValueMatched="{air_pressure_output_matched}"
                        intErrorCode="{air_pressure_output_error_code}"
                        strOutputMessage="{air_pressure_output_message}"/>
      
      <MotorPositionControl boolAbsolutePosition="true"
                            doubleAcceleration="100.0"
                            doubleDeceleration="100.0"
                            doubleDwellTime="0.5"
                            doubleMaxVelocity="50.0"
                            doubleTargetPosition="-364.0"
                            doubleTimeout="60.0"
                            intMotorId="7"
                            strDeviceId="R03001110003"
                            strMotorBrand="Kinco"
                            boolOutputSuccess="{motor7_position_success}"
                            doubleCurrentPosition="{motor7_current_position}"
                            doubleFinalPosition="{motor7_final_position}"
                            doublePositionError="{motor7_position_error}"
                            doubleProgress="{motor7_progress}"
                            intErrorCode="{motor7_error_code}"
                            strOutputMessage="{motor7_message}"/>
    </Sequence>
  </BehaviorTree>
</root>
