<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 保护壳固定主树 -->
    <BehaviorTree ID="FastenProCase">
        <Fallback name="FastenProCaseFallback">
            <Sequence name="主流程">
                <Script code="shell_count := 1" name="shellCount" />
                <Script code="progress_count := 0" name="progressCount" />

                <PubProcessFeedback
                strProcessStep="保护壳固定任务"
                strStatus="RUNNING"
                doubleProgress="0.0 "
                strMessage="开始执行保护壳固定任务"
                strOperation="开始执行保护壳固定任务" />
        
                <Repeat num_cycles="2">
                    <Sequence name="保护壳固定流程">
                        <Script code="progress_count := progress_count + 15" name="progressCount" />
                        <PubProcessFeedback
                        strProcessStep="保护壳规正"
                        strStatus="RUNNING"
                        doubleProgress="{progress_count}"
                        strMessage="摆放保护壳到规正位"
                        strOperation="摆放保护壳到规正位" />

                        <!-- <RobotArmControl
                        strDeviceName="/R03001010003"
                        intSpeedMultiplier="20"
                        intFunctionData0="{shell_count}"
                        intProjectId="121"
                        intTimeoutMs="120"/> -->

                       	<Wait name="等待10秒" intMsec="10000"/>
                        <Script code="progress_count := progress_count + 15" name="progressCount" />
                        <PubProcessFeedback
                        strProcessStep="保护壳规正"
                        strStatus="RUNNING"
                        doubleProgress="{progress_count}"
                        strMessage="虚拟机器人执行规正"
                        strOperation="虚拟机器人调用PLC执行规正" />

                        <!-- <ProtectShell 
                            boolOutputSuccess="{success}"
                            strOutputMessage="{error_message}"
                            strPlcFeedback="{plc_feedback}"/> -->

                        <Wait name="等5秒" intMsec="5000"/>
                        <Script code="progress_count := progress_count + 15" name="progressCount" />
                        <PubProcessFeedback
                        strProcessStep="执行保护壳锁付"
                        strStatus="RUNNING"
                        doubleProgress="{progress_count}"
                        strMessage="执行保护壳锁付"
                        strOperation="执行保护壳锁付" />

                        <!-- <RobotArmControl
                        strDeviceName="/R03001010003"
                        intSpeedMultiplier="20"
                        intFunctionData0="{shell_count}"
                        intProjectId="121"
                        intTimeoutMs="120"/> -->
                    </Sequence>
                </Repeat>

                <AlwaysSuccess>
                    <!-- <RobotArmControl
                            strDeviceName="/R03001010003"
                            intSpeedMultiplier="20"
                            intProjectId="123"
                            intTimeoutMs="120"/> -->
                         <PubProcessFeedback                              
                        strProcessStep="机械臂工程清零"
                        strStatus="RUNNING"
                        doubleProgress="100"
                        strMessage="机械臂工程清零"
                        strOperation="机械臂工程清零" />        
                </AlwaysSuccess>
            </Sequence>
            <Sequence name="保护壳固定失败">
                <PubProcessFeedback
                    strProcessStep="保护壳固定失败"
                    strStatus="ERROR"
                    doubleProgress="0.0"
                    strMessage="保护壳固定失败"
                    strOperation="保护壳固定失败" />
            </Sequence>
        </Fallback>
    </BehaviorTree>

</root>
