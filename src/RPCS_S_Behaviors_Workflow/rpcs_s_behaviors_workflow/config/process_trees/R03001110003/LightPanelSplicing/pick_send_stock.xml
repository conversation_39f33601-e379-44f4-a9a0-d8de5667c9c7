<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
	<!-- 取送料 -->
	<!-- 在取料点位，完成右料仓空tray盘放置 和 左料仓物料盘获取 -->
	<!-- 即控制两个电机，完成空tray池降落和原料tray抬升 -->
	<!-- 左右料仓电机动作 -->
	<!-- 不同小车差异说明-取送料，左物料仓抬升高度、速度、超时时间有差别 -->
	<BehaviorTree ID="LightBoardPickSendStock">
		<Sequence name="PickSendStockSequence">
			<PubProcessFeedback
				strProcessStep="取送料"
				strStatus="INITIALIZING"
				doubleProgress="0.0"
				strMessage="取送料"
				strOperation="开始取送料任务" />

			<!-- 取送料 -->
			<Fallback name="PickSendStockFallback">
				<Sequence name="PickSendStockSequence">
					<!-- 取送料第一阶段 -->
					<!-- 右料仓空tray盘放置-即空tray池降落 -->
					<PubProcessFeedback
						strProcessStep="右料仓空tray盘放置"
						strStatus="RUNNING"
						doubleProgress="0.0"
						strMessage="右料仓空tray盘放置"
						strOperation="右料仓空tray盘放置" />
						
					<MotorHoming 
						strDeviceId="Robot1"
						intMotorId="5"
						intHomingMethod="18"
						floatSpeedSwitch="30.0"
						floatSpeedZero="100.0"
						intHomeOffset="0"
						intPositionWindow="10"
						intPositionWindowTime="100"
						doubleTimeout="60.0"
						boolOutputSuccess="{motor6_homing_success}"
						floatFinalPosition="{motor6_final_position}"
						strOutputMessage="{motor6_message}"
						intCurrentStatus="{motor6_status}"
						strStatusDescription="{motor6_status_desc}"
						floatCurrentPosition="{motor6_current_pos}" />

					<!-- 取送料第二阶段 -->
					<!-- 左料仓物料盘获取-即原料tray抬升 -->
					<PubProcessFeedback
						strProcessStep="左料仓物料盘获取"
						strStatus="RUNNING"
						doubleProgress="50.0"
						strMessage="左料仓物料盘获取"
						strOperation="左料仓物料盘获取" />

					<MotorPositionControl
						strDeviceId="Robot1"
						intMotorId="6"
						doubleTargetPosition="-150.0"
						doubleMaxVelocity="100.0"
						doubleAcceleration="50.0"
						doubleDeceleration="50.0"
						doubleDwellTime="1.0"
						doubleTimeout="60.0"
						boolOutputSuccess="{motor6_position_success}"
						doubleFinalPosition="{motor6_position_final_position}"
						strOutputMessage="{motor6_position_message}"
						intErrorCode="{motor6_position_error_code}"
						doubleCurrentPosition="{motor6_position_current_position}" />

					<PubProcessFeedback
						strProcessStep="左料仓物料盘获取"
						strStatus="RUNNING"
						doubleProgress="100.0"
						strMessage="左料仓物料盘获取完毕"
						strOperation="左料仓物料盘获取" />
				</Sequence>

				<!-- 取送料失败处理 -->
				<Sequence name="PickSendStockFailureHandler">
					<PubProcessFeedback
						strProcessStep="取送料"
						strStatus="ERROR"
						doubleProgress="50.0"
						strMessage="取送料失败"
						strOperation="取送料失败" />
					<ReturnFailure>
						<AlwaysSuccess/>
					</ReturnFailure>
				</Sequence>
			</Fallback>

			<!-- 取送料完成 -->
			<PubProcessFeedback
				strProcessStep="取送料"
				strStatus="FINISHED"
				doubleProgress="100.0"
				strMessage="取送料完成"
				strOperation="取送料任务结束" />
		</Sequence>
	</BehaviorTree>
</root>
