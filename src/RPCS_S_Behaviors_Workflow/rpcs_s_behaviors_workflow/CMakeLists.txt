cmake_minimum_required(VERSION 3.10)
project(rpcs_s_behaviors_workflow)

## Use C++17
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

## Export compile commands for clangd
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

#######################
## Find dependencies ##
#######################

find_package(ament_cmake_auto REQUIRED)
find_package(std_srvs REQUIRED)
find_package(nlohmann_json REQUIRED)

# 添加当前工作空间的install路径
list(APPEND CMAKE_PREFIX_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install")

# 添加rpcs_s_interfaces_io_board包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/rpcs_s_interfaces_io_board/share/rpcs_s_interfaces_io_board/cmake")
find_package(rpcs_s_interfaces_io_board QUIET)

# 添加rpcs_interfaces_motor包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/rpcs_interfaces_motor/share/rpcs_interfaces_motor/cmake")
find_package(rpcs_interfaces_motor QUIET)

# 添加vir_robot_interfaces包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/vir_robot_interfaces/share/vir_robot_interfaces/cmake")
find_package(vir_robot_interfaces QUIET)

# 添加algorithm_interface包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/algorithm_interface/share/algorithm_interface/cmake")
find_package(algorithm_interface QUIET)

# 添加hikvision_interface包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/hikvision_interface/share/hikvision_interface/cmake")
find_package(hikvision_interface QUIET)

# 添加rpcs_s_interfaces_agv包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/rpcs_s_interfaces_agv/share/rpcs_s_interfaces_agv/cmake")
find_package(rpcs_s_interfaces_agv QUIET)

# 添加rpcs_s_interfaces_behavior_tree包
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../../install/rpcs_s_interfaces_behavior_tree/share/rpcs_s_interfaces_behavior_tree/cmake")
find_package(rpcs_s_interfaces_behavior_tree QUIET)

# 查找 yaml-cpp 依赖
ament_auto_find_build_dependencies()
find_package(yaml_cpp_vendor REQUIRED)

# 尝试查找 yaml-cpp，使用不同的方式
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(YAML_CPP QUIET yaml-cpp)
endif()

# 如果 pkg-config 找不到，尝试直接查找
if(NOT YAML_CPP_FOUND)
    find_package(yaml-cpp QUIET)
endif()

###########
## Build ##
###########

set(PACKAGE_INCLUDE_DEPENDS
    rpcs_s_interfaces_behavior_tree
    rpcs_s_interfaces_agv
    rpcs_interfaces_motor
    vir_robot_interfaces
    algorithm_interface
    hikvision_interface
    sensor_msgs
    cv_bridge
    rclcpp
    rclcpp_action
    std_msgs
    std_srvs
    behaviortree_cpp
    behaviortree_ros2
)

# 设置包含目录
include_directories(
    ${PROJECT_SOURCE_DIR}/include
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/plugins/include
)

# 添加通用依赖的函数
function(add_common_dependencies target)
    # 添加通用依赖
    ament_target_dependencies(${target} ${PACKAGE_INCLUDE_DEPENDS})

    # 直接链接 yaml-cpp 系统库
    target_link_libraries(${target} yaml-cpp::yaml-cpp)

    # 添加特定包的依赖
    if(rpcs_s_interfaces_io_board_FOUND)
        ament_target_dependencies(${target} rpcs_s_interfaces_io_board)
    endif()

    if(hikvision_interface_FOUND)
        ament_target_dependencies(${target} hikvision_interface)
    endif()
endfunction()

# 生成插件库列表变量
set(ALL_PLUGIN_LIBS "")
set(FRAMEWORK_base
  ${CMAKE_SOURCE_DIR}/src/rpcs_s_behaviors_workflow/base/RclcppNodeUtils.cpp
  ${CMAKE_SOURCE_DIR}/src/rpcs_s_behaviors_workflow/base/StringUtils.cpp
  ${CMAKE_SOURCE_DIR}/src/rpcs_s_behaviors_workflow/base/util.cpp
)

# 首先编译基础库，因为其他库可能依赖于它们
file(GLOB BASE_SOURCES plugins/src/base/*.cpp ${FRAMEWORK_base})
foreach(SOURCE_FILE ${BASE_SOURCES})
    get_filename_component(BASE_NAME ${SOURCE_FILE} NAME_WE)
    add_library(${BASE_NAME} SHARED ${SOURCE_FILE})
    add_common_dependencies(${BASE_NAME})
    target_compile_definitions(${BASE_NAME} PRIVATE BT_PLUGIN_EXPORT)
    if(${BASE_NAME} STREQUAL "util")
        target_link_libraries(${BASE_NAME} StringUtils)
    endif()
    install(TARGETS ${BASE_NAME} DESTINATION lib/${PROJECT_NAME})
    list(APPEND ALL_PLUGIN_LIBS ${BASE_NAME})
endforeach()

# 然后编译控制节点，它们可能依赖于基础库
file(GLOB CONTROL_SOURCES plugins/src/control/*.cpp)
foreach(SOURCE_FILE ${CONTROL_SOURCES})
    get_filename_component(BASE_NAME ${SOURCE_FILE} NAME_WE)
    add_library(${BASE_NAME} SHARED ${SOURCE_FILE})
    add_common_dependencies(${BASE_NAME})
    target_compile_definitions(${BASE_NAME} PRIVATE BT_PLUGIN_EXPORT)
    # 链接基础库
    foreach(BASE_LIB ${ALL_PLUGIN_LIBS})
        target_link_libraries(${BASE_NAME} ${BASE_LIB})
    endforeach()
    install(TARGETS ${BASE_NAME} DESTINATION lib/${PROJECT_NAME})
    list(APPEND ALL_PLUGIN_LIBS ${BASE_NAME})
endforeach()

# 最后编译动作节点，它们可能依赖于基础库和控制节点
file(GLOB ACTION_SOURCES plugins/src/action/*.cpp)
foreach(SOURCE_FILE ${ACTION_SOURCES})
    get_filename_component(BASE_NAME ${SOURCE_FILE} NAME_WE)
    add_library(${BASE_NAME} SHARED ${SOURCE_FILE})
    add_common_dependencies(${BASE_NAME})
    target_compile_definitions(${BASE_NAME} PRIVATE BT_PLUGIN_EXPORT)
    
    # 链接已编译的库
    foreach(BASE_LIB ${ALL_PLUGIN_LIBS})
        target_link_libraries(${BASE_NAME} ${BASE_LIB})
    endforeach()
    
    # 添加特定依赖
    if(${BASE_NAME} STREQUAL "AgvGoPoint")
        target_link_libraries(${BASE_NAME} nlohmann_json::nlohmann_json)
    endif()
    
    if(${BASE_NAME} STREQUAL "BoardAlign")
        target_link_libraries(${BASE_NAME} nlohmann_json::nlohmann_json opencv_imgcodecs opencv_imgproc opencv_core)
    endif()
    
    if(${BASE_NAME} STREQUAL "VehiclesAlign")
        target_link_libraries(${BASE_NAME} nlohmann_json::nlohmann_json opencv_imgcodecs opencv_imgproc opencv_core)
    endif()
    
    if(${BASE_NAME} STREQUAL "ImageDetection" OR ${BASE_NAME} STREQUAL "ImageDetectionToZero")
        target_link_libraries(${BASE_NAME} opencv_imgcodecs opencv_imgproc opencv_core)
    endif()
    
    # 安装插件库
    install(TARGETS ${BASE_NAME} DESTINATION lib/${PROJECT_NAME})
    
    # 添加到插件库列表
    list(APPEND ALL_PLUGIN_LIBS ${BASE_NAME})
endforeach()

# 单独编译SensorValueCheckInt节点
add_library(SensorValueCheckInt SHARED
  plugins/src/condition/SensorValueCheckInt.cpp
)
add_common_dependencies(SensorValueCheckInt)
target_compile_definitions(SensorValueCheckInt PRIVATE BT_PLUGIN_EXPORT)
install(TARGETS SensorValueCheckInt DESTINATION lib/${PROJECT_NAME})
list(APPEND ALL_PLUGIN_LIBS SensorValueCheckInt)

# 编译条件节点插件（排除SensorValueCheckInt.cpp）
file(GLOB CONDITION_SRC_ALL plugins/src/condition/*.cpp)
set(CONDITION_SRC "")
foreach(SRC_FILE ${CONDITION_SRC_ALL})
  get_filename_component(SRC_NAME ${SRC_FILE} NAME)
  if(NOT ${SRC_NAME} STREQUAL "SensorValueCheckInt.cpp")
    list(APPEND CONDITION_SRC ${SRC_FILE})
  endif()
endforeach()

add_library(condition_plugins SHARED
  ${CONDITION_SRC}
)
add_common_dependencies(condition_plugins)
target_compile_definitions(condition_plugins PRIVATE BT_PLUGIN_EXPORT)
install(TARGETS condition_plugins DESTINATION lib/${PROJECT_NAME})
list(APPEND ALL_PLUGIN_LIBS condition_plugins)

# 构建action_plugins注册器库
add_library(bt_action_plugins SHARED src/action_plugins.cpp)
add_common_dependencies(bt_action_plugins)
# 链接所有插件库，这样注册器可以访问它们的符号
target_link_libraries(bt_action_plugins ${ALL_PLUGIN_LIBS})
install(TARGETS bt_action_plugins DESTINATION lib/${PROJECT_NAME})

# 构建condition_plugins注册器库
add_library(bt_condition_plugins SHARED src/condition_plugins.cpp)
add_common_dependencies(bt_condition_plugins)
# 链接SensorValueCheckInt库
target_link_libraries(bt_condition_plugins SensorValueCheckInt)
install(TARGETS bt_condition_plugins DESTINATION lib/${PROJECT_NAME})

# 构建process_action_server可执行文件
add_executable(process_action_server
    src/process_action_server_main.cpp
    src/process_action_server.cpp
    src/process_tree_path_resolver.cpp
    ${FRAMEWORK_base}
)
add_common_dependencies(process_action_server)
target_link_libraries(process_action_server bt_action_plugins bt_condition_plugins)
install(TARGETS process_action_server DESTINATION lib/${PROJECT_NAME})

# 构建rpcs_behavior_tree可执行文件
add_executable(rpcs_behavior_tree
    src/rpcs_behavior_tree.cpp
    ${FRAMEWORK_base}
)
add_common_dependencies(rpcs_behavior_tree)
target_link_libraries(rpcs_behavior_tree bt_action_plugins bt_condition_plugins)
install(TARGETS rpcs_behavior_tree DESTINATION lib/${PROJECT_NAME})

# 注意：process_action_client_example.cpp 文件为空，已移除构建目标

# 安装配置文件和启动文件
install(DIRECTORY 
    launch/ 
    config/
    DESTINATION share/${PROJECT_NAME}/
)

# 安装示例文件和文档
install(DIRECTORY 
    examples/
    DESTINATION share/${PROJECT_NAME}/examples
)

install(DIRECTORY 
    docs/
    DESTINATION share/${PROJECT_NAME}/docs
)

# 安装脚本文件
install(DIRECTORY
    scripts/
    DESTINATION lib/${PROJECT_NAME}/scripts
    USE_SOURCE_PERMISSIONS
)

# 安装头文件，但不包括plugins目录（因为我们将单独安装它）
install(DIRECTORY 
    include/
    DESTINATION include/${PROJECT_NAME}
    PATTERN "plugins" EXCLUDE
)

# 安装插件头文件
install(DIRECTORY
    plugins/include/
    DESTINATION include/${PROJECT_NAME}/plugins
)

# 自动生成导出目标
ament_auto_package()
