/*****************************************************************************
 * @copyright   Copyright (c) 2025.7 软件组
 * @file        ImageDetectionToZero.cpp
 * @brief
 * @details     图像检测-用于机械臂回零工艺
 *
 * <AUTHOR>
 * @date        2025/7/19
 * @version
 * @attention
 * @remark      调用虚拟机器人的图像检测服务，期间会相机拍照，服务正常时，返回相机拍照的x、y、rz的值，并通过行为树返回。
                之后行为树的其他节点会使用这个x、y、rz的值，进行机械臂回零。
                协议地址:
                RPCS_M_Controllers_interfaces/src/vir_robot_interfaces/srv/ImageDetection.srv
                RPCS_M_Controllers_interfaces/src/vir_robot_interfaces/msg/ImageDetectionResult.msg
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "action/ImageDetectionToZero.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <nlohmann/json.hpp>

namespace rpcs_s_behaviors_workflow
{
    ImageDetectionToZero::ImageDetectionToZero(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_ms_(60000)
    {
        // 获取已有的ROS节点或创建新节点
        node_ = rclcpp::Node::SharedPtr(std::make_shared<rclcpp::Node>("image_detection_to_zero_node"));
        
        // 默认使用/Robot1命名空间
        std::string namespace_prefix = "/Robot1";
        try {
            namespace_prefix = config.input_ports.at("strNamespace");
        } catch (const std::exception& e) {
            // 使用默认值
        }
        createServiceClient(namespace_prefix);
    }

    void ImageDetectionToZero::createServiceClient(const std::string& namespace_prefix)
    {
        // 创建服务客户端
        std::string service_name = "image_detection";
        client_ = node_->create_client<vir_robot_interfaces::srv::ImageDetection>(service_name);
        std::cout << "image_detection创建服务客户端: " << service_name << std::endl;
    }

    BT::NodeStatus ImageDetectionToZero::onStart()
    {
        std::cout << "image_detection - onStart" << std::endl; 
        // 获取输入参数
        if (!getInput<std::string>("detect_type", detect_type_)) {
            std::cout << "ImageDetectionToZero - 无法获取detect_type参数" << std::endl;
            return BT::NodeStatus::FAILURE;
        }

        getInput("camera_ip", camera_ip_);
        if (!getInput<std::string>("camera_group", camera_group_)) {
            std::cout << "ImageDetectionToZero - 无法获取camera_group参数" << std::endl;
            return BT::NodeStatus::FAILURE;
        }
        getInput<int>("intTimeoutMs", timeout_ms_);

        // 等待服务可用
        if (!client_->wait_for_service(std::chrono::milliseconds(1000))) {
            std::cout << "image_detection Service not available, waiting..." << std::endl;
            // 继续等待
            return BT::NodeStatus::RUNNING;
        }

        // 创建请求
        auto request = std::make_shared<vir_robot_interfaces::srv::ImageDetection::Request>();
        request->detect_type = detect_type_;
        request->camera_ip = camera_ip_;
        request->camera_group = camera_group_;

        // 记录请求信息
        std::cout << "机械臂回零图像检测服务被调用: detect_type=" << detect_type_ << "  camera_ip=" << camera_ip_ << "  camera_group=" << camera_group_ << "  intTimeoutMs=" << timeout_ms_ << std::endl;

        // 发送异步请求
        auto result_future = client_->async_send_request(request);
        response_future_ = result_future.future.share();
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }


    BT::NodeStatus ImageDetectionToZero::onRunning()
    {
        std::cout << "image_detection - onRunning "  << std::endl;

        // 处理ROS事件，确保回调被处理
        rclcpp::spin_some(node_);
    
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();

        if (elapsed > timeout_ms_) {
            std::cout << "机械臂回零图像检测服务 image_detection - 服务请求超时 Service call failed:" << std::endl;

            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "机械臂回零图像检测服务请求超时");
            setOutput<std::string>("strOutputResponse", "机械臂回零图像检测服务请求超时");
            return BT::NodeStatus::FAILURE;
        }
        else {
            std::cout << "机械臂回零图像检测服务被调用 elapsed: " << elapsed << " timeout_ms_: " << timeout_ms_ << std::endl;
        }
        
        // 检查响应是否就绪
        auto status = response_future_.wait_for(std::chrono::milliseconds(10));
        std::cout << "机械臂回零图像检测服务 - 响应状态: " << (status == std::future_status::ready ? "就绪" : 
                                                          status == std::future_status::timeout ? "等待中" : "延迟") << std::endl;
        
        if (status == std::future_status::ready) {
            try {
                // 获取响应
                std::cout << "机械臂回零图像检测服务 - 响应就绪，获取响应内容" << std::endl;
                auto response = response_future_.get();
                std::cout << "机械臂回零图像检测服务被调用，成功. 原始数据. 单位mm Detection result: (" << response->detect_result.x << ", " << response->detect_result.y << ", " << response->detect_result.rz << ")  success=["
                         << response->success << "]  msg=" << response->msg << "  error_code=" << response->error_code << "  costMs=" << elapsed << std::endl;
                // 视觉相机过来的长度单位mm，由电机工程统一更换为m，机械臂回零图像检测节点不做长度单位转换
                // response->detect_result.x /= 1000;
                // response->detect_result.y /= 1000;
                // 按照机械臂和视觉要求，rz角度前面的正负号取反。
                response->detect_result.rz = -response->detect_result.rz;

                // 校验视觉相机过来的长度是否在正常范围
                if (response->success && (2000<response->detect_result.x || 2000<response->detect_result.y))
                {
                    // 处理异常数据
                    std::cout << "机械臂回零图像检测服务被调用，失败 x,y 单位是mm，很明细不在机械臂正常范围" << std::endl;
                    return BT::NodeStatus::FAILURE;
                }

                // 设置输出端口 - 直接映射服务响应
                setOutput<bool>("boolOutputSuccess", response->success);
                setOutput<std::string>("strOutputMessage", response->msg);
                setOutput<std::string>("strOutputResponse", response->error_code);
                setOutput<float>("image_detection_x", 0.0);
                setOutput<float>("image_detection_y", 0.0);
                setOutput<float>("image_detection_rz", 0.0);
                if (!response->success) {
                    // 记录响应
                    std::cout << "机械臂回零图像检测服务被调用，失败 Detection result: (0, 0, 0)  success=[]"
                              << response->success << "  msg=" << response->msg << "  error_code=" << response->error_code << "  costMs=" << elapsed << std::endl;
                    return BT::NodeStatus::FAILURE;
                }

                setOutput<float>("image_detection_x", response->detect_result.x);
                setOutput<float>("image_detection_y", response->detect_result.y);
                setOutput<float>("image_detection_rz", response->detect_result.rz);
                // 记录响应
                std::cout << "机械臂回零图像检测服务被调用，成功. 最终传递 单位是mm. Detection result: (" << response->detect_result.x << ", " << response->detect_result.y << ", " << response->detect_result.rz << ")  success=["
                          << response->success << "]  msg=" << response->msg << "  error_code=" << response->error_code << "  costMs=" << elapsed << std::endl;
                return BT::NodeStatus::SUCCESS;
            } catch (const std::exception& e) {
                // 记录响应
                std::cout << "机械臂回零图像检测服务被调用，失败 Service call failed exception: " << e.what() << std::endl;
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", std::string("机械臂回零图像检测服务被调用失败，出现异常 ") + e.what());
                setOutput<std::string>("strOutputResponse", std::string("机械臂回零图像检测服务被调用失败，出现异常 ") + e.what());
                return BT::NodeStatus::FAILURE;
            }
        }

        // 请求尚未完成，继续等待
        std::cout << "image_detection - onRunning 请求尚未完成，继续等待"  << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    void ImageDetectionToZero::onHalted()
    {
        // 添加取消请求的逻辑
        std::cout << "image_detection - onHalted" << std::endl;

        // 释放响应的Future
        try {
            // 重置响应Future，避免在任务切换时访问已释放的资源
            response_future_ = rclcpp::Client<vir_robot_interfaces::srv::ImageDetection>::SharedFuture();

            // 确保输出端口有合理的值，防止未初始化状态
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "image_detection操作被取消");
            setOutput<std::string>("strOutputResponse", "image_detection操作被取消");
            

            // 安全关闭服务客户端
            if (client_) {
                std::cout << "image_detection - 关闭服务客户端" << std::endl;
                client_.reset();
            }
 
            // 停止ROS事件处理，避免后续回调
            if (node_) {
                std::cout << "image_detection - 停止ROS节点" << std::endl;
                // 仅减少引用计数，不完全销毁节点，避免影响其他部分
                // 注意：ROS2中节点和客户端/服务端是强耦合的，直接重置可能导致问题
                node_.reset();
            }

            std::cout << "image_detection - 清理资源完成" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "image_detection - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::ImageDetectionToZero>("ImageDetectionToZero");
}


