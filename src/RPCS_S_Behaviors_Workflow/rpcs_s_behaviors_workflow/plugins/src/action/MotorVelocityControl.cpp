#include "action/MotorVelocityControl.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>

namespace rpcs_s_behaviors_workflow
{
    MotorVelocityControl::MotorVelocityControl(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(10.0)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("motor_velocity_control_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("motor_velocity_control_node", controller_motor_node_ros_domain_id);
    }

    std::string MotorVelocityControl::createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        if (device_id.empty() || device_id == "default") {
            return fmt::format("/{}_{}/velocity_control", motor_brand, motor_id);
        } else {
            return fmt::format("/{}/{}_{}/velocity_control", device_id, motor_brand, motor_id);
        }
    }

    void MotorVelocityControl::createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        std::string action_name = createActionName(device_id, motor_brand, motor_id);
        client_ = rclcpp_action::create_client<VelocityControlAction>(node_, action_name);

        std::cout << "MotorVelocityControl 创建Action客户端: " << action_name << std::endl;
    }

    void MotorVelocityControl::handleActionError(int error_code, const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<int>("intErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        
        std::cerr << "MotorVelocityControl Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus MotorVelocityControl::onStart()
    {
        std::cout << "MotorVelocityControl - onStart" << std::endl;
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "Robot1";
        }

        if (!getInput<std::string>("strMotorBrand", motor_brand_)) {
            motor_brand_ = "Kinco";
        }

        if (!getInput<int>("intMotorId", motor_id_)) {
            std::cout << "MotorVelocityControl - 无法获取intMotorId参数" << std::endl;
            handleActionError(-1, "缺少电机ID参数");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput<double>("doubleTargetVelocity", target_velocity_)) {
            std::cout << "MotorVelocityControl - 无法获取doubleTargetVelocity参数" << std::endl;
            handleActionError(-2, "缺少目标速度参数");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取其他参数（带默认值）
        acceleration_ = 20.0;  // 默认值
        getInput<double>("doubleAcceleration", acceleration_);
        
        deceleration_ = 100.0;  // 默认值
        getInput<double>("doubleDeceleration", deceleration_);
        
        duration_ = 0.0;  // 默认值
        getInput<double>("doubleDuration", duration_);
        
        target_current_limit_ = 20.0;  // 默认值（与头文件中的默认值一致）
        getInput<double>("doubleTargetCurrentLimit", target_current_limit_);
        
        // 详细调试电流限制参数
        std::cout << "MotorVelocityControl - 电流限制参数调试:" << std::endl;
        std::cout << "  默认值设置为: 20.0 A" << std::endl;
        std::cout << "  XML输入获取后: " << target_current_limit_ << " A" << std::endl;
        std::cout << "  预期CANopen值(×42.67): " << (target_current_limit_ * 42.67) << " DEC" << std::endl;
        
        use_position_limits_ = false;  // 默认值
        getInput<bool>("boolUsePositionLimits", use_position_limits_);
        
        min_position_ = -100.0;  // 默认值
        getInput<double>("doubleMinPosition", min_position_);
        
        max_position_ = 100.0;  // 默认值
        getInput<double>("doubleMaxPosition", max_position_);
        
        timeout_ = 10.0;  // 默认值
        getInput<double>("doubleTimeout", timeout_);
        
        // 创建Action客户端
        createActionClient(device_id_, motor_brand_, motor_id_);

        // 等待Action服务可用
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "MotorVelocityControl - Action服务不可用: " << createActionName(device_id_, motor_brand_, motor_id_) << std::endl;
            handleActionError(-3, "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建Action目标
        auto goal_msg = VelocityControlAction::Goal();
        goal_msg.target_velocity = target_velocity_;
        goal_msg.acceleration = acceleration_;
        goal_msg.deceleration = deceleration_;
        goal_msg.duration = duration_;
        goal_msg.target_current_limit = target_current_limit_;
        goal_msg.use_position_limits = use_position_limits_;
        goal_msg.min_position = min_position_;
        goal_msg.max_position = max_position_;
        goal_msg.timeout = timeout_;
        
        // 记录请求信息
        std::cout << "MotorVelocityControl - 发送速度控制请求:" << std::endl;
        std::cout << "  设备ID: " << device_id_ << ", 电机ID: " << motor_id_ << std::endl;
        std::cout << "  目标速度: " << target_velocity_ << " rpm" << std::endl;
        std::cout << "  加速度: " << acceleration_ << " rps/s" << std::endl;
        std::cout << "  减速度: " << deceleration_ << " rps/s" << std::endl;
        std::cout << "  电流限制: " << target_current_limit_ << " A" << std::endl;
        std::cout << "  运行时间: " << (duration_ > 0 ? std::to_string(duration_) + "s" : "持续运行") << std::endl;
        std::cout << "  位置限制: " << (use_position_limits_ ? "开启" : "关闭") << std::endl;
        if (use_position_limits_) {
            std::cout << "  位置限制范围: [" << min_position_ << ", " << max_position_ << "] mm" << std::endl;
        }
        std::cout << "  超时时间: " << timeout_ << "s" << std::endl;
        
        // 确认Action Goal参数传递
        std::cout << "MotorVelocityControl - Action Goal参数确认:" << std::endl;
        std::cout << "  goal_msg.use_position_limits = " << (goal_msg.use_position_limits ? "true" : "false") << std::endl;
        std::cout << "  goal_msg.min_position = " << goal_msg.min_position << std::endl;
        std::cout << "  goal_msg.max_position = " << goal_msg.max_position << std::endl;
        
        // 发送Action请求
        auto send_goal_options = rclcpp_action::Client<VelocityControlAction>::SendGoalOptions();
        
        // 设置反馈回调
        send_goal_options.feedback_callback = 
            [this](GoalHandleVelocityControl::SharedPtr, const std::shared_ptr<const VelocityControlAction::Feedback> feedback) {
                // 更新实时状态输出
                setOutput<double>("doubleCurrentVelocity", feedback->current_velocity);
                setOutput<double>("doubleElapsedTime", feedback->elapsed_time);
                
                
                std::cout << "MotorVelocityControl - 反馈: 当前速度=" << feedback->current_velocity 
                         << " rpm, 已运行时间=" << feedback->elapsed_time << "s" << std::endl;
            };
        
        // 发送目标
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus MotorVelocityControl::onRunning()
    {
        std::cout << "MotorVelocityControl - onRunning" << std::endl;
        
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查停止信号 - 支持动态黑板变量和静态参数两种方式
        bool stop_signal = false;
        bool stop_signal_found = false;
        
        // 添加详细的停止信号监控日志
        static bool last_stop_signal = false;
        static int log_counter = 0;
        static std::string stop_signal_source = "";
        
        // 方式1: 尝试从动态黑板变量读取（优先级更高）
        std::string stop_signal_key;
        if (getInput<std::string>("strStopSignalKey", stop_signal_key) && !stop_signal_key.empty()) {
            // 直接从黑板读取变量
            auto blackboard = this->config().blackboard;
            if (blackboard) {
                try {
                    auto any_value = blackboard->get<bool>(stop_signal_key);
                    stop_signal = any_value;
                    stop_signal_found = true;
                    stop_signal_source = "黑板变量[" + stop_signal_key + "]";
                    
                    // 添加详细调试信息
                    if (log_counter % 10 == 0) {
                        std::cout << "MotorVelocityControl - 黑板变量读取成功: " << stop_signal_key 
                                 << " = " << (stop_signal ? "TRUE" : "FALSE") << std::endl;
                    }
                } catch (const std::exception& e) {
                    // 黑板变量不存在或类型不匹配，继续使用静态参数
                    if (log_counter % 20 == 0) {
                        std::cout << "MotorVelocityControl - 黑板变量读取失败: " << stop_signal_key 
                                 << ", 错误: " << e.what() << std::endl;
                    }
                }
            } else {
                if (log_counter % 20 == 0) {
                    std::cout << "MotorVelocityControl - 黑板为空，无法读取: " << stop_signal_key << std::endl;
                }
            }
        }
        
        // 方式2: 如果动态方式失败，回退到静态参数
        if (!stop_signal_found) {
            auto stop_signal_result = getInput<bool>("boolStopSignal", stop_signal);
            if (stop_signal_result) {
                stop_signal_found = true;
                stop_signal_source = "静态参数[boolStopSignal]";
            }
        }
        
        if (stop_signal_found) {
            // 只在信号变化时或每10次循环时打印一次日志
            if (stop_signal != last_stop_signal || (log_counter % 10 == 0)) {
                std::cout << "MotorVelocityControl - 停止信号状态: " << (stop_signal ? "TRUE(停止)" : "FALSE(继续)") 
                         << " [第" << log_counter << "次检查] 来源: " << stop_signal_source << std::endl;
            }
            last_stop_signal = stop_signal;
            
            if (stop_signal) {
                std::cout << "MotorVelocityControl - *** 检测到停止信号，立即停止 *** 来源: " << stop_signal_source << std::endl;
                
                // 取消正在进行的Action
                if (goal_handle_) {
                    std::cout << "MotorVelocityControl - 取消Action目标（停止信号）" << std::endl;
                    client_->async_cancel_goal(goal_handle_);
                }
                
                // 设置输出状态
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "操作被停止信号中断");
                setOutput<int>("intErrorCode", -10);
                
                return BT::NodeStatus::FAILURE;
            }
        } else {
            // 如果无法读取停止信号，记录警告
            if (log_counter % 20 == 0) {  // 每20次循环警告一次
                std::cout << "MotorVelocityControl - 警告: 无法读取任何停止信号" << std::endl;
            }
        }
        
        log_counter++;
        
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_) {
            std::cout << "MotorVelocityControl - Action请求超时" << std::endl;
            handleActionError(-4, "Action请求超时");
            
            // 取消目标
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查目标句柄是否就绪
        if (!goal_handle_) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "MotorVelocityControl - 目标被拒绝" << std::endl;
                    handleActionError(-5, "Action目标被服务器拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                
                // 获取结果Future
                result_future_ = client_->async_get_result(goal_handle_);
                std::cout << "MotorVelocityControl - 目标已接受，开始执行" << std::endl;
            }
            return BT::NodeStatus::RUNNING;
        }
        
        // 检查结果是否就绪
        auto status = result_future_.wait_for(std::chrono::milliseconds(10));
        if (status == std::future_status::ready) {
            auto wrapped_result = result_future_.get();
            
            std::cout << "MotorVelocityControl - Action完成" << std::endl;
            
            // 设置输出端口
            bool success = (wrapped_result.code == rclcpp_action::ResultCode::SUCCEEDED);
            setOutput<bool>("boolOutputSuccess", success);
            setOutput<double>("doubleFinalVelocity", wrapped_result.result->final_velocity);
            setOutput<double>("doubleFinalPosition", wrapped_result.result->final_position);
            setOutput<int>("intErrorCode", wrapped_result.result->error_code);
            setOutput<std::string>("strOutputMessage", wrapped_result.result->error_message);
            
            // 记录结果
            std::cout << "MotorVelocityControl - 执行结果: " << (success ? "成功" : "失败") << std::endl;
            std::cout << "MotorVelocityControl - 最终速度: " << wrapped_result.result->final_velocity << " rpm" << std::endl;
            std::cout << "MotorVelocityControl - 最终位置: " << wrapped_result.result->final_position << "mm" << std::endl;
            std::cout << "MotorVelocityControl - 消息: " << wrapped_result.result->error_message << std::endl;
            
            return success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        // Action尚未完成，继续等待
        return BT::NodeStatus::RUNNING;
    }

    void MotorVelocityControl::onHalted()
    {
        std::cout << "MotorVelocityControl - onHalted" << std::endl;
        
        try {
            // 取消正在进行的Action
            if (goal_handle_) {
                std::cout << "MotorVelocityControl - 取消Action目标" << std::endl;
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 重置Future对象
            goal_future_ = std::shared_future<GoalHandleVelocityControl::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleVelocityControl::WrappedResult>();
            
            // 设置取消状态输出
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "操作被取消");
            setOutput<int>("intErrorCode", -99);
            
            // 清理Action客户端
            if (client_) {
                std::cout << "MotorVelocityControl - 清理Action客户端" << std::endl;
                client_.reset();
            }
            
            std::cout << "MotorVelocityControl - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "MotorVelocityControl - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MotorVelocityControl>("MotorVelocityControl");
} 