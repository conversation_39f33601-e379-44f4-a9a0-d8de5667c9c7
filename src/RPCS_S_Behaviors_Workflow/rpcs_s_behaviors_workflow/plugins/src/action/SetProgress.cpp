#include "action/SetProgress.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    SetProgress::SetProgress(const std::string& name, const BT::NodeConfiguration& config)
        : BT::SyncActionNode(name, config)
    {
    }

    BT::NodeStatus SetProgress::tick()
    {
        double progress = 0.0;
        if (!getInput("progress", progress)) {
            std::cout << "SetProgress - Failed to get progress value" << std::endl;
            return BT::NodeStatus::FAILURE;
        }

        // 确保进度值在有效范围内
        progress = std::max(0.0, std::min(100.0, progress));

        // 获取黑板
        auto blackboard = this->config().blackboard;
        if (!blackboard) {
            std::cout << "SetProgress - Failed to get blackboard" << std::endl;
            return BT::NodeStatus::FAILURE;
        }

        // 将进度值写入黑板
        blackboard->set("current_progress", progress);
        std::cout << "SetProgress - Progress set to: " << progress << "%" << std::endl;

        return BT::NodeStatus::SUCCESS;
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::SetProgress>("SetProgress");
} 