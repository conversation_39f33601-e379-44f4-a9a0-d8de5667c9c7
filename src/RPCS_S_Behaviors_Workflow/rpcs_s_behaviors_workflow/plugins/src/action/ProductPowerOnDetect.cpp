#include "action/ProductPowerOnDetect.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>
#include <thread> // Added for sleep_for

namespace rpcs_s_behaviors_workflow
{
    ProductPowerOnDetect::ProductPowerOnDetect(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(30.0)
    {
        node_ = std::make_shared<rclcpp::Node>("product_power_on_detect_node");
    }

    std::string ProductPowerOnDetect::createActionName()
    {
        return "/product_power_on_detect";
    }

    void ProductPowerOnDetect::createActionClient()
    {
        std::string action_name = createActionName();
        client_ = rclcpp_action::create_client<ProductPowerOnDetectAction>(node_, action_name);
        std::cout << "ProductPowerOnDetect 创建Action客户端: " << action_name << std::endl;
    }

    void ProductPowerOnDetect::handleActionError(const std::string& error_code, const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        std::cerr << "ProductPowerOnDetect Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus ProductPowerOnDetect::onStart()
    {
        std::cout << "ProductPowerOnDetect - onStart" << std::endl;
        
        // 清理可能的旧状态，确保全新开始
        goal_future_ = std::shared_future<GoalHandleProductPowerOnDetect::SharedPtr>();
        result_future_ = std::shared_future<GoalHandleProductPowerOnDetect::WrappedResult>();
        goal_handle_.reset();
        
        getInput<double>("doubleTimeout", timeout_);
        
        createActionClient();
        
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "ProductPowerOnDetect - Action服务不可用: " << createActionName() << std::endl;
            handleActionError("SERVICE_UNAVAILABLE", "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 短暂延迟确保Action服务完全准备好，并且旧请求已清理
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        auto goal_msg = ProductPowerOnDetectAction::Goal();
        
        auto send_goal_options = rclcpp_action::Client<ProductPowerOnDetectAction>::SendGoalOptions();
        send_goal_options.goal_response_callback = 
            [this](const GoalHandleProductPowerOnDetect::SharedPtr& goal_handle) {
                if (!goal_handle) {
                    std::cout << "ProductPowerOnDetect - 目标被拒绝" << std::endl;
                } else {
                    std::cout << "ProductPowerOnDetect - 目标被接受" << std::endl;
                }
            };
        send_goal_options.feedback_callback = 
            [this](GoalHandleProductPowerOnDetect::SharedPtr, 
                   const std::shared_ptr<const ProductPowerOnDetectAction::Feedback> feedback) {
                std::cout << "ProductPowerOnDetect - 收到PLC反馈" << std::endl;
                setOutput<std::string>("strPlcFeedback", "PLC反馈已更新");
            };
        send_goal_options.result_callback = 
            [this](const GoalHandleProductPowerOnDetect::WrappedResult& result) {
                std::cout << "ProductPowerOnDetect - 收到结果" << std::endl;
            };
        
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        start_time_ = std::chrono::steady_clock::now();
        
        std::cout << "ProductPowerOnDetect - 发送产品通电检测请求" << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus ProductPowerOnDetect::onRunning()
    {
        std::cout << "ProductPowerOnDetect - onRunning" << std::endl;
        rclcpp::spin_some(node_);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        if (elapsed > timeout_) {
            std::cout << "ProductPowerOnDetect - 操作超时" << std::endl;
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            handleActionError("TIMEOUT", "操作超时");
            return BT::NodeStatus::FAILURE;
        }
        
        if (goal_future_.valid()) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "ProductPowerOnDetect - 目标被拒绝" << std::endl;
                    handleActionError("GOAL_REJECTED", "目标被拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                result_future_ = client_->async_get_result(goal_handle_);
                goal_future_ = std::shared_future<GoalHandleProductPowerOnDetect::SharedPtr>();
            }
        }
        
        if (result_future_.valid()) {
            auto status = result_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                auto result = result_future_.get();
                
                // 先检查result是否有效
                if (!result.result) {
                    std::cout << "ProductPowerOnDetect - 收到空结果" << std::endl;
                    handleActionError("EMPTY_RESULT", "收到空结果");
                    return BT::NodeStatus::FAILURE;
                }
                
                switch (result.code) {
                    case rclcpp_action::ResultCode::SUCCEEDED:
                        {
                            auto action_result = result.result;
                            setOutput<bool>("boolOutputSuccess", action_result->success);
                            setOutput<std::string>("strErrorCode", action_result->error_code);
                            setOutput<std::string>("strOutputMessage", action_result->msg);
                            std::cout << "ProductPowerOnDetect - 执行完成: " 
                                      << (action_result->success ? "成功" : "失败") 
                                      << ", 消息: " << action_result->msg << std::endl;
                            return action_result->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
                        }
                        break;
                    case rclcpp_action::ResultCode::ABORTED:
                        std::cout << "ProductPowerOnDetect - Action被中止" << std::endl;
                        handleActionError("ABORTED", "Action被中止");
                        return BT::NodeStatus::FAILURE;
                    case rclcpp_action::ResultCode::CANCELED:
                        std::cout << "ProductPowerOnDetect - Action被取消" << std::endl;
                        handleActionError("CANCELED", "Action被取消");
                        return BT::NodeStatus::FAILURE;
                    default:
                        std::cout << "ProductPowerOnDetect - 未知结果状态" << std::endl;
                        handleActionError("UNKNOWN", "未知结果状态");
                        return BT::NodeStatus::FAILURE;
                }
            }
        }
        return BT::NodeStatus::RUNNING;
    }

    void ProductPowerOnDetect::onHalted()
    {
        std::cout << "ProductPowerOnDetect - onHalted" << std::endl;
        try {
            // 取消正在进行的goal
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 彻底清理所有future状态
            goal_future_ = std::shared_future<GoalHandleProductPowerOnDetect::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleProductPowerOnDetect::WrappedResult>();
            
            // 清理输出端口状态
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strErrorCode", "HALTED");
            setOutput<std::string>("strOutputMessage", "操作被中止");
            
            // 关闭并重置client连接
            if (client_) {
                client_.reset();
            }
            
            // 短暂延迟确保所有异步操作完成
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            std::cout << "ProductPowerOnDetect - 清理资源完成" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "ProductPowerOnDetect - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::ProductPowerOnDetect>("ProductPowerOnDetect");
} 