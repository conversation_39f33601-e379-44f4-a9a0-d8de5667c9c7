#include "action/DigitalOutputWrite.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <sstream>
#include <algorithm>

namespace rpcs_s_behaviors_workflow
{
    DigitalOutputWrite::DigitalOutputWrite(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
        , goal_sent_(false)
        , goal_completed_(false)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("digital_output_write_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("digital_output_write_node", controller_motor_node_ros_domain_id);
    }

    void DigitalOutputWrite::createActionClient(const std::string& device_id)
    {
        // 创建action客户端
        std::string action_name = "/" + device_id + "/io_board_1/digital_output_write";
        action_client_ = rclcpp_action::create_client<DigitalOutputWriteAction>(
            node_, action_name);
        
        RCLCPP_INFO(node_->get_logger(), "DigitalOutputWrite创建action客户端: %s", action_name.c_str());
    }

    // 辅助函数：从字符串解析整数向量
    std::vector<int> parseIntVector(const std::string& str, rclcpp::Logger logger) {
        std::vector<int> result;
        std::string temp = str;
        
        RCLCPP_INFO(logger, "开始解析字符串: '%s'", str.c_str());
        
        // 将所有逗号替换为空格
        std::replace(temp.begin(), temp.end(), ',', ' ');
        RCLCPP_INFO(logger, "替换逗号后: '%s'", temp.c_str());
        
        // 使用stringstream解析空格分隔的数字
        std::stringstream ss(temp);
        int value;
        while (ss >> value) {
            result.push_back(value);
            RCLCPP_INFO(logger, "解析到数值: %d", value);
        }
        
        RCLCPP_INFO(logger, "解析完成，共 %zu 个数值", result.size());
        for (size_t i = 0; i < result.size(); ++i) {
            RCLCPP_INFO(logger, "  [%zu] = %d", i, result[i]);
        }
        
        return result;
    }

    BT::NodeStatus DigitalOutputWrite::onStart()
    {
        RCLCPP_INFO(node_->get_logger(), "=== DigitalOutputWrite::onStart() 开始 ===");
        
        // 重置状态
        goal_sent_ = false;
        goal_completed_ = false;
        output_addresses_.clear();
        output_values_.clear();
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "robot1"; // 默认设备ID
        }
        
        // 创建action客户端
        createActionClient(device_id_);
        
        // 尝试获取地址列表作为向量
        if (!getInput<std::vector<int>>("intOutputAddresses", output_addresses_)) {
            // 如果失败，尝试获取为字符串并解析
            std::string addresses_str;
            RCLCPP_INFO(node_->get_logger(), "无法直接获取向量类型的地址列表，尝试获取字符串");
            if (getInput<std::string>("intOutputAddresses", addresses_str)) {
                RCLCPP_INFO(node_->get_logger(), "成功获取字符串类型的地址列表: '%s'", addresses_str.c_str());
                
                // 打印每个字符的ASCII码，以便检查是否有特殊字符
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < addresses_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, addresses_str[i], static_cast<int>(addresses_str[i]));
                }
                
                output_addresses_ = parseIntVector(addresses_str, node_->get_logger());
                if (output_addresses_.empty()) {
                    RCLCPP_ERROR(node_->get_logger(), "未提供有效的输出地址列表");
                    setOutput<bool>("boolOutputSuccess", false);
                    setOutput<std::string>("strOutputMessage", "未提供有效的输出地址列表");
                    return BT::NodeStatus::FAILURE;
                }
            } else {
                RCLCPP_ERROR(node_->get_logger(), "未提供输出地址列表");
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "未提供输出地址列表");
                return BT::NodeStatus::FAILURE;
            }
        } else {
            RCLCPP_INFO(node_->get_logger(), "成功直接获取向量类型的地址列表，共 %zu 个地址", output_addresses_.size());
            
            // 尝试获取原始字符串，即使向量已经成功获取
            std::string addresses_str;
            if (getInput<std::string>("intOutputAddresses", addresses_str)) {
                RCLCPP_INFO(node_->get_logger(), "原始字符串: '%s'", addresses_str.c_str());
                
                // 打印每个字符的ASCII码
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < addresses_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, addresses_str[i], static_cast<int>(addresses_str[i]));
                }
                
                // 尝试使用我们的解析函数解析这个字符串
                std::vector<int> parsed_addresses = parseIntVector(addresses_str, node_->get_logger());
                RCLCPP_INFO(node_->get_logger(), "通过parseIntVector解析得到 %zu 个地址", parsed_addresses.size());
                for (size_t i = 0; i < parsed_addresses.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  解析地址[%zu] = %d", i, parsed_addresses[i]);
                }
                
                // 如果我们的解析函数得到的结果比直接获取的向量多，使用我们的解析结果
                if (parsed_addresses.size() > output_addresses_.size()) {
                    RCLCPP_INFO(node_->get_logger(), "使用parseIntVector解析的结果，因为它得到了更多的地址");
                    output_addresses_ = parsed_addresses;
                }
            }
            
            for (size_t i = 0; i < output_addresses_.size(); ++i) {
                RCLCPP_INFO(node_->get_logger(), "  地址[%zu] = %d", i, output_addresses_[i]);
            }
        }
        
        // 尝试获取值列表作为向量
        if (!getInput<std::vector<int>>("boolOutputValues", output_values_)) {
            // 如果失败，尝试获取为字符串并解析
            std::string values_str;
            RCLCPP_INFO(node_->get_logger(), "无法直接获取向量类型的值列表，尝试获取字符串");
            if (getInput<std::string>("boolOutputValues", values_str)) {
                RCLCPP_INFO(node_->get_logger(), "成功获取字符串类型的值列表: '%s'", values_str.c_str());
                
                // 打印每个字符的ASCII码
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < values_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, values_str[i], static_cast<int>(values_str[i]));
                }
                
                output_values_ = parseIntVector(values_str, node_->get_logger());
                if (output_values_.empty()) {
                    RCLCPP_ERROR(node_->get_logger(), "未提供有效的输出值列表");
                    setOutput<bool>("boolOutputSuccess", false);
                    setOutput<std::string>("strOutputMessage", "未提供有效的输出值列表");
                    return BT::NodeStatus::FAILURE;
                }
            } else {
                RCLCPP_ERROR(node_->get_logger(), "未提供输出值列表");
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "未提供输出值列表");
                return BT::NodeStatus::FAILURE;
            }
        } else {
            RCLCPP_INFO(node_->get_logger(), "成功直接获取向量类型的值列表，共 %zu 个值", output_values_.size());
            
            // 尝试获取原始字符串，即使向量已经成功获取
            std::string values_str;
            if (getInput<std::string>("boolOutputValues", values_str)) {
                RCLCPP_INFO(node_->get_logger(), "原始值字符串: '%s'", values_str.c_str());
                
                // 打印每个字符的ASCII码
                RCLCPP_INFO(node_->get_logger(), "值字符串的ASCII码:");
                for (size_t i = 0; i < values_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, values_str[i], static_cast<int>(values_str[i]));
                }
                
                // 尝试使用我们的解析函数解析这个字符串
                std::vector<int> parsed_values = parseIntVector(values_str, node_->get_logger());
                RCLCPP_INFO(node_->get_logger(), "通过parseIntVector解析得到 %zu 个值", parsed_values.size());
                for (size_t i = 0; i < parsed_values.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  解析值[%zu] = %d", i, parsed_values[i]);
                }
                
                // 如果我们的解析函数得到的结果比直接获取的向量多，使用我们的解析结果
                if (parsed_values.size() > output_values_.size()) {
                    RCLCPP_INFO(node_->get_logger(), "使用parseIntVector解析的值结果，因为它得到了更多的值");
                    output_values_ = parsed_values;
                }
            }
            
            for (size_t i = 0; i < output_values_.size(); ++i) {
                RCLCPP_INFO(node_->get_logger(), "  值[%zu] = %d (%s)", 
                           i, output_values_[i], output_values_[i] != 0 ? "HIGH" : "LOW");
            }
        }
        
        // 检查地址和值的数量是否匹配
        if (output_addresses_.size() != output_values_.size()) {
            RCLCPP_ERROR(node_->get_logger(), 
                        "地址数量(%zu)与值数量(%zu)不匹配", 
                        output_addresses_.size(), output_values_.size());
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "地址数量与值数量不匹配");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取其他参数
        if (!getInput<bool>("boolVerifyWrite", verify_write_)) {
            verify_write_ = true; // 默认验证写入
        }
        
        if (!getInput<bool>("boolBatchOperation", batch_operation_)) {
            batch_operation_ = true; // 默认使用批量操作
        }
        
        if (!getInput<int>("intTimeoutMs", timeout_ms_)) {
            timeout_ms_ = 5000; // 默认超时5秒
        }

        // 创建goal
        goal_msg_ = std::make_shared<DigitalOutputWriteAction::Goal>();
        
        // 确认一下地址和值的顺序
        RCLCPP_INFO(node_->get_logger(), "=== 准备构建Action请求参数 ===");
        RCLCPP_INFO(node_->get_logger(), "地址数量: %zu", output_addresses_.size());
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "地址[%zu] = %d", i, output_addresses_[i]);
        }
        
        RCLCPP_INFO(node_->get_logger(), "值数量: %zu", output_values_.size());
        for (size_t i = 0; i < output_values_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "值[%zu] = %d (%s)", 
                       i, output_values_[i], output_values_[i] != 0 ? "HIGH" : "LOW");
        }
        
        // 按照输入顺序添加地址和值，确保顺序一致
        goal_msg_->output_addresses.clear();
        goal_msg_->output_values.clear();
            
        // 将int类型的地址转换为uint16
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            goal_msg_->output_addresses.push_back(static_cast<uint16_t>(output_addresses_[i]));
        }
        
        // 将int值转换为bool值
        for (size_t i = 0; i < output_values_.size(); i++) {
            goal_msg_->output_values.push_back(output_values_[i] != 0);
        }
        
        // 确保地址和值的数量匹配
        if (goal_msg_->output_addresses.size() != goal_msg_->output_values.size()) {
            RCLCPP_ERROR(node_->get_logger(), 
                        "地址数量(%zu)与值数量(%zu)不匹配，将自动调整", 
                        goal_msg_->output_addresses.size(), goal_msg_->output_values.size());
            
            // 如果地址多于值，用最后一个值填充
            if (goal_msg_->output_addresses.size() > goal_msg_->output_values.size() && !goal_msg_->output_values.empty()) {
                bool last_value = goal_msg_->output_values.back();
                while (goal_msg_->output_values.size() < goal_msg_->output_addresses.size()) {
                    goal_msg_->output_values.push_back(last_value);
                    RCLCPP_INFO(node_->get_logger(), "添加值: %s", last_value ? "HIGH" : "LOW");
                }
            }
            // 如果值多于地址，截断多余的值
            else if (goal_msg_->output_values.size() > goal_msg_->output_addresses.size()) {
                goal_msg_->output_values.resize(goal_msg_->output_addresses.size());
                RCLCPP_INFO(node_->get_logger(), "截断多余的值");
            }
        }
        
        goal_msg_->verify_write = verify_write_;
        goal_msg_->batch_operation = batch_operation_;
        goal_msg_->write_timeout = timeout_ms_ / 1000.0;  // 转换为秒
        
        // 打印详细的请求信息
        RCLCPP_INFO(node_->get_logger(), "=== DigitalOutputWrite 请求详情 ===");
        RCLCPP_INFO(node_->get_logger(), "Action名称: /%s/io_board_1/digital_output_write", device_id_.c_str());
        RCLCPP_INFO(node_->get_logger(), "设备ID: %s", device_id_.c_str());
        RCLCPP_INFO(node_->get_logger(), "地址数量: %zu", output_addresses_.size());
        RCLCPP_INFO(node_->get_logger(), "验证写入: %s", goal_msg_->verify_write ? "是" : "否");
        RCLCPP_INFO(node_->get_logger(), "批量操作: %s", goal_msg_->batch_operation ? "是" : "否");
        RCLCPP_INFO(node_->get_logger(), "写入超时: %.2f秒", goal_msg_->write_timeout);
        
        // 打印详细的Action请求参数
        RCLCPP_INFO(node_->get_logger(), "=== Action请求参数详情 ===");
        RCLCPP_INFO(node_->get_logger(), "请求类型: DigitalOutputWriteAction");
        RCLCPP_INFO(node_->get_logger(), "output_addresses: [");
        for (size_t i = 0; i < goal_msg_->output_addresses.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "  %zu: %u", i, goal_msg_->output_addresses[i]);
        }
        RCLCPP_INFO(node_->get_logger(), "]");
        RCLCPP_INFO(node_->get_logger(), "output_values: [");
        for (size_t i = 0; i < goal_msg_->output_values.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "  %zu: %s", i, goal_msg_->output_values[i] ? "true" : "false");
        }
        RCLCPP_INFO(node_->get_logger(), "]");
        RCLCPP_INFO(node_->get_logger(), "verify_write: %s", goal_msg_->verify_write ? "true" : "false");
        RCLCPP_INFO(node_->get_logger(), "batch_operation: %s", goal_msg_->batch_operation ? "true" : "false");
        RCLCPP_INFO(node_->get_logger(), "write_timeout: %.2f", goal_msg_->write_timeout);
        RCLCPP_INFO(node_->get_logger(), "=== Action请求参数详情结束 ===");
        
        // 打印所有地址和值
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "地址[%d] = %s", 
                       output_addresses_[i], 
                       (output_values_[i] != 0) ? "HIGH" : "LOW");
        }
        
        RCLCPP_INFO(node_->get_logger(), "=== 发送请求到服务器 ===");
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus DigitalOutputWrite::onRunning()
    {
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        
        if (elapsed > timeout_ms_) {
            RCLCPP_ERROR(node_->get_logger(), "DigitalOutputWrite超时，已等待%d毫秒", timeout_ms_);
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "操作超时");
            return BT::NodeStatus::FAILURE;
        }
        
        // 发送目标
        if (!goal_sent_) {
            if (!action_client_) {
                RCLCPP_ERROR(node_->get_logger(), "Action客户端未初始化");
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "Action客户端未初始化");
                return BT::NodeStatus::FAILURE;
            }
            
            if (!action_client_->wait_for_action_server(std::chrono::milliseconds(1000))) {
                RCLCPP_INFO(node_->get_logger(), "等待Action服务器...");
                return BT::NodeStatus::RUNNING;
            }
            
            auto send_goal_options = rclcpp_action::Client<DigitalOutputWriteAction>::SendGoalOptions();
            
            send_goal_options.goal_response_callback =
                [this](const GoalHandleDigitalOutputWrite::SharedPtr& goal_handle) {
                    if (!goal_handle) {
                        RCLCPP_ERROR(node_->get_logger(), "Goal被服务器拒绝");
                        goal_completed_ = true;  // 标记为完成，但失败
                    } else {
                        RCLCPP_INFO(node_->get_logger(), "Goal被服务器接受");
                        goal_handle_ = goal_handle;
                    }
                };
                
            send_goal_options.feedback_callback =
                [this](GoalHandleDigitalOutputWrite::SharedPtr,
                      const std::shared_ptr<const DigitalOutputWriteAction::Feedback> feedback) {
                    RCLCPP_INFO(node_->get_logger(), 
                               "当前地址: %d, 值: %s, 进度: %.2f%%",
                               feedback->current_address,
                               feedback->current_value ? "HIGH" : "LOW",
                               feedback->progress * 100.0);
                };
                
            send_goal_options.result_callback =
                [this](const GoalHandleDigitalOutputWrite::WrappedResult& result) {
                    goal_completed_ = true;
                    result_ = result.result;
                    
                    RCLCPP_INFO(node_->get_logger(), "=== DigitalOutputWrite 结果回调 ===");
                    
                    switch (result.code) {
                        case rclcpp_action::ResultCode::SUCCEEDED:
                            RCLCPP_INFO(node_->get_logger(), "Goal执行成功");
                            break;
                        case rclcpp_action::ResultCode::ABORTED:
                            RCLCPP_ERROR(node_->get_logger(), "Goal被中止");
                            break;
                        case rclcpp_action::ResultCode::CANCELED:
                            RCLCPP_ERROR(node_->get_logger(), "Goal被取消");
                            break;
                        default:
                            RCLCPP_ERROR(node_->get_logger(), "未知结果代码");
                            break;
                    }
                    
                    // 打印详细结果
                    if (result_) {
                        RCLCPP_INFO(node_->get_logger(), "操作成功: %s", result_->success ? "是" : "否");
                        RCLCPP_INFO(node_->get_logger(), "错误代码: %d", result_->error_code);
                        RCLCPP_INFO(node_->get_logger(), "错误消息: %s", result_->error_message.c_str());
                        RCLCPP_INFO(node_->get_logger(), "写入操作次数: %d", result_->write_operations);
                        RCLCPP_INFO(node_->get_logger(), "平均写入时间: %.2f毫秒", result_->average_write_time);
                        
                        // 打印所有地址和最终值
                        for (size_t i = 0; i < result_->addresses.size() && i < result_->final_values.size(); ++i) {
                            RCLCPP_INFO(node_->get_logger(), 
                                       "地址[%d] 最终值 = %s, 写入%s", 
                                       result_->addresses[i],
                                       result_->final_values[i] ? "HIGH" : "LOW",
                                       result_->write_success[i] ? "成功" : "失败");
                        }
                    } else {
                        RCLCPP_ERROR(node_->get_logger(), "结果为空");
                    }
                    
                    RCLCPP_INFO(node_->get_logger(), "=== 结果处理完成 ===");
                };
                
            goal_handle_future_ = action_client_->async_send_goal(*goal_msg_, send_goal_options);
            goal_sent_ = true;
            
            return BT::NodeStatus::RUNNING;
        }
        
        // 等待结果
        if (!goal_completed_) {
            return BT::NodeStatus::RUNNING;
        }
        
        // 处理结果
        if (!result_) {
            RCLCPP_ERROR(node_->get_logger(), "未收到结果");
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "未收到结果");
            return BT::NodeStatus::FAILURE;
        }
        
        // 设置输出端口
        bool success = result_->success;
        setOutput<bool>("boolOutputSuccess", success);
        setOutput<std::string>("strOutputMessage", result_->error_message);
        setOutput<int>("intErrorCode", result_->error_code);
        
        // 设置多地址输出
        std::vector<int> final_values(result_->final_values.size());
        std::vector<int> addresses;
        std::vector<int> write_successes(result_->write_success.size());
        
        // 将bool值转换为int值（0或1）
        for (size_t i = 0; i < result_->final_values.size(); i++) {
            final_values[i] = result_->final_values[i] ? 1 : 0;
        }
        for (size_t i = 0; i < result_->write_success.size(); i++) {
            write_successes[i] = result_->write_success[i] ? 1 : 0;
        }
        
        // 将uint16地址转换为int
        addresses.resize(result_->addresses.size());
        for (size_t i = 0; i < result_->addresses.size(); i++) {
            addresses[i] = static_cast<int>(result_->addresses[i]);
        }
        
        setOutput<std::vector<int>>("boolFinalValues", final_values);
        setOutput<std::vector<int>>("intOutputAddresses", addresses);
        setOutput<std::vector<int>>("boolWriteSuccesses", write_successes);
        
        RCLCPP_INFO(node_->get_logger(), 
                   "DigitalOutputWrite完成，总体结果: %s", 
                   success ? "成功" : "失败");
        
        return success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }

    void DigitalOutputWrite::onHalted()
    {
        RCLCPP_INFO(node_->get_logger(), "DigitalOutputWrite - 操作被中断");
        
        if (goal_handle_ && goal_sent_ && !goal_completed_) {
            // 取消goal
            auto cancel_future = action_client_->async_cancel_goal(goal_handle_);
            RCLCPP_INFO(node_->get_logger(), "Goal已取消");
        }
        
        goal_sent_ = false;
        goal_completed_ = false;
        goal_handle_.reset();
    }
} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::DigitalOutputWrite>("DigitalOutputWrite");
} 