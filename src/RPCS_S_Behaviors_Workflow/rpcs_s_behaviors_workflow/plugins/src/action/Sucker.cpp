#include "action/Sucker.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>
#include <string>
#include <vector>
#include <thread>

namespace rpcs_s_behaviors_workflow
{
    Sucker::Sucker(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), sucker_state_(true), timeout_(30.0)
    {
        node_ = std::make_shared<rclcpp::Node>("sucker_node");
    }

    std::string Sucker::createActionName()
    {
        return "/sucker";
    }

    void Sucker::createActionClient()
    {
        std::string action_name = createActionName();
        client_ = rclcpp_action::create_client<SuckerAction>(node_, action_name);
        std::cout << "Sucker 创建Action客户端: " << action_name << std::endl;
    }

    void Sucker::handleActionError(const std::string& error_code, const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        std::cerr << "Sucker Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus Sucker::onStart()
    {
        std::cout << "Sucker - onStart" << std::endl;
        
        // 清理可能的旧状态，确保全新开始
        goal_future_ = std::shared_future<GoalHandleSucker::SharedPtr>();
        result_future_ = std::shared_future<GoalHandleSucker::WrappedResult>();
        goal_handle_.reset();
        
        // 检查吸盘控制状态参数
        if (!getInput<bool>("boolSuckerState", sucker_state_)) {
            std::cout << "Sucker - 缺少吸盘控制状态参数" << std::endl;
            handleActionError("MISSING_PARAMS", "缺少吸盘控制状态参数");
            return BT::NodeStatus::FAILURE;
        }
        
        getInput<double>("doubleTimeout", timeout_);
        
        createActionClient();
        
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "Sucker - Action服务不可用: " << createActionName() << std::endl;
            handleActionError("SERVICE_UNAVAILABLE", "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 短暂延迟确保Action服务完全准备好，并且旧请求已清理
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 解析吸盘序号并构建控制消息
        auto goal_msg = SuckerAction::Goal();
        std::vector<int> sucker_ids;  // 将变量声明移到try块外面
        try {
            goal_msg.suckers.clear();
            
            // 优先处理 intSuckerId 参数（单个吸盘ID）
            if (getInput<int>("intSuckerId", sucker_id_)) {
                std::cout << "Sucker - 使用单个吸盘ID: " << sucker_id_ << std::endl;
                sucker_ids.push_back(sucker_id_);
            }
            // 如果没有 intSuckerId，则处理 strSuckerIds 参数（多个吸盘ID字符串）
            else if (getInput<std::string>("strSuckerIds", sucker_ids_)) {
                std::cout << "Sucker - 使用吸盘ID字符串: " << sucker_ids_ << std::endl;
                
                if (sucker_ids_.empty()) {
                    std::cout << "Sucker - 吸盘序号列表为空" << std::endl;
                    handleActionError("INVALID_CONFIG", "吸盘序号列表为空");
                    return BT::NodeStatus::FAILURE;
                }
                
                // 分割字符串获取吸盘序号
                size_t pos = 0;
                while (pos < sucker_ids_.length()) {
                    size_t comma_pos = sucker_ids_.find(',', pos);
                    std::string id_str;
                    if (comma_pos != std::string::npos) {
                        id_str = sucker_ids_.substr(pos, comma_pos - pos);
                        pos = comma_pos + 1;
                    } else {
                        id_str = sucker_ids_.substr(pos);
                        pos = sucker_ids_.length();
                    }
                    
                    // 去除空格
                    id_str.erase(0, id_str.find_first_not_of(" \t"));
                    id_str.erase(id_str.find_last_not_of(" \t") + 1);
                    
                    if (!id_str.empty()) {
                        try {
                            int id = std::stoi(id_str);
                            sucker_ids.push_back(id);
                        } catch (const std::exception& e) {
                            std::cout << "Sucker - 无效的吸盘序号: " << id_str << std::endl;
                            handleActionError("INVALID_CONFIG", "无效的吸盘序号: " + id_str);
                            return BT::NodeStatus::FAILURE;
                        }
                    }
                }
                
                if (sucker_ids.empty()) {
                    std::cout << "Sucker - 未解析到有效的吸盘序号" << std::endl;
                    handleActionError("INVALID_CONFIG", "未解析到有效的吸盘序号");
                    return BT::NodeStatus::FAILURE;
                }
            }
            // 如果两个参数都没有，报错
            else {
                std::cout << "Sucker - 缺少吸盘序号参数（intSuckerId 或 strSuckerIds）" << std::endl;
                handleActionError("MISSING_PARAMS", "缺少吸盘序号参数");
                return BT::NodeStatus::FAILURE;
            }
            
            // 为每个吸盘序号创建控制项
            for (int id : sucker_ids) {
                vir_robot_interfaces::msg::SuckerKeyValue sucker_item;
                sucker_item.key = static_cast<int8_t>(id);
                sucker_item.value = sucker_state_;
                goal_msg.suckers.push_back(sucker_item);
            }
        } catch (const std::exception& e) {
            std::cout << "Sucker - 参数解析失败: " << e.what() << std::endl;
            handleActionError("PARSE_ERROR", "参数解析失败");
            return BT::NodeStatus::FAILURE;
        }
        
        auto send_goal_options = rclcpp_action::Client<SuckerAction>::SendGoalOptions();
        send_goal_options.goal_response_callback = 
            [this](const GoalHandleSucker::SharedPtr& goal_handle) {
                if (!goal_handle) {
                    std::cout << "Sucker - 目标被拒绝" << std::endl;
                } else {
                    std::cout << "Sucker - 目标被接受" << std::endl;
                }
            };
        send_goal_options.feedback_callback = 
            [this](GoalHandleSucker::SharedPtr, 
                   const std::shared_ptr<const SuckerAction::Feedback> feedback) {
                std::cout << "Sucker - 收到PLC反馈" << std::endl;
                setOutput<std::string>("strPlcFeedback", "PLC反馈已更新");
            };
        send_goal_options.result_callback = 
            [this](const GoalHandleSucker::WrappedResult& result) {
                std::cout << "Sucker - 收到结果" << std::endl;
            };
        
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        start_time_ = std::chrono::steady_clock::now();
        
        // 构建吸盘序号显示字符串
        std::string sucker_display;
        if (sucker_ids.size() == 1) {
            sucker_display = std::to_string(sucker_ids[0]);
        } else {
            for (size_t i = 0; i < sucker_ids.size(); ++i) {
                if (i > 0) sucker_display += ",";
                sucker_display += std::to_string(sucker_ids[i]);
            }
        }
        
        std::cout << "Sucker - 发送吸盘操作请求, 序号: " << sucker_display 
                  << ", 状态: " << (sucker_state_ ? "开启" : "关闭") << std::endl;
        return BT::NodeStatus::RUNNING;
    }
    
    BT::NodeStatus Sucker::onRunning()
    {
        std::cout << "Sucker - onRunning" << std::endl;
        rclcpp::spin_some(node_);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        if (elapsed > timeout_) {
            std::cout << "Sucker - 操作超时" << std::endl;
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            handleActionError("TIMEOUT", "操作超时");
            return BT::NodeStatus::FAILURE;
        }
        
        if (goal_future_.valid()) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "Sucker - 目标被拒绝" << std::endl;
                    handleActionError("GOAL_REJECTED", "目标被拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                result_future_ = client_->async_get_result(goal_handle_);
                goal_future_ = std::shared_future<GoalHandleSucker::SharedPtr>();
            }
        }
        
        if (result_future_.valid()) {
            auto status = result_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                auto result = result_future_.get();
                
                // 先检查result是否有效
                if (!result.result) {
                    std::cout << "Sucker - 收到空结果" << std::endl;
                    handleActionError("EMPTY_RESULT", "收到空结果");
                    return BT::NodeStatus::FAILURE;
                }
                
                switch (result.code) {
                    case rclcpp_action::ResultCode::SUCCEEDED:
                        {
                            auto action_result = result.result;
                            setOutput<bool>("boolOutputSuccess", action_result->success);
                            setOutput<std::string>("strErrorCode", action_result->error_code);
                            setOutput<std::string>("strOutputMessage", action_result->msg);
                            std::cout << "Sucker - 执行完成: " 
                                      << (action_result->success ? "成功" : "失败") 
                                      << ", 消息: " << action_result->msg << std::endl;
                            return action_result->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
                        }
                        break;
                    case rclcpp_action::ResultCode::ABORTED:
                        std::cout << "Sucker - Action被中止" << std::endl;
                        handleActionError("ABORTED", "Action被中止");
                        return BT::NodeStatus::FAILURE;
                    case rclcpp_action::ResultCode::CANCELED:
                        std::cout << "Sucker - Action被取消" << std::endl;
                        handleActionError("CANCELED", "Action被取消");
                        return BT::NodeStatus::FAILURE;
                    default:
                        std::cout << "Sucker - 未知结果状态" << std::endl;
                        handleActionError("UNKNOWN", "未知结果状态");
                        return BT::NodeStatus::FAILURE;
                }
            }
        }
        return BT::NodeStatus::RUNNING;
    }
    
    void Sucker::onHalted()
    {
        std::cout << "Sucker - onHalted" << std::endl;
        try {
            // 取消正在进行的goal
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 彻底清理所有future状态
            goal_future_ = std::shared_future<GoalHandleSucker::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleSucker::WrappedResult>();
            
            // 清理输出端口状态
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strErrorCode", "HALTED");
            setOutput<std::string>("strOutputMessage", "操作被中止");
            
            // 关闭并重置client连接
            if (client_) {
                client_.reset();
            }
            
            // 短暂延迟确保所有异步操作完成
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            std::cout << "Sucker - 清理资源完成" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Sucker - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::Sucker>("Sucker");
}