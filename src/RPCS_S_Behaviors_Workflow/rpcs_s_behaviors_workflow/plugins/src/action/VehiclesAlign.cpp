#include "action/VehiclesAlign.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <opencv2/opencv.hpp>
#include <cv_bridge/cv_bridge.h>
#include <nlohmann/json.hpp>
#include <iostream>
#include <thread>
#include <future>
#include <filesystem>
#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    VehiclesAlign::VehiclesAlign(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
        , request_sent_(false)
    {
        // 初始化ROS节点和客户端
        if (!initializeROS()) {
            std::cerr << "VehiclesAlign - 初始化ROS失败" << std::endl;
        }
    }

    bool VehiclesAlign::initializeROS()
    {
        try {
            // 获取当前上下文中的ROS节点
            auto context = config().blackboard->get<rclcpp::Context::SharedPtr>("__shared_context");
            if (context) {
                // 如果有共享上下文，尝试获取现有节点
                auto existing_node = config().blackboard->get<rclcpp::Node::SharedPtr>("__shared_node");
                if (existing_node) {
                    node_ = existing_node;
                    std::cout << "VehiclesAlign - 使用现有ROS节点" << std::endl;
                } else {
                    // 创建新的ROS节点
                    node_ = rclcpp::Node::make_shared("vehicles_align_node", rclcpp::NodeOptions().context(context));
                    config().blackboard->set("__shared_node", node_);
                    std::cout << "VehiclesAlign - 创建新的ROS节点" << std::endl;
                }
            } else {
                // 创建默认ROS节点
                node_ = rclcpp::Node::make_shared("vehicles_align_node");
                std::cout << "VehiclesAlign - 创建默认ROS节点" << std::endl;
            }

            // 创建载板贴合算法服务客户端
            client_ = node_->create_client<VehiclesAlignService>("/vehicles_align");
            
            return true;
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - 初始化ROS时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    sensor_msgs::msg::Image::SharedPtr VehiclesAlign::loadImageFromPath(const std::string& image_path)
    {
        try {
            // 检查文件是否存在
            if (!std::filesystem::exists(image_path)) {
                std::cerr << "VehiclesAlign - 图像文件不存在: " << image_path << std::endl;
                return nullptr;
            }

            // 使用OpenCV加载图像
            cv::Mat cv_image = cv::imread(image_path, cv::IMREAD_COLOR);
            if (cv_image.empty()) {
                std::cerr << "VehiclesAlign - 无法加载图像: " << image_path << std::endl;
                return nullptr;
            }

            // 转换为ROS图像消息
            std_msgs::msg::Header header;
            header.stamp = node_->get_clock()->now();
            header.frame_id = "camera_frame";
            
            auto ros_image = cv_bridge::CvImage(header, "bgr8", cv_image).toImageMsg();
            auto image_msg = std::make_shared<sensor_msgs::msg::Image>(*ros_image);
            
            std::cout << "VehiclesAlign - 成功加载图像: " << image_path 
                      << ", 尺寸: " << cv_image.cols << "x" << cv_image.rows << std::endl;
            
            return image_msg;
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - 加载图像时发生异常: " << e.what() << std::endl;
            return nullptr;
        }
    }

    BT::NodeStatus VehiclesAlign::onStart()
    {
        try {
            std::cout << "VehiclesAlign - 开始执行载板贴合算法" << std::endl;
            
            // 获取输入参数
            if (!getInput<std::string>("strImagePathA", image_path_a_)) {
                std::cerr << "VehiclesAlign - 缺少图像路径A参数" << std::endl;
                setErrorOutput("缺少图像路径A参数");
                return BT::NodeStatus::FAILURE;
            }
            
            if (!getInput<std::string>("strImagePathB", image_path_b_)) {
                std::cerr << "VehiclesAlign - 缺少图像路径B参数" << std::endl;
                setErrorOutput("缺少图像路径B参数");
                return BT::NodeStatus::FAILURE;
            }
            
            std::string raw_param_overrides;
            if (!getInput<std::string>("strParamOverrides", raw_param_overrides)) {
                raw_param_overrides = "";
            }
            
            // 将键值对格式转换为JSON格式 (param1=123,param2=test -> {"param1":"123","param2":"test"})
            param_overrides_ = convertKeyValuePairsToJson(raw_param_overrides);
            
            if (!getInput<double>("doubleTimeout", timeout_)) {
                timeout_ = 10.0;  // 默认10秒超时
            }
            
            std::cout << "VehiclesAlign - 载板贴合参数: 图像A=" << image_path_a_ 
                      << ", 图像B=" << image_path_b_ 
                      << ", 参数覆盖=" << param_overrides_ 
                      << ", 超时=" << timeout_ << "秒" << std::endl;
            
            // 发送载板贴合算法请求
            if (!sendAlgorithmRequest()) {
                return BT::NodeStatus::FAILURE;
            }
            
            // 记录开始时间
            start_time_ = std::chrono::steady_clock::now();
            request_sent_ = true;
            
            return BT::NodeStatus::RUNNING;
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - onStart时发生异常: " << e.what() << std::endl;
            setErrorOutput("启动时发生异常: " + std::string(e.what()));
            return BT::NodeStatus::FAILURE;
        }
    }

    bool VehiclesAlign::sendAlgorithmRequest()
    {
        try {
            // 检查客户端
            if (!client_) {
                std::cerr << "VehiclesAlign - 载板贴合服务客户端未初始化" << std::endl;
                setErrorOutput("载板贴合服务客户端未初始化");
                return false;
            }
            
            // 等待载板贴合服务可用
            if (!client_->wait_for_service(std::chrono::seconds(2))) {
                std::cerr << "VehiclesAlign - 载板贴合服务不可用: /vehicles_align" << std::endl;
                setErrorOutput("载板贴合服务不可用");
                return false;
            }
            
            // 加载图像
            auto image_a = loadImageFromPath(image_path_a_);
            if (!image_a) {
                setErrorOutput("无法加载图像A: " + image_path_a_);
                return false;
            }
            
            auto image_b = loadImageFromPath(image_path_b_);
            if (!image_b) {
                setErrorOutput("无法加载图像B: " + image_path_b_);
                return false;
            }
            
            // 创建载板贴合算法请求
            auto request = std::make_shared<VehiclesAlignService::Request>();
            //request->image_a = *image_a;
            //request->image_b = *image_b;
            request->param_overrides = param_overrides_;
            
            std::cout << "VehiclesAlign - 正在发送载板贴合算法请求..." << std::endl;
            
            // 发送异步请求
            response_future_ = client_->async_send_request(request).future.share();
            
            std::cout << "VehiclesAlign - 载板贴合算法请求已发送" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - 发送载板贴合算法请求时发生异常: " << e.what() << std::endl;
            setErrorOutput("发送载板贴合算法请求时发生异常: " + std::string(e.what()));
            return false;
        }
    }

    BT::NodeStatus VehiclesAlign::onRunning()
    {
        try {
            // 检查超时
            auto elapsed = std::chrono::steady_clock::now() - start_time_;
            if (std::chrono::duration_cast<std::chrono::seconds>(elapsed).count() > timeout_) {
                std::cerr << "VehiclesAlign - 载板贴合算法执行超时" << std::endl;
                setErrorOutput("载板贴合算法执行超时");
                return BT::NodeStatus::FAILURE;
            }
            
            // 检查响应是否就绪
            if (response_future_.valid()) {
                auto status = response_future_.wait_for(std::chrono::milliseconds(1));
                if (status == std::future_status::ready) {
                    try {
                        auto response = response_future_.get();
                        std::cout << "VehiclesAlign - 收到载板贴合算法响应" << std::endl;
                        
                        if (handleAlgorithmResponse(response)) {
                            return BT::NodeStatus::SUCCESS;
                        } else {
                            return BT::NodeStatus::FAILURE;
                        }
                    } catch (const std::exception& e) {
                        std::cerr << "VehiclesAlign - 获取载板贴合算法响应时发生异常: " << e.what() << std::endl;
                        setErrorOutput("获取载板贴合算法响应时发生异常: " + std::string(e.what()));
                        return BT::NodeStatus::FAILURE;
                    }
                }
            }
            
            // 继续等待
            return BT::NodeStatus::RUNNING;
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - onRunning时发生异常: " << e.what() << std::endl;
            setErrorOutput("运行时发生异常: " + std::string(e.what()));
            return BT::NodeStatus::FAILURE;
        }
    }

    bool VehiclesAlign::handleAlgorithmResponse(VehiclesAlignService::Response::SharedPtr response)
    {
        try {
            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", response->success);
            setOutput<std::string>("strOutputMessage", response->message);
            setOutput<bool>("boolIsFinish", response->is_finish);
            
            // 处理移动命令
            std::string move_commands_json = serializeMoveCommands(response->move_commands);
            setOutput<std::string>("strMoveCommands", move_commands_json);
            setOutput<int>("intMoveCommandsCount", static_cast<int>(response->move_commands.size()));
            
            // 解析并输出x、y、r值
            double x_value = 0.0, y_value = 0.0, r_value = 0.0;
            bool x_enable = false, y_enable = false, r_enable = false;
            for (const auto& cmd : response->move_commands) {
                if (cmd.axis == "x") {
                    x_value = cmd.move_value;
                    x_enable = true;
                } else if (cmd.axis == "y") {
                    y_value = cmd.move_value;
                    y_enable = true;
                } else if (cmd.axis == "r") {
                    r_value = cmd.move_value;
                    r_enable = true;
                }
            }
            setOutput<double>("doubleOutputX", x_value);
            setOutput<double>("doubleOutputY", y_value);
            setOutput<double>("doubleOutputR", r_value);
            setOutput<bool>("boolOutputX", x_enable);
            setOutput<bool>("boolOutputY", y_enable);
            setOutput<bool>("boolOutputR", r_enable);
            // 记录载板贴合算法结果
            std::cout << "VehiclesAlign - 载板贴合算法执行完成: " 
                      << (response->success ? "成功" : "失败") 
                      << ", 消息: " << response->message 
                      << ", 是否完成: " << (response->is_finish ? "是" : "否")
                      << ", 移动命令数量: " << response->move_commands.size() 
                      << ", X=" << x_value << ", Y=" << y_value << ", R=" << r_value << ", X使能=" << x_enable << ", Y使能=" << y_enable << ", R使能=" << r_enable << std::endl;
            
            return response->success;
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - 处理载板贴合算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理载板贴合算法响应时发生异常: " + std::string(e.what()));
            return false;
        }
    }

    void VehiclesAlign::onHalted()
    {
        std::cout << "VehiclesAlign - 载板贴合算法节点被停止" << std::endl;
        request_sent_ = false;
        
        // 清理资源
        if (response_future_.valid()) {
            // 尝试取消请求(如果可能)
            response_future_ = rclcpp::Client<VehiclesAlignService>::SharedFuture();
        }
    }

    void VehiclesAlign::setErrorOutput(const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", error_message);
        setOutput<bool>("boolIsFinish", false);
        setOutput<std::string>("strMoveCommands", "[]");
        setOutput<int>("intMoveCommandsCount", 0);
        
        // 设置x、y、r默认值为0
        setOutput<double>("doubleOutputX", 0.0);
        setOutput<double>("doubleOutputY", 0.0);
        setOutput<double>("doubleOutputR", 0.0);
        
        std::cerr << "VehiclesAlign - 错误: " << error_message << std::endl;
    }

    std::string VehiclesAlign::serializeMoveCommands(const std::vector<algorithm_interface::msg::MoveCommand>& commands)
    {
        try {
            nlohmann::json json_array = nlohmann::json::array();
            
            for (const auto& cmd : commands) {
                nlohmann::json json_cmd;
                json_cmd["axis"] = cmd.axis;
                json_cmd["move_value"] = cmd.move_value;
                
                json_array.push_back(json_cmd);
            }
            
            return json_array.dump();
            
        } catch (const std::exception& e) {
            std::cerr << "VehiclesAlign - 序列化移动命令时发生异常: " << e.what() << std::endl;
            return "[]";
        }
    }

    std::string VehiclesAlign::convertKeyValuePairsToJson(const std::string& key_value_pairs)
    {
        if (key_value_pairs.empty()) {
            return "{}";
        }
        
        std::ostringstream json_stream;
        json_stream << "{";
        
        std::istringstream stream(key_value_pairs);
        std::string pair;
        bool first = true;
        
        // 分割键值对 (用逗号分隔)
        while (std::getline(stream, pair, ',')) {
            // 移除前后空格
            pair.erase(0, pair.find_first_not_of(" \t"));
            pair.erase(pair.find_last_not_of(" \t") + 1);
            
            if (pair.empty()) continue;
            
            // 查找等号位置
            size_t eq_pos = pair.find('=');
            if (eq_pos == std::string::npos) continue;
            
            std::string key = pair.substr(0, eq_pos);
            std::string value = pair.substr(eq_pos + 1);
            
            // 移除键值的前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (!first) {
                json_stream << ",";
            }
            first = false;
            
            // 构建JSON键值对（键和值都用双引号包围）
            json_stream << "\"" << key << "\":\"" << value << "\"";
        }
        
        json_stream << "}";
        return json_stream.str();
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::VehiclesAlign>("VehiclesAlign");
} 