#include "action/CommonAlign.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <iostream>
#include <future>
#include <thread>
#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <atomic>
#include <algorithm>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    CommonAlign::CommonAlign(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), request_sent_(false)
    {
        // 创建ROS节点
        node_ = std::make_shared<rclcpp::Node>("common_align_node");
    }

    BT::NodeStatus CommonAlign::onStart()
    {
        std::cout << "CommonAlign - onStart: " << name() << std::endl;
        
        // 获取超时参数
        auto timeout_opt = getInput<double>("doubleTimeout");
        timeout_ = timeout_opt.value_or(30.0);
        
        // 准备算法请求
        if (!prepareAlgorithmRequest()) {
            setErrorOutput("准备算法请求失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 发送算法请求
        if (!sendAlgorithmRequest()) {
            setErrorOutput("发送算法请求失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 设置开始时间
        start_time_ = std::chrono::steady_clock::now();
        request_sent_ = true;
        
        std::cout << "CommonAlign - 算法请求已发送，等待响应..." << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus CommonAlign::onRunning()
    {
        // 检查超时
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time_).count();
        
        if (elapsed >= timeout_) {
            setErrorOutput("算法执行超时");
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查算法响应
        if (handleAlgorithmResponse()) {
            std::cout << "CommonAlign - 算法执行完成" << std::endl;
            return BT::NodeStatus::SUCCESS;
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void CommonAlign::onHalted()
    {
        std::cout << "CommonAlign - onHalted" << std::endl;
        request_sent_ = false;
    }

    bool CommonAlign::prepareAlgorithmRequest()
    {
        try {
            // 获取输入参数
            auto model_path_opt = getInput<std::string>("strModelPath");
            model_path_ = model_path_opt.value_or("/test");
            
            auto pixel_dimensions_opt = getInput<double>("doublePixelDimensions");
            pixel_dimensions_ = pixel_dimensions_opt.value_or(0.1);
            
            auto h_w_c_opt = getInput<std::string>("strHWC");
            h_w_c_ = h_w_c_opt.value_or("test");
            
            auto param_overrides_opt = getInput<std::string>("strParamOverrides");
            std::string raw_param_overrides = param_overrides_opt.value_or("");
            
            // 将键值对格式转换为JSON格式 (param1=123,param2=test -> {"param1":"123","param2":"test"})
            param_overrides_ = convertKeyValuePairsToJson(raw_param_overrides);

            std::cout << "CommonAlign - 准备算法请求: 模板路径=" << model_path_ 
                      << ", 像素尺寸=" << pixel_dimensions_ << std::endl
                      << ", 3x3变换矩阵=" << h_w_c_ << std::endl
                      << ", 参数覆盖=" << param_overrides_ << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "CommonAlign - 准备算法请求时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool CommonAlign::sendAlgorithmRequest()
    {
        try {
            std::cout << "CommonAlign - 开始发送算法请求..." << std::endl;
            
            // 创建Service客户端
            if (!client_) {
                std::string service_name = "/common_align";
                client_ = node_->create_client<CommonAlignService>(service_name);
                
                if (!client_->wait_for_service(std::chrono::seconds(20))) {
                    std::cerr << "CommonAlign Service服务不可用: " << service_name << std::endl;
                    return false;
                }
            }

            // 创建请求
            auto request = std::make_shared<CommonAlignService::Request>();
            request->model_path = model_path_;
            request->pixel_dimensions = pixel_dimensions_;
            request->h_w_c = h_w_c_;
            request->param_overrides = param_overrides_;

            std::cout << "CommonAlign - 正在调用算法服务..." << std::endl;
            algorithm_future_ = client_->async_send_request(request);
            
            // 检查future是否有效
            if (!algorithm_future_.valid()) {
                std::cerr << "CommonAlign - 算法future无效，服务调用失败" << std::endl;
                return false;
            }
            
            std::cout << "CommonAlign - 算法服务请求已发送" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "CommonAlign - 发送算法请求时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool CommonAlign::handleAlgorithmResponse()
    {
        try {
            // 检查是否有响应
            if (!algorithm_future_.valid()) {
                return false;
            }
            
            // 非阻塞检查响应
            auto status = algorithm_future_.wait_for(std::chrono::milliseconds(10));
            if (status != std::future_status::ready) {
                return false; // 还没有响应
            }
            
            auto response = algorithm_future_.get();
            if (!response) {
                std::cerr << "CommonAlign - 收到空响应" << std::endl;
                setErrorOutput("收到空响应");
                return true; // 返回true表示处理完成，但结果是失败
            }
            
            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", response->success);
            setOutput<std::string>("strOutputMessage", response->message);
            setOutput<double>("doubleX", response->x);
            setOutput<double>("doubleY", response->y);
            setOutput<double>("doubleRZ", response->rz);
            
            // 记录结果
            std::cout << "CommonAlign - 算法执行完成: " 
                      << (response->success ? "成功" : "失败") 
                      << ", 消息: " << response->message 
                      << ", 坐标: (" << response->x 
                      << ", " << response->y 
                      << ", " << response->rz << ")" << std::endl;
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "CommonAlign - 处理算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理算法响应时发生异常: " + std::string(e.what()));
            return true; // 返回true表示处理完成，但结果是失败
        }
    }

    void CommonAlign::setErrorOutput(const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", error_message);
        setOutput<double>("doubleX", 0.0);
        setOutput<double>("doubleY", 0.0);
        setOutput<double>("doubleRZ", 0.0);
        
        std::cerr << "CommonAlign - 错误: " << error_message << std::endl;
    }

    std::string CommonAlign::convertKeyValuePairsToJson(const std::string& key_value_pairs)
    {
        if (key_value_pairs.empty()) {
            return "{}";
        }
        
        std::ostringstream json_stream;
        json_stream << "{";
        
        std::istringstream stream(key_value_pairs);
        std::string pair;
        bool first = true;
        
        // 分割键值对 (用逗号分隔)
        while (std::getline(stream, pair, ',')) {
            // 移除前后空格
            pair.erase(0, pair.find_first_not_of(" \t"));
            pair.erase(pair.find_last_not_of(" \t") + 1);
            
            if (pair.empty()) continue;
            
            // 查找等号位置
            size_t eq_pos = pair.find('=');
            if (eq_pos == std::string::npos) continue;
            
            std::string key = pair.substr(0, eq_pos);
            std::string value = pair.substr(eq_pos + 1);
            
            // 移除键值的前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (!first) {
                json_stream << ",";
            }
            first = false;
            
            // 构建JSON键值对（键和值都用双引号包围）
            json_stream << "\"" << key << "\":\"" << value << "\"";
        }
        
        json_stream << "}";
        return json_stream.str();
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::CommonAlign>("CommonAlign");
} 