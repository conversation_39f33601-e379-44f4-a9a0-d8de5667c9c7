#include "action/MotorPositionControl.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>

namespace rpcs_s_behaviors_workflow
{
    MotorPositionControl::MotorPositionControl(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(15.0)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("motor_position_control_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("motor_position_control_node", controller_motor_node_ros_domain_id);
        
        // 获取设备ID和电机ID(在onStart中获取，此处不做处理)
        // 创建Action客户端将在onStart中进行
    }

    std::string MotorPositionControl::createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        if (device_id.empty() || device_id == "default") {
            return fmt::format("/{}_{}/position_control", motor_brand, motor_id);
        } else {
            return fmt::format("/{}/{}_{}/position_control", device_id, motor_brand, motor_id);
        }
    }

    void MotorPositionControl::createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        // 创建Action客户端
        std::string action_name = createActionName(device_id, motor_brand, motor_id);
        client_ = rclcpp_action::create_client<PositionControlAction>(node_, action_name);

        std::cout << "MotorPositionControl 创建Action客户端: " << action_name << std::endl;
    }

    void MotorPositionControl::handleActionError(int error_code, const std::string& error_message)
    {
        // 在错误信息中添加电机ID标识
        std::string enhanced_message = fmt::format("motor_id_{} - {}", motor_id_, error_message);
        
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<int>("intErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", enhanced_message);
        
        // 记录详细日志
        std::cerr << "MotorPositionControl Action失败: [" << error_code << "] " << enhanced_message << std::endl;
    }

    BT::NodeStatus MotorPositionControl::onStart()
    {
        std::cout << "MotorPositionControl - onStart" << std::endl;
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "Robot1"; // 使用默认值
        }

        if (!getInput<std::string>("strMotorBrand", motor_brand_)) {
            motor_brand_ = "Kinco"; // 使用默认值
        }

        if (!getInput<int>("intMotorId", motor_id_)) {
            std::cout << "MotorPositionControl - 无法获取intMotorId参数" << std::endl;
            handleActionError(-1, "缺少电机ID参数");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput<double>("doubleTargetPosition", target_position_)) {
            std::cout << "MotorPositionControl - 无法获取doubleTargetPosition参数" << std::endl;
            handleActionError(-2, "缺少目标位置参数");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取其他参数（带默认值）
        getInput<bool>("boolAbsolutePosition", absolute_position_);
        getInput<double>("doubleMaxVelocity", max_velocity_);
        getInput<double>("doubleAcceleration", acceleration_);
        getInput<double>("doubleDeceleration", deceleration_);
        getInput<double>("doubleDwellTime", dwell_time_);
        getInput<double>("doubleTimeout", timeout_);
        
        // 创建Action客户端
        createActionClient(device_id_, motor_brand_, motor_id_);

        // 等待Action服务可用
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "MotorPositionControl - Action服务不可用: " << createActionName(device_id_, motor_brand_, motor_id_) << std::endl;
            handleActionError(-3, "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建Action目标
        auto goal_msg = PositionControlAction::Goal();
        goal_msg.target_position = target_position_;
        goal_msg.absolute_position = absolute_position_;
        goal_msg.max_velocity = max_velocity_;
        goal_msg.acceleration = acceleration_;
        goal_msg.deceleration = deceleration_;
        goal_msg.dwell_time = dwell_time_;
        goal_msg.timeout = timeout_;
        
        // 记录请求信息
        std::cout << "MotorPositionControl - 发送位置控制请求:" << std::endl;
        std::cout << "  设备ID: " << device_id_ << ", 电机ID: " << motor_id_ << std::endl;
        std::cout << "  目标位置: " << target_position_ << "mm" << std::endl;
        std::cout << "  绝对位置: " << (absolute_position_ ? "是" : "否") << std::endl;
        std::cout << "  最大速度: " << max_velocity_ << "mm/s" << std::endl;
        std::cout << "  加速度: " << acceleration_ << "mm/s²" << std::endl;
        std::cout << "  减速度: " << deceleration_ << "mm/s²" << std::endl;
        std::cout << "  停留时间: " << dwell_time_ << "s" << std::endl;
        std::cout << "  超时时间: " << timeout_ << "s" << std::endl;
        
        // 发送Action请求
        auto send_goal_options = rclcpp_action::Client<PositionControlAction>::SendGoalOptions();
        
        // 设置反馈回调
        send_goal_options.feedback_callback = 
            [this](GoalHandlePositionControl::SharedPtr, const std::shared_ptr<const PositionControlAction::Feedback> feedback) {
                // 更新实时状态输出
                setOutput<double>("doubleCurrentPosition", feedback->current_position);
                setOutput<double>("doubleProgress", feedback->progress);
                
                std::cout << "MotorPositionControl - 反馈: 当前位置=" << feedback->current_position 
                         << "mm, 进度=" << (feedback->progress * 100) << "%" << std::endl;
            };
        
        // 发送目标
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus MotorPositionControl::onRunning()
    {
        std::cout << "MotorPositionControl - onRunning" << std::endl;
        
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_) {
            std::cout << "MotorPositionControl - Action请求超时" << std::endl;
            handleActionError(-4, "Action请求超时");
            
            // 取消目标
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查目标句柄是否就绪
        if (!goal_handle_) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "MotorPositionControl - 目标被拒绝" << std::endl;
                    handleActionError(-5, "Action目标被服务器拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                
                // 获取结果Future
                result_future_ = client_->async_get_result(goal_handle_);
                std::cout << "MotorPositionControl - 目标已接受，开始执行" << std::endl;
            }
            return BT::NodeStatus::RUNNING;
        }
        
        // 检查结果是否就绪
        auto status = result_future_.wait_for(std::chrono::milliseconds(10));
        if (status == std::future_status::ready) {
            auto wrapped_result = result_future_.get();
            
            std::cout << "MotorPositionControl - Action完成" << std::endl;
            
            // 设置输出端口
            bool success = (wrapped_result.code == rclcpp_action::ResultCode::SUCCEEDED);
            setOutput<bool>("boolOutputSuccess", success);
            setOutput<double>("doubleFinalPosition", wrapped_result.result->final_position);
            setOutput<double>("doublePositionError", wrapped_result.result->position_error);
            setOutput<int>("intErrorCode", wrapped_result.result->error_code);
            
            // 在错误信息中添加电机ID标识
            std::string result_message = wrapped_result.result->error_message;
            if (!success) {
                result_message = fmt::format("motor_id_{} - {}", motor_id_, result_message);
            }
            setOutput<std::string>("strOutputMessage", result_message);
            
            // 记录结果
            std::cout << "MotorPositionControl - 执行结果: " << (success ? "成功" : "失败") << std::endl;
            std::cout << "MotorPositionControl - 最终位置: " << wrapped_result.result->final_position << "mm" << std::endl;
            std::cout << "MotorPositionControl - 位置误差: " << wrapped_result.result->position_error << "mm" << std::endl;
            std::cout << "MotorPositionControl - 消息: " << result_message << std::endl;
            
            return success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        // Action尚未完成，继续等待
        return BT::NodeStatus::RUNNING;
    }

    void MotorPositionControl::onHalted()
    {
        std::cout << "MotorPositionControl - onHalted" << std::endl;
        
        try {
            // 取消正在进行的Action
            if (goal_handle_) {
                std::cout << "MotorPositionControl - 取消Action目标" << std::endl;
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 重置Future对象
            goal_future_ = std::shared_future<GoalHandlePositionControl::SharedPtr>();
            result_future_ = std::shared_future<GoalHandlePositionControl::WrappedResult>();
            
            // 设置取消状态输出，添加电机ID标识
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", fmt::format("motor_id_{} - 操作被取消", motor_id_));
            setOutput<int>("intErrorCode", -99);
            
            // 清理Action客户端
            if (client_) {
                std::cout << "MotorPositionControl - 清理Action客户端" << std::endl;
                client_.reset();
            }
            
            std::cout << "MotorPositionControl - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "MotorPositionControl - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MotorPositionControl>("MotorPositionControl");
} 