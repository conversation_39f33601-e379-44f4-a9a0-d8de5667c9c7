#include "action/DepthFusion.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <iostream>
#include <future>
#include <thread>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    using DepthFusionService = algorithm_interface::srv::DepthFusion;
    
    DepthFusion::DepthFusion(const std::string& name, const BT::NodeConfiguration& config)
        : AlgorithmNodeWithCamera(name, config), current_phase_(ExecutionPhase::CALLING_ALGORITHM), timeout_(30.0), algorithm_success_(false)
    {
        // 基类已经创建了ROS节点，无需重复创建
    }

    // 重写基类的onStart方法，跳过相机初始化
    BT::NodeStatus DepthFusion::onStart()
    {
        std::cout << "DepthFusion - onStart: " << name() << std::endl;
        
        // 获取超时参数
        if (!getInput<double>("doubleTimeout", timeout_)) {
            timeout_ = 30.0;  // 默认30秒
        }
        
        // 获取输入参数
        if (!getInput<std::string>("strInputDirectory", input_directory_)) {
            std::cerr << "DepthFusion - 缺少输入目录参数" << std::endl;
            setErrorOutput("缺少输入目录参数");
            return BT::NodeStatus::FAILURE;
        }

        if (!getInput<std::string>("strOutputPath", output_path_)) {
            std::cerr << "DepthFusion - 缺少输出路径参数" << std::endl;
            setErrorOutput("缺少输出路径参数");
            return BT::NodeStatus::FAILURE;
        }

        std::string raw_param_overrides;
        if (!getInput<std::string>("strParamOverrides", raw_param_overrides)) {
            raw_param_overrides = "";
        }
        
        // 将键值对格式转换为JSON格式 (param1=123,param2=test -> {"param1":"123","param2":"test"})
        param_overrides_ = convertKeyValuePairsToJson(raw_param_overrides);

        // 创建Service客户端
        if (!client_) {
            std::string service_name = getAlgorithmServiceName();
            auto node = getNode();
            if (!node) {
                std::cerr << "DepthFusion - ROS节点不可用" << std::endl;
                setErrorOutput("ROS节点不可用");
                return BT::NodeStatus::FAILURE;
            }
            
            client_ = node->create_client<DepthFusionService>(service_name);
            
            if (!client_->wait_for_service(std::chrono::seconds(2))) {
                std::cerr << "DepthFusion Service服务不可用: " << service_name << std::endl;
                setErrorOutput("DepthFusion Service服务不可用: " + service_name);
                return BT::NodeStatus::FAILURE;
            }
        }

        // 准备算法请求
        auto request = prepareAlgorithmRequest({});  // 传入空图像列表
        if (!request) {
            std::cerr << "DepthFusion - 准备算法请求失败" << std::endl;
            setErrorOutput("准备算法请求失败");
            return BT::NodeStatus::FAILURE;
        }

        // 发送算法请求
        algorithm_future_ = sendAlgorithmRequest(request);
        if (!algorithm_future_.valid()) {
            std::cerr << "DepthFusion - 发送算法请求失败" << std::endl;
            setErrorOutput("发送算法请求失败");
            return BT::NodeStatus::FAILURE;
        }

        // 设置开始时间和阶段
        start_time_ = std::chrono::steady_clock::now();
        current_phase_ = ExecutionPhase::CALLING_ALGORITHM;
        
        std::cout << "DepthFusion - 开始调用算法服务: " << getAlgorithmServiceName() << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    // 重写基类的onRunning方法
    BT::NodeStatus DepthFusion::onRunning()
    {
        // 处理ROS事件，确保服务响应能够被接收
        auto node = getNode();
        if (node) {
            rclcpp::spin_some(node);
        }
        
        // 检查超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        if (elapsed > timeout_) {
            std::cout << "DepthFusion - 执行超时 (已运行 " << elapsed << " 秒，超时时间 " << timeout_ << " 秒)" << std::endl;
            setErrorOutput("执行超时");
            return BT::NodeStatus::FAILURE;
        }
        
        // 每5秒打印一次进度信息
        static auto last_progress_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::seconds>(current_time - last_progress_time).count() >= 5) {
            std::cout << "DepthFusion - 已运行 " << elapsed << " 秒，当前阶段: 算法调用阶段" << std::endl;
            last_progress_time = current_time;
        }
        
        switch (current_phase_) {
            case ExecutionPhase::CALLING_ALGORITHM:
                if (checkAlgorithmResponse()) {
                    current_phase_ = ExecutionPhase::COMPLETED;
                    std::cout << "DepthFusion - 算法执行完成，检查结果..." << std::endl;
                    // 根据算法执行结果返回相应状态
                    if (algorithm_success_) {
                        std::cout << "DepthFusion - 算法执行成功" << std::endl;
                        return BT::NodeStatus::SUCCESS;
                    } else {
                        std::cout << "DepthFusion - 算法执行失败" << std::endl;
                        return BT::NodeStatus::FAILURE;
                    }
                }
                break;
                
            case ExecutionPhase::COMPLETED:
                // 根据算法执行结果返回相应状态
                if (algorithm_success_) {
                    return BT::NodeStatus::SUCCESS;
                } else {
                    return BT::NodeStatus::FAILURE;
                }
        }
        
        return BT::NodeStatus::RUNNING;
    }

    // 重写基类的onHalted方法
    void DepthFusion::onHalted()
    {
        std::cout << "DepthFusion - onHalted: " << name() << std::endl;
        
        try {
            setErrorOutput("操作被中止");
            
            // 清理Future
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            
            std::cout << "DepthFusion - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "DepthFusion - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }

    std::string DepthFusion::getAlgorithmServiceName()
    {
        return "/depth_fusion";
    }

    std::vector<AlgorithmNodeWithCamera::CameraConfig> DepthFusion::getCameraConfigs()
    {
        // DepthFusion不需要相机，但为了兼容基类，返回空配置
        return {};
    }

    std::shared_ptr<void> DepthFusion::prepareAlgorithmRequest(
        const std::vector<sensor_msgs::msg::Image>& images)
    {
        try {
            // DepthFusion不需要图像输入，但为了兼容基类接口，我们接受图像参数但不使用
            // 检查图像数量（可以为0，因为DepthFusion不需要相机）
            if (!images.empty()) {
                std::cout << "DepthFusion - 收到" << images.size() << "张图像，但DepthFusion不需要图像输入" << std::endl;
            }

            // 创建请求
            auto request = std::make_shared<DepthFusionService::Request>();
            
            // 确保使用正确的字符串类型，与ROS2服务接口兼容
            request->input_directory = std::string(input_directory_);
            request->output_path = std::string(output_path_);
            request->param_overrides = std::string(param_overrides_);

            std::cout << "DepthFusion - 准备算法请求: 输入目录=" << input_directory_ 
                      << ", 输出路径=" << output_path_ 
                      << ", 参数覆盖=" << param_overrides_ << std::endl;
            return request;

        } catch (const std::exception& e) {
            std::cerr << "DepthFusion - 准备算法请求时发生异常: " << e.what() << std::endl;
            return nullptr;
        }
    }

    std::shared_future<std::shared_ptr<void>> DepthFusion::sendAlgorithmRequest(
        std::shared_ptr<void> request)
    {
        try {
            auto depth_fusion_request = std::static_pointer_cast<DepthFusionService::Request>(request);
            // 发送异步请求
            auto future = client_->async_send_request(depth_fusion_request);
            // 将FutureAndRequestId转换为std::shared_future<std::shared_ptr<void>>
            auto shared_future = std::make_shared<std::promise<std::shared_ptr<void>>>();
            auto result_future = shared_future->get_future().share();
            
            // 异步处理结果
            std::thread([future = std::move(future), shared_future]() mutable {
                try {
                    auto response = future.get();
                    shared_future->set_value(response);
                } catch (const std::exception& e) {
                    shared_future->set_exception(std::current_exception());
                }
            }).detach();
            
            return result_future;
        } catch (const std::exception& e) {
            std::cerr << "DepthFusion - 发送算法请求时发生异常: " << e.what() << std::endl;
            // 返回一个空的future
            return std::shared_future<std::shared_ptr<void>>();
        }
    }

    bool DepthFusion::handleAlgorithmResponse(std::shared_ptr<void> response)
    {
        try {
            auto depth_fusion_response = std::static_pointer_cast<DepthFusionService::Response>(response);
            
            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", depth_fusion_response->success);
            setOutput<std::string>("strOutputMessage", depth_fusion_response->message);
            setOutput<std::string>("strOutputPath", depth_fusion_response->output_path);
            
            // 记录算法执行结果
            algorithm_success_ = depth_fusion_response->success;
            
            // 记录结果
            std::cout << "DepthFusion - 算法执行完成: " 
                      << (depth_fusion_response->success ? "成功" : "失败") 
                      << ", 消息: " << depth_fusion_response->message 
                      << ", 输出路径: " << depth_fusion_response->output_path << std::endl;
            
            return depth_fusion_response->success;
            
        } catch (const std::exception& e) {
            std::cerr << "DepthFusion - 处理算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理算法响应时发生异常: " + std::string(e.what()));
            algorithm_success_ = false;
            return false;
        }
    }

    void DepthFusion::setErrorOutput(const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", error_message);
        setOutput<std::string>("strOutputPath", "");
        
        std::cerr << "DepthFusion - 错误: " << error_message << std::endl;
    }

    // 检查算法响应的方法
    bool DepthFusion::checkAlgorithmResponse()
    {
        // 检查future是否有效
        if (!algorithm_future_.valid()) {
            // 如果future无效且当前阶段是CALLING_ALGORITHM，说明已经处理过了
            if (current_phase_ == ExecutionPhase::CALLING_ALGORITHM) {
                // 这种情况不应该发生，说明有逻辑错误
                std::cerr << "DepthFusion - 算法future无效，但仍在CALLING_ALGORITHM阶段" << std::endl;
                return false;
            }
            return false;
        }
        
        auto status = algorithm_future_.wait_for(std::chrono::milliseconds(10));
        if (status != std::future_status::ready) {
            return false; // 算法还未完成
        }
        
        try {
            std::cout << "DepthFusion - 算法响应已就绪，开始处理..." << std::endl;
            
            // 获取响应并立即处理，避免future被重复消费
            auto response = algorithm_future_.get();
            bool result = handleAlgorithmResponse(response);
            
            // 清理future，避免重复处理
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            
            // 设置执行状态为已完成
            current_phase_ = ExecutionPhase::COMPLETED;
            
            return result;
            
        } catch (const std::exception& e) {
            std::cerr << "DepthFusion - 处理算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理算法响应时发生异常: " + std::string(e.what()));
            
            // 清理future
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            
            // 设置执行状态为已完成
            current_phase_ = ExecutionPhase::COMPLETED;
            return false;
        }
    }

    std::string DepthFusion::convertKeyValuePairsToJson(const std::string& key_value_pairs)
    {
        if (key_value_pairs.empty()) {
            return "{}";
        }
        
        std::ostringstream json_stream;
        json_stream << "{";
        
        std::istringstream stream(key_value_pairs);
        std::string pair;
        bool first = true;
        
        // 分割键值对 (用逗号分隔)
        while (std::getline(stream, pair, ',')) {
            // 移除前后空格
            pair.erase(0, pair.find_first_not_of(" \t"));
            pair.erase(pair.find_last_not_of(" \t") + 1);
            
            if (pair.empty()) continue;
            
            // 查找等号位置
            size_t eq_pos = pair.find('=');
            if (eq_pos == std::string::npos) continue;
            
            std::string key = pair.substr(0, eq_pos);
            std::string value = pair.substr(eq_pos + 1);
            
            // 移除键值的前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (!first) {
                json_stream << ",";
            }
            first = false;
            
            // 构建JSON键值对（键和值都用双引号包围）
            json_stream << "\"" << key << "\":\"" << value << "\"";
        }
        
        json_stream << "}";
        return json_stream.str();
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::DepthFusion>("DepthFusion");
} 