#include "action/AgvGoPoint.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <nlohmann/json.hpp>
#include "rpcs_s_interfaces_agv/srv/agv_go_point.hpp"

namespace rpcs_s_behaviors_workflow
{
    AgvGoPoint::AgvGoPoint(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_ms_(30000)
    {
        // 获取已有的ROS节点或创建新节点
        node_ = rclcpp::Node::SharedPtr(std::make_shared<rclcpp::Node>("agv_go_point_node"));
        
        // 默认使用/Robot1命名空间
        std::string namespace_prefix = "/Robot1";
        try {
            namespace_prefix = config.input_ports.at("strNamespace");
        } catch (const std::exception& e) {
            // 使用默认值
        }
        createServiceClient(namespace_prefix);
    }

    void AgvGoPoint::createServiceClient(const std::string& namespace_prefix)
    {
        // 创建服务客户端
        std::string service_name = namespace_prefix + "/AgvGoPointService";
        client_ = node_->create_client<rpcs_s_interfaces_agv::srv::AgvGoPoint>(service_name);
        
        std::cout << "AgvGoPoint创建服务客户端: " << service_name << std::endl;
    }

    BT::NodeStatus AgvGoPoint::onStart()
    {
        std::cout << "AgvGoPoint - onStart" << std::endl;
        
        // 获取输入参数
        if (!getInput<std::string>("strGoPointName", go_point_name_)) {
            std::cout << "AgvGoPoint - 无法获取strGoPointName参数" << std::endl;
            return BT::NodeStatus::FAILURE;
        }

        getInput<int>("intTimeoutMs", timeout_ms_);
        
        // 等待服务可用
        if (!client_->wait_for_service(std::chrono::milliseconds(1000))) {
            std::cout << "AgvGoPoint - 服务不可用: " << client_->get_service_name() << std::endl;
            
            // 模拟服务调用成功
            std::cout << "AgvGoPoint - 模拟服务调用成功" << std::endl;
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "服务不可用");
            setOutput<std::string>("strOutputResponse", "");
            
            // 直接返回成功
            return BT::NodeStatus::FAILURE;
        }

        // 创建请求
        auto request = std::make_shared<rpcs_s_interfaces_agv::srv::AgvGoPoint::Request>();
        
        // 构造JSON请求 - 使用原始字符串字面量
        std::string json_str = R"({"go_point_name": ")" + go_point_name_ + R"("})";
        request->request = json_str;
        
        // 记录请求信息
        std::cout << "AgvGoPoint - 发送AGV移动请求到: " << go_point_name_ << std::endl;
        std::cout << "AgvGoPoint - 请求内容: " << request->request << std::endl;

        // 发送服务请求 - 修复API废弃警告
        auto result_future = client_->async_send_request(request);
        response_future_ = result_future.future.share();
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus AgvGoPoint::onRunning()
    {
        std::cout << "AgvGoPoint - onRunning "  << std::endl;
        
        // 处理ROS事件，确保回调被处理
        rclcpp::spin_some(node_);
        
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_ms_) {
            std::cout << "AgvGoPoint - 服务请求超时" << std::endl;
            
            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "服务请求超时");
            setOutput<std::string>("strOutputResponse", "");
            
            return BT::NodeStatus::FAILURE;
        }
        else {
            std::cout << "elapsed: " << elapsed << " timeout_ms_: " << timeout_ms_ << std::endl;
        }
        
        // 检查响应是否就绪
        auto status = response_future_.wait_for(std::chrono::milliseconds(10));
        std::cout << "AgvGoPoint - 响应状态: " << (status == std::future_status::ready ? "就绪" : 
                                                 status == std::future_status::timeout ? "等待中" : "延迟") << std::endl;
        
        if (status == std::future_status::ready) {
            // 获取响应
            std::cout << "AgvGoPoint - 响应就绪，获取响应内容" << std::endl;
            auto response = response_future_.get();
            
            // 设置输出端口 - 直接映射服务响应
            setOutput<bool>("boolOutputSuccess", response->success);
            setOutput<std::string>("strOutputMessage", response->message);
            setOutput<std::string>("strOutputResponse", response->response);
            
            // 记录响应
            std::cout << "AgvGoPoint - 响应状态: " << (response->success ? "成功" : "失败") << std::endl;
            std::cout << "AgvGoPoint - 响应消息: " << response->message << std::endl;
            
            // 返回节点状态
            return response->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        // 请求尚未完成，继续等待
        return BT::NodeStatus::RUNNING;
    }

    void AgvGoPoint::onHalted()
    {
        std::cout << "AgvGoPoint - onHalted" << std::endl;
        // 添加取消请求的逻辑
        
        // 释放响应的Future
        try {
            // 重置响应Future，避免在任务切换时访问已释放的资源
            response_future_ = rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedFuture();
            
            // 确保输出端口有合理的值，防止未初始化状态
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "操作被取消");
            setOutput<std::string>("strOutputResponse", "");
            
            // 安全关闭服务客户端
            if (client_) {
                std::cout << "AgvGoPoint - 关闭服务客户端" << std::endl;
                client_.reset();
            }
            
            // 停止ROS事件处理，避免后续回调
            if (node_) {
                std::cout << "AgvGoPoint - 停止ROS节点" << std::endl;
                // 仅减少引用计数，不完全销毁节点，避免影响其他部分
                // 注意：ROS2中节点和客户端/服务端是强耦合的，直接重置可能导致问题
                node_.reset();
            }
            
            std::cout << "AgvGoPoint - 清理资源完成" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "AgvGoPoint - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::AgvGoPoint>("AgvGoPoint");
} 