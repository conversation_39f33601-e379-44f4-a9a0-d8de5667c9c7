#include "action/CleanStatus.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    CleanStatus::CleanStatus(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
    {
    }

    BT::NodeStatus CleanStatus::onStart()
    {
        std::cout << "CleanStatus - onStart" << std::endl;
        start_time = std::chrono::steady_clock::now();
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus CleanStatus::onRunning()
    {
        std::cout << "CleanStatus - onRunning" << std::endl;
        if (std::chrono::steady_clock::now() - start_time < std::chrono::seconds(rpcs_s_behaviors_workflow::DEFAULT_DELAY_TIME))
        {
            return BT::NodeStatus::RUNNING;
        }
        else
        {
            setOutput("strAllstatus", true);
            return BT::NodeStatus::SUCCESS;
        }
    }

    void CleanStatus::onHalted()
    {
        std::cout << "CleanStatus - onHalted" << std::endl;
        // TODO: Implement the halted behavior
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::CleanStatus>("CleanStatus");
}
