#include "action/CorrectionPosition.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>
#include <random> // Added for generateRequestId
#include <sstream> // Added for generateRequestId
#include <thread> // Added for sleep_for

namespace rpcs_s_behaviors_workflow
{
    CorrectionPosition::CorrectionPosition(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(30.0)
    {
        // 生成随机字符串用于node名称，避免多个实例冲突
        std::string random_suffix = generateRandomString(8);
        std::string node_name = "correction_position_node_" + random_suffix;
        node_ = std::make_shared<rclcpp::Node>(node_name);
        std::cout << "CorrectionPosition - 创建节点: " << node_name << std::endl;
    }

    std::string CorrectionPosition::createActionName()
    {
        return "/correction_position_v1";
    }

    std::string CorrectionPosition::generateRandomString(int length)
    {
        // 生成指定长度的随机十六进制字符串
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);
        
        std::stringstream ss;
        for (int i = 0; i < length; ++i) {
            ss << std::hex << dis(gen);
        }
        return ss.str();
    }

    std::string CorrectionPosition::generateRequestId()
    {
        // 生成随机字符串作为请求ID
        return "req_" + generateRandomString(16);
    }

    void CorrectionPosition::createActionClient()
    {
        std::string action_name = createActionName();
        client_ = rclcpp_action::create_client<CorrectionPositionAction>(node_, action_name);
        std::cout << "CorrectionPosition 创建Action客户端: " << action_name << std::endl;
    }

    void CorrectionPosition::handleActionError(const std::string& error_code, const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        std::cerr << "CorrectionPosition Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus CorrectionPosition::onStart()
    {
        std::cout << "CorrectionPosition - onStart" << std::endl;
        
        // 清理可能的旧状态，确保全新开始
        goal_future_ = std::shared_future<GoalHandleCorrectionPosition::SharedPtr>();
        result_future_ = std::shared_future<GoalHandleCorrectionPosition::WrappedResult>();
        goal_handle_.reset();
        
        // 获取必须参数
        if (!getInput<std::string>("strCommandType", command_type_)) {
            std::cout << "CorrectionPosition - 缺少必须参数: strCommandType" << std::endl;
            handleActionError("MISSING_PARAMS", "缺少必须参数: strCommandType");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取可选参数
        getInput<double>("doubleTimeout", timeout_);
        
        createActionClient();
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "CorrectionPosition - Action服务不可用: " << createActionName() << std::endl;
            handleActionError("SERVICE_UNAVAILABLE", "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 短暂延迟确保Action服务完全准备好，并且旧请求已清理
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 组装Goal - 只添加实际传入的参数
        auto goal_msg = CorrectionPositionAction::Goal();
        request_id_ = generateRequestId();  // 生成请求ID
        goal_msg.request_id = request_id_;
        goal_msg.command_type = command_type_;
        
        // 动态添加位置参数
        if (getInput<double>("doubleAbsX", abs_x_)) {
            vir_robot_interfaces::msg::Command cmd;
            cmd.key = "x";
            cmd.value = abs_x_;
            goal_msg.commands.push_back(cmd);
        }
        if (getInput<double>("doubleAbsY", abs_y_)) {
            vir_robot_interfaces::msg::Command cmd;
            cmd.key = "y";
            cmd.value = abs_y_;
            goal_msg.commands.push_back(cmd);
        }
        if (getInput<double>("doubleAbsZ", abs_z_)) {
            vir_robot_interfaces::msg::Command cmd;
            cmd.key = "z";
            cmd.value = abs_z_;
            goal_msg.commands.push_back(cmd);
        }
        if (getInput<double>("doubleAbsR", abs_r_)) {
            vir_robot_interfaces::msg::Command cmd;
            cmd.key = "r";
            cmd.value = abs_r_;
            goal_msg.commands.push_back(cmd);
        }
        auto send_goal_options = rclcpp_action::Client<CorrectionPositionAction>::SendGoalOptions();
        send_goal_options.goal_response_callback = 
            [this](const GoalHandleCorrectionPosition::SharedPtr& goal_handle) {
                if (!goal_handle) {
                    std::cout << "CorrectionPosition - 目标被拒绝" << std::endl;
                } else {
                    std::cout << "CorrectionPosition - 目标被接受" << std::endl;
                }
            };
        send_goal_options.feedback_callback = 
            [this](GoalHandleCorrectionPosition::SharedPtr, 
                   const std::shared_ptr<const CorrectionPositionAction::Feedback> feedback) {
                std::cout << "CorrectionPosition - 收到PLC反馈" << std::endl;
                setOutput<std::string>("strPlcFeedback", "PLC反馈已更新");
            };
        send_goal_options.result_callback = 
            [this](const GoalHandleCorrectionPosition::WrappedResult& result) {
                std::cout << "CorrectionPosition - 收到结果" << std::endl;
            };
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        start_time_ = std::chrono::steady_clock::now();
        
        // 构建动态日志信息
        std::string log_msg = "CorrectionPosition - 发送对位平台请求: request_id=" + request_id_ + ", type=" + command_type_;
        for (const auto& cmd : goal_msg.commands) {
            log_msg += ", " + cmd.key + "=" + std::to_string(cmd.value);
        }
        std::cout << log_msg << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus CorrectionPosition::onRunning()
    {
        std::cout << "CorrectionPosition - onRunning" << std::endl;
        rclcpp::spin_some(node_);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        if (elapsed > timeout_) {
            std::cout << "CorrectionPosition - 操作超时" << std::endl;
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            handleActionError("TIMEOUT", "操作超时");
            return BT::NodeStatus::FAILURE;
        }
        
        if (goal_future_.valid()) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "CorrectionPosition - 目标被拒绝" << std::endl;
                    handleActionError("GOAL_REJECTED", "目标被拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                result_future_ = client_->async_get_result(goal_handle_);
                goal_future_ = std::shared_future<GoalHandleCorrectionPosition::SharedPtr>();
            }
        }
        
        if (result_future_.valid()) {
            auto status = result_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                auto result = result_future_.get();
                
                // 先检查result是否有效
                if (!result.result) {
                    std::cout << "CorrectionPosition - 收到空结果" << std::endl;
                    handleActionError("EMPTY_RESULT", "收到空结果");
                    return BT::NodeStatus::FAILURE;
                }
                
                switch (result.code) {
                    case rclcpp_action::ResultCode::SUCCEEDED:
                        {
                            auto action_result = result.result;
                            
                            // 验证request_id一致性
                            if (action_result->request_id != request_id_) {
                                std::cout << "CorrectionPosition - 请求ID不匹配: 期望=" << request_id_ 
                                          << ", 实际=" << action_result->request_id << std::endl;
                                
                                // 不要立即返回失败，而是清理这个旧响应，继续等待正确的响应
                                result_future_ = std::shared_future<GoalHandleCorrectionPosition::WrappedResult>();
                                
                                // 如果已经超时，才返回失败
                                if (elapsed > timeout_ * 0.8) { // 在80%超时时间后才认为是真正的ID不匹配错误
                                    handleActionError("REQUEST_ID_MISMATCH", "请求ID不匹配");
                                    return BT::NodeStatus::FAILURE;
                                }
                                
                                // 否则继续等待
                                return BT::NodeStatus::RUNNING;
                            }
                            
                            setOutput<bool>("boolOutputSuccess", action_result->success);
                            setOutput<std::string>("strErrorCode", action_result->error_code);
                            setOutput<std::string>("strOutputMessage", action_result->msg);
                            std::cout << "CorrectionPosition - 执行完成: " 
                                      << (action_result->success ? "成功" : "失败") 
                                      << ", 消息: " << action_result->msg << std::endl;
                            return action_result->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
                        }
                        break;
                    case rclcpp_action::ResultCode::ABORTED:
                        std::cout << "CorrectionPosition - Action被中止" << std::endl;
                        handleActionError("ABORTED", "Action被中止");
                        return BT::NodeStatus::FAILURE;
                    case rclcpp_action::ResultCode::CANCELED:
                        std::cout << "CorrectionPosition - Action被取消" << std::endl;
                        handleActionError("CANCELED", "Action被取消");
                        return BT::NodeStatus::FAILURE;
                    default:
                        std::cout << "CorrectionPosition - 未知结果状态" << std::endl;
                        handleActionError("UNKNOWN", "未知结果状态");
                        return BT::NodeStatus::FAILURE;
                }
            }
        }
        return BT::NodeStatus::RUNNING;
    }

    void CorrectionPosition::onHalted()
    {
        std::cout << "CorrectionPosition - onHalted" << std::endl;
        try {
            // 取消正在进行的goal
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 彻底清理所有future状态
            goal_future_ = std::shared_future<GoalHandleCorrectionPosition::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleCorrectionPosition::WrappedResult>();
            
            // 清理输出端口状态
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strErrorCode", "HALTED");
            setOutput<std::string>("strOutputMessage", "操作被中止");
            
            // 关闭并重置client连接
            if (client_) {
                client_.reset();
            }
            
            // 短暂延迟确保所有异步操作完成
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            std::cout << "CorrectionPosition - 清理资源完成" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "CorrectionPosition - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::CorrectionPosition>("CorrectionPosition");
} 