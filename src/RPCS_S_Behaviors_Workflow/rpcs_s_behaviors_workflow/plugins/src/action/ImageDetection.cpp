#include "action/ImageDetection.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <sstream>
#include <iomanip>

namespace rpcs_s_behaviors_workflow
{
    ImageDetection::ImageDetection(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
        , node_(nullptr)
        , client_(nullptr)
        , timeout_ms_(10000)
        , request_sent_(false)
    {
        // 创建ROS2节点
        node_ = std::make_shared<rclcpp::Node>("image_detection_node");
        
        // 获取命名空间并创建服务客户端
        std::string namespace_prefix = "";
        getInput<std::string>("strNamespace", namespace_prefix);
        createServiceClient(namespace_prefix);
        
        std::cout << "ImageDetection - 节点初始化完成" << std::endl;
    }

    void ImageDetection::createServiceClient(const std::string& namespace_prefix)
    {
        // 构建服务名称：如果命名空间为空，则直接使用/ImageDetection服务
        std::string service_name;
        if (namespace_prefix.empty()) {
            service_name = "/ImageDetection";
        } else {
            service_name = namespace_prefix + "/vision/image_detection";
        }
        
        client_ = node_->create_client<vir_robot_interfaces::srv::ImageDetection>(service_name);
        
        std::cout << "ImageDetection - 创建服务客户端: " << service_name << std::endl;
    }

    BT::NodeStatus ImageDetection::onStart()
    {
        std::cout << "ImageDetection - onStart" << std::endl;
        
        // 重置状态
        request_sent_ = false;
        
        // 读取输入参数
        getInput<std::string>("strDetectType", detect_type_);
        getInput<std::string>("strCameraIp", camera_ip_);
        getInput<std::string>("strCameraGroup", camera_group_);
        getInput<int>("intTimeoutMs", timeout_ms_);
        
        std::cout << "ImageDetection - 参数设置: 识别类型=" << detect_type_ 
                  << ", 相机IP=" << camera_ip_ 
                  << ", 相机组=" << camera_group_ 
                  << ", 超时=" << timeout_ms_ << "ms" << std::endl;
        
        // 等待服务可用
        if (!client_->wait_for_service(std::chrono::seconds(1))) {
            std::cout << "ImageDetection - 服务不可用" << std::endl;
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorCode", "SERVICE_UNAVAILABLE");
            setOutput<std::string>("strOutputMessage", "视觉识别服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建请求
        auto request = std::make_shared<vir_robot_interfaces::srv::ImageDetection::Request>();
        request->detect_type = detect_type_;
        request->camera_ip = camera_ip_;
        request->camera_group = camera_group_;
        
        std::cout << "ImageDetection - 发送识别请求..." << std::endl;
        
        // 发送异步请求
        try {
            auto result_future = client_->async_send_request(request);
            response_future_ = result_future.future.share();
            request_sent_ = true;
            start_time_ = std::chrono::steady_clock::now();
            
            std::cout << "ImageDetection - 请求已发送，等待响应..." << std::endl;
            return BT::NodeStatus::RUNNING;
            
        } catch (const std::exception& e) {
            std::cout << "ImageDetection - 发送请求失败: " << e.what() << std::endl;
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorCode", "REQUEST_FAILED");
            setOutput<std::string>("strOutputMessage", std::string("发送请求失败: ") + e.what());
            return BT::NodeStatus::FAILURE;
        }
    }

    BT::NodeStatus ImageDetection::onRunning()
    {
        // 处理ROS2事件
        rclcpp::spin_some(node_);
        
        // 检查超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_ms_) {
            std::cout << "ImageDetection - 请求超时 (" << elapsed << "ms)" << std::endl;
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorCode", "TIMEOUT");
            setOutput<std::string>("strOutputMessage", "视觉识别请求超时");
            setOutput<std::string>("strDetectionSummary", "超时：未获取到检测结果");
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查响应是否就绪
        if (!request_sent_) {
            return BT::NodeStatus::RUNNING;
        }
        
        auto status = response_future_.wait_for(std::chrono::milliseconds(10));
        
        if (status == std::future_status::ready) {
            try {
                auto response = response_future_.get();
                
                std::cout << "ImageDetection - 收到响应: 成功=" << response->success 
                          << ", 错误代码=" << response->error_code 
                          << ", 消息=" << response->msg << std::endl;
                
                // 设置基本输出
                setOutput<bool>("boolOutputSuccess", response->success);
                setOutput<std::string>("strOutputErrorCode", response->error_code);
                setOutput<std::string>("strOutputMessage", response->msg);
                
                if (response->success) {
                    // 设置检测结果输出
                    setOutput<std::string>("strResultCameraIp", response->detect_result.camera_ip);
                    setOutput<float>("doubleResultX", static_cast<float>(response->detect_result.x));
                    setOutput<float>("doubleResultY", static_cast<float>(response->detect_result.y));
                    setOutput<float>("doubleResultRz", static_cast<float>(response->detect_result.rz));
                    
                    // 生成检测结果摘要
                    std::string summary = formatDetectionSummary(response->detect_result);
                    setOutput<std::string>("strDetectionSummary", summary);
                    
                    std::cout << "ImageDetection - 检测成功: " << summary << std::endl;
                    return BT::NodeStatus::SUCCESS;
                    
                } else {
                    // 检测失败，清空结果
                    setOutput<std::string>("strResultCameraIp", "");
                    setOutput<float>("doubleResultX", 0.0f);
                    setOutput<float>("doubleResultY", 0.0f);
                    setOutput<float>("doubleResultRz", 0.0f);
                    setOutput<std::string>("strDetectionSummary", "检测失败：" + response->msg);
                    
                    std::cout << "ImageDetection - 检测失败: " << response->msg << std::endl;
                    return BT::NodeStatus::FAILURE;
                }
                
            } catch (const std::exception& e) {
                std::cout << "ImageDetection - 处理响应时出错: " << e.what() << std::endl;
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputErrorCode", "RESPONSE_ERROR");
                setOutput<std::string>("strOutputMessage", std::string("处理响应时出错: ") + e.what());
                setOutput<std::string>("strDetectionSummary", "响应处理错误");
                return BT::NodeStatus::FAILURE;
            }
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void ImageDetection::onHalted()
    {
        std::cout << "ImageDetection - onHalted" << std::endl;
        
        // 如果有pending的请求，尝试取消
        if (request_sent_) {
            // 注意：ROS2服务调用不能像Action那样取消，但我们可以标记为不再等待
            request_sent_ = false;
        }
        
        // 设置输出表示操作被中断
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputErrorCode", "HALTED");
        setOutput<std::string>("strOutputMessage", "视觉识别操作被中断");
        setOutput<std::string>("strDetectionSummary", "操作中断");
    }

    std::string ImageDetection::formatDetectionSummary(const vir_robot_interfaces::msg::ImageDetectionResult& result)
    {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(3);
        oss << "相机[" << result.camera_ip << "] ";
        oss << "偏差(X:" << result.x << ", Y:" << result.y << ", Rz:" << result.rz << "°)";
        return oss.str();
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::ImageDetection>("ImageDetection");
} 