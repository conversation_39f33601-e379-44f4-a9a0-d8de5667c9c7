#include "action/AgvMaterial.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>

namespace rpcs_s_behaviors_workflow
{
    AgvMaterial::AgvMaterial(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(30.0)
    {
        node_ = std::make_shared<rclcpp::Node>("agv_material_node");
    }

    std::string AgvMaterial::createActionName()
    {
        return "/agv_material";
    }

    void AgvMaterial::createActionClient()
    {
        std::string action_name = createActionName();
        client_ = rclcpp_action::create_client<AgvMaterialAction>(node_, action_name);
        std::cout << "AgvMaterial 创建Action客户端: " << action_name << std::endl;
    }

    void AgvMaterial::handleActionError(const std::string& error_code, const std::string& error_message)
    {
        setOutput<bool>("boolExist", false);
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        std::cerr << "AgvMaterial Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus AgvMaterial::onStart()
    {
        std::cout << "AgvMaterial - onStart" << std::endl;
        
        if (!getInput<std::string>("strMaterialType", material_type_)) {
            std::cout << "AgvMaterial - 缺少物料类型参数" << std::endl;
            handleActionError("MISSING_PARAMS", "缺少物料类型参数");
            return BT::NodeStatus::FAILURE;
        }
        
        getInput<double>("doubleTimeout", timeout_);
        
        createActionClient();
        
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "AgvMaterial - Action服务不可用: " << createActionName() << std::endl;
            handleActionError("SERVICE_UNAVAILABLE", "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        auto goal_msg = AgvMaterialAction::Goal();
        goal_msg.material_type = material_type_;
        
        auto send_goal_options = rclcpp_action::Client<AgvMaterialAction>::SendGoalOptions();
        
        send_goal_options.goal_response_callback = 
            [this](const GoalHandleAgvMaterial::SharedPtr& goal_handle) {
                if (!goal_handle) {
                    std::cout << "AgvMaterial - 目标被拒绝" << std::endl;
                } else {
                    std::cout << "AgvMaterial - 目标被接受" << std::endl;
                }
            };
        
        send_goal_options.feedback_callback = 
            [this](GoalHandleAgvMaterial::SharedPtr, 
                   const std::shared_ptr<const AgvMaterialAction::Feedback> feedback) {
                std::cout << "AgvMaterial - 收到PLC反馈" << std::endl;
                setOutput<std::string>("strPlcFeedback", "PLC反馈已更新");
            };
        
        send_goal_options.result_callback = 
            [this](const GoalHandleAgvMaterial::WrappedResult& result) {
                std::cout << "AgvMaterial - 收到结果" << std::endl;
            };
        
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        start_time_ = std::chrono::steady_clock::now();
        
        std::cout << "AgvMaterial - 发送AGV物料检测请求: " << material_type_ << std::endl;
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus AgvMaterial::onRunning()
    {
        std::cout << "AgvMaterial - onRunning" << std::endl;
        
        rclcpp::spin_some(node_);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_) {
            std::cout << "AgvMaterial - 操作超时" << std::endl;
            
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            
            handleActionError("TIMEOUT", "操作超时");
            return BT::NodeStatus::FAILURE;
        }
        
        if (goal_future_.valid()) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                
                if (!goal_handle_) {
                    std::cout << "AgvMaterial - 目标被拒绝" << std::endl;
                    handleActionError("GOAL_REJECTED", "目标被拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                
                result_future_ = client_->async_get_result(goal_handle_);
                goal_future_ = std::shared_future<GoalHandleAgvMaterial::SharedPtr>();
            }
        }
        
        if (result_future_.valid()) {
            auto status = result_future_.wait_for(std::chrono::milliseconds(10));
            
            if (status == std::future_status::ready) {
                auto result = result_future_.get();
                
                switch (result.code) {
                    case rclcpp_action::ResultCode::SUCCEEDED:
                        {
                            auto action_result = result.result;
                            
                            setOutput<bool>("boolExist", action_result->exist);
                            setOutput<bool>("boolOutputSuccess", action_result->success);
                            setOutput<std::string>("strErrorCode", action_result->error_code);
                            setOutput<std::string>("strOutputMessage", action_result->msg);
                            
                            std::cout << "AgvMaterial - 执行完成: " 
                                      << (action_result->success ? "成功" : "失败") 
                                      << ", 物料存在: " << (action_result->exist ? "是" : "否")
                                      << ", 消息: " << action_result->msg << std::endl;
                            
                            return action_result->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
                        }
                        break;
                        
                    case rclcpp_action::ResultCode::ABORTED:
                        std::cout << "AgvMaterial - Action被中止" << std::endl;
                        handleActionError("ABORTED", "Action被中止");
                        return BT::NodeStatus::FAILURE;
                        
                    case rclcpp_action::ResultCode::CANCELED:
                        std::cout << "AgvMaterial - Action被取消" << std::endl;
                        handleActionError("CANCELED", "Action被取消");
                        return BT::NodeStatus::FAILURE;
                        
                    default:
                        std::cout << "AgvMaterial - 未知结果状态" << std::endl;
                        handleActionError("UNKNOWN", "未知结果状态");
                        return BT::NodeStatus::FAILURE;
                }
            }
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void AgvMaterial::onHalted()
    {
        std::cout << "AgvMaterial - onHalted" << std::endl;
        
        try {
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            setOutput<bool>("boolExist", false);
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strErrorCode", "HALTED");
            setOutput<std::string>("strOutputMessage", "操作被中止");
            
            goal_future_ = std::shared_future<GoalHandleAgvMaterial::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleAgvMaterial::WrappedResult>();
            
            std::cout << "AgvMaterial - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "AgvMaterial - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::AgvMaterial>("AgvMaterial");
} 