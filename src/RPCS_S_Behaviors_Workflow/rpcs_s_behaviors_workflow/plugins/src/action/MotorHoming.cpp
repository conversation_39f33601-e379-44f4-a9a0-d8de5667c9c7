#include "action/MotorHoming.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>

namespace rpcs_s_behaviors_workflow
{
    MotorHoming::MotorHoming(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(30.0)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("motor_homing_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("motor_homing_node", controller_motor_node_ros_domain_id);
    }

    std::string MotorHoming::createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        if (device_id.empty() || device_id == "default") {
            return fmt::format("/{}_{}/homing_control", motor_brand, motor_id);
        } else {
            return fmt::format("/{}/{}_{}/homing_control", device_id, motor_brand, motor_id);
        }
    }

    void MotorHoming::createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        std::string action_name = createActionName(device_id, motor_brand, motor_id);
        client_ = rclcpp_action::create_client<HomingAction>(node_, action_name);

        std::cout << "MotorHoming 创建Action客户端: " << action_name << std::endl;
    }

    void MotorHoming::handleActionError(int error_code, const std::string& error_message)
    {
        // 在错误信息中添加电机ID标识
        std::string enhanced_message = fmt::format("motor_id_{} - {}", motor_id_, error_message);
        
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", enhanced_message);
        
        std::cerr << "MotorHoming Action失败: [" << error_code << "] " << enhanced_message << std::endl;
    }

    std::string MotorHoming::getStatusDescription(int status)
    {
        // 根据状态字返回描述
        switch (status) {
            case 0: return "等待开始";
            case 1: return "正在搜索限位开关";
            case 2: return "正在搜索零点信号";
            case 3: return "正在移动到原点偏移位置";
            case 4: return "回零完成";
            case -1: return "回零失败：超时";
            case -2: return "回零失败：限位开关故障";
            case -3: return "回零失败：零点信号故障";
            case -4: return "回零失败：位置超限";
            default: return fmt::format("未知状态: {}", status);
        }
    }

    BT::NodeStatus MotorHoming::onStart()
    {
        std::cout << "MotorHoming - onStart" << std::endl;
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "Robot1";
        }

        if (!getInput<std::string>("strMotorBrand", motor_brand_)) {
            motor_brand_ = "Kinco";
        }

        if (!getInput<int>("intMotorId", motor_id_)) {
            std::cout << "MotorHoming - 无法获取intMotorId参数" << std::endl;
            handleActionError(-1, "缺少电机ID参数");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取回零参数（带默认值）
        getInput<int>("intHomingMethod", homing_method_);
        getInput<float>("floatSpeedSwitch", speed_switch_);
        getInput<float>("floatSpeedZero", speed_zero_);
        getInput<int>("intHomeOffset", home_offset_);
        getInput<int>("intPositionWindow", position_window_);
        getInput<int>("intPositionWindowTime", position_window_time_);
        getInput<double>("doubleTimeout", timeout_);
        
        // 创建Action客户端
        createActionClient(device_id_, motor_brand_, motor_id_);
        
        // 等待Action服务可用
        if (!client_->wait_for_action_server(std::chrono::seconds(3))) {
            std::cout << "MotorHoming - Action服务不可用: " << createActionName(device_id_, motor_brand_, motor_id_) << std::endl;
            handleActionError(-3, "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建Action目标
        auto goal_msg = HomingAction::Goal();
        goal_msg.homing_method = homing_method_;
        goal_msg.speed_switch = speed_switch_;
        goal_msg.speed_zero = speed_zero_;
        goal_msg.home_offset = home_offset_;
        goal_msg.position_window = position_window_;
        goal_msg.position_window_time = position_window_time_;
        goal_msg.timeout = timeout_;  // 添加超时时间传递
        
        // 记录请求信息
        std::cout << "MotorHoming - 发送回零控制请求:" << std::endl;
        std::cout << "  设备ID: " << device_id_ << ", 电机ID: " << motor_id_ << std::endl;
        std::cout << "  回零方法: " << homing_method_ << std::endl;
        std::cout << "  开关搜索速度: " << speed_switch_ << " rpm" << std::endl;
        std::cout << "  零点搜索速度: " << speed_zero_ << " rpm" << std::endl;
        std::cout << "  原点偏移: " << home_offset_ << " mm" << std::endl;
        std::cout << "  位置窗口: " << position_window_ << " inc" << std::endl;
        std::cout << "  超时时间: " << timeout_ << " s" << std::endl;
        
        // 发送Action请求
        auto send_goal_options = rclcpp_action::Client<HomingAction>::SendGoalOptions();
        
        // 设置反馈回调
        send_goal_options.feedback_callback = 
            [this](GoalHandleHoming::SharedPtr, const std::shared_ptr<const HomingAction::Feedback> feedback) {
                // 更新实时状态输出
                setOutput<int>("intCurrentStatus", feedback->status);
                setOutput<std::string>("strStatusDescription", feedback->status_description);
                setOutput<float>("floatCurrentPosition", feedback->current_position);
                
                std::cout << "MotorHoming - 反馈: 状态=" << feedback->status_description 
                         << ", 当前位置=" << feedback->current_position << "mm" << std::endl;
            };
        
        // 发送目标
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus MotorHoming::onRunning()
    {
        std::cout << "MotorHoming - onRunning" << std::endl;
        
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_) {
            std::cout << "MotorHoming - Action请求超时" << std::endl;
            handleActionError(-4, "回零操作超时");
            
            // 取消目标
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查目标句柄是否就绪
        if (!goal_handle_) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "MotorHoming - 目标被拒绝" << std::endl;
                    handleActionError(-5, "回零目标被服务器拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                
                // 获取结果Future
                result_future_ = client_->async_get_result(goal_handle_);
                std::cout << "MotorHoming - 目标已接受，开始回零" << std::endl;
            }
            return BT::NodeStatus::RUNNING;
        }
        
        // 检查结果是否就绪
        auto status = result_future_.wait_for(std::chrono::milliseconds(10));
        if (status == std::future_status::ready) {
            auto wrapped_result = result_future_.get();
            
            std::cout << "MotorHoming - Action完成" << std::endl;
            
            // 设置输出端口
            bool success = (wrapped_result.code == rclcpp_action::ResultCode::SUCCEEDED);
            setOutput<bool>("boolOutputSuccess", success);
            setOutput<float>("floatFinalPosition", wrapped_result.result->final_position);
            
            // 在错误信息中添加电机ID标识
            std::string result_message = wrapped_result.result->message;
            if (!success) {
                result_message = fmt::format("motor_id_{} - {}", motor_id_, result_message);
            }
            setOutput<std::string>("strOutputMessage", result_message);
            
            // 记录结果
            std::cout << "MotorHoming - 执行结果: " << (success ? "成功" : "失败") << std::endl;
            std::cout << "MotorHoming - 最终位置: " << wrapped_result.result->final_position << "mm" << std::endl;
            std::cout << "MotorHoming - 消息: " << result_message << std::endl;
            
            return success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        // Action尚未完成，继续等待
        return BT::NodeStatus::RUNNING;
    }

    void MotorHoming::onHalted()
    {
        std::cout << "MotorHoming - onHalted" << std::endl;
        
        try {
            // 取消正在进行的Action
            if (goal_handle_) {
                std::cout << "MotorHoming - 取消回零操作" << std::endl;
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 重置Future对象
            goal_future_ = std::shared_future<GoalHandleHoming::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleHoming::WrappedResult>();
            
            // 设置取消状态输出，添加电机ID标识
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", fmt::format("motor_id_{} - 回零操作被取消", motor_id_));
            
            // 清理Action客户端
            if (client_) {
                std::cout << "MotorHoming - 清理Action客户端" << std::endl;
                client_.reset();
            }
            
            std::cout << "MotorHoming - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "MotorHoming - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MotorHoming>("MotorHoming");
} 