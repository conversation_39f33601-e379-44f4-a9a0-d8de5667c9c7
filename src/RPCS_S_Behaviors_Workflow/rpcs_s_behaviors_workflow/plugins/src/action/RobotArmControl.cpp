#include "action/RobotArmControl.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <sstream>
#include <algorithm>

namespace rpcs_s_behaviors_workflow
{
    RobotArmControl::RobotArmControl(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config),
          goal_sent_(false),
          goal_accepted_(false),
          result_received_(false),
          goal_aborted_(false),
          timeout_ms_(60000)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = rclcpp::Node::SharedPtr(std::make_shared<rclcpp::Node>("robot_arm_control_node"));
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("robot_arm_control_node", controller_motor_node_ros_domain_id);
        
        // 获取设备名称/命名空间
        std::string device_name = "/robot1";
        try {
            getInput<std::string>("strDeviceName", device_name);
        } catch (const std::exception& e) {
            // 使用默认值
        }
        
        createActionClient(device_name);
        
        std::cout << "RobotArmControl节点初始化完成，设备名称: " << device_name << std::endl;
    }

    void RobotArmControl::createActionClient(const std::string& device_name)
    {
        // 创建action客户端，action名称格式：设备名称 + /flexiv_robot_arm/robot_arm_control
        std::string action_name = device_name + "/flexiv_robot_arm/robot_arm_control";
        action_client_ = rclcpp_action::create_client<RobotArmControlAction>(node_, action_name);
        
        std::cout << "RobotArmControl创建Action客户端: " << action_name << std::endl;
    }



    BT::NodeStatus RobotArmControl::onStart()
    {
        std::cout << "RobotArmControl - onStart" << std::endl;
        
        // 重置状态变量
        goal_sent_ = false;
        goal_accepted_ = false;
        result_received_ = false;
        goal_aborted_ = false;
        latest_feedback_.reset();
        latest_result_.reset();
        
        // 获取输入参数
        int project_id = 1;
        int speed_multiplier = 100;
        float position_x = 0.0f, position_y = 0.0f, position_z = 0.0f;
        float rotation_rx = 0.0f, rotation_ry = 0.0f, rotation_rz = 0.0f;
        int function_data0 = 0, function_data1 = 0, function_data2 = 0;
        int function_data3 = 0, function_data4 = 0, function_data5 = 0;
        
        getInput<int>("intProjectId", project_id);
        getInput<int>("intSpeedMultiplier", speed_multiplier);
        
        // 限制速度倍率在1-100范围内
        if (speed_multiplier < 1) {
            std::cout << "RobotArmControl - 速度倍率过小，调整为1: " << speed_multiplier << std::endl;
            speed_multiplier = 1;
        } else if (speed_multiplier > 100) {
            std::cout << "RobotArmControl - 速度倍率过大，调整为100: " << speed_multiplier << std::endl;
            speed_multiplier = 100;
        }
        
        getInput<float>("floatPositionX", position_x);
        getInput<float>("floatPositionY", position_y);
        getInput<float>("floatPositionZ", position_z);
        
        // 单位转换：毫米转换为米
        position_x /= 1000.0f;
        position_y /= 1000.0f;
        position_z /= 1000.0f;
        
        getInput<float>("floatRotationRx", rotation_rx);
        getInput<float>("floatRotationRy", rotation_ry);
        getInput<float>("floatRotationRz", rotation_rz);
        getInput<int>("intFunctionData0", function_data0);
        getInput<int>("intFunctionData1", function_data1);
        getInput<int>("intFunctionData2", function_data2);
        getInput<int>("intFunctionData3", function_data3);
        getInput<int>("intFunctionData4", function_data4);
        getInput<int>("intFunctionData5", function_data5);
        getInput<int>("intTimeoutMs", timeout_ms_);
        
        // 等待action服务可用
        if (!action_client_->wait_for_action_server(std::chrono::milliseconds(2000))) {
            std::cout << "RobotArmControl - Action服务不可用" << std::endl;
            
            // 设置失败输出
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorMessage", "Action服务不可用");
            setOutput<double>("doubleExecutionTime", 0.0);
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建goal消息
        auto goal_msg = RobotArmControlAction::Goal();
        goal_msg.project_id = project_id;
        goal_msg.speed_multiplier = speed_multiplier;
        goal_msg.position_x = position_x;
        goal_msg.position_y = position_y;
        goal_msg.position_z = position_z;
        goal_msg.rotation_rx = rotation_rx;
        goal_msg.rotation_ry = rotation_ry;
        goal_msg.rotation_rz = rotation_rz;
        // 构建功能数据数组
        goal_msg.function_data = {function_data0, function_data1, function_data2, 
                                  function_data3, function_data4, function_data5};
        // 设置超时时间（秒）- 从毫秒转换为秒
        goal_msg.timeout_seconds = static_cast<double>(timeout_ms_) / 1000.0;
        
        std::cout << "发送机械臂控制目标: 工程ID=" << project_id 
                  << ", 位置=(" << position_x * 1000.0f << "mm," << position_y * 1000.0f << "mm," << position_z * 1000.0f << "mm)"
                  << " [转换为米:(" << position_x << "m," << position_y << "m," << position_z << "m)]"
                  << ", 旋转=(" << rotation_rx << "," << rotation_ry << "," << rotation_rz << ")"
                  << ", 功能数据=[" << function_data0 << "," << function_data1 << "," 
                  << function_data2 << "," << function_data3 << "," << function_data4 
                  << "," << function_data5 << "]"
                  << ", 超时时间=" << goal_msg.timeout_seconds << "秒" << std::endl;
        
        // 设置回调函数
        auto send_goal_options = rclcpp_action::Client<RobotArmControlAction>::SendGoalOptions();
        send_goal_options.goal_response_callback =
            std::bind(&RobotArmControl::goalResponseCallback, this, std::placeholders::_1);
        send_goal_options.feedback_callback =
            std::bind(&RobotArmControl::feedbackCallback, this, std::placeholders::_1, std::placeholders::_2);
        send_goal_options.result_callback =
            std::bind(&RobotArmControl::resultCallback, this, std::placeholders::_1);
        
        // 发送goal
        auto goal_handle_future = action_client_->async_send_goal(goal_msg, send_goal_options);
        goal_sent_ = true;
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus RobotArmControl::onRunning()
    {
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_ms_) {
            std::cout << "RobotArmControl - 操作超时" << std::endl;
            
            // 取消goal
            if (goal_handle_ && goal_accepted_) {
                auto cancel_future = action_client_->async_cancel_goal(goal_handle_);
            }
            
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorMessage", "操作超时");
            setOutput<double>("doubleExecutionTime", static_cast<double>(elapsed) / 1000.0);
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 更新feedback输出
        if (latest_feedback_) {
            setOutput<double>("doubleProgressPercentage", latest_feedback_->progress_percentage);
            setOutput<std::string>("strCurrentStatus", latest_feedback_->current_status);
            setOutput<double>("doubleExecutionTime", latest_feedback_->elapsed_time);
            
            // 实时状态反馈
            setOutput<bool>("boolProgramRunning", latest_feedback_->program_running_feedback);
            setOutput<bool>("boolProgramPaused", latest_feedback_->program_paused_feedback);
            setOutput<bool>("boolRemoteModeEnabled", latest_feedback_->remote_mode_feedback);
            setOutput<bool>("boolEmergencyStop", latest_feedback_->emergency_stop_feedback);
            setOutput<bool>("boolErrorStatus", latest_feedback_->error_status_feedback);
            setOutput<bool>("boolMotorPowerStatus", latest_feedback_->motor_power_feedback);
            
            // 当前位置反馈
            setOutput<float>("floatCurrentX", latest_feedback_->current_x);
            setOutput<float>("floatCurrentY", latest_feedback_->current_y);
            setOutput<float>("floatCurrentZ", latest_feedback_->current_z);
            setOutput<float>("floatCurrentRx", latest_feedback_->current_rx);
            setOutput<float>("floatCurrentRy", latest_feedback_->current_ry);
            setOutput<float>("floatCurrentRz", latest_feedback_->current_rz);
        }
        
        // 检查是否goal被拒绝
        if (goal_sent_ && !goal_accepted_ && goal_aborted_) {
            std::cout << "RobotArmControl - Goal被拒绝" << std::endl;
            
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputErrorMessage", "Goal被服务器拒绝");
            setOutput<double>("doubleExecutionTime", static_cast<double>(elapsed) / 1000.0);
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查结果
        if (result_received_ && latest_result_) {
            std::cout << "RobotArmControl - 接收到结果，成功: " << latest_result_->success << std::endl;
            
            // 设置基本输出
            setOutput<bool>("boolOutputSuccess", latest_result_->success);
            setOutput<std::string>("strOutputErrorMessage", latest_result_->error_message);
            setOutput<double>("doubleExecutionTime", latest_result_->execution_time);
            setOutput<int>("intErrorCode", latest_result_->error_code);
            
            // 程序状态输出
            setOutput<bool>("boolProgramRunning", latest_result_->program_running);
            setOutput<bool>("boolProgramPaused", latest_result_->program_paused);
            setOutput<bool>("boolRemoteModeEnabled", latest_result_->remote_mode_enabled);
            setOutput<bool>("boolEmergencyStop", latest_result_->emergency_stop);
            setOutput<bool>("boolProgramClearRequest", latest_result_->program_clear_request);
            setOutput<bool>("boolErrorStatus", latest_result_->error_status);
            setOutput<bool>("boolMotorPowerStatus", latest_result_->motor_power_status);
            setOutput<int>("intProjectIdStatus", latest_result_->project_id_status);
            
            return latest_result_->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void RobotArmControl::onHalted()
    {
        std::cout << "RobotArmControl - onHalted" << std::endl;
        
        // 取消正在执行的goal
        if (goal_handle_ && goal_accepted_ && !result_received_) {
            std::cout << "RobotArmControl - 取消执行中的goal" << std::endl;
            auto cancel_future = action_client_->async_cancel_goal(goal_handle_);
        }
        
        // 重置状态
        goal_sent_ = false;
        goal_accepted_ = false;
        result_received_ = false;
        goal_aborted_ = false;
        goal_handle_.reset();
        latest_feedback_.reset();
        latest_result_.reset();
    }

    void RobotArmControl::goalResponseCallback(const GoalHandleRobotArmControl::SharedPtr& goal_handle)
    {
        if (!goal_handle) {
            std::cout << "RobotArmControl - Goal被拒绝" << std::endl;
            goal_aborted_ = true;
        } else {
            std::cout << "RobotArmControl - Goal被接受" << std::endl;
            goal_handle_ = goal_handle;
            goal_accepted_ = true;
        }
    }

    void RobotArmControl::feedbackCallback(
        const GoalHandleRobotArmControl::SharedPtr,
        const std::shared_ptr<const RobotArmControlAction::Feedback> feedback)
    {
        latest_feedback_ = std::const_pointer_cast<RobotArmControlAction::Feedback>(feedback);
        
        std::cout << "RobotArmControl - 进度: " << feedback->progress_percentage 
                  << "%, 状态: " << feedback->current_status << std::endl;
    }

    void RobotArmControl::resultCallback(const GoalHandleRobotArmControl::WrappedResult& result)
    {
        std::cout << "RobotArmControl - 接收到最终结果" << std::endl;
        
        switch (result.code) {
            case rclcpp_action::ResultCode::SUCCEEDED:
                std::cout << "RobotArmControl - Action成功完成" << std::endl;
                break;
            case rclcpp_action::ResultCode::ABORTED:
                std::cout << "RobotArmControl - Action被中止" << std::endl;
                break;
            case rclcpp_action::ResultCode::CANCELED:
                std::cout << "RobotArmControl - Action被取消" << std::endl;
                break;
            default:
                std::cout << "RobotArmControl - Action未知结果代码" << std::endl;
                break;
        }
        
        latest_result_ = result.result;
        result_received_ = true;
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::RobotArmControl>("RobotArmControl");
} 