#include "action/CameraCapture.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

#include <chrono>
#include <filesystem>
#include <iomanip>
#include <sstream>

using namespace std::chrono_literals;

namespace rpcs_s_behaviors_workflow
{

CameraCapture::CameraCapture(const std::string & name, const BT::NodeConfiguration & config)
: BT::StatefulActionNode(name, config), _rosNode(nullptr), _serviceClient(nullptr)
{
  // 获取或创建 ROS2 节点
  _rosNode = rclcpp::Node::make_shared("camera_capture_bt_node");

  RCLCPP_DEBUG(_rosNode->get_logger(), "CameraCapture 节点已创建: %s", name.c_str());
}

BT::PortsList CameraCapture::providedPorts()
{
  return {// 输入端口
          BT::InputPort<std::string>("strNamespace", "HJGC", "相机命名空间"),
          BT::InputPort<bool>("boolTriggerImage", true, "是否触发拍照"),
          BT::InputPort<std::string>("strImageTopic", "", "可选：将图像发布到指定话题"),
          BT::InputPort<std::string>("strSaveImagePath", "", "可选：保存图像到本地路径"),

          // 输出端口
          BT::OutputPort<bool>("boolSuccess", "拍照是否成功"),
          BT::OutputPort<std::string>("stringMessage", "返回消息")};
}

BT::NodeStatus CameraCapture::onStart()
{
  RCLCPP_INFO(_rosNode->get_logger(), "CameraCapture 节点开始执行");

  // 读取输入端口
  if (!getInput<std::string>("strNamespace", _cameraNamespace)) {
    _cameraNamespace = "HJGC";  // 使用默认值
  }

  if (!getInput<bool>("boolTriggerImage", _triggerImage)) {
    _triggerImage = true;  // 使用默认值
  }

  // 读取可选的图像发布话题
  if (!getInput<std::string>("strImageTopic", _imageTopicName)) {
    _imageTopicName = "";
  }

  // 读取可选的图像保存路径
  if (!getInput<std::string>("strSaveImagePath", _saveImagePath)) {
    _saveImagePath = "";
  }

  // 初始化服务客户端
  if (!initializeServiceClient()) {
    const std::string error_msg = "初始化相机服务客户端失败";
    RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
    setOutputs(false, error_msg);
    return BT::NodeStatus::FAILURE;
  }

  // 等待服务可用
  if (!_serviceClient->wait_for_service(3s)) {
    const std::string error_msg = "相机服务不可用，请检查相机驱动是否运行";
    RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
    setOutputs(false, error_msg);
    return BT::NodeStatus::FAILURE;
  }

  // 创建图像发布器（如果需要）
  if (!_imageTopicName.empty()) {
    // 使用transient_local QoS，确保新订阅者能获取到最新的图像
    auto qos = rclcpp::QoS(1)
                 .durability(rclcpp::DurabilityPolicy::TransientLocal)
                 .reliability(rclcpp::ReliabilityPolicy::Reliable);

    _imagePublisher = _rosNode->create_publisher<sensor_msgs::msg::Image>(_imageTopicName, qos);
    RCLCPP_INFO(
      _rosNode->get_logger(), "将在话题 %s 上发布图像 (使用transient_local QoS)",
      _imageTopicName.c_str());
  }

  // 创建请求
  auto request = std::make_shared<hikvision_interface::srv::GetSingleImage::Request>();
  request->trigger_image = _triggerImage;

  // 发送异步请求
  try {
    _serviceFuture = _serviceClient->async_send_request(request).future.share();
    
    // 初始化计时器
    _serviceStartTime = std::chrono::steady_clock::now();
    _lastLogTime = _serviceStartTime;
    
    RCLCPP_INFO(
      _rosNode->get_logger(), "已发送相机拍照请求，命名空间: %s", _cameraNamespace.c_str());
    return BT::NodeStatus::RUNNING;
  } catch (const std::exception & e) {
    const std::string error_msg = "发送相机拍照请求失败: " + std::string(e.what());
    RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
    setOutputs(false, error_msg);
    return BT::NodeStatus::FAILURE;
  }
}

BT::NodeStatus CameraCapture::onRunning()
{
  // 处理 ROS2 回调，确保异步服务响应能够被接收
  rclcpp::spin_some(_rosNode);
  
  // 检查服务调用是否完成
  auto status = _serviceFuture.wait_for(std::chrono::milliseconds(1));
  
  if (status == std::future_status::ready) {
    try {
      auto response = _serviceFuture.get();

      if (response->success) {
        RCLCPP_INFO(_rosNode->get_logger(), "相机拍照成功: %s", response->message.c_str());

        // 发布图像（如果需要）
        publishImageIfRequested(response->image);

        // 保存图像到本地（如果需要）
        if (!_saveImagePath.empty()) {
          if (saveImageToLocal(response->image)) {
            RCLCPP_INFO(_rosNode->get_logger(), "图像已保存到: %s", _saveImagePath.c_str());
          } else {
            RCLCPP_WARN(_rosNode->get_logger(), "图像保存失败: %s", _saveImagePath.c_str());
          }
        }

        // 设置输出
        setOutputs(true, response->message);
        return BT::NodeStatus::SUCCESS;
      } else {
        const std::string error_msg = "相机拍照失败: " + response->message;
        RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
        setOutputs(false, error_msg);
        return BT::NodeStatus::FAILURE;
      }
    } catch (const std::exception & e) {
      const std::string error_msg = "处理相机拍照响应时发生异常: " + std::string(e.what());
      RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
      setOutputs(false, error_msg);
      return BT::NodeStatus::FAILURE;
    }
  } else if (status == std::future_status::timeout) {
    // 检查是否超时（添加超时机制，比如 10 秒）
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - _serviceStartTime);
    
    if (elapsed.count() > 10) {  // 10 秒超时
      const std::string error_msg = "相机拍照请求超时，服务可能无响应";
      RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
      setOutputs(false, error_msg);
      return BT::NodeStatus::FAILURE;
    }
    
    // 每 2 秒打印一次等待信息，避免日志刷屏
    if (std::chrono::duration_cast<std::chrono::seconds>(current_time - _lastLogTime).count() >= 2) {
      RCLCPP_INFO(_rosNode->get_logger(), "等待相机服务响应中... (已等待 %ld 秒)", elapsed.count());
      _lastLogTime = current_time;
    }
    
    return BT::NodeStatus::RUNNING;
  } else {
    // std::future_status::deferred - 不应该发生，因为我们使用了 async_send_request
    const std::string error_msg = "服务调用状态异常";
    RCLCPP_ERROR(_rosNode->get_logger(), "%s", error_msg.c_str());
    setOutputs(false, error_msg);
    return BT::NodeStatus::FAILURE;
  }
}

void CameraCapture::onHalted()
{
  RCLCPP_WARN(_rosNode->get_logger(), "CameraCapture 节点被停止");

  // 取消正在进行的服务调用
  if (_serviceFuture.valid()) {
    // 注意：std::future 没有 cancel 方法，这里只是清理资源
    RCLCPP_WARN(_rosNode->get_logger(), "正在等待的相机拍照请求将继续执行");
  }

  // 重置计时器
  _serviceStartTime = std::chrono::steady_clock::time_point{};
  _lastLogTime = std::chrono::steady_clock::time_point{};

  setOutputs(false, "节点被外部停止");
}

bool CameraCapture::initializeServiceClient()
{
  try {
    // 构建服务名称：/driver/hikvision/{namespace}/get_single_image
    const std::string service_name = "/driver/hikvision/" + _cameraNamespace + "/get_single_image";

    _serviceClient =
      _rosNode->create_client<hikvision_interface::srv::GetSingleImage>(service_name);

    RCLCPP_INFO(_rosNode->get_logger(), "初始化相机服务客户端: %s", service_name.c_str());
    return true;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(_rosNode->get_logger(), "创建相机服务客户端失败: %s", e.what());
    return false;
  }
}

void CameraCapture::publishImageIfRequested(const sensor_msgs::msg::Image & image)
{
  if (_imagePublisher && !_imageTopicName.empty()) {
    try {
      _imagePublisher->publish(image);
      RCLCPP_INFO(_rosNode->get_logger(), "图像已发布到话题: %s", _imageTopicName.c_str());
    } catch (const std::exception & e) {
      RCLCPP_WARN(_rosNode->get_logger(), "发布图像失败: %s", e.what());
    }
  }
}

bool CameraCapture::saveImageToLocal(const sensor_msgs::msg::Image & image)
{
  try {
    // 使用cv_bridge将ROS图像消息转换为OpenCV格式
    cv_bridge::CvImagePtr cv_ptr = cv_bridge::toCvCopy(image, sensor_msgs::image_encodings::BGR8);

    // 生成带时间戳的文件名
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream filename_ss;
    filename_ss << _saveImagePath;

    // 如果路径以'/'结尾，说明是目录，需要生成文件名
    if (_saveImagePath.back() == '/') {
      filename_ss << "camera_" << _cameraNamespace << "_"
                  << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S") << "_"
                  << std::setfill('0') << std::setw(3) << ms.count() << ".jpg";
    }
    // 否则直接使用指定的文件路径

    std::string full_path = filename_ss.str();

    // 确保目录存在
    std::filesystem::path file_path(full_path);
    std::filesystem::create_directories(file_path.parent_path());

    // 保存图像
    bool success = cv::imwrite(full_path, cv_ptr->image);

    if (success) {
      RCLCPP_INFO(_rosNode->get_logger(), "图像已成功保存: %s", full_path.c_str());
      return true;
    } else {
      RCLCPP_ERROR(_rosNode->get_logger(), "OpenCV保存图像失败: %s", full_path.c_str());
      return false;
    }
  } catch (cv_bridge::Exception & e) {
    RCLCPP_ERROR(_rosNode->get_logger(), "cv_bridge转换失败: %s", e.what());
    return false;
  } catch (const std::exception & e) {
    RCLCPP_ERROR(_rosNode->get_logger(), "保存图像时发生异常: %s", e.what());
    return false;
  }
}

void CameraCapture::setOutputs(bool success, const std::string & message)
{
  setOutput<bool>("boolSuccess", success);
  setOutput<std::string>("stringMessage", message);
}

}  // namespace rpcs_s_behaviors_workflow
#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::CameraCapture>("CameraCapture");
}
