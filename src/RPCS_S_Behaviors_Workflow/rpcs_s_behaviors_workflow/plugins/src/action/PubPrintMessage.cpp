#include "action/PubPrintMessage.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    PubPrintMessage::PubPrintMessage(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
    {
    }

    BT::NodeStatus PubPrintMessage::onStart()
    {
        // 读取print_message参数并打印
        auto print_message = getInput<std::string>("strPrintMessage");
        if (print_message)
        {
            std::cout << "📢 " << print_message.value() << std::endl;
        }
        else
        {
            std::cout << "PubPrintMessage - onStart (no message)" << std::endl;
        }
        
        start_time = std::chrono::steady_clock::now();
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus PubPrintMessage::onRunning()
    {
        // 简化运行时的输出，避免过多日志
        if (std::chrono::steady_clock::now() - start_time < std::chrono::seconds(rpcs_s_behaviors_workflow::DEFAULT_DELAY_TIME))
        {
            return BT::NodeStatus::RUNNING;
        }
        else
        {
            setOutput("done", true);
            return BT::NodeStatus::SUCCESS;
        }
    }

    void PubPrintMessage::onHalted()
    {
        std::cout << "PubPrintMessage - onHalted" << std::endl;
        // TODO: Implement the halted behavior
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::PubPrintMessage>("PubPrintMessage");
}
