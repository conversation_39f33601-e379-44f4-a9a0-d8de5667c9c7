#include "action/MotorTorqueControl.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <fmt/format.h>

namespace rpcs_s_behaviors_workflow
{
    MotorTorqueControl::MotorTorqueControl(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), timeout_(10.0)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("motor_torque_control_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("motor_torque_control_node", controller_motor_node_ros_domain_id);
    }

    std::string MotorTorqueControl::createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        if (device_id.empty() || device_id == "default") {
            return fmt::format("/{}_{}/torque_control", motor_brand, motor_id);
        } else {
            return fmt::format("/{}/{}_{}/torque_control", device_id, motor_brand, motor_id);
        }
    }

    void MotorTorqueControl::createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id)
    {
        std::string action_name = createActionName(device_id, motor_brand, motor_id);
        client_ = rclcpp_action::create_client<TorqueControlAction>(node_, action_name);

        std::cout << "MotorTorqueControl 创建Action客户端: " << action_name << std::endl;
    }

    void MotorTorqueControl::handleActionError(int error_code, const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<int>("intErrorCode", error_code);
        setOutput<std::string>("strOutputMessage", error_message);
        
        std::cerr << "MotorTorqueControl Action失败: [" << error_code << "] " << error_message << std::endl;
    }

    BT::NodeStatus MotorTorqueControl::onStart()
    {
        std::cout << "MotorTorqueControl - onStart" << std::endl;
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "Robot1";
        }

        if (!getInput<std::string>("strMotorBrand", motor_brand_)) {
            motor_brand_ = "Kinco";
        }

        if (!getInput<int>("intMotorId", motor_id_)) {
            std::cout << "MotorTorqueControl - 无法获取intMotorId参数" << std::endl;
            handleActionError(-1, "缺少电机ID参数");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput<double>("doubleTargetTorque", target_torque_)) {
            std::cout << "MotorTorqueControl - 无法获取doubleTargetTorque参数" << std::endl;
            handleActionError(-2, "缺少目标转矩参数");
            return BT::NodeStatus::FAILURE;
        }
        
        // 获取其他参数（带默认值）
        getInput<double>("doubleVelocityLimit", velocity_limit_);
        getInput<double>("doubleTorqueSlope", torque_slope_);
        getInput<double>("doubleDuration", duration_);
        getInput<bool>("boolUsePositionLimits", use_position_limits_);
        getInput<double>("doubleMinPosition", min_position_);
        getInput<double>("doubleMaxPosition", max_position_);
        getInput<double>("doubleTimeout", timeout_);
        
        // 创建Action客户端
        createActionClient(device_id_, motor_brand_, motor_id_);
        
        // 等待Action服务可用
        if (!client_->wait_for_action_server(std::chrono::seconds(30))) {
            std::cout << "MotorTorqueControl - Action服务不可用: " << createActionName(device_id_, motor_brand_, motor_id_) << std::endl;
            handleActionError(-3, "Action服务不可用");
            return BT::NodeStatus::FAILURE;
        }
        
        // 创建Action目标
        auto goal_msg = TorqueControlAction::Goal();
        goal_msg.target_torque = target_torque_;
        goal_msg.velocity_limit = velocity_limit_;
        goal_msg.torque_slope = torque_slope_;
        goal_msg.duration = duration_;
        goal_msg.use_position_limits = use_position_limits_;
        goal_msg.min_position = min_position_;
        goal_msg.max_position = max_position_;
        
        // 记录请求信息
        std::cout << "MotorTorqueControl - 发送转矩控制请求:" << std::endl;
        std::cout << "  设备ID: " << device_id_ << ", 电机ID: " << motor_id_ << std::endl;
        std::cout << "  目标转矩: " << target_torque_ << "Nm" << std::endl;
        std::cout << "  速度限制: " << velocity_limit_ << "mm/s" << std::endl;
        std::cout << "  转矩斜率: " << torque_slope_ << "Nm/s" << std::endl;
        std::cout << "  运行时间: " << (duration_ > 0 ? std::to_string(duration_) + "s" : "持续运行") << std::endl;
        std::cout << "  位置限制: " << (use_position_limits_ ? "开启" : "关闭") << std::endl;
        
        // 发送Action请求
        auto send_goal_options = rclcpp_action::Client<TorqueControlAction>::SendGoalOptions();
        
        // 设置反馈回调
        send_goal_options.feedback_callback = 
            [this](GoalHandleTorqueControl::SharedPtr, const std::shared_ptr<const TorqueControlAction::Feedback> feedback) {
                // 更新实时状态输出
                setOutput<double>("doubleCurrentTorque", feedback->current_torque);
                setOutput<double>("doubleElapsedTime", feedback->elapsed_time);
                
                std::cout << "MotorTorqueControl - 反馈: 当前转矩=" << feedback->current_torque 
                         << "Nm, 已运行时间=" << feedback->elapsed_time << "s" << std::endl;
            };
        
        // 发送目标
        goal_future_ = client_->async_send_goal(goal_msg, send_goal_options);
        
        // 记录开始时间
        start_time_ = std::chrono::steady_clock::now();
        
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus MotorTorqueControl::onRunning()
    {
        std::cout << "MotorTorqueControl - onRunning" << std::endl;
        
        // 处理ROS事件
        rclcpp::spin_some(node_);
        
        // 检查是否超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        if (elapsed > timeout_) {
            std::cout << "MotorTorqueControl - Action请求超时" << std::endl;
            handleActionError(-4, "Action请求超时");
            
            // 取消目标
            if (goal_handle_) {
                client_->async_cancel_goal(goal_handle_);
            }
            
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查目标句柄是否就绪
        if (!goal_handle_) {
            auto status = goal_future_.wait_for(std::chrono::milliseconds(10));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_future_.get();
                if (!goal_handle_) {
                    std::cout << "MotorTorqueControl - 目标被拒绝" << std::endl;
                    handleActionError(-5, "Action目标被服务器拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                
                // 获取结果Future
                result_future_ = client_->async_get_result(goal_handle_);
                std::cout << "MotorTorqueControl - 目标已接受，开始执行" << std::endl;
            }
            return BT::NodeStatus::RUNNING;
        }
        
        // 检查结果是否就绪
        auto status = result_future_.wait_for(std::chrono::milliseconds(10));
        if (status == std::future_status::ready) {
            auto wrapped_result = result_future_.get();
            
            std::cout << "MotorTorqueControl - Action完成" << std::endl;
            
            // 设置输出端口
            bool success = (wrapped_result.code == rclcpp_action::ResultCode::SUCCEEDED);
            setOutput<bool>("boolOutputSuccess", success);
            setOutput<double>("doubleFinalTorque", wrapped_result.result->final_torque);
            setOutput<double>("doubleFinalVelocity", wrapped_result.result->final_velocity);
            setOutput<double>("doubleFinalPosition", wrapped_result.result->final_position);
            setOutput<int>("intErrorCode", wrapped_result.result->error_code);
            setOutput<std::string>("strOutputMessage", wrapped_result.result->error_message);
            
            // 记录结果
            std::cout << "MotorTorqueControl - 执行结果: " << (success ? "成功" : "失败") << std::endl;
            std::cout << "MotorTorqueControl - 最终转矩: " << wrapped_result.result->final_torque << "Nm" << std::endl;
            std::cout << "MotorTorqueControl - 最终速度: " << wrapped_result.result->final_velocity << "mm/s" << std::endl;
            std::cout << "MotorTorqueControl - 最终位置: " << wrapped_result.result->final_position << "mm" << std::endl;
            std::cout << "MotorTorqueControl - 消息: " << wrapped_result.result->error_message << std::endl;
            
            return success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
        }
        
        // Action尚未完成，继续等待
        return BT::NodeStatus::RUNNING;
    }

    void MotorTorqueControl::onHalted()
    {
        std::cout << "MotorTorqueControl - onHalted" << std::endl;
        
        try {
            // 取消正在进行的Action
            if (goal_handle_) {
                std::cout << "MotorTorqueControl - 取消Action目标" << std::endl;
                client_->async_cancel_goal(goal_handle_);
                goal_handle_.reset();
            }
            
            // 重置Future对象
            goal_future_ = std::shared_future<GoalHandleTorqueControl::SharedPtr>();
            result_future_ = std::shared_future<GoalHandleTorqueControl::WrappedResult>();
            
            // 设置取消状态输出
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "操作被取消");
            setOutput<int>("intErrorCode", -99);
            
            // 清理Action客户端
            if (client_) {
                std::cout << "MotorTorqueControl - 清理Action客户端" << std::endl;
                client_.reset();
            }
            
            std::cout << "MotorTorqueControl - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "MotorTorqueControl - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MotorTorqueControl>("MotorTorqueControl");
} 