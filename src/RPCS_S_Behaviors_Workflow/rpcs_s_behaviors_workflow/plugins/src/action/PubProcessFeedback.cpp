#include "action/PubProcessFeedback.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    PubProcessFeedback::PubProcessFeedback(const std::string& name, const BT::NodeConfiguration& config)
        : BT::SyncActionNode(name, config)
    {
    }

    BT::NodeStatus PubProcessFeedback::tick()
    {
        std::string process_step;
        std::string status;
        double progress = 0.0;
        std::string message;
        std::string operation;

        // 获取输入端口的值
        if (!getInput("strProcessStep", process_step)) {
            process_step = "未知步骤";
        }

        if (!getInput("strStatus", status)) {
            status = "RUNNING";
        }

        if (!getInput("doubleProgress", progress)) {
            progress = 0.0;
        }

        if (!getInput("strMessage", message)) {
            message = "";
        }

        if (!getInput("strOperation", operation)) {
            operation = "";
        }

        // 确保进度值在有效范围内
        progress = std::min(std::max(progress, 0.0), 100.0);

        // 获取黑板
        auto blackboard = this->config().blackboard;
        if (!blackboard) {
            RCLCPP_ERROR(rclcpp::get_logger("PubProcessFeedback"), "Failed to get blackboard");
            return BT::NodeStatus::FAILURE;
        }

        // 将反馈信息写入黑板
        blackboard->set("current_process_step", process_step);
        blackboard->set("current_status", status);
        blackboard->set("progress_percent", progress);
        blackboard->set("status_message", message);
        blackboard->set("current_operation", operation);

        // 记录反馈信息
        RCLCPP_INFO(
            rclcpp::get_logger("PubProcessFeedback"),
            "工艺执行反馈: [步骤: %s] [状态: %s] [进度: %.1f%%] [操作: %s] [消息: %s]",
            process_step.c_str(), status.c_str(), progress, operation.c_str(), message.c_str());

        // 总是返回成功
        return BT::NodeStatus::SUCCESS;
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::PubProcessFeedback>("PubProcessFeedback");
} 