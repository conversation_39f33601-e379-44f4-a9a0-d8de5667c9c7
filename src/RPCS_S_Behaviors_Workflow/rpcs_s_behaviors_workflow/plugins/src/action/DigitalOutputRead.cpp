#include "action/DigitalOutputRead.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <sstream>
#include <algorithm>

namespace rpcs_s_behaviors_workflow
{
    // 辅助函数：从字符串解析整数向量
    std::vector<int> parseIntVector(const std::string& str, rclcpp::Logger logger) {
        std::vector<int> result;
        std::string temp = str;
        
        RCLCPP_INFO(logger, "开始解析字符串: '%s'", str.c_str());
        
        // 将所有逗号替换为空格
        std::replace(temp.begin(), temp.end(), ',', ' ');
        RCLCPP_INFO(logger, "替换逗号后: '%s'", temp.c_str());
        
        // 使用stringstream解析空格分隔的数字
        std::stringstream ss(temp);
        int value;
        while (ss >> value) {
            result.push_back(value);
            RCLCPP_INFO(logger, "解析到数值: %d", value);
        }
        
        RCLCPP_INFO(logger, "解析完成，共 %zu 个数值", result.size());
        for (size_t i = 0; i < result.size(); ++i) {
            RCLCPP_INFO(logger, "  [%zu] = %d", i, result[i]);
        }
        
        return result;
    }
    DigitalOutputRead::DigitalOutputRead(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
        , goal_sent_(false)
        , goal_completed_(false)
        , should_cancel_(false)
    {
        static int controller_motor_node_ros_domain_id = Utils::GetControllerMotorNodeRosDomainId();
        // 创建ROS节点
        // node_ = std::make_shared<rclcpp::Node>("digital_output_read_node");
        // 函数create_node_with_domain的函数实现位置，有使用说明、注意事项
        node_ = create_node_with_domain("digital_output_read_node", controller_motor_node_ros_domain_id);
    }

    void DigitalOutputRead::createActionClient(const std::string& device_id)
    {
        // 创建action客户端
        std::string action_name = "/" + device_id + "/io_board_1/digital_output_read";
        action_client_ = rclcpp_action::create_client<DigitalOutputReadAction>(
            node_, action_name);
        
        RCLCPP_INFO(node_->get_logger(), "DigitalOutputRead创建action客户端: %s", action_name.c_str());
    }

    BT::NodeStatus DigitalOutputRead::onStart()
    {
        RCLCPP_DEBUG(node_->get_logger(), "DigitalOutputRead - onStart开始");
        
        // 重置状态
        goal_sent_ = false;
        goal_completed_ = false;
        output_addresses_.clear();
        expected_values_.clear();
        current_values_.clear();
        value_matched_.clear();
        
        // 获取输入参数
        if (!getInput<std::string>("strDeviceId", device_id_)) {
            device_id_ = "robot1"; // 默认设备ID
        }
        
        // 获取地址列表
        if (!getInput<std::vector<int>>("intOutputAddresses", output_addresses_)) {
            // 如果失败，尝试获取为字符串并解析
            std::string addresses_str;
            RCLCPP_INFO(node_->get_logger(), "无法直接获取向量类型的地址列表，尝试获取字符串");
            if (getInput<std::string>("intOutputAddresses", addresses_str)) {
                RCLCPP_INFO(node_->get_logger(), "成功获取字符串类型的地址列表: '%s'", addresses_str.c_str());
                
                // 打印每个字符的ASCII码，以便检查是否有特殊字符
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < addresses_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, addresses_str[i], static_cast<int>(addresses_str[i]));
                }
                
                output_addresses_ = parseIntVector(addresses_str, node_->get_logger());
                if (output_addresses_.empty()) {
                    RCLCPP_ERROR(node_->get_logger(), "未提供有效的输出地址列表");
                    setOutput<bool>("boolOutputSuccess", false);
                    setOutput<std::string>("strOutputMessage", "未提供有效的输出地址列表");
                    return BT::NodeStatus::FAILURE;
                }
            } else {
                RCLCPP_ERROR(node_->get_logger(), "未提供输出地址列表");
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "未提供输出地址列表");
                return BT::NodeStatus::FAILURE;
            }
        } else {
            RCLCPP_INFO(node_->get_logger(), "成功直接获取向量类型的地址列表，共 %zu 个地址", output_addresses_.size());
            
            // 尝试获取原始字符串，即使向量已经成功获取
            std::string addresses_str;
            if (getInput<std::string>("intOutputAddresses", addresses_str)) {
                RCLCPP_INFO(node_->get_logger(), "原始字符串: '%s'", addresses_str.c_str());
                
                // 打印每个字符的ASCII码
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < addresses_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, addresses_str[i], static_cast<int>(addresses_str[i]));
                }
                
                // 尝试使用我们的解析函数解析这个字符串
                std::vector<int> parsed_addresses = parseIntVector(addresses_str, node_->get_logger());
                RCLCPP_INFO(node_->get_logger(), "通过parseIntVector解析得到 %zu 个地址", parsed_addresses.size());
                for (size_t i = 0; i < parsed_addresses.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  解析地址[%zu] = %d", i, parsed_addresses[i]);
                }
                
                // 如果我们的解析函数得到的结果比直接获取的向量多，使用我们的解析结果
                if (parsed_addresses.size() > output_addresses_.size()) {
                    RCLCPP_INFO(node_->get_logger(), "使用parseIntVector解析的结果，因为它得到了更多的地址");
                    output_addresses_ = parsed_addresses;
                }
            }
        }
        
        // 获取其他参数
        if (!getInput<bool>("boolContinuousRead", continuous_read_)) {
            continuous_read_ = false; // 默认不连续读取
        }
        
        if (!getInput<double>("doubleReadInterval", read_interval_)) {
            read_interval_ = 0.1; // 默认读取间隔100ms
        }
        
        if (!getInput<double>("doubleDuration", duration_)) {
            duration_ = 0.0; // 默认无持续时间限制
        }
        
        if (!getInput<int>("intTimeoutMs", timeout_ms_)) {
            timeout_ms_ = 5000; // 默认超时5秒
        }
        
        // 获取期望值列表
        RCLCPP_INFO(node_->get_logger(), "开始获取期望值列表...");
        expected_values_.clear();  // 确保清空之前的值
        
        // 尝试获取单个期望值
        bool single_expected_value = false;
        bool has_single_expected = false;
        auto single_expected_result = getInput<bool>("boolExpectedValue", single_expected_value);
        has_single_expected = single_expected_result.has_value();
        
        // 尝试获取期望值列表作为向量
        std::vector<int> temp_values;
        bool has_expected_vector = false;
        auto expected_vector_result = getInput<std::vector<int>>("boolExpectedValues", temp_values);
        has_expected_vector = expected_vector_result.has_value();
        
        // 处理单个期望值或期望值列表
        if (has_single_expected) {
            RCLCPP_INFO(node_->get_logger(), "成功获取单个期望值: %s", single_expected_value ? "HIGH" : "LOW");
            expected_values_.push_back(single_expected_value ? 1 : 0);
        } else if (has_expected_vector) {
            RCLCPP_INFO(node_->get_logger(), "成功直接获取向量类型的期望值列表，共 %zu 个值", temp_values.size());
            expected_values_ = temp_values;
            
            // 尝试获取原始字符串，即使向量已经成功获取
            std::string values_str;
            if (getInput<std::string>("boolExpectedValues", values_str)) {
                RCLCPP_INFO(node_->get_logger(), "原始期望值字符串: '%s'", values_str.c_str());
                
                // 打印每个字符的ASCII码
                RCLCPP_INFO(node_->get_logger(), "期望值字符串的ASCII码:");
                for (size_t i = 0; i < values_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, values_str[i], static_cast<int>(values_str[i]));
                }
                
                // 尝试使用我们的解析函数解析这个字符串
                std::vector<int> parsed_values = parseIntVector(values_str, node_->get_logger());
                RCLCPP_INFO(node_->get_logger(), "通过parseIntVector解析得到 %zu 个期望值", parsed_values.size());
                for (size_t i = 0; i < parsed_values.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  解析期望值[%zu] = %d", i, parsed_values[i]);
                }
                
                // 如果我们的解析函数得到的结果比直接获取的向量多，使用我们的解析结果
                if (parsed_values.size() > temp_values.size()) {
                    RCLCPP_INFO(node_->get_logger(), "使用parseIntVector解析的期望值结果，因为它得到了更多的值");
                    expected_values_ = parsed_values;
                }
            }
        } else {
            // 尝试获取为字符串并解析
            std::string values_str;
            RCLCPP_INFO(node_->get_logger(), "无法获取期望值列表，尝试获取字符串");
            if (getInput<std::string>("boolExpectedValues", values_str)) {
                RCLCPP_INFO(node_->get_logger(), "成功获取字符串类型的期望值列表: '%s'", values_str.c_str());
                
                // 打印每个字符的ASCII码，以便检查是否有特殊字符
                RCLCPP_INFO(node_->get_logger(), "字符串的ASCII码:");
                for (size_t i = 0; i < values_str.length(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  字符[%zu] = '%c' (ASCII: %d)", 
                               i, values_str[i], static_cast<int>(values_str[i]));
                }
                
                expected_values_ = parseIntVector(values_str, node_->get_logger());
                RCLCPP_INFO(node_->get_logger(), "从字符串解析得到 %zu 个期望值", expected_values_.size());
                for (size_t i = 0; i < expected_values_.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), "  解析的期望值[%zu] = %d", i, expected_values_[i]);
                }
            }
            
            // 如果仍然为空，默认所有期望值为0 (false)
            if (expected_values_.empty()) {
                expected_values_.resize(output_addresses_.size(), 0);
                RCLCPP_INFO(node_->get_logger(), "未提供期望值，默认为LOW");
            }
        }
        
        // 打印最终期望值列表
        for (size_t i = 0; i < expected_values_.size(); ++i) {
            RCLCPP_INFO(node_->get_logger(), "  期望值[%zu] = %s", i, expected_values_[i] != 0 ? "HIGH" : "LOW");
        }
        
        // 检查地址和期望值的数量是否匹配
        if (expected_values_.size() < output_addresses_.size()) {
            // 如果期望值数量不足，用最后一个期望值填充
            int last_value = expected_values_.empty() ? 0 : expected_values_.back();
            while (expected_values_.size() < output_addresses_.size()) {
                expected_values_.push_back(last_value);
            }
            RCLCPP_WARN(node_->get_logger(), "期望值数量不足，已自动填充至%zu个", expected_values_.size());
        } else if (expected_values_.size() > output_addresses_.size()) {
            // 如果期望值数量过多，截断多余的
            expected_values_.resize(output_addresses_.size());
            RCLCPP_WARN(node_->get_logger(), "期望值数量过多，已截断至%zu个", expected_values_.size());
        }
        
        // 初始化当前值和匹配状态
        current_values_.resize(output_addresses_.size(), 0);
        value_matched_.resize(output_addresses_.size(), 0);
        
        // 获取可选的黑板变量名
        if (!getInput<std::string>("strBlackboardKey", blackboard_key_)) {
            blackboard_key_ = ""; // 默认为空，表示不更新黑板变量
        }

        // 创建action客户端
        createActionClient(device_id_);

        // 等待action服务可用
        if (!action_client_->wait_for_action_server(std::chrono::milliseconds(1000))) {
            RCLCPP_ERROR(node_->get_logger(), 
                "Action服务不可用: /%s/io_board_1/digital_output_read", device_id_.c_str());
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "数字输出读取服务不可用");
            return BT::NodeStatus::FAILURE;
        }

        // 创建goal
        goal_msg_ = std::make_shared<DigitalOutputReadAction::Goal>();
        
        // 确认一下地址的顺序
        RCLCPP_INFO(node_->get_logger(), "=== 准备构建Action请求参数 ===");
        RCLCPP_INFO(node_->get_logger(), "地址数量: %zu", output_addresses_.size());
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "地址[%zu] = %d", i, output_addresses_[i]);
        }
        
        // 构建goal消息中的地址列表
        goal_msg_->output_addresses.clear();
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            goal_msg_->output_addresses.push_back(static_cast<uint16_t>(output_addresses_[i]));
            RCLCPP_INFO(node_->get_logger(), "  添加地址[%zu] = %u", i, goal_msg_->output_addresses[i]);
        }
        
        // 打印地址和期望值的映射关系
        RCLCPP_INFO(node_->get_logger(), "地址和期望值的映射关系:");
        for (size_t i = 0; i < output_addresses_.size() && i < expected_values_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "  地址[%d] 期望值 = %s", 
                       output_addresses_[i], 
                       expected_values_[i] != 0 ? "HIGH" : "LOW");
        }
        
        goal_msg_->continuous_read = continuous_read_;
        goal_msg_->read_interval = read_interval_;
        goal_msg_->duration = duration_;

        // 打印详细的请求信息
        RCLCPP_INFO(node_->get_logger(), 
            "=== DigitalOutputRead 请求详情 ===");
        RCLCPP_INFO(node_->get_logger(), 
            "Action名称: /%s/io_board_1/digital_output_read", device_id_.c_str());
        RCLCPP_INFO(node_->get_logger(), 
            "设备ID: %s", device_id_.c_str());
        RCLCPP_INFO(node_->get_logger(), 
            "地址数量: %zu", output_addresses_.size());
        RCLCPP_INFO(node_->get_logger(), 
            "连续读取: %s", goal_msg_->continuous_read ? "是" : "否");
        RCLCPP_INFO(node_->get_logger(), 
            "读取间隔: %.2f秒", goal_msg_->read_interval);
        RCLCPP_INFO(node_->get_logger(), 
            "持续时间: %.2f秒", goal_msg_->duration);
        RCLCPP_INFO(node_->get_logger(), 
            "超时设置: %dms", timeout_ms_);
        
        // 打印所有地址
        for (size_t i = 0; i < output_addresses_.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), 
                "地址[%d]", output_addresses_[i]);
        }
        
        // 打印完整的Action请求参数
        RCLCPP_INFO(node_->get_logger(), "=== Action请求参数详情 ===");
        RCLCPP_INFO(node_->get_logger(), "请求类型: DigitalOutputReadAction");
        RCLCPP_INFO(node_->get_logger(), "output_addresses: [");
        for (size_t i = 0; i < goal_msg_->output_addresses.size(); i++) {
            RCLCPP_INFO(node_->get_logger(), "  %zu: %u", i, goal_msg_->output_addresses[i]);
        }
        RCLCPP_INFO(node_->get_logger(), "]");
        RCLCPP_INFO(node_->get_logger(), "continuous_read: %s", goal_msg_->continuous_read ? "true" : "false");
        RCLCPP_INFO(node_->get_logger(), "read_interval: %.2f", goal_msg_->read_interval);
        RCLCPP_INFO(node_->get_logger(), "duration: %.2f", goal_msg_->duration);
        RCLCPP_INFO(node_->get_logger(), "=== Action请求参数详情结束 ===");
        
        RCLCPP_INFO(node_->get_logger(), 
            "=== 发送请求到服务器 ===");

        // 发送goal
        auto send_goal_options = rclcpp_action::Client<DigitalOutputReadAction>::SendGoalOptions();
        
        send_goal_options.goal_response_callback = 
            [this](GoalHandleDigitalOutputRead::SharedPtr goal_handle) {
                if (!goal_handle) {
                    RCLCPP_ERROR(node_->get_logger(), "Goal被服务器拒绝");
                } else {
                    RCLCPP_INFO(node_->get_logger(), "Goal被服务器接受");
                    goal_handle_ = goal_handle;
                }
            };
            
        // 添加feedback回调
        send_goal_options.feedback_callback =
            [this](GoalHandleDigitalOutputRead::SharedPtr goal_handle,
                  const std::shared_ptr<const DigitalOutputReadAction::Feedback> feedback) {
                // 保存最新的反馈数据
                feedback_ = feedback;
                
                if (feedback && !feedback->current_values.empty()) {
                    RCLCPP_INFO(node_->get_logger(), 
                               "已读取 %d 次, 平均读取时间: %.2f毫秒", 
                               feedback->read_count,
                               feedback->average_read_time);
                               
                    // 打印当前值
                    for (size_t i = 0; i < feedback->addresses.size() && i < feedback->current_values.size(); ++i) {
                        RCLCPP_INFO(node_->get_logger(), 
                                   "地址[%d] = %s", 
                                   feedback->addresses[i],
                                   feedback->current_values[i] ? "HIGH" : "LOW");
                    }
                    
                    // 更新当前值列表
                    size_t min_size = std::min(feedback->current_values.size(), current_values_.size());
                    for (size_t i = 0; i < min_size; i++) {
                        current_values_[i] = feedback->current_values[i] ? 1 : 0;
                    }
                    
                    // 更新匹配状态列表
                    bool all_matched = true;
                    for (size_t i = 0; i < min_size; i++) {
                        // 找到当前地址在output_addresses_中的索引
                        size_t addr_idx = 0;
                        bool addr_found = false;
                        for (size_t j = 0; j < output_addresses_.size(); j++) {
                            if (static_cast<int>(feedback->addresses[i]) == output_addresses_[j]) {
                                addr_idx = j;
                                addr_found = true;
                                break;
                            }
                        }
                        
                        if (!addr_found) {
                            RCLCPP_WARN(node_->get_logger(), "地址 %d 不在预期地址列表中", feedback->addresses[i]);
                            continue;
                        }
                        
                        bool current_bool_value = (feedback->current_values[i] ? 1 : 0);
                        bool expected_bool_value = (expected_values_[addr_idx] != 0);
                        
                        // 更新当前值和匹配状态
                        current_values_[addr_idx] = current_bool_value ? 1 : 0;
                        value_matched_[addr_idx] = (current_bool_value == expected_bool_value) ? 1 : 0;
                        
                        RCLCPP_INFO(node_->get_logger(), 
                            "【匹配检查】地址[%d] 当前值[%s] %s期望值[%s]", 
                            feedback->addresses[i],
                            current_bool_value ? "HIGH" : "LOW",
                            value_matched_[addr_idx] != 0 ? "== " : "!= ",
                            expected_bool_value ? "HIGH" : "LOW");
                        
                        if (!value_matched_[addr_idx]) {
                            all_matched = false;
                        }
                    }
                    
                    // 添加总体匹配状态的日志
                    RCLCPP_INFO(node_->get_logger(), "【匹配结果】所有值匹配: %s", all_matched ? "是" : "否");
                    
                    // 设置多地址输出
                    setOutput<std::vector<int>>("boolOutputValues", current_values_);
                    setOutput<std::vector<int>>("boolValueMatched", value_matched_);
                    
                    // 如果所有值都匹配期望值，并且是连续读取模式，取消goal以结束操作
                    if (all_matched && continuous_read_ && goal_handle) {
                        RCLCPP_INFO(node_->get_logger(), "所有期望值已匹配，取消连续读取");
                        
                        // 使用完全非阻塞的方式发送取消请求
                        try {
                            RCLCPP_INFO(node_->get_logger(), "直接发送取消请求(非阻塞)");
                            
                            // 创建一个独立的线程来处理取消操作，避免阻塞主线程
                            std::thread([this, goal_handle]() {
                                try {
                                    RCLCPP_INFO(node_->get_logger(), "在独立线程中发送取消请求");
                                    auto cancel_future = action_client_->async_cancel_goal(goal_handle);
                                    
                                    // 设置较短的超时时间
                                    auto status = cancel_future.wait_for(std::chrono::milliseconds(200));
                                    if (status == std::future_status::ready) {
                                        RCLCPP_INFO(node_->get_logger(), "取消请求成功完成");
                                    } else {
                                        RCLCPP_WARN(node_->get_logger(), "取消请求等待超时，继续执行");
                                    }
                                } catch (const std::exception& e) {
                                    RCLCPP_ERROR(node_->get_logger(), "线程中取消请求异常: %s", e.what());
                                }
                            }).detach(); // 分离线程，让它独立运行
                            
                            RCLCPP_INFO(node_->get_logger(), "已启动独立线程处理取消请求");
                        } catch (const std::exception& e) {
                            RCLCPP_ERROR(node_->get_logger(), "创建取消线程异常: %s", e.what());
                        }
                        
                        // 标记为成功完成
                        goal_completed_ = true;
                        RCLCPP_INFO(node_->get_logger(), "已设置goal_completed_ = true");
                        
                        // 存储当前的反馈值作为结果
                        auto result = std::make_shared<DigitalOutputReadAction::Result>();
                        result->success = true;
                        result->error_code = 0;
                        result->error_message = "已匹配，提前完成";
                        result->addresses.resize(feedback->addresses.size());
                        result->output_values.resize(feedback->current_values.size());
                        
                        for (size_t i = 0; i < feedback->addresses.size(); i++) {
                            result->addresses[i] = feedback->addresses[i];
                        }
                        
                        for (size_t i = 0; i < feedback->current_values.size(); i++) {
                            result->output_values[i] = feedback->current_values[i];
                        }
                        
                        result_ = result;
                        RCLCPP_INFO(node_->get_logger(), "已创建结果对象");
                        
                        // 设置输出
                        setOutput<bool>("boolOutputSuccess", true);
                        setOutput<std::string>("strOutputMessage", "读取成功，所有值都匹配");
                        
                        // 输出当前读取的值 - 重要：确保按照行为树期望的格式输出
                        std::vector<int> current_values_for_output;
                        for (size_t i = 0; i < result_->output_values.size(); i++) {
                            // 注意：需要将HIGH/LOW转换为行为树期望的1/0值
                            current_values_for_output.push_back(result_->output_values[i] ? 1 : 0);
                        }
                        
                        // 输出当前值
                        setOutput<std::vector<int>>("boolOutputValues", current_values_for_output);
                        
                        // 打印当前值，便于调试
                        std::string values_str = "[";
                        for (size_t i = 0; i < current_values_for_output.size(); i++) {
                            values_str += std::to_string(current_values_for_output[i]);
                            if (i < current_values_for_output.size() - 1) {
                                values_str += ",";
                            }
                        }
                        values_str += "]";
                        RCLCPP_INFO(node_->get_logger(), "已设置当前值输出: %s", values_str.c_str());
                        
                        RCLCPP_INFO(node_->get_logger(), "已设置输出端口");
                        
                        // 仍然设置标志，以防取消请求失败
                        should_cancel_ = true;
                        
                        // 强制设置节点状态为成功
                        RCLCPP_INFO(node_->get_logger(), "强制设置节点状态为SUCCESS");
                        this->setStatus(BT::NodeStatus::SUCCESS);
                        RCLCPP_INFO(node_->get_logger(), "节点状态已设置为SUCCESS");
                        
                        RCLCPP_INFO(node_->get_logger(), "feedback_callback处理完成，返回成功状态");
                    }
                    
                    // 如果是单个地址，设置单个输出值
                    if (output_addresses_.size() == 1 && !current_values_.empty()) {
                        setOutput<bool>("boolCurrentValue", current_values_[0] != 0);
                    }
                    
                    // 如果指定了黑板变量名，则更新匹配状态到黑板变量
                    auto blackboard = this->config().blackboard;
                    if (blackboard && !blackboard_key_.empty()) {
                        // 如果是单个地址，存储单个值匹配状态
                        if (min_size == 1) {
                            blackboard->set(blackboard_key_, value_matched_[0] != 0);
                            
                            RCLCPP_INFO(node_->get_logger(), 
                                "【黑板更新】%s = %s (匹配: %s)", 
                                blackboard_key_.c_str(),
                                value_matched_[0] != 0 ? "TRUE" : "FALSE",
                                value_matched_[0] != 0 ? "是" : "否");
                        } else {
                            blackboard->set(blackboard_key_, all_matched);
                            
                            RCLCPP_INFO(node_->get_logger(), 
                                "【黑板更新】%s = %s (全部匹配: %s)", 
                                blackboard_key_.c_str(),
                                all_matched ? "TRUE" : "FALSE",
                                all_matched ? "是" : "否");
                        }
                    }
                }
            };

        send_goal_options.result_callback = 
            [this](const GoalHandleDigitalOutputRead::WrappedResult & result) {
                RCLCPP_INFO(node_->get_logger(), "*** result_callback 被调用 ***");
                
                goal_completed_ = true;
                result_ = result.result;

                // 打印详细的应答信息
                RCLCPP_INFO(node_->get_logger(), 
                    "=== DigitalOutputRead 应答详情 ===");
                RCLCPP_INFO(node_->get_logger(), 
                    "Action结果状态: %s", 
                    result.code == rclcpp_action::ResultCode::SUCCEEDED ? "成功" : 
                    result.code == rclcpp_action::ResultCode::ABORTED ? "中止" : 
                    result.code == rclcpp_action::ResultCode::CANCELED ? "取消" : "未知");
                
                if (result_) {
                    RCLCPP_INFO(node_->get_logger(), 
                        "操作成功: %s", result_->success ? "是" : "否");
                    RCLCPP_INFO(node_->get_logger(), 
                        "错误代码: %d", result_->error_code);
                    RCLCPP_INFO(node_->get_logger(), 
                        "错误消息: %s", result_->error_message.c_str());
                    RCLCPP_INFO(node_->get_logger(), 
                        "请求地址数量: %zu", goal_msg_->output_addresses.size());
                    RCLCPP_INFO(node_->get_logger(), 
                        "读取值数量: %zu", result_->output_values.size());
                    
                    // 更新当前值列表
                    size_t min_size = std::min(result_->output_values.size(), current_values_.size());
                    for (size_t i = 0; i < min_size; i++) {
                        current_values_[i] = result_->output_values[i] ? 1 : 0;
                    }
                    
                    // 更新匹配状态列表
                    bool all_matched = true;
                    for (size_t i = 0; i < min_size; i++) {
                        // 找到当前地址在output_addresses_中的索引
                        size_t addr_idx = 0;
                        bool addr_found = false;
                        for (size_t j = 0; j < output_addresses_.size(); j++) {
                            if (static_cast<int>(result_->addresses[i]) == output_addresses_[j]) {
                                addr_idx = j;
                                addr_found = true;
                                break;
                            }
                        }
                        
                        if (!addr_found) {
                            RCLCPP_WARN(node_->get_logger(), "地址 %d 不在预期地址列表中", result_->addresses[i]);
                            continue;
                        }
                        
                        // 将布尔比较转换为整数比较
                        bool current_bool_value = (result_->output_values[i] ? 1 : 0);
                        bool expected_bool_value = (expected_values_[addr_idx] != 0);
                        
                        // 更新当前值和匹配状态
                        current_values_[addr_idx] = current_bool_value ? 1 : 0;
                        value_matched_[addr_idx] = (current_bool_value == expected_bool_value) ? 1 : 0;
                        
                        if (value_matched_[addr_idx] == 0) {
                            all_matched = false;
                        }
                        
                        // 添加匹配状态的日志
                        RCLCPP_INFO(node_->get_logger(), 
                            "【结果匹配检查】地址[%d] 当前值[%s] %s期望值[%s]", 
                            result_->addresses[i],
                            current_bool_value ? "HIGH" : "LOW",
                            value_matched_[addr_idx] != 0 ? "== " : "!= ",
                            expected_bool_value ? "HIGH" : "LOW");
                    }
                    
                    // 添加总体匹配状态的日志
                    RCLCPP_INFO(node_->get_logger(), "【结果匹配结果】所有值匹配: %s", all_matched ? "是" : "否");
                    
                    // 设置多地址输出
                    setOutput<std::vector<int>>("boolOutputValues", current_values_);
                    setOutput<std::vector<int>>("boolValueMatched", value_matched_);
                    setOutput<std::vector<int>>("intOutputAddresses", output_addresses_);
                    
                    // 如果是单个地址，设置单个输出值
                    if (output_addresses_.size() == 1 && !current_values_.empty()) {
                        setOutput<bool>("boolCurrentValue", current_values_[0] != 0);
                    }
                    
                    // 设置操作是否成功的输出
                    setOutput<bool>("boolOutputSuccess", all_matched);
                    setOutput<std::string>("strOutputMessage", all_matched ? 
                        "读取成功，所有值都匹配" : "读取成功，但值不匹配");
                    
                    for (size_t i = 0; i < goal_msg_->output_addresses.size() && i < result_->output_values.size(); ++i) {
                        RCLCPP_INFO(node_->get_logger(), 
                            "地址[%d] = %s", 
                            static_cast<int>(goal_msg_->output_addresses[i]),
                            result_->output_values[i] ? "HIGH(1)" : "LOW(0)");
                    }
                    
                    if (goal_msg_->output_addresses.size() != result_->output_values.size()) {
                        RCLCPP_WARN(node_->get_logger(), 
                            "警告: 请求的地址数量(%zu)与返回的值数量(%zu)不匹配", 
                            goal_msg_->output_addresses.size(), result_->output_values.size());
                    }
                    
                    // 如果指定了黑板变量名，则更新匹配状态到黑板变量
                    auto blackboard = this->config().blackboard;
                    if (blackboard && !blackboard_key_.empty()) {
                        // 如果是单个地址，存储单个值匹配状态
                        if (min_size == 1) {
                            blackboard->set(blackboard_key_, value_matched_[0] != 0);
                            
                            RCLCPP_INFO(node_->get_logger(), 
                                "【黑板更新】%s = %s (匹配: %s)", 
                                blackboard_key_.c_str(),
                                value_matched_[0] != 0 ? "TRUE" : "FALSE",
                                value_matched_[0] != 0 ? "是" : "否");
                        } else {
                            blackboard->set(blackboard_key_, all_matched);
                            
                            RCLCPP_INFO(node_->get_logger(), 
                                "【黑板更新】%s = %s (全部匹配: %s)", 
                                blackboard_key_.c_str(),
                                all_matched ? "TRUE" : "FALSE",
                                all_matched ? "是" : "否");
                        }
                    }
                } else {
                    RCLCPP_ERROR(node_->get_logger(), "result_ 为空!");
                }
                RCLCPP_INFO(node_->get_logger(), 
                    "=== 应答处理完成 ===");
            };

        goal_handle_future_ = action_client_->async_send_goal(*goal_msg_, send_goal_options);
        
        goal_sent_ = true;
        goal_completed_ = false;
        start_time_ = std::chrono::steady_clock::now();

        RCLCPP_INFO(node_->get_logger(), 
            "开始读取数字输出: 设备[%s], 地址数量[%zu]", 
            device_id_.c_str(), output_addresses_.size());

        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus DigitalOutputRead::onRunning()
    {
        // 添加更多的日志，以便跟踪函数调用
        RCLCPP_DEBUG(node_->get_logger(), "DigitalOutputRead::onRunning() 被调用");
        
        // 检查是否需要取消请求
        if (should_cancel_ && goal_handle_ && goal_sent_) {
            RCLCPP_INFO(node_->get_logger(), "在onRunning中执行请求取消");
            try {
                // 使用非阻塞方式取消
                RCLCPP_INFO(node_->get_logger(), "执行async_cancel_goal...");
                action_client_->async_cancel_goal(goal_handle_);
                RCLCPP_INFO(node_->get_logger(), "async_cancel_goal已触发");
            } catch (const std::exception& e) {
                RCLCPP_WARN(node_->get_logger(), "取消请求异常: %s", e.what());
            }
            should_cancel_ = false;  // 重置标志，避免重复取消
        }
        
        // 多次处理ROS事件，确保我们捕获所有响应
        for (int i = 0; i < 5; i++) {
            rclcpp::spin_some(node_);
            // 短暂休眠，给ROS系统一些时间处理响应
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        // 检查goal handle是否已经准备好
        if (!goal_handle_ && goal_handle_future_.valid()) {
            auto status = goal_handle_future_.wait_for(std::chrono::milliseconds(0));
            if (status == std::future_status::ready) {
                goal_handle_ = goal_handle_future_.get();
                if (!goal_handle_) {
                    RCLCPP_ERROR(node_->get_logger(), "Goal被服务器拒绝");
                    setOutput<bool>("boolOutputSuccess", false);
                    setOutput<std::string>("strOutputMessage", "Goal被服务器拒绝");
                    return BT::NodeStatus::FAILURE;
                }
                RCLCPP_INFO(node_->get_logger(), "Goal handle已准备好");
            }
        }
        
        // 首先检查是否已经完成，如果完成则立即返回结果
        if (goal_completed_) {
            RCLCPP_INFO(node_->get_logger(), "目标已完成，返回最终结果");
            
            // 确保节点不再被调度
            this->setStatus(BT::NodeStatus::SUCCESS);
            
            if (result_) {
                bool success = result_->success;
                
                RCLCPP_INFO(node_->get_logger(), 
                    "=== DigitalOutputRead 最终结果 ===");
                RCLCPP_INFO(node_->get_logger(), 
                    "操作结果: %s", success ? "成功" : "失败");
                RCLCPP_INFO(node_->get_logger(), 
                    "读取地址数量: %zu", output_addresses_.size());
                RCLCPP_INFO(node_->get_logger(), 
                    "读取值数量: %zu", result_->output_values.size());
                RCLCPP_INFO(node_->get_logger(), 
                    "错误代码: %d", result_->error_code);
                RCLCPP_INFO(node_->get_logger(), 
                    "错误消息: %s", result_->error_message.c_str());
                    
                // 打印所有地址和值
                for (size_t i = 0; i < result_->addresses.size() && i < result_->output_values.size(); ++i) {
                    RCLCPP_INFO(node_->get_logger(), 
                        "地址[%d] = %s", 
                        static_cast<int>(result_->addresses[i]),
                        result_->output_values[i] ? "HIGH" : "LOW");
                }
                
                RCLCPP_INFO(node_->get_logger(), 
                    "=== 操作完成 ===");

                // 设置输出端口
                setOutput<bool>("boolOutputSuccess", success);
                setOutput<std::string>("strOutputMessage", result_->error_message);
                setOutput<int>("intErrorCode", result_->error_code);
                
                // 设置多地址输出
                std::vector<int> output_values(result_->output_values.size());
                std::vector<int> addresses;
                
                // 将bool值转换为int值（0或1）
                for (size_t i = 0; i < result_->output_values.size(); i++) {
                    output_values[i] = result_->output_values[i] ? 1 : 0;
                }
                
                // 将uint16地址转换为int
                addresses.resize(result_->addresses.size());
                for (size_t i = 0; i < result_->addresses.size(); i++) {
                    addresses[i] = static_cast<int>(result_->addresses[i]);
                }
                
                setOutput<std::vector<int>>("boolOutputValues", output_values);
                setOutput<std::vector<int>>("intOutputAddresses", addresses);

                RCLCPP_INFO(node_->get_logger(), "DigitalOutputRead操作成功完成，从onRunning返回SUCCESS");
                return BT::NodeStatus::SUCCESS;
            } else {
                RCLCPP_WARN(node_->get_logger(), "目标完成但结果为空，返回失败");
                setOutput<bool>("boolOutputSuccess", false);
                setOutput<std::string>("strOutputMessage", "目标已完成但结果为空");
                return BT::NodeStatus::FAILURE;
            }
        }
        
        // 检查是否超时
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start_time_).count();
            
        // 每秒打印一次等待信息
        static int last_log_time = 0;
        int current_time = static_cast<int>(elapsed_ms / 1000);
        if (current_time > last_log_time) {
            last_log_time = current_time;
            RCLCPP_INFO(node_->get_logger(), "等待服务器响应，已等待 %d 秒...", current_time);
        }
            
        if (elapsed_ms > timeout_ms_) {
            RCLCPP_ERROR(node_->get_logger(), "数字输出读取超时(%d毫秒)", timeout_ms_);
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "数字输出读取超时");
            setOutput<int>("intErrorCode", -1);
            return BT::NodeStatus::FAILURE;
        }
        
        // 处理反馈
        if (feedback_) {
            RCLCPP_INFO(node_->get_logger(), "*** 处理feedback回调的数据 ***");
            
            // 特殊检查：如果已经有反馈匹配了所有值，但节点没有被设置为完成
            if (!goal_completed_) {
                RCLCPP_INFO(node_->get_logger(), "检查是否需要直接完成节点...");
                
                // 检查当前值和期望值是否匹配
                bool all_matched = true;
                size_t min_size = std::min(feedback_->current_values.size(), expected_values_.size());
                
                for (size_t i = 0; i < min_size; i++) {
                    // 找到当前地址在output_addresses_中的索引
                    size_t addr_idx = 0;
                    bool addr_found = false;
                    for (size_t j = 0; j < output_addresses_.size(); j++) {
                        if (static_cast<int>(feedback_->addresses[i]) == output_addresses_[j]) {
                            addr_idx = j;
                            addr_found = true;
                            break;
                        }
                    }
                    
                    if (!addr_found) {
                        continue;
                    }
                    
                    bool current_bool_value = feedback_->current_values[i];
                    bool expected_bool_value = (expected_values_[addr_idx] != 0);
                    
                    if (current_bool_value != expected_bool_value) {
                        all_matched = false;
                        break;
                    }
                }
                
                if (all_matched && continuous_read_) {
                    RCLCPP_INFO(node_->get_logger(), "在onRunning中检测到匹配，强制完成节点");
                    
                    // 标记为成功完成
                    goal_completed_ = true;
                    
                    // 设置输出
                    setOutput<bool>("boolOutputSuccess", true);
                    setOutput<std::string>("strOutputMessage", "读取成功，所有值都匹配");
                    
                    // 输出当前读取的值 - 重要：确保按照行为树期望的格式输出
                    std::vector<int> current_values_for_output;
                    for (size_t i = 0; i < feedback_->current_values.size(); i++) {
                        // 注意：需要将HIGH/LOW转换为行为树期望的1/0值
                        current_values_for_output.push_back(feedback_->current_values[i] ? 1 : 0);
                    }
                    
                    // 输出当前值
                    setOutput<std::vector<int>>("boolOutputValues", current_values_for_output);
                    
                    // 打印当前值，便于调试
                    std::string values_str = "[";
                    for (size_t i = 0; i < current_values_for_output.size(); i++) {
                        values_str += std::to_string(current_values_for_output[i]);
                        if (i < current_values_for_output.size() - 1) {
                            values_str += ",";
                        }
                    }
                    values_str += "]";
                    RCLCPP_INFO(node_->get_logger(), "已设置当前值输出: %s", values_str.c_str());
                    
                    // 设置状态
                    this->setStatus(BT::NodeStatus::SUCCESS);
                    
                    // 如果有目标句柄，取消请求
                    if (goal_handle_) {
                        RCLCPP_INFO(node_->get_logger(), "从onRunning中取消请求");
                        action_client_->async_cancel_goal(goal_handle_);
                    }
                    
                    RCLCPP_INFO(node_->get_logger(), "onRunning中强制返回SUCCESS");
                    return BT::NodeStatus::SUCCESS;
                }
            }
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void DigitalOutputRead::onHalted()
    {
        RCLCPP_INFO(node_->get_logger(), "DigitalOutputRead - 操作被中断");
        
        // 检查是否需要取消但尚未执行
        if (should_cancel_) {
            RCLCPP_INFO(node_->get_logger(), "检测到未执行的取消请求");
            should_cancel_ = false;
        }
        
        if (goal_handle_ && goal_sent_ && !goal_completed_) {
            // 取消goal
            RCLCPP_INFO(node_->get_logger(), "执行onHalted中的async_cancel_goal...");
            try {
                auto cancel_future = action_client_->async_cancel_goal(goal_handle_);
                RCLCPP_INFO(node_->get_logger(), "Goal已取消");
                
                // 不等待取消完成，避免阻塞
                RCLCPP_INFO(node_->get_logger(), "不等待取消完成，继续清理");
            } catch (const std::exception& e) {
                RCLCPP_WARN(node_->get_logger(), "取消请求异常: %s", e.what());
            }
        }
        
        goal_sent_ = false;
        goal_completed_ = true;  // 标记为已完成，防止后续操作
        RCLCPP_INFO(node_->get_logger(), "已设置goal_completed_ = true");
        
        // 确保重置目标句柄
        if (goal_handle_) {
            RCLCPP_INFO(node_->get_logger(), "重置goal_handle_");
            goal_handle_.reset();
        }
        
        // 确保有最终结果，即使是取消的
        if (!result_) {
            auto result = std::make_shared<DigitalOutputReadAction::Result>();
            result->success = false;
            result->error_code = 1;  // 用户取消
            result->error_message = "操作被用户中断";
            result_ = result;
            RCLCPP_INFO(node_->get_logger(), "已创建取消结果对象");
        }
        
        // 设置输出，即使被中断也要有结果
        setOutput<bool>("boolOutputSuccess", false);  // 中断视为失败
        setOutput<std::string>("strOutputMessage", "操作被中断");
        
        RCLCPP_INFO(node_->get_logger(), "DigitalOutputRead - 清理完成");
    }
} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::DigitalOutputRead>("DigitalOutputRead");
} 