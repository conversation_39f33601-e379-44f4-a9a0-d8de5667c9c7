#include "action/BoardAlign.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"
#include <chrono>
#include <nlohmann/json.hpp>
#include <iostream>
#include <thread>
#include <string>
#include <vector>
#include <memory>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    BoardAlign::BoardAlign(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), request_sent_(false)
    {
        // 创建ROS节点
        node_ = std::make_shared<rclcpp::Node>("board_align_node");
    }

    BT::NodeStatus BoardAlign::onStart()
    {
        std::cout << "BoardAlign - onStart: " << name() << std::endl;
        
        // 获取超时参数
        auto timeout_opt = getInput<double>("doubleTimeout");
        timeout_ = timeout_opt.value_or(10.0);
        
        // 准备算法请求
        if (!prepareAlgorithmRequest()) {
            setErrorOutput("准备算法请求失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 发送算法请求
        if (!sendAlgorithmRequest()) {
            setErrorOutput("发送算法请求失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 设置开始时间
        start_time_ = std::chrono::steady_clock::now();
        request_sent_ = true;
        
        std::cout << "BoardAlign - 算法请求已发送，等待响应..." << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus BoardAlign::onRunning()
    {
        // 检查超时
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time_).count();
        
        if (elapsed >= timeout_) {
            setErrorOutput("算法执行超时");
            return BT::NodeStatus::FAILURE;
        }
        
        // 检查算法响应
        if (handleAlgorithmResponse()) {
            std::cout << "BoardAlign - 算法执行完成" << std::endl;
            return BT::NodeStatus::SUCCESS;
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void BoardAlign::onHalted()
    {
        std::cout << "BoardAlign - onHalted" << std::endl;
        request_sent_ = false;
    }

    bool BoardAlign::prepareAlgorithmRequest()
    {
        try {
            // 获取输入参数
            auto board_num_opt = getInput<int>("intBoardNum");
            if (!board_num_opt) {
                std::cerr << "BoardAlign - 缺少贴砖号参数" << std::endl;
                return false;
            }
            board_num_ = board_num_opt.value();

            auto param_overrides_opt = getInput<std::string>("strParamOverrides");
            std::string raw_param_overrides = param_overrides_opt.value_or("");
            
            // 将键值对格式转换为JSON格式 (param1=123,param2=test -> {"param1":"123","param2":"test"})
            param_overrides_ = convertKeyValuePairsToJson(raw_param_overrides);
            
            std::cout << "BoardAlign - 准备算法请求: 贴砖号=" << board_num_ 
                      << ", 参数覆盖=" << param_overrides_ << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "BoardAlign - 准备算法请求时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool BoardAlign::sendAlgorithmRequest()
    {
        try {
            std::cout << "BoardAlign - 开始发送算法请求..." << std::endl;
            
            // 创建Service客户端
            if (!client_) {
                std::string service_name = "/boards_align";
                client_ = node_->create_client<BoardAlignService>(service_name);
                
                if (!client_->wait_for_service(std::chrono::seconds(2))) {
                    std::cerr << "BoardAlign Service服务不可用: " << service_name << std::endl;
                    return false;
                }
            }

            // 创建请求
            auto request = std::make_shared<BoardAlignService::Request>();
            request->board_num = board_num_;
            request->param_overrides = param_overrides_;

            std::cout << "BoardAlign - 正在调用算法服务..." << std::endl;
            algorithm_future_ = client_->async_send_request(request);
            
            // 检查future是否有效
            if (!algorithm_future_.valid()) {
                std::cerr << "BoardAlign - 算法future无效，服务调用失败" << std::endl;
                return false;
            }
            
            std::cout << "BoardAlign - 算法服务请求已发送" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "BoardAlign - 发送算法请求时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool BoardAlign::handleAlgorithmResponse()
    {
        try {
            // 检查是否有响应
            if (!algorithm_future_.valid()) {
                return false;
            }
            
            // 非阻塞检查响应
            auto status = algorithm_future_.wait_for(std::chrono::milliseconds(10));
            if (status != std::future_status::ready) {
                return false; // 还没有响应
            }
            
            auto response = algorithm_future_.get();
            if (!response) {
                std::cerr << "BoardAlign - 收到空响应" << std::endl;
                setErrorOutput("收到空响应");
                return true; // 返回true表示处理完成，但结果是失败
            }
            
            // 设置输出端口
            setOutput<bool>("boolOutputSuccess", response->success);
            setOutput<std::string>("strOutputMessage", response->message);
            setOutput<bool>("boolIsFinish", response->is_finish);
            
            // 处理移动命令
            std::string move_commands_json = serializeMoveCommands(response->move_commands);
            setOutput<std::string>("strMoveCommands", move_commands_json);
            setOutput<int>("intMoveCommandsCount", static_cast<int>(response->move_commands.size()));
            
            // 解析并输出x、y、r值
            double x_value = 0.0, y_value = 0.0, r_value = 0.0;
            bool x_enable = false, y_enable = false, r_enable = false;
            for (const auto& cmd : response->move_commands) {
                if (cmd.axis == "x") {
                    x_value = cmd.move_value;
                    x_enable = true;
                } else if (cmd.axis == "y") {
                    y_value = cmd.move_value;
                    y_enable = true;
                } else if (cmd.axis == "r") {
                    r_value = cmd.move_value;
                    r_enable = true;
                }
            }
            setOutput<double>("doubleOutputX", x_value);
            setOutput<double>("doubleOutputY", y_value);
            setOutput<double>("doubleOutputR", r_value);
            setOutput<bool>("boolOutputX", x_enable);
            setOutput<bool>("boolOutputY", y_enable);
            setOutput<bool>("boolOutputR", r_enable);
            
            // 记录结果
            std::cout << "BoardAlign - 算法执行完成: " 
                      << (response->success ? "成功" : "失败") 
                      << ", 消息: " << response->message 
                      << ", 是否完成: " << (response->is_finish ? "是" : "否")
                      << ", 移动命令数量: " << response->move_commands.size() 
                      << ", X=" << x_value << ", Y=" << y_value << ", R=" << r_value 
                      << ", X使能=" << x_enable << ", Y使能=" << y_enable << ", R使能=" << r_enable << std::endl;
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "BoardAlign - 处理算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理算法响应时发生异常: " + std::string(e.what()));
            return true; // 返回true表示处理完成，但结果是失败
        }
    }

    void BoardAlign::setErrorOutput(const std::string& error_message)
    {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", error_message);
        setOutput<bool>("boolIsFinish", false);
        setOutput<std::string>("strMoveCommands", "[]");
        setOutput<int>("intMoveCommandsCount", 0);
        
        // 设置x、y、r默认值为0
        setOutput<double>("doubleOutputX", 0.0);
        setOutput<double>("doubleOutputY", 0.0);
        setOutput<double>("doubleOutputR", 0.0);
        setOutput<bool>("boolOutputX", false);
        setOutput<bool>("boolOutputY", false);
        setOutput<bool>("boolOutputR", false);
        
        std::cerr << "BoardAlign - 错误: " << error_message << std::endl;
    }

    std::string BoardAlign::serializeMoveCommands(const std::vector<algorithm_interface::msg::MoveCommand>& commands)
    {
        try {
            nlohmann::json json_array = nlohmann::json::array();
            
            for (const auto& cmd : commands) {
                nlohmann::json json_cmd;
                json_cmd["axis"] = cmd.axis;
                json_cmd["move_value"] = cmd.move_value;
                
                json_array.push_back(json_cmd);
            }
            
            return json_array.dump();
            
        } catch (const std::exception& e) {
            std::cerr << "序列化移动命令时发生异常: " << e.what() << std::endl;
            return "[]";
        }
    }

    std::string BoardAlign::convertKeyValuePairsToJson(const std::string& key_value_pairs)
    {
        if (key_value_pairs.empty()) {
            return "{}";
        }
        
        std::ostringstream json_stream;
        json_stream << "{";
        
        std::istringstream stream(key_value_pairs);
        std::string pair;
        bool first = true;
        
        // 分割键值对 (用逗号分隔)
        while (std::getline(stream, pair, ',')) {
            // 移除前后空格
            pair.erase(0, pair.find_first_not_of(" \t"));
            pair.erase(pair.find_last_not_of(" \t") + 1);
            
            if (pair.empty()) continue;
            
            // 查找等号位置
            size_t eq_pos = pair.find('=');
            if (eq_pos == std::string::npos) continue;
            
            std::string key = pair.substr(0, eq_pos);
            std::string value = pair.substr(eq_pos + 1);
            
            // 移除键值的前后空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (!first) {
                json_stream << ",";
            }
            first = false;
            
            // 构建JSON键值对（键和值都用双引号包围）
            json_stream << "\"" << key << "\":\"" << value << "\"";
        }
        
        json_stream << "}";
        return json_stream.str();
    }

} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::BoardAlign>("BoardAlign");
} 