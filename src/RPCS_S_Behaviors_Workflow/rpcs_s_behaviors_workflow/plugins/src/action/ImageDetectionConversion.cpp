#include "action/ImageDetectionConversion.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
// ImageDetectionConversion节点的实现已在头文件中完成，这里不需要额外代码
} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::ImageDetectionConversion>("ImageDetectionConversion");
}


