#include "action/GetActionParameters.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    GetActionParameters::GetActionParameters(const std::string& name, const BT::NodeConfiguration& config)
        : BT::SyncActionNode(name, config)
    {
    }

    BT::NodeStatus GetActionParameters::tick()
    {
        // 获取黑板
        auto blackboard = this->config().blackboard;
        if (!blackboard) {
            RCLCPP_ERROR(rclcpp::get_logger("GetActionParameters"), "Failed to get blackboard");
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", "无法获取黑板");
            return BT::NodeStatus::FAILURE;
        }

        try {
            // 获取基本信息
            std::string robot_id, process_id, process_type;
            int timeout_seconds = 0;
            bool preempt_current = false;
            
            if (blackboard->get("action_robot_id", robot_id)) {
                setOutput<std::string>("strRobotId", robot_id);
            }
            
            if (blackboard->get("action_process_id", process_id)) {
                setOutput<std::string>("strProcessId", process_id);
            }
            
            if (blackboard->get("action_process_type", process_type)) {
                setOutput<std::string>("strProcessType", process_type);
            }
            
            if (blackboard->get("action_timeout_seconds", timeout_seconds)) {
                setOutput<int>("intTimeoutSeconds", timeout_seconds);
            }
            
            if (blackboard->get("action_preempt_current", preempt_current)) {
                setOutput<bool>("boolPreemptCurrent", preempt_current);
            }

            // 获取参数数量
            int param_count = 0;
            if (blackboard->get("action_param_count", param_count)) {
                setOutput<int>("intParameterCount", param_count);
            }

            // 获取完整参数列表
            std::vector<std::string> param_list;
            if (blackboard->get("action_process_parameters", param_list)) {
                setOutput<std::vector<std::string>>("strParameterList", param_list);
            }

            // 获取扩展信息数量
            int extend_count = 0;
            if (blackboard->get("action_extends_count", extend_count)) {
                setOutput<int>("intExtendCount", extend_count);
            }

            // 根据输入端口获取特定参数
            int param_index = -1;
            std::string param_key;
            std::string param_value;
            bool found_param = false;

            // 方式1: 通过索引获取参数
            if (getInput<int>("intParameterIndex", param_index)) {
                if (param_index >= 0 && param_index < param_count) {
                    std::string key = "action_param_" + std::to_string(param_index);
                    if (blackboard->get(key, param_value)) {
                        setOutput<std::string>("strParameterValue", param_value);
                        found_param = true;
                        RCLCPP_INFO(rclcpp::get_logger("GetActionParameters"), 
                                   "通过索引获取参数[%d]: %s", param_index, param_value.c_str());
                    }
                } else {
                    RCLCPP_WARN(rclcpp::get_logger("GetActionParameters"), 
                               "参数索引 %d 超出范围 [0, %d)", param_index, param_count);
                }
            }

            // 方式2: 通过键名获取参数
            if (!found_param && getInput<std::string>("strParameterKey", param_key)) {
                if (blackboard->get(param_key, param_value)) {
                    setOutput<std::string>("strParameterValue", param_value);
                    found_param = true;
                    RCLCPP_INFO(rclcpp::get_logger("GetActionParameters"), 
                               "通过键名获取参数[%s]: %s", param_key.c_str(), param_value.c_str());
                } else {
                    RCLCPP_WARN(rclcpp::get_logger("GetActionParameters"), 
                               "未找到键名为 '%s' 的参数", param_key.c_str());
                }
            }

            // 处理扩展信息获取
            std::string extend_key, extend_value, extend_key_out;
            bool found_extend = false;

            // 方式1: 通过扩展键名获取值
            if (getInput<std::string>("strExtendKey", extend_key)) {
                std::string blackboard_extend_key = "extend_" + extend_key;
                if (blackboard->get(blackboard_extend_key, extend_value)) {
                    setOutput<std::string>("strExtendValue", extend_value);
                    setOutput<std::string>("strExtendKeyOut", extend_key);
                    found_extend = true;
                    RCLCPP_INFO(rclcpp::get_logger("GetActionParameters"),
                               "通过键名获取扩展信息[%s]: %s", extend_key.c_str(), extend_value.c_str());
                } else {
                    RCLCPP_WARN(rclcpp::get_logger("GetActionParameters"),
                               "未找到键名为 '%s' 的扩展信息", extend_key.c_str());
                }
            }

            // 方式2: 通过索引获取扩展信息
            int extend_index = -1;
            if (!found_extend && getInput<int>("intExtendIndex", extend_index)) {
                if (extend_index >= 0 && extend_index < extend_count) {
                    std::string key_name = "action_extend_" + std::to_string(extend_index) + "_key";
                    std::string value_name = "action_extend_" + std::to_string(extend_index) + "_value";

                    if (blackboard->get(key_name, extend_key_out) && blackboard->get(value_name, extend_value)) {
                        setOutput<std::string>("strExtendValue", extend_value);
                        setOutput<std::string>("strExtendKeyOut", extend_key_out);
                        found_extend = true;
                        RCLCPP_INFO(rclcpp::get_logger("GetActionParameters"),
                                   "通过索引获取扩展信息[%d]: %s = %s",
                                   extend_index, extend_key_out.c_str(), extend_value.c_str());
                    }
                } else {
                    RCLCPP_WARN(rclcpp::get_logger("GetActionParameters"),
                               "扩展信息索引 %d 超出范围 [0, %d)", extend_index, extend_count);
                }
            }

            // 记录获取到的信息
            RCLCPP_INFO(rclcpp::get_logger("GetActionParameters"),
                       "Action参数信息: 机器人ID=%s, 工艺ID=%s, 工艺类型=%s, 参数数量=%d, 扩展信息数量=%d",
                       robot_id.c_str(), process_id.c_str(), process_type.c_str(), param_count, extend_count);

            setOutput<bool>("boolOutputSuccess", true);
            setOutput<std::string>("strOutputMessage", "成功获取Action参数");
            return BT::NodeStatus::SUCCESS;

        } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("GetActionParameters"), 
                        "获取Action参数时发生错误: %s", e.what());
            setOutput<bool>("boolOutputSuccess", false);
            setOutput<std::string>("strOutputMessage", std::string("获取参数失败: ") + e.what());
            return BT::NodeStatus::FAILURE;
        }
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::GetActionParameters>("GetActionParameters");
}
