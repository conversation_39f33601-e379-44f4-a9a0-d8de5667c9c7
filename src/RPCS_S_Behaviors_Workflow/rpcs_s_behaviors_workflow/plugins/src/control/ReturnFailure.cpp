#include "control/ReturnFailure.hpp"
#include <iostream>

namespace rpcs_s_behaviors_workflow
{
    ReturnFailure::ReturnFailure(const std::string& name, const BT::NodeConfiguration& config)
        : ControlNode(name, config)
    {
    }

    BT::NodeStatus ReturnFailure::tick()
    {
        std::cout << "ReturnFailure - tick" << std::endl;
        
        // 执行所有子节点
        for (size_t i = 0; i < children_nodes_.size(); i++)
        {
            auto child_status = children_nodes_[i]->executeTick();
            std::cout << "ReturnFailure - 子节点 " << i << " 返回状态: " << BT::toStr(child_status) << std::endl;
        }
        
        // 无论子节点返回什么状态，该节点始终返回FAILURE
        std::cout << "ReturnFailure - 返回FAILURE状态" << std::endl;
        return BT::NodeStatus::FAILURE;
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::ReturnFailure>("ReturnFailure");
} 