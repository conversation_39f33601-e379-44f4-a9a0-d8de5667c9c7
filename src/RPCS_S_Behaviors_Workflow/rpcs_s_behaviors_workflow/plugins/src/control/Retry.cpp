#include "control/Retry.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    Retry::Retry(const std::string& name, const BT::NodeConfig& config)
        : BT::ControlNode(name, config)
    {
    }

    BT::NodeStatus Retry::tick()
    {
        std::cout << "Retry" << std::endl;
        // TODO: Implement the behavior
        return BT::NodeStatus::SUCCESS;
    }
} // namespace rpcs_s_behaviors_workflow

#include "behaviortree_cpp/bt_factory.h"

BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::Retry>("Retry");
}
