#include "base/AlgorithmNodeWithCamera.hpp"
#include <iostream>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    AlgorithmNodeWithCamera::AlgorithmNodeWithCamera(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config), current_phase_(ExecutionPhase::CAPTURING_IMAGES), timeout_(10.0)
    {
        // 创建ROS节点
        node_ = std::make_shared<rclcpp::Node>("algorithm_node_with_camera");
    }

    BT::NodeStatus AlgorithmNodeWithCamera::onStart()
    {
        std::cout << "AlgorithmNodeWithCamera - onStart: " << name() << std::endl;
        
        // 获取超时参数
        timeout_ = getTimeout();
        
        // 初始化相机客户端
        if (!initializeCameraClients()) {
            setErrorOutput("初始化相机客户端失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 开始拍照
        if (!captureImages()) {
            setErrorOutput("启动相机拍照失败");
            return BT::NodeStatus::FAILURE;
        }
        
        // 设置开始时间和阶段
        start_time_ = std::chrono::steady_clock::now();
        current_phase_ = ExecutionPhase::CAPTURING_IMAGES;
        
        std::cout << "AlgorithmNodeWithCamera - 开始拍照阶段" << std::endl;
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus AlgorithmNodeWithCamera::onRunning()
    {
        // 处理ROS事件，确保服务响应能够被接收
        rclcpp::spin_some(node_);
        
        // 检查超时
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        if (elapsed > timeout_) {
            std::cout << "AlgorithmNodeWithCamera - 执行超时 (已运行 " << elapsed << " 秒，超时时间 " << timeout_ << " 秒)" << std::endl;
            setErrorOutput("执行超时");
            return BT::NodeStatus::FAILURE;
        }
        
        // 每5秒打印一次进度信息
        static auto last_progress_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::seconds>(current_time - last_progress_time).count() >= 5) {
            std::cout << "AlgorithmNodeWithCamera - 已运行 " << elapsed << " 秒，当前阶段: ";
            switch (current_phase_) {
                case ExecutionPhase::CAPTURING_IMAGES:
                    std::cout << "拍照阶段";
                    break;
                case ExecutionPhase::CALLING_ALGORITHM:
                    std::cout << "算法调用阶段";
                    break;
                case ExecutionPhase::COMPLETED:
                    std::cout << "已完成";
                    break;
            }
            std::cout << std::endl;
            last_progress_time = current_time;
        }
        
        switch (current_phase_) {
            case ExecutionPhase::CAPTURING_IMAGES:
                if (checkCameraResponses()) {
                    // 拍照完成，开始调用算法
                    if (callAlgorithmService()) {
                        current_phase_ = ExecutionPhase::CALLING_ALGORITHM;
                        std::cout << "AlgorithmNodeWithCamera - 进入算法调用阶段" << std::endl;
                    } else {
                        setErrorOutput("调用算法服务失败");
                        return BT::NodeStatus::FAILURE;
                    }
                }
                break;
                
            case ExecutionPhase::CALLING_ALGORITHM:
                if (checkAlgorithmResponse()) {
                    current_phase_ = ExecutionPhase::COMPLETED;
                    std::cout << "AlgorithmNodeWithCamera - 算法执行完成，检查结果..." << std::endl;
                    // checkAlgorithmResponse() 返回的是 handleAlgorithmResponse() 的结果
                    // 这个结果表示算法是否成功执行
                    return BT::NodeStatus::SUCCESS;
                }
                // 如果没有收到响应，检查是否有致命错误
                if (current_phase_ == ExecutionPhase::COMPLETED) {
                    // 如果状态已被设置为COMPLETED，说明发生了致命错误
                    std::cout << "AlgorithmNodeWithCamera - 算法执行失败（致命错误）" << std::endl;
                    return BT::NodeStatus::FAILURE;
                }
                // 否则继续等待
                break;
                
            case ExecutionPhase::COMPLETED:
                return BT::NodeStatus::SUCCESS;
        }
        
        return BT::NodeStatus::RUNNING;
    }

    void AlgorithmNodeWithCamera::onHalted()
    {
        std::cout << "AlgorithmNodeWithCamera - onHalted: " << name() << std::endl;
        
        try {
            setErrorOutput("操作被中止");
            
            // 清理Future
            camera_futures_.clear();
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            
            std::cout << "AlgorithmNodeWithCamera - 清理资源完成" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "AlgorithmNodeWithCamera - 清理资源时发生异常: " << e.what() << std::endl;
        }
    }

    double AlgorithmNodeWithCamera::getTimeout()
    {
        double timeout = 30.0;  // 增加默认超时时间到30秒
        getInput<double>("doubleTimeout", timeout);
        return timeout;
    }

    bool AlgorithmNodeWithCamera::initializeCameraClients()
    {
        try {
            // 获取相机配置 - 优先从输入端口获取
            auto camera_configs = getCameraConfigsFromInput();
            if (camera_configs.empty()) {
                std::cerr << "没有配置相机" << std::endl;
                return false;
            }
            
            // 为每个相机创建客户端
            for (const auto& config : camera_configs) {
                std::string service_name = createCameraServiceName(config.namespace_);
                auto client = node_->create_client<hikvision_interface::srv::GetSingleImage>(service_name);
                
                if (!client->wait_for_service(std::chrono::seconds(20))) {
                    std::cerr << "相机服务不可用: " << service_name << std::endl;
                    return false;
                }
                
                camera_clients_[config.namespace_] = client;
                std::cout << "相机服务客户端初始化成功: " << service_name << std::endl;
            }
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "初始化相机客户端时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool AlgorithmNodeWithCamera::captureImages()
    {
        try {
            // 获取相机配置 - 优先从输入端口获取
            auto camera_configs = getCameraConfigsFromInput();
            
            // 为每个相机发送拍照请求
            for (const auto& config : camera_configs) {
                auto request = std::make_shared<hikvision_interface::srv::GetSingleImage::Request>();
                request->trigger_image = config.trigger_image;
                
                auto client = camera_clients_[config.namespace_];
                camera_futures_[config.namespace_] = client->async_send_request(request).future.share();
                
                std::cout << "发送相机拍照请求: " << config.namespace_ << std::endl;
            }
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "发送拍照请求时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool AlgorithmNodeWithCamera::checkCameraResponses()
    {
        // 获取相机配置 - 优先从输入端口获取
        auto camera_configs = getCameraConfigsFromInput();
        
        // 检查所有相机是否都完成了拍照
        for (const auto& config : camera_configs) {
            auto it = camera_futures_.find(config.namespace_);
            if (it == camera_futures_.end()) {
                std::cerr << "找不到相机Future: " << config.namespace_ << std::endl;
                return false;
            }
            
            auto status = it->second.wait_for(std::chrono::milliseconds(10000));
            if (status != std::future_status::ready) {
                return false; // 该相机还未完成
            }
        }
        
        try {
            // 获取所有相机的拍照结果
            captured_images_.clear();
            for (const auto& config : camera_configs) {
                auto it = camera_futures_.find(config.namespace_);
                auto response = it->second.get();
                
                if (!response->success) {
                    std::cerr << "相机拍照失败: " << config.namespace_ << " - " << response->message << std::endl;
                    return false;
                }
                
                captured_images_.push_back(response->image);
                std::cout << "相机拍照成功: " << config.namespace_ << std::endl;
            }
            
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "处理相机响应时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool AlgorithmNodeWithCamera::callAlgorithmService()
    {
        try {
            std::cout << "AlgorithmNodeWithCamera - 开始调用算法服务..." << std::endl;
            
            // 准备算法服务请求
            auto request = prepareAlgorithmRequest(captured_images_);
            if (!request) {
                std::cerr << "准备算法服务请求失败" << std::endl;
                return false;
            }
            
            std::cout << "AlgorithmNodeWithCamera - 算法请求准备完成，开始发送..." << std::endl;
            
            // 发送算法服务请求
            algorithm_future_ = sendAlgorithmRequest(request);
            
            // 检查future是否有效
            if (!algorithm_future_.valid()) {
                std::cerr << "AlgorithmNodeWithCamera - 算法future无效，服务调用失败" << std::endl;
                return false;
            }
            
            std::cout << "AlgorithmNodeWithCamera - 算法服务请求已发送: " << getAlgorithmServiceName() << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "调用算法服务时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

    bool AlgorithmNodeWithCamera::checkAlgorithmResponse()
    {
        // 检查future是否有效
        if (!algorithm_future_.valid()) {
            std::cerr << "AlgorithmNodeWithCamera - 算法future无效" << std::endl;
            setErrorOutput("算法future无效");
            current_phase_ = ExecutionPhase::COMPLETED;  // 强制进入完成状态
            return false;
        }
        
        // 处理ROS事件，确保服务响应能够被接收
        rclcpp::spin_some(node_);
        
        auto status = algorithm_future_.wait_for(std::chrono::milliseconds(100));
        if (status != std::future_status::ready) {
            // 添加周期性日志，每5秒打印一次等待状态
            static auto last_log_time = std::chrono::steady_clock::now();
            auto current_time = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(current_time - last_log_time).count() >= 5) {
                std::cout << "AlgorithmNodeWithCamera - 等待算法响应中..." << std::endl;
                last_log_time = current_time;
            }
            return false; // 算法还未完成，继续等待
        }
        
        try {
            std::cout << "AlgorithmNodeWithCamera - 算法响应已就绪，开始处理..." << std::endl;
            
            // 获取响应并立即处理，避免future被重复消费
            auto response = algorithm_future_.get();
            bool result = handleAlgorithmResponse(response);
            
            // 清理future，避免重复处理
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            
            return result;
            
        } catch (const std::exception& e) {
            std::cerr << "处理算法响应时发生异常: " << e.what() << std::endl;
            setErrorOutput("处理算法响应时发生异常: " + std::string(e.what()));
            
            // 清理future
            algorithm_future_ = std::shared_future<std::shared_ptr<void>>();
            current_phase_ = ExecutionPhase::COMPLETED;  // 强制进入完成状态
            return false;
        }
    }

    std::string AlgorithmNodeWithCamera::createCameraServiceName(const std::string& camera_namespace)
    {
        return "/driver/hikvision/" + camera_namespace + "/get_single_image";
    }

    std::vector<AlgorithmNodeWithCamera::CameraConfig> AlgorithmNodeWithCamera::getCameraConfigsFromInput()
    {
        std::vector<CameraConfig> configs;
        
        try {
            // 尝试获取相机配置字符串，格式为: "camera1,camera2" 或 "camera1"
            std::string camera_namespaces;
            if (getInput<std::string>("strCameraNamespaces", camera_namespaces)) {
                // 解析相机命名空间字符串
                std::stringstream ss(camera_namespaces);
                std::string camera_namespace;
                
                while (std::getline(ss, camera_namespace, ',')) {
                    // 去除空格
                    camera_namespace.erase(0, camera_namespace.find_first_not_of(" \t"));
                    camera_namespace.erase(camera_namespace.find_last_not_of(" \t") + 1);
                    
                    if (!camera_namespace.empty()) {
                        configs.emplace_back(camera_namespace, true);
                        std::cout << "AlgorithmNodeWithCamera - 从输入端口获取相机配置: " << camera_namespace << std::endl;
                    }
                }
            }
            
            // 如果没有配置相机，使用默认配置
            if (configs.empty()) {
                std::cout << "AlgorithmNodeWithCamera - 未从输入端口获取到相机配置，使用默认配置" << std::endl;
                // 调用子类的默认实现
                configs = getCameraConfigs();
            }
            
        } catch (const std::exception& e) {
            std::cerr << "AlgorithmNodeWithCamera - 从输入端口获取相机配置时发生异常: " << e.what() << std::endl;
            // 使用子类的默认实现
            configs = getCameraConfigs();
        }
        
        return configs;
    }

} // namespace rpcs_s_behaviors_workflow 