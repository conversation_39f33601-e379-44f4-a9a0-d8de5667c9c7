// 继承于StatefulActionNode，添加了消息发布功能，添加OnErrorHandler异常处理函数
#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__BASE__BASESTATEFULACTIONNODE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__BASE__BASESTATEFULACTIONNODE_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "behaviortree_ros2/bt_topic_pub_node.hpp"
#include "behaviortree_ros2/bt_topic_sub_node.hpp"
namespace rpcs_s_behaviors_workflow
{
class BaseStatefulActionNode : public BT::StatefulActionNode
{
public:
    BaseStatefulActionNode(const std::string& name, const BT::NodeConfiguration& config);
    virtual ~BaseStatefulActionNode() = default;  // 默认析构函数
    virtual BT::NodeStatus onStart() override;    // 节点启动时执行
    virtual BT::NodeStatus onRunning() override;  // 节点运行时执行
    virtual void onHalted() override;             // 节点停止时执行
    virtual void onErrorHandler(const std::string& error_msg) override;  // 错误处理函数
    virtual bool setMessage(TopicT& msg) = 0;     // 设置消息内容
    //急停，暂停，取消，恢复
    virtual void onEmergencyStopHandler() override;
    virtual void onPauseHandler() override;
    virtual void onCancelHandler() override;
    virtual void onResumeHandler() override;

};

} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__BASE__BASESTATEFULACTIONNODE_HPP_ 
