#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__BASE__ALGORITHMNODE_WITH_CAMERA_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__BASE__ALGORITHMNODE_WITH_CAMERA_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "hikvision_interface/srv/get_single_image.hpp"
#include "sensor_msgs/msg/image.hpp"
#include <chrono>
#include <memory>
#include <string>
#include <future>
#include <vector>
#include <map>

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 带相机拍照功能的算法节点抽象基类
     * 
     * 为所有需要使用相机拍照的算法节点提供统一的相机接口
     * 支持灵活配置相机数量和命名空间
     */
    class AlgorithmNodeWithCamera : public BT::StatefulActionNode
    {
    public:
        AlgorithmNodeWithCamera(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~AlgorithmNodeWithCamera() = default;

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    protected:
        // 相机配置结构
        struct CameraConfig {
            std::string namespace_;
            bool trigger_image;
            
            CameraConfig(const std::string& ns, bool trigger = true) 
                : namespace_(ns), trigger_image(trigger) {}
        };

        // 算法执行阶段枚举
        enum class ExecutionPhase {
            CAPTURING_IMAGES,    // 正在拍照
            CALLING_ALGORITHM,   // 正在调用算法
            COMPLETED           // 执行完成
        };

        // 抽象方法 - 子类必须实现
        /**
         * @brief 获取算法服务名称
         * @return 服务名称
         */
        virtual std::string getAlgorithmServiceName() = 0;
        
        /**
         * @brief 获取相机配置列表 - 从输入端口读取
         * @return 相机配置列表
         */
        virtual std::vector<CameraConfig> getCameraConfigs() = 0;
        
        /**
         * @brief 准备算法服务请求
         * @param images 拍摄的图像列表，按相机配置顺序排列
         * @return 算法服务请求的shared_ptr，失败返回nullptr
         */
        virtual std::shared_ptr<void> prepareAlgorithmRequest(
            const std::vector<sensor_msgs::msg::Image>& images) = 0;
        
        /**
         * @brief 发送算法服务请求
         * @param request 算法服务请求
         * @return 算法服务响应的future
         */
        virtual std::shared_future<std::shared_ptr<void>> sendAlgorithmRequest(
            std::shared_ptr<void> request) = 0;
        
        /**
         * @brief 处理算法服务响应
         * @param response 算法服务响应
         * @return 处理是否成功
         */
        virtual bool handleAlgorithmResponse(std::shared_ptr<void> response) = 0;
        
        /**
         * @brief 设置错误输出
         * @param error_message 错误消息
         */
        virtual void setErrorOutput(const std::string& error_message) = 0;

        // 工具方法
        /**
         * @brief 获取输入参数超时时间
         * @return 超时时间（秒）
         */
        double getTimeout();
        
        /**
         * @brief 获取ROS节点
         * @return ROS节点共享指针
         */
        rclcpp::Node::SharedPtr getNode() const { return node_; }

        /**
         * @brief 检查算法响应（可被子类重写）
         * @return 是否收到响应
         */
        virtual bool checkAlgorithmResponse();

        /**
         * @brief 从输入端口获取相机配置
         * @return 相机配置列表
         */
        std::vector<CameraConfig> getCameraConfigsFromInput();

    private:
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // 相机服务客户端映射
        std::map<std::string, rclcpp::Client<hikvision_interface::srv::GetSingleImage>::SharedPtr> camera_clients_;
        
        // 相机服务响应的Future映射
        std::map<std::string, std::shared_future<hikvision_interface::srv::GetSingleImage::Response::SharedPtr>> camera_futures_;
        
        // 算法服务响应的Future
        std::shared_future<std::shared_ptr<void>> algorithm_future_;
        
        // 执行状态
        ExecutionPhase current_phase_;
        
        // 拍摄的图像列表
        std::vector<sensor_msgs::msg::Image> captured_images_;
        
        // 超时参数
        double timeout_;
        
        // 开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 内部方法
        bool initializeCameraClients();
        bool captureImages();
        bool checkCameraResponses();
        bool callAlgorithmService();
        std::string createCameraServiceName(const std::string& camera_namespace);
    };

} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__BASE__ALGORITHMNODE_WITH_CAMERA_HPP_ 