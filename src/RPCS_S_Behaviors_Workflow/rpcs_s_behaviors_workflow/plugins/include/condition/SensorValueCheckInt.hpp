#pragma once

#include <string>
#include <behaviortree_cpp/bt_factory.h>
#include <behaviortree_cpp/action_node.h>

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 检查整型传感器值是否符合期望的条件节点
 */
class SensorValueCheckInt : public BT::ConditionNode
{
public:
    /**
     * @brief 构造函数
     * @param name 节点名称
     * @param config 节点配置
     */
    SensorValueCheckInt(const std::string& name, const BT::NodeConfig& config)
    : BT::ConditionNode(name, config)
    {
    }

    /**
     * @brief 获取节点的端口配置
     * @return 端口配置
     */
    static BT::PortsList providedPorts()
    {
        return {
            BT::InputPort<int>("sensor_value", "传感器值"),
            BT::InputPort<int>("expected_value", 1, "期望值")
        };
    }

private:
    /**
     * @brief 节点的执行逻辑
     * @return 节点的执行结果
     */
    BT::NodeStatus tick() override
    {
        int sensor_value = 0;
        int expected_value = 1;

        // 获取传感器值
        if (!getInput("sensor_value", sensor_value)) {
            return BT::NodeStatus::FAILURE;
        }

        // 获取期望值
        getInput("expected_value", expected_value);

        // 比较传感器值和期望值
        if (sensor_value == expected_value) {
            return BT::NodeStatus::SUCCESS;
        } else {
            return BT::NodeStatus::FAILURE;
        }
    }
};

}  // namespace rpcs_s_behaviors_workflow 