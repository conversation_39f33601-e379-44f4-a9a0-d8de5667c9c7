#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PRODUCTPOWERONDETECT_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PRODUCTPOWERONDETECT_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/product_power_on_detect.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class ProductPowerOnDetect : public BT::StatefulActionNode
    {
    public:
        ProductPowerOnDetect(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~ProductPowerOnDetect() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 检测步骤参数
                BT::InputPort<std::string>("strStep", "检测步骤：ON_CHECK-移动到检测台，PLUGIN-插接线，PULLOUT-拔出，NG_UNLOAD-ng品下料，OK_UNLOAD-成品下料，GET_RESULT-获取结果，AWAIT_RESULT-等待结果"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolResult", "成品检测结果：true-ok，false-ng"),
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时反馈
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using ProductPowerOnDetectAction = vir_robot_interfaces::action::ProductPowerOnDetect;
        using GoalHandleProductPowerOnDetect = rclcpp_action::ClientGoalHandle<ProductPowerOnDetectAction>;
        
        void createActionClient();
        std::string createActionName();
        void handleActionError(const std::string& error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<ProductPowerOnDetectAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleProductPowerOnDetect::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleProductPowerOnDetect::WrappedResult> result_future_;
        
        // 输入参数
        std::string step_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleProductPowerOnDetect::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PRODUCTPOWERONDETECT_HPP_ 