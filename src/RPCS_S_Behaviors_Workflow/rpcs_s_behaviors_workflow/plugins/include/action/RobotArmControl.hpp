#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTARMCONTROL_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTARMCONTROL_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/robot_arm_control.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class RobotArmControl : public BT::StatefulActionNode
    {
    public:
        using RobotArmControlAction = rpcs_interfaces_motor::action::RobotArmControl;
        using GoalHandleRobotArmControl = rclcpp_action::ClientGoalHandle<RobotArmControlAction>;

        RobotArmControl(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~RobotArmControl() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口 - 基于action文件的Goal部分
                BT::InputPort<std::string>("strDeviceName", "/robot1", "设备名称/命名空间"),
                BT::InputPort<int>("intProjectId", 1, "工程ID"),
                BT::InputPort<int>("intSpeedMultiplier", 100, "速度倍率(1-100)"),
                BT::InputPort<float>("floatPositionX", 0.0, "X坐标"),
                BT::InputPort<float>("floatPositionY", 0.0, "Y坐标"),
                BT::InputPort<float>("floatPositionZ", 0.0, "Z坐标"),
                BT::InputPort<float>("floatRotationRx", 0.0, "Rx旋转角度"),
                BT::InputPort<float>("floatRotationRy", 0.0, "Ry旋转角度"),
                BT::InputPort<float>("floatRotationRz", 0.0, "Rz旋转角度"),
                BT::InputPort<int>("intFunctionData0", 0, "功能数据0(夹爪控制: 0=开启, 1=关闭)"),
                BT::InputPort<int>("intFunctionData1", 0, "功能数据1(工具激活: 0=禁用, 1=启用)"),
                BT::InputPort<int>("intFunctionData2", 0, "功能数据2(传送带控制: 0=停止, 1=启动)"),
                BT::InputPort<int>("intFunctionData3", 0, "功能数据3(检测功能: 0=关闭, 1=开启)"),
                BT::InputPort<int>("intFunctionData4", 0, "功能数据4(自定义功能1)"),
                BT::InputPort<int>("intFunctionData5", 0, "功能数据5(自定义功能2)"),
                BT::InputPort<int>("intTimeoutMs", 60000, "超时时间(毫秒)"),
                
                // 输出端口 - 基于action文件的Result部分
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputErrorMessage", "错误信息"),
                BT::OutputPort<double>("doubleExecutionTime", "执行时间(秒)"),
                
                // 程序状态输出
                BT::OutputPort<bool>("boolProgramRunning", "程序运行状态"),
                BT::OutputPort<bool>("boolProgramPaused", "程序暂停状态"),
                BT::OutputPort<bool>("boolRemoteModeEnabled", "远程模式启用状态"),
                BT::OutputPort<bool>("boolEmergencyStop", "急停状态"),
                BT::OutputPort<bool>("boolProgramClearRequest", "程序清求状态"),
                BT::OutputPort<bool>("boolErrorStatus", "错误状态"),
                BT::OutputPort<bool>("boolMotorPowerStatus", "电机电源状态"),
                BT::OutputPort<int>("intProjectIdStatus", "工程ID状态"),
                
                // 当前位置输出
                BT::OutputPort<float>("floatCurrentX", "当前X坐标"),
                BT::OutputPort<float>("floatCurrentY", "当前Y坐标"),
                BT::OutputPort<float>("floatCurrentZ", "当前Z坐标"),
                BT::OutputPort<float>("floatCurrentRx", "当前Rx角度"),
                BT::OutputPort<float>("floatCurrentRy", "当前Ry角度"),
                BT::OutputPort<float>("floatCurrentRz", "当前Rz角度"),
                
                // 进度和状态
                BT::OutputPort<double>("doubleProgressPercentage", "执行进度百分比"),
                BT::OutputPort<std::string>("strCurrentStatus", "当前状态描述")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createActionClient(const std::string& device_name);
        void goalResponseCallback(const GoalHandleRobotArmControl::SharedPtr& goal_handle);
        void feedbackCallback(const GoalHandleRobotArmControl::SharedPtr,
                            const std::shared_ptr<const RobotArmControlAction::Feedback> feedback);
        void resultCallback(const GoalHandleRobotArmControl::WrappedResult& result);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<RobotArmControlAction>::SharedPtr action_client_;
        
        // Goal handle
        GoalHandleRobotArmControl::SharedPtr goal_handle_;
        
        // 控制变量
        bool goal_sent_;
        bool goal_accepted_;
        bool result_received_;
        bool goal_aborted_;
        int timeout_ms_;
        std::chrono::steady_clock::time_point start_time_;
        
        // 最新的feedback和result数据
        RobotArmControlAction::Feedback::SharedPtr latest_feedback_;
        RobotArmControlAction::Result::SharedPtr latest_result_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTARMCONTROL_HPP_ 