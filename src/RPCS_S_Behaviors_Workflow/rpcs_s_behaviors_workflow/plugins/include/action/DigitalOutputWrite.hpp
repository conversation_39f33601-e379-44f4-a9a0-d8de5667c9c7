#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTWRITE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTWRITE_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/digital_output_write.hpp"
#include <chrono>
#include <string>
#include <vector>

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 行为树节点：写入数字输出
     * 
     * 该节点通过action接口批量写入指定地址的数字输出状态
     */
    class DigitalOutputWrite : public BT::StatefulActionNode
    {
    public:
        using DigitalOutputWriteAction = rpcs_interfaces_motor::action::DigitalOutputWrite;
        using GoalHandleDigitalOutputWrite = rclcpp_action::ClientGoalHandle<DigitalOutputWriteAction>;
        
        DigitalOutputWrite(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~DigitalOutputWrite() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口
                BT::InputPort<std::string>("strDeviceId", "robot1", "设备ID"),
                BT::InputPort<std::vector<int>>("intOutputAddresses", "要写入的输出地址列表"),
                BT::InputPort<std::vector<int>>("boolOutputValues", "要写入的输出值列表，必须与地址数量一致"),
                BT::InputPort<bool>("boolVerifyWrite", true, "是否验证写入结果"),
                BT::InputPort<bool>("boolBatchOperation", false, "是否使用批量操作模式"),
                BT::InputPort<int>("intTimeoutMs", 5000, "超时时间(毫秒)"),
                
                // 输出端口
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::vector<int>>("boolFinalValues", "最终的输出值列表"),
                BT::OutputPort<std::vector<int>>("intOutputAddresses", "对应的地址列表"),
                BT::OutputPort<std::vector<int>>("boolWriteSuccesses", "每个地址的写入是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createActionClient(const std::string& device_id);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<DigitalOutputWriteAction>::SharedPtr action_client_;
        
        // Goal处理相关变量
        bool goal_sent_;
        bool goal_completed_;
        GoalHandleDigitalOutputWrite::SharedPtr goal_handle_;
        std::shared_future<GoalHandleDigitalOutputWrite::SharedPtr> goal_handle_future_;
        std::shared_ptr<typename DigitalOutputWriteAction::Result> result_;
        std::shared_ptr<DigitalOutputWriteAction::Goal> goal_msg_;

        // 成员变量
        std::string device_id_;
        std::vector<int> output_addresses_;
        std::vector<int> output_values_;
        bool verify_write_;
        bool batch_operation_;
        int timeout_ms_;
        std::chrono::steady_clock::time_point start_time_;
    };
} // namespace rpcs_s_behaviors_workflow
#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTWRITE_HPP_ 