#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALINPUTREAD_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALINPUTREAD_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/digital_input_read.hpp"
#include <chrono>
#include <string>
#include <vector>
#include <thread>
#include <future>

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 行为树节点：读取数字输入
     * 
     * 该节点通过action接口批量读取指定地址的数字输入状态
     */
    class DigitalInputRead : public BT::StatefulActionNode
    {
    public:
        using DigitalInputReadAction = rpcs_interfaces_motor::action::DigitalInputRead;
        using GoalHandleDigitalInputRead = rclcpp_action::ClientGoalHandle<DigitalInputReadAction>;
        
        DigitalInputRead(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~DigitalInputRead() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口
                BT::InputPort<std::string>("strDeviceId", "robot1", "设备ID"),
                // 只支持多个地址的输入方式
                BT::InputPort<std::vector<int>>("intInputAddresses", "要读取的输入地址列表"),
                // 只支持多个期望值的输入方式
                BT::InputPort<std::vector<int>>("boolExpectedValues", "期望的输入值列表，与地址列表一一对应"),
                BT::InputPort<bool>("boolContinuousRead", false, "是否连续读取模式"),
                BT::InputPort<double>("doubleReadInterval", 0.1, "连续读取间隔(秒)"),
                BT::InputPort<double>("doubleDuration", 0.0, "连续读取持续时间(秒)"),
                BT::InputPort<int>("intTimeoutMs", 5000, "超时时间(毫秒)"),
                BT::InputPort<std::string>("strBlackboardKey", "", "黑板变量名，用于存储匹配状态"),

                // 输出端口
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<bool>("boolCurrentValue", "单个地址的当前值"),
                BT::OutputPort<std::vector<int>>("boolCurrentValues", "当前读取到的输入值列表"),
                BT::OutputPort<std::vector<int>>("intInputAddresses", "对应的地址列表"),
                BT::OutputPort<std::vector<int>>("boolValueMatched", "每个地址的值是否匹配期望值"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createActionClient(const std::string& device_id);
        
        // ROS节点
        std::shared_ptr<rclcpp::Node> node_;
        
        // Action客户端
        rclcpp_action::Client<DigitalInputReadAction>::SharedPtr action_client_;
        
        // Goal处理相关变量
        GoalHandleDigitalInputRead::SharedPtr goal_handle_;
        std::shared_future<GoalHandleDigitalInputRead::SharedPtr> goal_future_;
        std::shared_future<GoalHandleDigitalInputRead::WrappedResult> result_future_;
        std::shared_ptr<typename DigitalInputReadAction::Result> result_;
        std::shared_ptr<const typename DigitalInputReadAction::Feedback> feedback_;

        // 成员变量
        std::string device_id_;
        std::vector<int> input_addresses_;
        std::vector<int> expected_values_;
        std::vector<int> current_values_;
        std::vector<int> value_matched_;
        bool continuous_read_;
        double read_interval_;
        double duration_;
        int timeout_ms_;
        std::string blackboard_key_;
        std::chrono::steady_clock::time_point start_time_;
        bool goal_sent_;
        bool goal_completed_;
        bool should_cancel_ = false;  // 标记是否需要取消请求
    };
} // namespace rpcs_s_behaviors_workflow
#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALINPUTREAD_HPP_ 