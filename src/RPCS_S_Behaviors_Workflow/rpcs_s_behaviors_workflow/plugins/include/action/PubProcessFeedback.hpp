#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPROCESSFEEDBACK_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPROCESSFEEDBACK_HPP_

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/bt_factory.h"
#include "rclcpp/rclcpp.hpp"

namespace rpcs_s_behaviors_workflow
{
    class PubProcessFeedback : public BT::SyncActionNode
    {
    public:
        PubProcessFeedback(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::InputPort<std::string>("strProcessStep", "当前执行的工艺步骤名称"),
                BT::InputPort<std::string>("strStatus", "当前状态 (RUNNING, SUCCESS, FAILURE)"),
                BT::InputPort<double>("doubleProgress", "工艺执行进度百分比 (0.0-100.0)"),
                BT::InputPort<std::string>("strMessage", "状态描述信息"),
                BT::InputPort<std::string>("strOperation", "当前操作描述")
            };
        }

        BT::NodeStatus tick() override;
    };
} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPROCESSFEEDBACK_HPP_ 