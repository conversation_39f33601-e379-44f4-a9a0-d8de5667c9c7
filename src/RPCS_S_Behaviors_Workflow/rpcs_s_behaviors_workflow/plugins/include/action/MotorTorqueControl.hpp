#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORTORQUECONTROL_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORTORQUECONTROL_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/torque_control.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MotorTorqueControl : public BT::StatefulActionNode
    {
    public:
        MotorTorqueControl(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
                BT::InputPort<std::string>("strMotor<PERSON>rand", "Kinco", "电机品牌名称(Kinco, UMot等)"),
                BT::InputPort<int>("intMotorId", "电机ID编号"),
                
                // 转矩控制参数
                BT::InputPort<double>("doubleTargetTorque", "目标转矩(Nm)"),
                BT::InputPort<double>("doubleVelocityLimit", 30.0, "最大速度限制(mm/s)"),
                BT::InputPort<double>("doubleTorqueSlope", 1.0, "转矩变化斜率(Nm/s)"),
                BT::InputPort<double>("doubleDuration", 0.0, "运行时间(s)，0表示持续运行"),
                
                // 安全限制
                BT::InputPort<bool>("boolUsePositionLimits", false, "是否使用位置限制"),
                BT::InputPort<double>("doubleMinPosition", -1000.0, "最小位置限制(mm)"),
                BT::InputPort<double>("doubleMaxPosition", 1000.0, "最大位置限制(mm)"),
                BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(s)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<double>("doubleFinalTorque", "最终转矩(Nm)"),
                BT::OutputPort<double>("doubleFinalVelocity", "最终速度(mm/s)"),
                BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码"),
                
                // 实时状态
                BT::OutputPort<double>("doubleCurrentTorque", "当前转矩(Nm)"),
                BT::OutputPort<double>("doubleElapsedTime", "已运行时间(s)")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using TorqueControlAction = rpcs_interfaces_motor::action::TorqueControl;
        using GoalHandleTorqueControl = rclcpp_action::ClientGoalHandle<TorqueControlAction>;
        
        void createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id);
        std::string createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id);
        void handleActionError(int error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<TorqueControlAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleTorqueControl::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleTorqueControl::WrappedResult> result_future_;
        
        // 输入参数
        std::string device_id_;
        std::string motor_brand_;
        int motor_id_;
        double target_torque_;
        double velocity_limit_;
        double torque_slope_;
        double duration_;
        bool use_position_limits_;
        double min_position_;
        double max_position_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleTorqueControl::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORTORQUECONTROL_HPP_ 