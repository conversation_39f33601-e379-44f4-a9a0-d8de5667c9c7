#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORHOMING_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORHOMING_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/homing_control.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MotorHoming : public BT::StatefulActionNode
    {
    public:
        MotorHoming(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
                BT::InputPort<std::string>("strMotorBrand", "Kinco", "电机品牌名称(Kinco, UMot等)"),
                BT::InputPort<int>("intMotorId", "电机ID编号"),
                
                // 回零参数
                BT::InputPort<int>("intHomingMethod", 17, "寻找原点方法(1-35)"),
                BT::InputPort<float>("floatSpeedSwitch", 20.0, "开关搜索速度(rpm)"),
                BT::InputPort<float>("floatSpeedZero", 20.0, "零点搜索速度(rpm)"),
                BT::InputPort<int>("intHomeOffset", 0, "原点偏移量(mm)"),
                BT::InputPort<int>("intPositionWindow", 100, "位置窗口(inc)"),
                BT::InputPort<int>("intPositionWindowTime", 1000, "位置窗口时间(ms)"),
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(s)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "回零是否成功"),
                BT::OutputPort<float>("floatFinalPosition", "最终位置(mm)"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时状态
                BT::OutputPort<int>("intCurrentStatus", "当前状态字"),
                BT::OutputPort<std::string>("strStatusDescription", "状态描述"),
                BT::OutputPort<float>("floatCurrentPosition", "当前位置(mm)")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using HomingAction = rpcs_interfaces_motor::action::HomingControl;
        using GoalHandleHoming = rclcpp_action::ClientGoalHandle<HomingAction>;
        
        void createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id);
        std::string createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id);
        void handleActionError(int error_code, const std::string& error_message);
        std::string getStatusDescription(int status);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<HomingAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleHoming::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleHoming::WrappedResult> result_future_;
        
        // 输入参数
        std::string device_id_;
        std::string motor_brand_;
        int motor_id_;
        int homing_method_;
        float speed_switch_;
        float speed_zero_;
        int home_offset_;
        int position_window_;
        int position_window_time_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleHoming::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORHOMING_HPP_ 