#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__GETACTIONPARAMETERS_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__GETACTIONPARAMETERS_HPP_

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/bt_factory.h"
#include "rclcpp/rclcpp.hpp"

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 获取Action接口参数的节点
     * 
     * 该节点用于从黑板中读取Action接口传入的参数，并输出到指定的端口
     */
    class GetActionParameters : public BT::SyncActionNode
    {
    public:
        GetActionParameters(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口
                BT::InputPort<int>("intParameterIndex", "要获取的参数索引 (0-based)"),
                BT::InputPort<std::string>("strParameterKey", "要获取的特定参数键名"),
                BT::InputPort<std::string>("strExtendKey", "要获取的扩展信息键名"),
                BT::InputPort<int>("intExtendIndex", "要获取的扩展信息索引 (0-based)"),

                // 输出端口
                BT::OutputPort<std::string>("strParameterValue", "获取到的参数值"),
                BT::OutputPort<std::vector<std::string>>("strParameterList", "完整的参数列表"),
                BT::OutputPort<int>("intParameterCount", "参数总数"),
                BT::OutputPort<std::string>("strExtendValue", "获取到的扩展信息值"),
                BT::OutputPort<int>("intExtendCount", "扩展信息总数"),
                BT::OutputPort<std::string>("strExtendKeyOut", "获取到的扩展信息键名"),
                BT::OutputPort<std::string>("strRobotId", "机器人ID"),
                BT::OutputPort<std::string>("strProcessId", "工艺流程ID"),
                BT::OutputPort<std::string>("strProcessType", "工艺动作类型"),
                BT::OutputPort<int>("intTimeoutSeconds", "超时时间"),
                BT::OutputPort<bool>("boolPreemptCurrent", "是否抢占当前任务"),
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "输出消息")
            };
        }

        BT::NodeStatus tick() override;
    };
} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__GETACTIONPARAMETERS_HPP_
