#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORVELOCITYCONTROL_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORVELOCITYCONTROL_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/velocity_control.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MotorVelocityControl : public BT::StatefulActionNode
    {
    public:
        MotorVelocityControl(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
                BT::InputPort<std::string>("strMotor<PERSON>rand", "Kinco", "电机品牌名称(Kinco, UMot等)"),
                BT::InputPort<int>("intMotorId", "电机ID编号"),
                
                // 速度控制参数
                BT::InputPort<double>("doubleTargetVelocity", "目标速度(rpm)"),
                BT::InputPort<double>("doubleAcceleration", 100.0, "加速度(rps/s)"),
                BT::InputPort<double>("doubleDeceleration", 100.0, "减速度(rps/s)"),
                BT::InputPort<double>("doubleDuration", 0.0, "运行时间(s)，0表示持续运行"),
                BT::InputPort<double>("doubleTargetCurrentLimit", 20.0, "目标电流限制(A)，转换系数42.67"),
                
                // 安全限制
                BT::InputPort<bool>("boolUsePositionLimits", false, "是否使用位置限制"),
                BT::InputPort<double>("doubleMinPosition", -1000.0, "最小位置限制(mm)"),
                BT::InputPort<double>("doubleMaxPosition", 1000.0, "最大位置限制(mm)"),
                BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(s)"),
                
                // 停止控制
                BT::InputPort<bool>("boolStopSignal", false, "停止信号，当为true时立即停止"),
                BT::InputPort<std::string>("strStopSignalKey", "", "动态停止信号黑板变量名，如motor5_stop_signal"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<double>("doubleFinalVelocity", "最终速度(rpm)"),
                BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码"),
                
                // 实时状态
                BT::OutputPort<double>("doubleCurrentVelocity", "当前速度(rpm)"),
                BT::OutputPort<double>("doubleElapsedTime", "已运行时间(s)")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using VelocityControlAction = rpcs_interfaces_motor::action::VelocityControl;
        using GoalHandleVelocityControl = rclcpp_action::ClientGoalHandle<VelocityControlAction>;
        
        void createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id);
        std::string createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id);
        void handleActionError(int error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<VelocityControlAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleVelocityControl::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleVelocityControl::WrappedResult> result_future_;
        
        // 输入参数
        std::string device_id_;
        std::string motor_brand_;
        int motor_id_;
        double target_velocity_;
        double acceleration_;
        double deceleration_;
        double duration_;
        double target_current_limit_;
        bool use_position_limits_;
        double min_position_;
        double max_position_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleVelocityControl::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORVELOCITYCONTROL_HPP_ 