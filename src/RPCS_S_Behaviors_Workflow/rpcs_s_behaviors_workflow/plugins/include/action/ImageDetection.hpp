#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__IMAGEDETECTION_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__IMAGEDETECTION_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "vir_robot_interfaces/srv/image_detection.hpp"

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 视觉识别行为树节点
     * 
     * 用于调用视觉算法服务，获取目标物体的位置偏差值
     * 支持多种识别类型，如机械臂归零识别等
     */
    class ImageDetection : public BT::StatefulActionNode
    {
    public:
        ImageDetection(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~ImageDetection() = default;

        /**
         * @brief 提供端口列表
         */
        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口
                BT::InputPort<std::string>("strDetectType", "ROBOT_ALIGN", "识别类型(ROBOT_ALIGN=机械臂归零识别)"),
                BT::InputPort<std::string>("strCameraIp", "", "相机IP地址(可选，优先级最高)"),
                BT::InputPort<std::string>("strCameraGroup", "", "相机组名称(可选)"),
                BT::InputPort<std::string>("strNamespace", "/Robot1", "节点命名空间"),
                BT::InputPort<int>("intTimeoutMs", 10000, "超时时间(毫秒)"),
                
                // 输出端口 - 基于ImageDetectionResult
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 检测结果输出
                BT::OutputPort<std::string>("strResultCameraIp", "结果相机IP地址"),
                BT::OutputPort<float>("doubleResultX", "检测到的X坐标偏差"),
                BT::OutputPort<float>("doubleResultY", "检测到的Y坐标偏差"), 
                BT::OutputPort<float>("doubleResultRz", "检测到的旋转角度偏差"),
                
                // 组合结果输出（便于使用）
                BT::OutputPort<std::string>("strDetectionSummary", "检测结果摘要")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        /**
         * @brief 创建服务客户端
         */
        void createServiceClient(const std::string& namespace_prefix);
        
        /**
         * @brief 格式化检测结果摘要
         */
        std::string formatDetectionSummary(const vir_robot_interfaces::msg::ImageDetectionResult& result);

        // ROS2相关成员
        rclcpp::Node::SharedPtr node_;
        rclcpp::Client<vir_robot_interfaces::srv::ImageDetection>::SharedPtr client_;
        rclcpp::Client<vir_robot_interfaces::srv::ImageDetection>::SharedFuture response_future_;
        
        // 请求参数
        std::string detect_type_;
        std::string camera_ip_;
        std::string camera_group_;
        int timeout_ms_;
        
        // 执行状态
        std::chrono::steady_clock::time_point start_time_;
        bool request_sent_;
    };

}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__IMAGEDETECTION_HPP_ 