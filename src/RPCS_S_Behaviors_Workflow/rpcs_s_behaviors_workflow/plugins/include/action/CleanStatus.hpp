#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CLEANSTATUS_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CLEANSTATUS_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include <iostream>

namespace rpcs_s_behaviors_workflow
{
    class CleanStatus : public BT::StatefulActionNode
    {
    public:
        CleanStatus(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::OutputPort<std::string>("strAllstatus")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        std::chrono::steady_clock::time_point start_time;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CLEANSTATUS_HPP_
