#pragma once
#include <string>
#include <vector>
#include <sstream>
#include <iostream>
#include "behaviortree_cpp/action_node.h"

namespace rpcs_s_behaviors_workflow
{
/**
 * @brief 新增图像检测翻转处理节点，可以翻转x、y、rz的正负号，最后转为float
          将图像检测结果转换为float，并且可以根据操作类型进行取反
 */
class ImageDetectionConversion : public BT::SyncActionNode
{
public:
    ImageDetectionConversion(const std::string& name, const BT::NodeConfiguration& config)
    : BT::SyncActionNode(name, config)
    {
    }

    static BT::PortsList providedPorts()
    {
        return {
            // 输入端口
            BT::InputPort<std::string>("strType", "操作类型，格式：\"-1,-1,0\""),
            BT::InputPort<double>("doubleX", 3.402823e+38, "x坐标(double)"),
            BT::InputPort<double>("doubleY", 3.402823e+38, "y坐标(double)"),
            BT::InputPort<double>("doubleRZ", 3.402823e+38, "旋转角度(double)"),
            BT::InputPort<float>("floatX", 3.402823e+38f, "x坐标(float)"),
            BT::InputPort<float>("floatY", 3.402823e+38f, "y坐标(float)"),
            BT::InputPort<float>("floatRZ", 3.402823e+38f, "旋转角度(float)"),
            BT::InputPort<double>("compensateX", 3.402823e+38, "x坐标(double)"),
            BT::InputPort<double>("compensateY", 3.402823e+38, "y坐标(double)"),

            // 输出端口
            BT::OutputPort<float>("image_detection_x", "处理后的X坐标(float)"),
            BT::OutputPort<float>("image_detection_y", "处理后的Y坐标(float)"),
            BT::OutputPort<float>("image_detection_rz", "处理后的旋转角度(float)"),
            BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
            BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
            BT::OutputPort<std::string>("strOutputResponse", "完整响应数据")
        };
    }

    BT::NodeStatus tick() override
    {
        // 获取操作类型
        std::string strType;
        if (!getInput<std::string>("strType", strType)) {
            return reportError("操作类型strType为必传参数");
        }

        // 解析操作类型
        std::vector<int> operations;
        std::istringstream ss(strType);
        std::string token;

        while (std::getline(ss, token, ',')) {
            try {
                int op = std::stoi(token);
                if (op != -1 && op != 0) {
                    return reportError("操作类型只能包含-1或0，当前值: " + token);
                }
                operations.push_back(op);
            } catch (...) {
                return reportError("无效的操作类型格式: " + strType);
            }
        }

        if (operations.size() != 3) {
            return reportError("操作类型需要3个参数，当前数量: " + std::to_string(operations.size()));
        }

        // 获取坐标值（优先使用double类型输入）
        double x=0.0, y=0.0, rz=0.0, doubleCompensateX=0.0, doubleCompensateY=0.0, double_big=3.4e+38;
        float fx=0.0f, fy=0.0f, frz=0.0f, compensateX=0.0f, compensateY=0.0f, float_big=3.4e+38f;
        bool isSetCompensateX=false, isSetCompensateY=false;
        getInput<float>("floatX", fx);
        getInput<double>("doubleX", x);
        getInput<float>("floatY", fy);
        getInput<double>("doubleY", y);
        getInput<float>("floatRZ", frz);
        getInput<double>("doubleRZ", rz);
        getInput<double>("compensateX", doubleCompensateX);
        getInput<double>("compensateY", doubleCompensateY);

        if (doubleCompensateX < double_big) {
            isSetCompensateX=true;
            compensateX=static_cast<float>(doubleCompensateX);
        } else {
            compensateX=0.0f;
        }

        if (doubleCompensateY < double_big) {
            isSetCompensateY=true;
            compensateY=static_cast<float>(doubleCompensateY);
        } else {
            compensateY=0.0f;
        }

        if (fx>float_big && x>double_big) {
            return reportError("缺少X坐标输入");
        }

        if (fy>float_big && y>double_big) {
            return reportError("缺少Y坐标输入");
        }

       if (frz>float_big && rz>double_big) {
            return reportError("缺少RZ坐标输入");
        }

        if ((fx<float_big && x<double_big) || (fy<float_big && y<double_big) || (frz<float_big && rz<double_big))
        {
            return reportError("float和double不要同时输入");
        }

        if (x<double_big) {
            fx = static_cast<float>(x);
        }

        if (y<double_big) {
            fy = static_cast<float>(y);
        }

        if (rz<double_big) {
            frz = static_cast<float>(rz);
        }

        if (fx>2000 || fx<-2000 || fy>2000 || fy<-2000)
        {
            return reportError("坐标数据明显超出正常范围");
        }

        if ((isSetCompensateX && (compensateX>4000 || compensateX<-4000)) || (isSetCompensateY && (compensateY>4000 || compensateY<-4000)))
        {
            return reportError("补偿坐标数据明显超出正常范围");
        }

        // 打印输入数据
        std::ostringstream request;
        request << "输入数据: X=" << fx  << ", Y=" << fy  << ", RZ=" << frz << ",  compensateX=" << compensateX << ", compensateY=" << compensateY << " | 操作类型: " << strType;
        std::cout << "ImageDetectionConversion: " << request.str() << std::endl;

        // 应用转换操作
        float result_x = turnoverOperation(fx, operations[0]);
        if (isSetCompensateX) {
            result_x += compensateX;
            std::cout << "result_x += compensateX;" << std::endl;
        }
        float result_y = turnoverOperation(fy, operations[1]);
        if (isSetCompensateY) {
            result_y += compensateY;
            std::cout << "result_y += compensateY;" << std::endl;
        }
        float result_rz = turnoverOperation(frz, operations[2]);

        // 设置输出
        setOutput("image_detection_x", result_x);
        setOutput("image_detection_y", result_y);
        setOutput("image_detection_rz", result_rz);
        setOutput("boolOutputSuccess", true);
        setOutput("strOutputMessage", "转换成功完成");

        // 构建完整响应
        std::ostringstream response;
        response << "转换结果: X=" << result_x  << ", Y=" << result_y  << ", RZ=" << result_rz << ",  compensateX=" << compensateX << ", compensateY=" << compensateY << " | 操作类型: " << strType;
        setOutput("strOutputResponse", response.str());

        std::cout << "ImageDetectionConversion: " << response.str() << std::endl;
        return BT::NodeStatus::SUCCESS;
    }

private:
    // 应用操作：-1表示取反，0表示保持原样
    float turnoverOperation(float value, int operation) const
    {
        return (operation == -1) ? -value : value;
    }

    // 错误处理
    BT::NodeStatus reportError(const std::string& message)
    {
        std::cerr << "ImageDetectionConversion错误: " << message << std::endl;
        setOutput("boolOutputSuccess", false);
        setOutput("strOutputMessage", "错误: " + message);
        setOutput("strOutputResponse", "错误: " + message);
        return BT::NodeStatus::FAILURE;
    }
};

}    // namespace rpcs_s_behaviors_workflow


