#pragma once
/*****************************************************************************
 * @copyright   Copyright (c) 2025.7 软件组
 * @file        ImageDetectionToZero.hpp
 * @brief
 * @details     图像检测-用于机械臂回零工艺
 *
 * <AUTHOR>
 * @date        2025/7/19
 * @version
 * @attention
 * @remark      调用虚拟机器人的图像检测服务，期间会相机拍照，服务正常时，返回相机拍照的x、y、rz的值，并通过行为树返回。
                之后行为树的其他节点会使用这个x、y、rz的值，进行机械臂回零。
                协议地址:
                RPCS_M_Controllers_interfaces/src/vir_robot_interfaces/srv/ImageDetection.srv
                RPCS_M_Controllers_interfaces/src/vir_robot_interfaces/msg/ImageDetectionResult.msg
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "vir_robot_interfaces/srv/image_detection.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <memory>

namespace rpcs_s_behaviors_workflow
{
    // 图像检测-用于机械臂回零工艺
    class ImageDetectionToZero : public BT::StatefulActionNode
    {
    public:
        ImageDetectionToZero(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::InputPort<std::string>("detect_type", "ROBOT_ALIGN", "检测类型"),
                BT::InputPort<std::string>("camera_ip", "", "相机IP地址"),
                BT::InputPort<std::string>("camera_group", "", "相机组名称"),
                BT::InputPort<int>("intTimeoutMs", 60000, "超时时间(毫秒)"),
                BT::OutputPort<float>("image_detection_x", "X坐标"),
                BT::OutputPort<float>("image_detection_y", "Y坐标"),
                BT::OutputPort<float>("image_detection_rz", "旋转角度"),
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<std::string>("strOutputResponse", "完整响应数据")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createServiceClient(const std::string& namespace_prefix);

        // ROS节点
        rclcpp::Node::SharedPtr node_;
        // 服务客户端
        rclcpp::Client<vir_robot_interfaces::srv::ImageDetection>::SharedPtr client_;
        // 服务请求的响应future
        rclcpp::Client<vir_robot_interfaces::srv::ImageDetection>::SharedFuture response_future_;

        // 超时时间（毫秒）
        int timeout_ms_;
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        // 识别类型 ROBOT_ALIGN 机械臂归零识别 
        std::string detect_type_;
        // 相机ip（可选） 优先级最高
        std::string camera_ip_;
        // 相机组名称   使用robot1、robot2、robot3区分第几台机器人
        std::string camera_group_;
    };
}  // namespace rpcs_s_behaviors_workflow


