#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DEPTHFUSION_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DEPTHFUSION_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "algorithm_interface/srv/depth_fusion.hpp"
#include "../base/AlgorithmNodeWithCamera.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <vector>

namespace rpcs_s_behaviors_workflow
{
    class DepthFusion : public AlgorithmNodeWithCamera
    {
    public:
        DepthFusion(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~DepthFusion() = default;

        // 重写基类的状态管理方法，跳过相机初始化
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 相机配置（DepthFusion不需要相机，但为了统一性保留）
                BT::InputPort<std::string>("strCameraNamespaces", "", "相机命名空间，多个相机用逗号分隔（DepthFusion不需要相机）"),
                
                // 算法参数
                BT::InputPort<std::string>("strInputDirectory", "", "输入图像目录路径"),
                BT::InputPort<std::string>("strOutputPath", "", "输出图像文件路径"),
                BT::InputPort<std::string>("strParamOverrides", "", "参数覆盖，JSON格式"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "状态消息"),
                BT::OutputPort<std::string>("strOutputPath", "融合后图像的保存路径")
            };
        }

        // AlgorithmNodeWithCamera抽象方法实现
        std::string getAlgorithmServiceName() override;
        std::vector<CameraConfig> getCameraConfigs() override;
        std::shared_ptr<void> prepareAlgorithmRequest(
            const std::vector<sensor_msgs::msg::Image>& images) override;
        std::shared_future<std::shared_ptr<void>> sendAlgorithmRequest(
            std::shared_ptr<void> request) override;
        bool handleAlgorithmResponse(std::shared_ptr<void> response) override;
        void setErrorOutput(const std::string& error_message) override;

    private:
        // 工具方法
        std::string convertKeyValuePairsToJson(const std::string& key_value_pairs);
        using DepthFusionService = algorithm_interface::srv::DepthFusion;
        
        // 算法参数
        std::string input_directory_;
        std::string output_path_;
        std::string param_overrides_;
        
        // 算法服务客户端
        rclcpp::Client<DepthFusionService>::SharedPtr client_;
        
        // 算法执行状态
        enum class ExecutionPhase {
            CALLING_ALGORITHM,   // 正在调用算法
            COMPLETED           // 执行完成
        };
        ExecutionPhase current_phase_;
        std::shared_future<std::shared_ptr<void>> algorithm_future_;
        std::chrono::steady_clock::time_point start_time_;
        double timeout_;
        
        // 检查算法响应的方法
        bool checkAlgorithmResponse();
        
        // 算法执行结果跟踪
        bool algorithm_success_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DEPTHFUSION_HPP_