#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CORRECTIONPOSITION_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CORRECTIONPOSITION_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/correction_position_v1.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <random>
#include <sstream>

namespace rpcs_s_behaviors_workflow
{
    class CorrectionPosition : public BT::StatefulActionNode
    {
    public:
        CorrectionPosition(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~CorrectionPosition() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                BT::InputPort<std::string>("strCommandType", "移动类型: ABS/RELA"),
                BT::InputPort<double>("doubleAbsX", "X轴目标位置"),
                BT::InputPort<double>("doubleAbsY", "Y轴目标位置"),
                BT::InputPort<double>("doubleAbsZ", "Z轴目标位置"),
                BT::InputPort<double>("doubleAbsR", "R轴目标位置"),
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using CorrectionPositionAction = vir_robot_interfaces::action::CorrectionPositionV1;
        using GoalHandleCorrectionPosition = rclcpp_action::ClientGoalHandle<CorrectionPositionAction>;
        void createActionClient();
        std::string createActionName();
        std::string generateRequestId();
        std::string generateRandomString(int length);  // 生成指定长度的随机十六进制字符串
        void handleActionError(const std::string& error_code, const std::string& error_message);
        rclcpp::Node::SharedPtr node_;
        rclcpp_action::Client<CorrectionPositionAction>::SharedPtr client_;
        std::shared_future<GoalHandleCorrectionPosition::SharedPtr> goal_future_;
        std::shared_future<GoalHandleCorrectionPosition::WrappedResult> result_future_;
        std::string command_type_;
        std::string request_id_;  // 请求ID，用于验证请求和响应的一致性
        double abs_x_, abs_y_, abs_z_, abs_r_;
        double timeout_;
        std::chrono::steady_clock::time_point start_time_;
        GoalHandleCorrectionPosition::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CORRECTIONPOSITION_HPP_ls
