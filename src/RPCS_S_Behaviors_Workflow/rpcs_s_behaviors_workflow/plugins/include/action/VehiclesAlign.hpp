#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__VEHICLESALIGN_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__VEHICLESALIGN_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "algorithm_interface/srv/vehicles_align.hpp"
#include "sensor_msgs/msg/image.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <future>

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 载板贴合算法行为树节点
     * 
     * 用于调用载板贴合算法服务，通过输入两个图像路径进行贴合对齐计算
     * 不需要相机拍照，直接从指定路径加载图像
     */
    class VehiclesAlign : public BT::StatefulActionNode
    {
    public:
        VehiclesAlign(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~VehiclesAlign() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 图像路径
                BT::InputPort<std::string>("strImagePathA", "第一张图像的文件路径"),
                BT::InputPort<std::string>("strImagePathB", "第二张图像的文件路径"),
                
                // 算法参数
                BT::InputPort<std::string>("strParamOverrides", "{}", "参数覆盖(JSON格式)"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "状态消息"),
                BT::OutputPort<bool>("boolIsFinish", "是否完成"),
                BT::OutputPort<std::string>("strMoveCommands", "移动命令数组(JSON格式)"),
                BT::OutputPort<int>("intMoveCommandsCount", "移动命令数量"),
                
                // === 解析后的移动值输出端口 ===
                BT::OutputPort<double>("doubleOutputX", "X轴移动值"),
                BT::OutputPort<double>("doubleOutputY", "Y轴移动值"),
                BT::OutputPort<double>("doubleOutputR", "R轴移动值"),
                BT::OutputPort<bool>("boolOutputX", "X轴移动使能"),
                BT::OutputPort<bool>("boolOutputY", "Y轴移动使能"),
                BT::OutputPort<bool>("boolOutputR", "R轴移动使能")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using VehiclesAlignService = algorithm_interface::srv::VehiclesAlign;
        
        /**
         * @brief 创建ROS节点和服务客户端
         */
        bool initializeROS();
        
        /**
         * @brief 从文件路径加载图像为ROS消息
         * @param image_path 图像文件路径
         * @return 图像消息，失败返回nullptr
         */
        sensor_msgs::msg::Image::SharedPtr loadImageFromPath(const std::string& image_path);
        
        /**
         * @brief 发送算法服务请求
         * @return 是否成功发送请求
         */
        bool sendAlgorithmRequest();
        
        /**
         * @brief 处理算法服务响应
         * @param response 服务响应
         * @return 处理是否成功
         */
        bool handleAlgorithmResponse(VehiclesAlignService::Response::SharedPtr response);
        
        /**
         * @brief 设置错误输出
         * @param error_message 错误消息
         */
        void setErrorOutput(const std::string& error_message);
        
        /**
         * @brief 序列化移动命令为JSON字符串
         * @param commands 移动命令数组
         * @return JSON字符串
         */
        std::string serializeMoveCommands(const std::vector<algorithm_interface::msg::MoveCommand>& commands);
        std::string convertKeyValuePairsToJson(const std::string& key_value_pairs);
        
        // ROS2相关成员
        rclcpp::Node::SharedPtr node_;
        rclcpp::Client<VehiclesAlignService>::SharedPtr client_;
        rclcpp::Client<VehiclesAlignService>::SharedFuture response_future_;
        
        // 输入参数
        std::string image_path_a_;
        std::string image_path_b_;
        std::string param_overrides_;
        double timeout_;
        
        // 执行状态
        std::chrono::steady_clock::time_point start_time_;
        bool request_sent_;
    };

}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__VEHICLESALIGN_HPP_ 