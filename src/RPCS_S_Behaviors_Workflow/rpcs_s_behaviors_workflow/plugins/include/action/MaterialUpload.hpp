#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALUPLOAD_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALUPLOAD_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/material_upload.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MaterialUpload : public BT::StatefulActionNode
    {
    public:
        MaterialUpload(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~MaterialUpload() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 上料类型参数
                BT::InputPort<std::string>("strUploadType", "上料类型：LIGHT_BOARD-玻璃板上料，FPC-FPC转接线上料，PRODUCT_TRAY-成品Tray上料，EMPTY_FPC_CONVERTER_TRAY-空FPC转换器Tray下料"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时反馈
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using MaterialUploadAction = vir_robot_interfaces::action::MaterialUpload;
        using GoalHandleMaterialUpload = rclcpp_action::ClientGoalHandle<MaterialUploadAction>;
        
        void createActionClient();
        std::string createActionName();
        void handleActionError(const std::string& error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<MaterialUploadAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleMaterialUpload::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleMaterialUpload::WrappedResult> result_future_;
        
        // 输入参数
        std::string upload_type_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleMaterialUpload::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALUPLOAD_HPP_ 