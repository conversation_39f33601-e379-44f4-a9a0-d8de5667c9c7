#pragma once
#include <string>
#include <chrono>
#include <thread>
#include <iostream>
#include <unordered_set>
#include <string>
#include "behaviortree_cpp/action_node.h"

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief FPC转接线，到了处理第几个了
 */
class FPCAdapterNumber : public BT::SyncActionNode
{
public:
    /**
     * @brief 构造函数
     * @param name 节点名称
     * @param config 节点配置
     */
    FPCAdapterNumber(const std::string& name, const BT::NodeConfiguration& config)
    : BT::SyncActionNode(name, config)
    {
    }

    /**
     * @brief 获取节点配置
     * @return 节点参数配置
     */
    static BT::PortsList providedPorts()
    {
        return {
            // 操作类型 BOARD、TRAY、BOARD_ZERO、TRAY_ZERO 全部大写
			// TRAY，需要从tray盘获取时使用
            // BOARD，需要粘贴到载板时使用
			// BOARD_ZERO、TRAY_ZERO 只有程序故障，需要手工设置时使用，如果程序没有报错，第n个之后自动变为1
            BT::InputPort<std::string>("strType", "", "操作类型"),
            // 返回参数: 当前需要操作的FPC转接线是第几个
            // TRAY，返回需要从tray盘获取时，到第几个了
            // BOARD，返回需要粘贴到载板时，到第几个了
            // TRAY_ZERO，拿了一个新的tray盘，从第1个开始，只有程序故障，需要手工设置时使用，如果程序没有报错，第8个之后自动变为1
            // BOARD_ZERO，拿了一个新的载板，从第1个开始，只有程序故障，需要手工设置时使用，如果程序没有报错，第4个之后自动变为1
            BT::OutputPort<int>("intFPCAdapterNumber", "获取当前需要操作的FPC转接线是第几个")                 
        };
    }

    /**
     * @brief FPC转接线，到了处理第几个了
     * @return 当前需要处理的是第几个
     * @remark 读取静态变量，没有耗时操作
     */
    BT::NodeStatus tick() override
    {
        static int tray_number = 1;
        static int board_number = 1;
        static std::unordered_set<std::string> typeSet = {"TRAY", "BOARD", "TRAY_ZERO", "BOARD_ZERO"};
        std::string strType;
        // 获取输入参数
        if (!getInput<std::string>("strType", strType)) {
            std::cerr << "FPCAdapterNumber strType为必传参数" << std::endl;
            throw BT::RuntimeError("FPCAdapterNumber strType为必传参数");
        }

        if (typeSet.find(strType) == typeSet.end()) {
            std::cerr << "FPCAdapterNumber strType参数只能为BOARD、TRAY、BOARD_ZERO、TRAY_ZERO，现在是[" << strType << "]，不匹配" << std::endl;
            throw BT::RuntimeError("FPCAdapterNumber strType参数只能为BOARD、TRAY、BOARD_ZERO、TRAY_ZERO");
        }

        if ("TRAY" == strType) {
            setOutput<int>("intFPCAdapterNumber", tray_number);
            std::cout << "FPCAdapterNumber intFPCAdapterNumber_TRAY = [" << tray_number << "]" << std::endl;
            if (8 == tray_number) {
                tray_number = 1;
            } else {
                tray_number++;
            }
            return BT::NodeStatus::SUCCESS;
        }

        if ("BOARD" == strType) {
            setOutput<int>("intFPCAdapterNumber", board_number);
            std::cout << "FPCAdapterNumber intFPCAdapterNumber_BOARD = [" << board_number << "]" << std::endl;
            if (4 == board_number) {
                board_number = 1;
            } else {
                board_number++;
            }
            return BT::NodeStatus::SUCCESS;
        }

        if ("TRAY_ZERO" == strType) {
            tray_number = 1;
            std::cout << "FPCAdapterNumber set intFPCAdapterNumber_TRAY = [" << tray_number << "]" << std::endl;
            return BT::NodeStatus::SUCCESS;
        }

        if ("BOARD_ZERO" == strType) {
            board_number = 1;
            std::cout << "FPCAdapterNumber set intFPCAdapterNumber_BOARD = [" << board_number << "]" << std::endl;
            return BT::NodeStatus::SUCCESS;
        }

        return BT::NodeStatus::SUCCESS;
    }
};

}    // namespace rpcs_s_behaviors_workflow


