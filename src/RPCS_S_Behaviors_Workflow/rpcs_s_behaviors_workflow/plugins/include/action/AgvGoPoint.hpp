#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__AGVGOPOINT_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__AGVGOPOINT_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rpcs_s_interfaces_agv/srv/agv_go_point.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class AgvGoPoint : public BT::StatefulActionNode
    {
    public:
        AgvGoPoint(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::InputPort<std::string>("strGoPointName", "目标点名称"),
                BT::InputPort<std::string>("strNamespace", "/Robot1", "节点命名空间"),
                BT::InputPort<int>("intTimeoutMs", 30000, "超时时间(毫秒)"),
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<std::string>("strOutputResponse", "完整响应数据")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createServiceClient(const std::string& namespace_prefix);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // 服务客户端
        rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedPtr client_;
        
        // 服务请求的响应future
        rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedFuture response_future_;
        
        // 目标点名称
        std::string go_point_name_;
        
        // 超时时间（毫秒）
        int timeout_ms_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__AGVGOPOINT_HPP_ 