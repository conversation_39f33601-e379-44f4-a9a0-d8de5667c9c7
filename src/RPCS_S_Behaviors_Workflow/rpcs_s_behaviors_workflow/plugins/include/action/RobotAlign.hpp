#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTALIGN_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTALIGN_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "algorithm_interface/srv/robot_align.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <future>

namespace rpcs_s_behaviors_workflow
{
    class RobotAlign : public BT::StatefulActionNode
    {
    public:
        RobotAlign(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~RobotAlign() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 算法参数
                BT::InputPort<std::string>("strParamOverrides", "", "参数覆盖，JSON格式"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "状态消息"),
                BT::OutputPort<double>("doubleX", "x坐标"),
                BT::OutputPort<double>("doubleY", "y坐标"),
                BT::OutputPort<double>("doubleRZ", "旋转角度")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using RobotAlignService = algorithm_interface::srv::RobotAlign;
        
        // 工具方法
        void setErrorOutput(const std::string& error_message);
        bool prepareAlgorithmRequest();
        bool sendAlgorithmRequest();
        bool handleAlgorithmResponse();
        std::string convertKeyValuePairsToJson(const std::string& key_value_pairs);
        
        // ROS节点和服务客户端
        rclcpp::Node::SharedPtr node_;
        rclcpp::Client<RobotAlignService>::SharedPtr client_;
        std::shared_future<RobotAlignService::Response::SharedPtr> algorithm_future_;
        
        // 输入参数
        std::string param_overrides_;
        double timeout_;
        
        // 运行状态
        std::chrono::steady_clock::time_point start_time_;
        bool request_sent_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__ROBOTALIGN_HPP_ 