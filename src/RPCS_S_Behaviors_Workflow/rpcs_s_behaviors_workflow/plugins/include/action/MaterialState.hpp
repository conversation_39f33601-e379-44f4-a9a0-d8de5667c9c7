#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALSTATE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALSTATE_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/material_state.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MaterialState : public BT::StatefulActionNode
    {
    public:
        MaterialState(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~MaterialState() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 物料类型参数
                BT::InputPort<std::string>("strMaterialType", "物料类型：LIGHT_BOARD-玻璃板到位，FPC-FPC托盘到位，EMPTY_FPC-空FPC托盘到位，NAIL-钉子到位"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时反馈
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using MaterialStateAction = vir_robot_interfaces::action::MaterialState;
        using GoalHandleMaterialState = rclcpp_action::ClientGoalHandle<MaterialStateAction>;
        
        void createActionClient();
        std::string createActionName();
        void handleActionError(const std::string& error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<MaterialStateAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleMaterialState::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleMaterialState::WrappedResult> result_future_;
        
        // 输入参数
        std::string material_type_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleMaterialState::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MATERIALSTATE_HPP_ 