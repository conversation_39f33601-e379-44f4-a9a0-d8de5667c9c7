#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CONVEYOR_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CONVEYOR_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/conveyor.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class Conveyor : public BT::StatefulActionNode
    {
    public:
        Conveyor(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~Conveyor() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时反馈
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using ConveyorAction = vir_robot_interfaces::action::Conveyor;
        using GoalHandleConveyor = rclcpp_action::ClientGoalHandle<ConveyorAction>;
        
        void createActionClient();
        std::string createActionName();
        void handleActionError(const std::string& error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<ConveyorAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleConveyor::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleConveyor::WrappedResult> result_future_;
        
        // 输入参数
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleConveyor::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CONVEYOR_HPP_ 