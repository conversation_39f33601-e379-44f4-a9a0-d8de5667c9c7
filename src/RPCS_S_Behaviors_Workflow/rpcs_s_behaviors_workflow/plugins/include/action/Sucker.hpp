#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SUCKER_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SUCKER_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "vir_robot_interfaces/action/sucker.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class Sucker : public BT::StatefulActionNode
    {
    public:
        Sucker(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~Sucker() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 吸盘序号列表 (格式: "1,2,3" 或单个吸盘 "1")
                BT::InputPort<std::string>("strSuckerIds", "吸盘序号列表，逗号分隔"),
                BT::InputPort<int>("intSuckerId", "吸盘序号"),
                // 控制类型 (true=开, false=关)
                BT::InputPort<bool>("boolSuckerState", "吸盘控制状态，true=开启，false=关闭"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strErrorCode", "错误代码"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                
                // 实时反馈
                BT::OutputPort<std::string>("strPlcFeedback", "PLC反馈信息")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using SuckerAction = vir_robot_interfaces::action::Sucker;
        using GoalHandleSucker = rclcpp_action::ClientGoalHandle<SuckerAction>;
        
        void createActionClient();
        std::string createActionName();
        void handleActionError(const std::string& error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<SuckerAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandleSucker::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandleSucker::WrappedResult> result_future_;
        
        // 输入参数
        std::string sucker_ids_;
        int sucker_id_;
        bool sucker_state_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandleSucker::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SUCKER_HPP_ 