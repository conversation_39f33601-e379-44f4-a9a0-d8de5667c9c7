#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SET_PROGRESS_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SET_PROGRESS_HPP_

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/bt_factory.h"

namespace rpcs_s_behaviors_workflow
{
    class SetProgress : public BT::SyncActionNode
    {
    public:
        SetProgress(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::InputPort<double>("progress", "Progress value (0-100)")
            };
        }

        BT::NodeStatus tick() override;
    };
} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__SET_PROGRESS_HPP_ 