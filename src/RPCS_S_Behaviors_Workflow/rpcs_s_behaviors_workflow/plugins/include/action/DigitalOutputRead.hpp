#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTREAD_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTREAD_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/digital_output_read.hpp"
#include <chrono>
#include <string>
#include <vector>
#include <thread>
#include <future>

namespace rpcs_s_behaviors_workflow
{
    /**
     * @brief 行为树节点：读取数字输出
     * 
     * 该节点通过action接口批量读取指定地址的数字输出状态
     */
    class DigitalOutputRead : public BT::StatefulActionNode
    {
    public:
        using DigitalOutputReadAction = rpcs_interfaces_motor::action::DigitalOutputRead;
        using GoalHandleDigitalOutputRead = rclcpp_action::ClientGoalHandle<DigitalOutputReadAction>;
        
        DigitalOutputRead(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~DigitalOutputRead() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // 输入端口
                BT::InputPort<std::string>("strDeviceId", "robot1", "设备ID"),
                BT::InputPort<std::vector<int>>("intOutputAddresses", "要读取的输出地址列表"),
                BT::InputPort<std::vector<int>>("boolExpectedValues", "期望的输出值列表，与地址列表一一对应"),
                BT::InputPort<bool>("boolContinuousRead", false, "是否连续读取模式"),
                BT::InputPort<double>("doubleReadInterval", 0.1, "连续读取间隔(秒)"),
                BT::InputPort<double>("doubleDuration", 0.0, "连续读取持续时间(秒)"),
                BT::InputPort<int>("intTimeoutMs", 5000, "超时时间(毫秒)"),
                BT::InputPort<std::string>("strBlackboardKey", "", "黑板变量名，用于存储匹配状态"),
                
                // 输出端口
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<bool>("boolCurrentValue", "单个地址的当前值"),
                BT::OutputPort<std::vector<int>>("boolOutputValues", "读取到的输出值列表"),
                BT::OutputPort<std::vector<int>>("intOutputAddresses", "对应的地址列表"),
                BT::OutputPort<std::vector<int>>("boolValueMatched", "每个地址的值是否匹配期望值"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        void createActionClient(const std::string& device_id);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<DigitalOutputReadAction>::SharedPtr action_client_;
        
        // Goal处理相关变量
        bool goal_sent_;
        bool goal_completed_;
        GoalHandleDigitalOutputRead::SharedPtr goal_handle_;
        std::shared_future<GoalHandleDigitalOutputRead::SharedPtr> goal_handle_future_;
        std::shared_ptr<typename DigitalOutputReadAction::Result> result_;
        std::shared_ptr<DigitalOutputReadAction::Goal> goal_msg_;

        // 成员变量
        std::string device_id_;
        std::vector<int> output_addresses_;
        std::vector<int> expected_values_;
        std::vector<int> current_values_;
        std::vector<int> value_matched_;
        bool continuous_read_;
        double read_interval_;
        double duration_;
        int timeout_ms_;
        std::string blackboard_key_;
        std::chrono::steady_clock::time_point start_time_;
        bool should_cancel_ = false;  // 标记是否需要取消请求
        std::shared_ptr<const typename DigitalOutputReadAction::Feedback> feedback_;
    };
} // namespace rpcs_s_behaviors_workflow
#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__DIGITALOUTPUTREAD_HPP_ 