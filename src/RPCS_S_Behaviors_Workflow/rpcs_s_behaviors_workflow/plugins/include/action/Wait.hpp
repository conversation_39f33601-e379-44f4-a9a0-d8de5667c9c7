#ifndef RPCS_BEHAVIOR_TREE_PLUGINS_ACTION_WAIT_HPP_
#define RPCS_BEHAVIOR_TREE_PLUGINS_ACTION_WAIT_HPP_

#include <string>
#include <chrono>
#include <thread>

#include "behaviortree_cpp/action_node.h"

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 等待节点，提供简单的延时功能
 */
class Wait : public BT::SyncActionNode
{
public:
  /**
   * @brief 构造函数
   * @param name 节点名称
   * @param config 节点配置
   */
  Wait(const std::string& name, const BT::NodeConfiguration& config)
  : BT::SyncActionNode(name, config)
  {
  }

  /**
   * @brief 获取节点配置
   * @return 节点参数配置
   */
  static BT::PortsList providedPorts()
  {
    return {
      BT::InputPort<int>("intMsec", "等待时间(毫秒)")
    };
  }

  /**
   * @brief 执行等待操作
   * @return 节点执行结果
   */
  BT::NodeStatus tick() override
  {
    int msec = 0;
    if (!getInput("intMsec", msec)) {
      throw BT::RuntimeError("Missing required input [intMsec]");
    }

    if (msec < 0) {
      throw BT::RuntimeError("Wait time cannot be negative");
    }

    // 执行等待
    std::this_thread::sleep_for(std::chrono::milliseconds(msec));
    
    return BT::NodeStatus::SUCCESS;
  }
};

}  // namespace rpcs_s_behaviors_workflow

#endif  // RPCS_BEHAVIOR_TREE_PLUGINS_ACTION_WAIT_HPP_ 