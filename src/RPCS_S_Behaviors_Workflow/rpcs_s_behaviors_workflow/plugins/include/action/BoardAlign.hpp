#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__BOARDALIGN_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__BOARDALIGN_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "algorithm_interface/srv/board_align.hpp"
#include <chrono>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <future>

namespace rpcs_s_behaviors_workflow
{
    class BoardAlign : public BT::StatefulActionNode
    {
    public:
        BoardAlign(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~BoardAlign() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 算法参数
                BT::InputPort<int>("intBoardNum", "贴砖号"),
                BT::InputPort<std::string>("strParamOverrides", "{}", "参数覆盖，JSON格式的键值对字符串"),
                
                // 控制参数
                BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(秒)"),
                
                // === 输出端口 ===
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<std::string>("strOutputMessage", "状态消息"),
                BT::OutputPort<bool>("boolIsFinish", "是否完成"),
                BT::OutputPort<std::string>("strMoveCommands", "移动命令数组(JSON格式)"),
                BT::OutputPort<int>("intMoveCommandsCount", "移动命令数量"),
                
                // === 解析后的移动值输出端口 ===
                BT::OutputPort<double>("doubleOutputX", "X轴移动值"),
                BT::OutputPort<double>("doubleOutputY", "Y轴移动值"),
                BT::OutputPort<double>("doubleOutputR", "R轴移动值"),
                BT::OutputPort<bool>("boolOutputX", "X轴移动使能"),
                BT::OutputPort<bool>("boolOutputY", "Y轴移动使能"),
                BT::OutputPort<bool>("boolOutputR", "R轴移动使能")
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using BoardAlignService = algorithm_interface::srv::BoardAlign;
        
        // 工具方法
        std::string serializeMoveCommands(const std::vector<algorithm_interface::msg::MoveCommand>& commands);
        void setErrorOutput(const std::string& error_message);
        bool prepareAlgorithmRequest();
        bool sendAlgorithmRequest();
        bool handleAlgorithmResponse();
        std::string convertKeyValuePairsToJson(const std::string& key_value_pairs);
        
        // ROS节点和服务客户端
        rclcpp::Node::SharedPtr node_;
        rclcpp::Client<BoardAlignService>::SharedPtr client_;
        std::shared_future<BoardAlignService::Response::SharedPtr> algorithm_future_;
        
        // 输入参数
        int board_num_;
        std::string param_overrides_;
        double timeout_;
        
        // 运行状态
        std::chrono::steady_clock::time_point start_time_;
        bool request_sent_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__BOARDALIGN_HPP_ 