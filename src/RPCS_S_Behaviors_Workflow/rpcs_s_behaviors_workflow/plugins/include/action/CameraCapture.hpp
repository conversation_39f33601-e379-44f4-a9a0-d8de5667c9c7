#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CAMERACAPTURE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CAMERACAPTURE_HPP_

#include "behaviortree_cpp/action_node.h"
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <hikvision_interface/srv/get_single_image.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>
#include <memory>
#include <string>
#include <chrono>

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 相机拍照控制节点
 * 
 * 调用海康威视相机接口获取单张图像
 * 服务调用：/driver/hikvision/{namespace}/get_single_image
 */
class CameraCapture : public BT::StatefulActionNode
{
public:
    /**
     * @brief 构造函数
     * @param name 节点名称
     * @param config 节点配置
     */
    CameraCapture(const std::string& name, const BT::NodeConfiguration& config);

    /**
     * @brief 提供端口信息
     * @return 端口配置信息
     */
    static BT::PortsList providedPorts();

    /**
     * @brief 节点启动时调用
     * @return 节点状态
     */
    BT::NodeStatus onStart() override;

    /**
     * @brief 节点运行时调用
     * @return 节点状态
     */
    BT::NodeStatus onRunning() override;

    /**
     * @brief 节点停止时调用
     */
    void onHalted() override;

private:
    // ROS2 节点
    rclcpp::Node::SharedPtr _rosNode;
    
    // 服务客户端
    rclcpp::Client<hikvision_interface::srv::GetSingleImage>::SharedPtr _serviceClient;
    
    // 服务请求的 Future
    std::shared_future<hikvision_interface::srv::GetSingleImage::Response::SharedPtr> _serviceFuture;
    
    // 相机命名空间
    std::string _cameraNamespace;
    
    // 是否触发拍照
    bool _triggerImage;
    
    // 图像发布话题（可选）
    std::string _imageTopicName;
    
    // 图像发布器（可选）
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr _imagePublisher;
    
    // 图像保存路径（可选）
    std::string _saveImagePath;

    // 计时相关成员变量
    std::chrono::steady_clock::time_point _serviceStartTime;
    std::chrono::steady_clock::time_point _lastLogTime;

    /**
     * @brief 初始化服务客户端
     * @return 是否初始化成功
     */
    bool initializeServiceClient();
    
    /**
     * @brief 发布图像到话题（如果配置了话题名称）
     * @param image 要发布的图像
     */
    void publishImageIfRequested(const sensor_msgs::msg::Image& image);
    
    /**
     * @brief 保存图像到本地文件（如果配置了保存路径）
     * @param image 要保存的图像
     * @return 是否保存成功
     */
    bool saveImageToLocal(const sensor_msgs::msg::Image& image);
    
    /**
     * @brief 设置输出端口值
     * @param success 操作是否成功
     * @param message 返回消息
     */
    void setOutputs(bool success, const std::string& message);
};

} // namespace rpcs_s_behaviors_workflow 

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__CAMERACAPTURE_HPP_