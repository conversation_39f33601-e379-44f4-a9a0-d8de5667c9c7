#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORPOSITIONCONTROL_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORPOSITIONCONTROL_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "rpcs_interfaces_motor/action/position_control.hpp"
#include <chrono>
#include <iostream>
#include <string>

namespace rpcs_s_behaviors_workflow
{
    class MotorPositionControl : public BT::StatefulActionNode
    {
    public:
        MotorPositionControl(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                // === 输入端口 ===
                // 设备和电机标识
                BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
                BT::InputPort<std::string>("strMotorBrand", "Kinco", "电机品牌名称(Kinco, UMot等)"),
                BT::InputPort<int>("intMotorId", "电机ID编号"),

                // 位置控制参数
                BT::InputPort<double>("doubleTargetPosition", "目标位置(mm)"),
                BT::InputPort<bool>("boolAbsolutePosition", true, "位置模式：true=绝对位置，false=相对位置"),
                
                // 运动参数
                BT::InputPort<double>("doubleMaxVelocity", 50.0, "最大速度(mm/s)"),
                BT::InputPort<double>("doubleAcceleration", 100.0, "加速度(mm/s²)"),
                BT::InputPort<double>("doubleDeceleration", 100.0, "减速度(mm/s²)"),
                BT::InputPort<double>("doubleDwellTime", 1.0, "到达后停留时间(s)"),
                BT::InputPort<double>("doubleTimeout", 15.0, "超时时间(s)"),
                
                // === 输出端口 ===
                // 执行结果
                BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
                BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
                BT::OutputPort<double>("doublePositionError", "位置误差(mm)"),
                BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
                BT::OutputPort<int>("intErrorCode", "错误代码"),
                
                // 实时状态
                BT::OutputPort<double>("doubleCurrentPosition", "当前位置(mm)"),
                BT::OutputPort<double>("doubleProgress", "完成进度(0.0-1.0)")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        using PositionControlAction = rpcs_interfaces_motor::action::PositionControl;
        using GoalHandlePositionControl = rclcpp_action::ClientGoalHandle<PositionControlAction>;
        
        void createActionClient(const std::string& device_id, const std::string& motor_brand, int motor_id);
        std::string createActionName(const std::string& device_id, const std::string& motor_brand, int motor_id);
        void handleActionError(int error_code, const std::string& error_message);
        
        // ROS节点
        rclcpp::Node::SharedPtr node_;
        
        // Action客户端
        rclcpp_action::Client<PositionControlAction>::SharedPtr client_;
        
        // Action目标句柄的Future
        std::shared_future<GoalHandlePositionControl::SharedPtr> goal_future_;
        
        // Action结果的Future
        std::shared_future<GoalHandlePositionControl::WrappedResult> result_future_;
        
        // 输入参数
        std::string device_id_;
        std::string motor_brand_;
        int motor_id_;
        double target_position_;
        bool absolute_position_;
        double max_velocity_;
        double acceleration_;
        double deceleration_;
        double dwell_time_;
        double timeout_;
        
        // 请求开始时间
        std::chrono::steady_clock::time_point start_time_;
        
        // 目标句柄
        GoalHandlePositionControl::SharedPtr goal_handle_;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MOTORPOSITIONCONTROL_HPP_ 