#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPRINTMESSAGE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPRINTMESSAGE_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
#include <iostream>

namespace rpcs_s_behaviors_workflow
{
    class PubPrintMessage : public BT::StatefulActionNode
    {
    public:
        PubPrintMessage(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts()
        {
            return {
                BT::InputPort<std::string>("strTopicName"),
                BT::InputPort<std::string>("strPrintMessage")
            };
        }

        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        std::chrono::steady_clock::time_point start_time;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__PUBPRINTMESSAGE_HPP_
