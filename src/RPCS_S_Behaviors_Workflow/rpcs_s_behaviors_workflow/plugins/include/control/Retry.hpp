#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETRY_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETRY_HPP_

#include "behaviortree_cpp/control_node.h"
#include "rclcpp/rclcpp.hpp"
#include <iostream>

namespace rpcs_s_behaviors_workflow
{
    class Retry : public BT::ControlNode
    {
    public:
        Retry(const std::string& name, const BT::NodeConfig& config);

        static BT::PortsList providedPorts()
        {
            return {
            };
        }

        BT::NodeStatus tick() override;

    private:
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETRY_HPP_
