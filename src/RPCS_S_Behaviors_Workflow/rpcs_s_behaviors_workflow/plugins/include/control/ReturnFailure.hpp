#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETURNFAILURE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETURNFAILURE_HPP_

#include "behaviortree_cpp/control_node.h"

namespace rpcs_s_behaviors_workflow
{
    // ReturnFailure节点：无论子节点返回什么状态，该节点始终返回FAILURE
    class ReturnFailure : public BT::ControlNode
    {
    public:
        ReturnFailure(const std::string& name, const BT::NodeConfiguration& config);

        static BT::PortsList providedPorts() { return {}; }

    private:
        BT::NodeStatus tick() override;
    };
}  // namespace rpcs_s_behaviors_workflow

#endif  // RPCS_BEHAVIOR_TREE__PLUGINS__CONTROL__RETURNFAILURE_HPP_ 