#!/usr/bin/env python3

import os
import re
import xml.etree.ElementTree as ET
import xml.dom.minidom as minidom
import argparse
import logging
from pathlib import Path

def setup_logging(log_level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def extract_ports_from_file(file_path):
    """从C++文件中提取类名、输入端口和输出端口，以及描述和默认值"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logging.error(f"无法读取文件 {file_path}: {e}")
        return None, [], [], {}, {}
    
    # 提取类名 - 支持多种继承方式
    class_patterns = [
        r'class\s+(\w+)\s*:\s*public\s+BT::(\w+)',  # 标准继承
        r'class\s+(\w+)\s*:\s*public\s+(\w+)',      # 简化继承
        r'class\s+(\w+)\s*:'                        # 基本类定义
    ]
    
    class_name = None
    node_base_type = None
    
    for pattern in class_patterns:
        class_match = re.search(pattern, content)
        if class_match:
            class_name = class_match.group(1)
            if len(class_match.groups()) > 1:
                node_base_type = class_match.group(2)
            break
    
    if not class_name:
        logging.debug(f"未找到类定义: {file_path}")
        return None, [], [], {}, {}
    
    # 提取端口定义
    input_ports = []
    output_ports = []
    port_descriptions = {}
    port_defaults = {}
    
    # 查找 providedPorts 函数
    ports_function_match = re.search(
        r'static\s+BT::PortsList\s+providedPorts\s*\(\s*\)\s*{(.*?)}', 
        content, 
        re.DOTALL
    )
    
    if ports_function_match:
        ports_content = ports_function_match.group(1)
        
        # 提取所有端口定义行
        # 每个端口定义通常是一行，以BT::InputPort或BT::OutputPort开头
        # 可能跨多行，以逗号结束
        
        # 首先按行分割
        lines = ports_content.split('\n')
        
        # 合并可能跨行的端口定义
        port_definitions = []
        current_def = ""
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):  # 跳过空行和注释
                continue
                
            # 如果是新的端口定义开始
            if ('InputPort' in line or 'OutputPort' in line) and not current_def:
                current_def = line
            # 如果是当前定义的继续
            elif current_def:
                current_def += ' ' + line
            
            # 如果当前定义结束（以逗号或右括号结束）
            if current_def and (current_def.rstrip().endswith(',') or current_def.rstrip().endswith(')')):
                port_definitions.append(current_def)
                current_def = ""
        
        # 处理最后一个可能未处理的定义
        if current_def:
            port_definitions.append(current_def)
        
        # 现在处理每个端口定义
        for port_def in port_definitions:
            # 确定端口类型
            if 'InputPort' in port_def:
                port_type = 'Input'
            elif 'OutputPort' in port_def:
                port_type = 'Output'
            else:
                continue  # 不是端口定义
            
            # 提取端口名称（第一个引号对之间的内容）
            port_name_match = re.search(r'"([^"]+)"', port_def)
            if not port_name_match:
                continue
            
            port_name = port_name_match.group(1)
            
            # 提取所有引号内的内容
            quoted_strings = re.findall(r'"([^"]*)"', port_def)
            
            # 提取默认值和描述
            default_value = ""
            description = ""
            
            if port_type == 'Input':
                # 输入端口可能有默认值和描述
                if len(quoted_strings) > 1:
                    # 第二个引号内容通常是描述
                    description = quoted_strings[1]
                
                # 尝试提取默认值 - 在第一个引号后，可能的第二个引号前
                # 注意：默认值可能是字符串、数字、布尔值或复杂表达式
                default_match = re.search(r'"[^"]+"\s*,\s*([^,]+?)(?:\s*,\s*"|$)', port_def)
                if default_match:
                    default_value = default_match.group(1).strip()
            else:
                # 输出端口通常只有描述
                if len(quoted_strings) > 1:
                    description = quoted_strings[1]
            
            # 添加到相应的列表
            if port_type == 'Input':
                if port_name not in input_ports:
                    input_ports.append(port_name)
                    port_descriptions[port_name] = description
                    port_defaults[port_name] = default_value
            else:
                if port_name not in output_ports:
                    output_ports.append(port_name)
                    port_descriptions[port_name] = description
                    port_defaults[port_name] = default_value
            
            logging.debug(f"提取到端口: {port_type}Port '{port_name}', 默认值: '{default_value}', 描述: '{description}'")
    
    # 如果没有找到端口，尝试使用更简单的方法
    if not input_ports and not output_ports:
        logging.debug(f"从providedPorts函数中未找到端口，尝试直接搜索: {file_path}")
        
        # 查找所有可能的端口定义
        input_matches = re.findall(r'InputPort\s*<[^>]*>\s*\(\s*"([^"]+)"', content)
        output_matches = re.findall(r'OutputPort\s*<[^>]*>\s*\(\s*"([^"]+)"', content)
        
        for port_name in input_matches:
            if port_name not in input_ports:
                input_ports.append(port_name)
                port_descriptions[port_name] = ""
                port_defaults[port_name] = ""
        
        for port_name in output_matches:
            if port_name not in output_ports:
                output_ports.append(port_name)
                port_descriptions[port_name] = ""
                port_defaults[port_name] = ""
    
    # 检查是否有特殊的节点需要手动添加端口
    node_specific_ports = {
        "DigitalOutputWrite": {
            "input": ["strDeviceId", "intOutputAddresses", "boolOutputValues",
                     "boolVerifyWrite", "boolBatchOperation", "intTimeoutMs"],
            "output": ["boolOutputSuccess", "boolFinalValues", "intOutputAddresses",
                      "boolWriteSuccesses", "strOutputMessage", "intErrorCode"]
        },
        # 可以添加其他特殊节点的端口定义
    }

    # 定义需要排除的端口（针对特定节点）
    node_excluded_ports = {
        "DigitalInputRead": {
            "input": ["intInputAddress", "boolExpectedValue"],  # 排除单个形式，只保留数组形式
            "output": []
        }
    }
    
    # 如果当前节点在特殊节点列表中，确保所有必要的端口都存在
    for node_name, ports in node_specific_ports.items():
        if node_name in file_path or (class_name and node_name in class_name):
            # 添加缺失的输入端口
            for port in ports["input"]:
                if port not in input_ports:
                    input_ports.append(port)
                    port_descriptions[port] = ""
                    port_defaults[port] = ""
                    logging.debug(f"添加特殊节点 {node_name} 的缺失输入端口: {port}")

            # 添加缺失的输出端口
            for port in ports["output"]:
                if port not in output_ports:
                    output_ports.append(port)
                    port_descriptions[port] = ""
                    port_defaults[port] = ""
                    logging.debug(f"添加特殊节点 {node_name} 的缺失输出端口: {port}")

    # 应用端口排除规则
    for node_name, excluded_ports in node_excluded_ports.items():
        if node_name in file_path or (class_name and node_name in class_name):
            # 排除指定的输入端口
            for port in excluded_ports["input"]:
                if port in input_ports:
                    input_ports.remove(port)
                    if port in port_descriptions:
                        del port_descriptions[port]
                    if port in port_defaults:
                        del port_defaults[port]
                    logging.debug(f"排除节点 {node_name} 的输入端口: {port}")

            # 排除指定的输出端口
            for port in excluded_ports["output"]:
                if port in output_ports:
                    output_ports.remove(port)
                    if port in port_descriptions:
                        del port_descriptions[port]
                    if port in port_defaults:
                        del port_defaults[port]
                    logging.debug(f"排除节点 {node_name} 的输出端口: {port}")
    
    logging.debug(f"类 {class_name}: 输入端口 {len(input_ports)}, 输出端口 {len(output_ports)}")
    if input_ports:
        logging.debug(f"输入端口: {', '.join(input_ports)}")
    if output_ports:
        logging.debug(f"输出端口: {', '.join(output_ports)}")
    
    return class_name, input_ports, output_ports, port_descriptions, port_defaults

def clean_html_encoding(value):
    """清理HTML编码，特别是引号的编码"""
    if not value:
        return value

    # 移除HTML编码的引号
    value = value.replace('&quot;', '"')

    # 如果值是 '""'（空字符串的引号形式），转换为空字符串
    if value == '""':
        return ""

    # 如果值被双重引号包围，移除外层引号
    if value.startswith('"') and value.endswith('"') and len(value) > 1:
        value = value[1:-1]

    return value

def determine_node_type(file_path, class_name, base_type=None):
    """根据文件路径、类名和基类确定节点类型"""
    # 根据目录结构确定类型
    path_parts = Path(file_path).parts
    
    # 目录映射
    directory_type_mapping = {
        'action': 'Action',
        'condition': 'Condition', 
        'control': 'Control',
        'decorator': 'Decorator',
        'action_nodes': 'Action',
        'condition_nodes': 'Condition',
        'control_nodes': 'Control',
        'decorator_nodes': 'Decorator'
    }
    
    # 检查路径中的目录
    for part in path_parts:
        if part.lower() in directory_type_mapping:
            return directory_type_mapping[part.lower()]
    
    # 根据基类确定类型
    base_type_mapping = {
        'StatefulActionNode': 'Action',
        'SyncActionNode': 'Action',
        'ActionNodeBase': 'Action',
        'ConditionNode': 'Condition',
        'SimpleConditionNode': 'Condition',
        'ControlNode': 'Control',
        'DecoratorNode': 'Decorator'
    }
    
    if base_type and base_type in base_type_mapping:
        return base_type_mapping[base_type]
    
    # 根据类名模式推断
    class_name_lower = class_name.lower()
    if any(keyword in class_name_lower for keyword in ['action', 'do', 'move', 'set', 'pub', 'motor']):
        return 'Action'
    elif any(keyword in class_name_lower for keyword in ['condition', 'check', 'has', 'is', 'can']):
        return 'Condition'
    elif any(keyword in class_name_lower for keyword in ['control', 'sequence', 'fallback', 'parallel']):
        return 'Control'
    elif any(keyword in class_name_lower for keyword in ['decorator', 'retry', 'timeout', 'inverter']):
        return 'Decorator'
    
    # 默认为Action
    logging.warning(f"无法确定节点类型，使用默认值 Action: {class_name}")
    return 'Action'

def create_btproj_file(node_infos, output_file):
    """创建BT项目文件"""
    root = ET.Element("root")
    root.set("BTCPP_format", "4")
    root.set("project_name", "RPCS_Behavior_Tree_Project")
    
    # 添加注释
    comment = ET.Comment(" Auto-generated Node Models for Groot2 Editor ")
    root.append(comment)
    
    tree_nodes_model = ET.SubElement(root, "TreeNodesModel")
    
    # 按类型分组节点
    nodes_by_type = {}
    for node_type, node_id, input_ports, output_ports, port_descriptions, port_defaults in node_infos:
        if node_type not in nodes_by_type:
            nodes_by_type[node_type] = []
        nodes_by_type[node_type].append((node_id, input_ports, output_ports, port_descriptions, port_defaults))
    
    # 为每个类型的节点添加元素
    for node_type in sorted(nodes_by_type.keys()):
        # 添加类型注释
        type_comment = ET.Comment(f" {node_type} Nodes ")
        tree_nodes_model.append(type_comment)
        
        for node_id, input_ports, output_ports, port_descriptions, port_defaults in sorted(nodes_by_type[node_type]):
            node_elem = ET.SubElement(tree_nodes_model, node_type)
            node_elem.set("ID", node_id)
            node_elem.set("editable", "true")
            
            # 添加输入端口
            for port in sorted(input_ports):
                input_port = ET.SubElement(node_elem, "input_port")
                input_port.set("name", port)

                # 使用从代码中提取的默认值，如果没有则使用智能推断
                if port in port_defaults and port_defaults[port]:
                    default_value = port_defaults[port]
                    # 清理HTML编码的引号
                    default_value = clean_html_encoding(default_value)
                else:
                    default_value = get_default_value(port)
                input_port.set("default", default_value)

                # 使用从代码中提取的描述，如果没有则使用智能推断
                if port in port_descriptions and port_descriptions[port]:
                    description = port_descriptions[port]
                else:
                    description = get_port_description(port)
                input_port.set("description", description)
            
            # 添加输出端口
            for port in sorted(output_ports):
                output_port = ET.SubElement(node_elem, "output_port")
                output_port.set("name", port)

                # 重要：输出端口不应该设置默认值！
                # 原因：在行为树编辑器中，输出端口的默认值会被错误地当作输入参数使用，
                # 导致类型转换错误（如 boolFinalValues="false" 无法转换为 std::vector<int>）
                # 输出端口的值应该由节点内部逻辑生成，而不是通过默认值设置

                # 只设置描述信息
                if port in port_descriptions and port_descriptions[port]:
                    description = port_descriptions[port]
                else:
                    description = get_port_description(port)
                output_port.set("description", description)
    
    # 美化XML格式
    rough_string = ET.tostring(root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    pretty_xml = reparsed.toprettyxml(indent="    ")
    
    # 修复XML格式问题
    lines = pretty_xml.split('\n')
    # 移除空行并保留第一行XML声明
    filtered_lines = [line for line in lines if line.strip()]
    pretty_xml = '\n'.join(filtered_lines)
    
    # 确保正确的XML声明
    if not pretty_xml.startswith('<?xml'):
        pretty_xml = '<?xml version="1.0" encoding="UTF-8"?>\n' + pretty_xml
    
    # 写入文件
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
        logging.info(f"成功生成项目文件: {output_file}")
    except Exception as e:
        logging.error(f"写入文件失败 {output_file}: {e}")

def get_default_value(port_name):
    """根据端口名称获取默认值（仅用于输入端口）"""
    port_name_lower = port_name.lower()

    # 特定参数的默认值（优先级最高）
    if 'timeout' in port_name_lower and 'ms' in port_name_lower:
        return "5000"  # 默认超时5000毫秒

    if 'device' in port_name_lower and 'id' in port_name_lower:
        return "R03001110003"  # 默认设备ID

    # 特定的字符串参数
    if port_name_lower == 'strmotorbrand':
        return "Kinco"  # 电机品牌

    if port_name_lower == 'strblackboardkey':
        return ""  # 黑板键默认为空

    # 特殊的布尔参数 - 大部分使用字符串格式，只有特定的使用数字格式
    if port_name_lower == 'boolverifywrite':
        return "true"  # 默认验证写入

    if port_name_lower == 'boolbatchoperation':
        return "false"  # 默认不使用批量操作

    if port_name_lower == 'boolcontinuousread':
        return "false"  # 默认不连续读取

    # 特殊的布尔值参数 - 这些需要使用数字格式（1/0）而不是字符串（true/false）
    if port_name_lower == 'boolexpectedvalue':
        return "1"  # 期望值使用数字格式

    if port_name_lower == 'boolexpectedvalues':
        return "1"  # 期望值列表使用数字格式

    # 特殊的数值参数
    if port_name_lower == 'doublereadinterval':
        return "0.5"  # 读取间隔默认0.5秒

    if port_name_lower == 'doubleduration':
        return "5"  # 持续时间默认5秒

    # 布尔类型参数 - 大部分使用字符串格式
    if port_name_lower.startswith(('bool', 'is_', 'has_', 'should_', 'can_')):
        return "false"

    # 整数类型参数
    if port_name_lower.startswith(('int', 'count', 'num_', 'index')):
        return "0"

    # 浮点数类型参数
    if port_name_lower.startswith(('double', 'float', 'time_', 'duration', 'interval', 'position', 'velocity', 'acceleration')):
        return "0.0"

    # 字符串类型参数 - 避免HTML编码
    if port_name_lower.startswith(('str', 'name', 'path', 'message', 'text', 'id', 'key', 'value')):
        return ""

    # 对于其他参数，返回空字符串
    return ""

def get_port_description(port_name):
    """根据端口名称生成描述"""
    port_name_lower = port_name.lower()

    # 特定参数的详细描述（优先级最高）
    specific_descriptions = {
        'booloutputsuccess': '操作是否成功',
        'boolfinalvalues': '最终的输出值列表',
        'boolwritesuccesses': '每个地址的写入是否成功',
        'intoutputaddresses': '输出地址列表',
        'booloutputvalues': '输出值列表',
        'stroutputmessage': '操作结果消息',
        'interrorcode': '错误代码',
        'strmessage': '消息内容',
        'strdeviceid': '设备ID',
        'inttimeoutms': '超时时间(毫秒)',
        'boolverifywrite': '是否验证写入结果',
        'boolbatchoperation': '是否使用批量操作模式',
        'boolcontinuousread': '是否连续读取模式',
        'doublereadinterval': '连续读取间隔(秒)',
        'doubleduration': '连续读取持续时间(秒)',
        'boolexpectedvalues': '期望的值列表',
        'boolcurrentvalues': '当前读取的值列表',
        'boolvaluematched': '值是否匹配期望值',
        'strblackboardkey': '黑板变量名'
    }

    if port_name_lower in specific_descriptions:
        return specific_descriptions[port_name_lower]

    # 通用模式匹配
    if 'timeout' in port_name_lower:
        if 'ms' in port_name_lower:
            return "超时时间(毫秒)"
        return "超时时间(秒)"

    if 'device' in port_name_lower and 'id' in port_name_lower:
        return "设备ID"

    if 'success' in port_name_lower:
        return "操作是否成功"

    if 'message' in port_name_lower:
        return "消息内容"

    if 'error' in port_name_lower and 'code' in port_name_lower:
        return "错误代码"

    # 类型推断描述
    if port_name_lower.startswith('bool'):
        return f"布尔值: {port_name}"

    if port_name_lower.startswith('int'):
        return f"整数值: {port_name}"

    if port_name_lower.startswith(('double', 'float')):
        return f"浮点数值: {port_name}"

    if port_name_lower.startswith('str'):
        return f"字符串: {port_name}"

    # 对于其他参数，返回基本描述
    return f"参数: {port_name}"

def scan_directory(directory_path):
    """扫描目录，提取节点信息"""
    node_infos = []
    
    if not os.path.exists(directory_path):
        logging.error(f"目录不存在: {directory_path}")
        return node_infos
    
    logging.info(f"开始扫描目录: {directory_path}")
    
    # 支持的文件扩展名
    supported_extensions = {'.hpp', '.h', '.cpp', '.cc', '.cxx'}
    
    for root, dirs, files in os.walk(directory_path):
        # 过滤掉不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            if file_ext in supported_extensions:
                logging.debug(f"处理文件: {file_path}")
                class_name, input_ports, output_ports, port_descriptions, port_defaults = extract_ports_from_file(file_path)
                
                if class_name and (input_ports or output_ports):
                    node_type = determine_node_type(file_path, class_name)
                    node_infos.append((node_type, class_name, input_ports, output_ports, port_descriptions, port_defaults))
                    logging.info(f"发现节点: {class_name} ({node_type}) - 输入:{len(input_ports)}, 输出:{len(output_ports)}")
    
    logging.info(f"总共找到 {len(node_infos)} 个节点")
    return node_infos

def get_default_paths():
    """获取默认的源目录和输出路径"""
    # 获取脚本所在目录
    script_dir = Path(__file__).parent.absolute()
    
    # 推断项目根目录
    project_root = script_dir.parent
    
    # 默认源目录 - 指定为plugins目录
    source_dir = str(project_root / "plugins")
    
    # 检查目录是否存在
    if not os.path.exists(source_dir):
        logging.warning(f"指定的plugins目录不存在: {source_dir}")
        # 尝试其他可能的路径
        alt_source_dir = str(project_root / "include" / "rpcs_s_behaviors_workflow" / "plugins")
        if os.path.exists(alt_source_dir):
            source_dir = alt_source_dir
            logging.info(f"使用替代plugins目录: {source_dir}")
        else:
            logging.warning(f"替代plugins目录也不存在: {alt_source_dir}")
    
    # 默认输出文件
    output_file = script_dir / "rpcs_s_behaviors_workflow.btproj"
    
    return source_dir, str(output_file)

def main():
    """主函数"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description='生成行为树项目文件(.btproj)，用于Groot2编辑器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s  # 使用默认路径
  %(prog)s -s /path/to/plugins -o output.btproj  # 指定路径
  %(prog)s --verbose  # 显示详细日志
        """
    )
    
    default_source, default_output = get_default_paths()
    
    # 如果默认源目录不是指定的plugins目录，尝试直接使用指定路径
    specific_plugins_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
        "plugins"
    )
    if os.path.exists(specific_plugins_path):
        default_source = specific_plugins_path
    
    parser.add_argument(
        '-s', '--source', 
        default=default_source,
        help=f'源代码目录路径 (默认: {default_source})'
    )
    parser.add_argument(
        '-o', '--output',
        default=default_output, 
        help=f'输出的.btproj文件路径 (默认: {default_output})'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志信息'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    
    logging.info("=== RPCS 行为树项目文件生成器 ===")
    logging.info(f"源目录: {args.source}")
    logging.info(f"输出文件: {args.output}")
    
    # 检查源目录是否存在
    if not os.path.exists(args.source):
        logging.error(f"源目录不存在: {args.source}")
        logging.info("尝试使用指定的plugins目录路径...")
        args.source = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            "plugins"
        )
        if os.path.exists(args.source):
            logging.info(f"使用找到的plugins目录: {args.source}")
        else:
            logging.error(f"无法找到有效的plugins目录")
            return 1
    
    # 扫描目录并提取节点信息
    node_infos = scan_directory(args.source)
    
    if not node_infos:
        logging.warning("未找到任何有效的行为树节点")
        return 1
    
    # 生成项目文件
    create_btproj_file(node_infos, args.output)
    
    # 输出统计信息
    type_counts = {}
    for node_type, _, _, _, _, _ in node_infos:
        type_counts[node_type] = type_counts.get(node_type, 0) + 1
    
    logging.info("=== 节点统计 ===")
    for node_type, count in sorted(type_counts.items()):
        logging.info(f"{node_type}: {count} 个节点")
    
    logging.info("=== 生成完成 ===")
    return 0

if __name__ == "__main__":
    exit(main()) 