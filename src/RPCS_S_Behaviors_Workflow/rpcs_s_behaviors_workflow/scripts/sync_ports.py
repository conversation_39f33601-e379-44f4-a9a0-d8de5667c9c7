#!/usr/bin/env python3

import os
import re
import glob
import sys

def extract_ports_from_hpp(hpp_file):
    """从hpp文件中提取端口名称"""
    with open(hpp_file, 'r') as f:
        content = f.read()
    
    # 提取输入端口和输出端口
    input_port_pattern = r'BT::InputPort<[^>]+>\s*\("([^"]+)"\)'
    output_port_pattern = r'BT::OutputPort<[^>]+>\s*\("([^"]+)"\)'
    
    input_ports = re.findall(input_port_pattern, content)
    output_ports = re.findall(output_port_pattern, content)
    
    return input_ports, output_ports

def update_cpp_file(cpp_file, hpp_file):
    """根据hpp文件更新cpp文件中的端口名称"""
    input_ports, output_ports = extract_ports_from_hpp(hpp_file)
    
    if not input_ports and not output_ports:
        print(f"  没有找到端口定义: {hpp_file}")
        return False
    
    with open(cpp_file, 'r') as f:
        content = f.read()
    
    original_content = content
    modified = False
    
    # 在cpp文件中查找端口使用情况
    for port in input_ports:
        # 在getInput调用中更新端口名称
        if re.search(r'getInput<[^>]+>\s*\([^"]*"([^"]+)"', content):
            old_port_pattern = r'getInput<([^>]+)>\s*\([^"]*"([^"]+)"'
            matches = re.finditer(old_port_pattern, content)
            
            for match in matches:
                old_port_name = match.group(2)
                if old_port_name != port and old_port_name not in input_ports:
                    # 找到对应的替换项
                    replacement = f'getInput<{match.group(1)}>("{port}"'
                    content = content.replace(match.group(0), replacement)
                    print(f"  替换 getInput: {old_port_name} -> {port}")
                    modified = True
    
    # 在setOutput调用中更新端口名称
    for port in output_ports:
        if re.search(r'setOutput\s*\([^"]*"([^"]+)"', content):
            old_port_pattern = r'setOutput\s*\([^"]*"([^"]+)"'
            matches = re.finditer(old_port_pattern, content)
            
            for match in matches:
                old_port_name = match.group(1)
                if old_port_name != port and old_port_name not in output_ports:
                    # 找到对应的替换项
                    replacement = f'setOutput("{port}"'
                    content = content.replace(match.group(0), replacement)
                    print(f"  替换 setOutput: {old_port_name} -> {port}")
                    modified = True
    
    # 如果有修改，写入文件
    if modified:
        with open(cpp_file, 'w') as f:
            f.write(content)
        print(f"已更新文件: {cpp_file}")
        return True
    else:
        print(f"  无需更新: {cpp_file}")
        return False

def sync_hpp_cpp_ports():
    """同步hpp和cpp文件中的端口名称"""
    base_dir = "/home/<USER>/workspaces/home_git/AutoCreateRPS_home/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow"
    
    # 找到所有hpp文件
    hpp_files = glob.glob(os.path.join(base_dir, "include/rpcs_s_behaviors_workflow/plugins/**/*.hpp"), recursive=True)
    
    modified_count = 0
    for hpp_file in hpp_files:
        # 构造对应的cpp文件路径
        rel_path = os.path.relpath(hpp_file, os.path.join(base_dir, "include/rpcs_s_behaviors_workflow"))
        file_name = os.path.basename(hpp_file)
        cpp_name = os.path.splitext(file_name)[0] + ".cpp"
        
        # 尝试在plugins目录下找到对应的cpp文件
        plugin_type = os.path.basename(os.path.dirname(hpp_file))  # action, condition, control
        cpp_file = os.path.join(base_dir, "plugins", plugin_type, cpp_name)
        
        if os.path.exists(cpp_file):
            print(f"处理文件对: {os.path.basename(hpp_file)} -> {os.path.basename(cpp_file)}")
            if update_cpp_file(cpp_file, hpp_file):
                modified_count += 1
        else:
            print(f"找不到对应的cpp文件: {hpp_file} -> {cpp_file}")
    
    print(f"共同步了 {modified_count} 个文件")

if __name__ == "__main__":
    sync_hpp_cpp_ports() 