#!/usr/bin/env python3
"""
迁移行为树文件到新的目录结构

该脚本将旧的扁平目录结构下的行为树文件迁移到新的层次化目录结构。
"""

import os
import sys
import shutil
import yaml
import argparse
from pathlib import Path

def parse_args():
    parser = argparse.ArgumentParser(description='迁移行为树文件到新的目录结构')
    parser.add_argument('--config', type=str, required=True, 
                        help='原始配置文件路径')
    parser.add_argument('--new-config', type=str, required=True, 
                        help='新配置文件路径')
    parser.add_argument('--source-dir', type=str, required=True, 
                        help='原始行为树目录')
    parser.add_argument('--target-dir', type=str, required=True, 
                        help='目标行为树目录')
    parser.add_argument('--copy', action='store_true', default=False,
                        help='复制文件而不是移动')
    parser.add_argument('--dry-run', action='store_true', default=False,
                        help='仅打印将要执行的操作，不实际执行')
    return parser.parse_args()

def load_yaml(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def load_old_config(config_path):
    """加载旧的配置文件格式"""
    try:
        config = load_yaml(config_path)
        return config
    except Exception as e:
        print(f"加载旧配置文件失败: {e}")
        return None

def create_new_config(old_config):
    """根据旧配置创建新配置"""
    new_config = {"mapping": {}}
    
    # 定义映射规则
    # 可以根据命名规则或其他模式来分类
    process_type_mappings = {
        # Robot1的工艺类型映射
        "Robot1": {
            "LightBoard": "LightPanelSplicing",  # 灯板相关工艺
            "Material": "MaterialTransfer",      # 物料转移相关工艺
            "Motor": "Testing",                  # 测试相关工艺
            "Io": "Testing",                     # 测试相关工艺
            "stack": "LightPanelSplicing",       # 灯板相关工艺
            "Remove": "LightPanelSplicing",      # 灯板相关工艺
            "Bond": "LightPanelSplicing",        # 灯板相关工艺
            "arm_test": "Testing",               # 测试相关工艺
            "Example": "VisionAlignment",        # 视觉对齐示例
            "camera": "VisionAlignment",         # 视觉相关工艺
            "image": "VisionAlignment",          # 视觉相关工艺
        },
        # Robot2的工艺类型映射
        "Robot2": {
            "LightBoard": "LightPanelSplicing",  # 灯板相关工艺
            "Material": "MaterialTransfer",      # 物料转移相关工艺
            "move_wip": "PcbaProcessing",        # PCBA处理工艺
            "place": "PcbaProcessing",           # PCBA处理工艺
            "adapter": "PcbaProcessing",         # PCBA处理工艺
            "open_fpc": "PcbaProcessing",        # PCBA处理工艺
            "transfer": "PcbaProcessing",        # PCBA处理工艺
            "Io": "Testing",                     # 测试相关工艺
        },
        # Robot3的工艺类型映射
        "Robot3": {
            "LightBoard": "LightPanelSplicing",  # 灯板相关工艺
            "ProCase": "ProtectiveCase",         # 保护壳相关工艺
            "screw": "ProtectiveCase",           # 保护壳相关工艺
            "connect": "ProtectiveCase",         # 保护壳相关工艺
            "fasten": "ProtectiveCase",          # 保护壳相关工艺
        }
    }
    
    # 遍历旧配置
    for robot_id, actions in old_config.items():
        if robot_id not in new_config["mapping"]:
            new_config["mapping"][robot_id] = {}
        
        # 处理每个动作映射
        for action_type, file_path in actions.items():
            # 确定工艺类型
            process_type = None
            for key, value in process_type_mappings.get(robot_id, {}).items():
                if key in action_type or key in file_path.lower():
                    process_type = value
                    break
            
            # 如果没有找到匹配的工艺类型，使用默认值
            if process_type is None:
                process_type = "Other"
            
            # 确保工艺类型存在于配置中
            if process_type not in new_config["mapping"][robot_id]:
                new_config["mapping"][robot_id][process_type] = {}
            
            # 创建新的文件名（不包含机器人ID前缀）
            file_name = file_path
            if file_path.startswith(f"{robot_id}_"):
                file_name = file_path.replace(f"{robot_id}_", "", 1)
            elif file_path.startswith(f"{robot_id.lower()}_"):
                file_name = file_path.replace(f"{robot_id.lower()}_", "", 1)
            
            # 添加到新配置
            new_config["mapping"][robot_id][process_type][action_type] = file_name
    
    return new_config

def create_directory_structure(target_dir, config):
    """创建新的目录结构"""
    for robot_id, processes in config["mapping"].items():
        for process_type in processes.keys():
            dir_path = os.path.join(target_dir, robot_id, process_type)
            os.makedirs(dir_path, exist_ok=True)
            print(f"创建目录: {dir_path}")

def migrate_files(old_config, new_config, source_dir, target_dir, copy=False, dry_run=False):
    """迁移文件到新的目录结构"""
    for robot_id, processes in new_config["mapping"].items():
        for process_type, actions in processes.items():
            for action_type, new_file_name in actions.items():
                # 获取旧文件路径
                old_file_name = old_config.get(robot_id, {}).get(action_type)
                if not old_file_name:
                    print(f"警告: 在旧配置中找不到映射: {robot_id}.{action_type}")
                    continue
                
                old_file_path = os.path.join(source_dir, old_file_name)
                if not os.path.exists(old_file_path):
                    print(f"警告: 文件不存在: {old_file_path}")
                    continue
                
                # 新文件路径
                new_file_path = os.path.join(target_dir, robot_id, process_type, new_file_name)
                
                # 打印操作
                op = "复制" if copy else "移动"
                print(f"{op}文件: {old_file_path} -> {new_file_path}")
                
                if not dry_run:
                    # 执行复制或移动操作
                    if copy:
                        shutil.copy2(old_file_path, new_file_path)
                    else:
                        shutil.move(old_file_path, new_file_path)

def save_new_config(config, file_path, dry_run=False):
    """保存新的配置文件"""
    print(f"保存新配置到: {file_path}")
    if not dry_run:
        with open(file_path, 'w', encoding='utf-8') as file:
            yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

def main():
    args = parse_args()
    
    # 加载旧配置
    old_config = load_old_config(args.config)
    if old_config is None:
        sys.exit(1)
    
    # 创建新配置
    new_config = create_new_config(old_config)
    
    # 创建目录结构
    if not args.dry_run:
        create_directory_structure(args.target_dir, new_config)
    
    # 迁移文件
    migrate_files(old_config, new_config, args.source_dir, args.target_dir, args.copy, args.dry_run)
    
    # 保存新配置
    save_new_config(new_config, args.new_config, args.dry_run)
    
    print("迁移完成!")

if __name__ == "__main__":
    main() 