#!/bin/bash

# RPCS ProcessActionServer 启动脚本 (使用层次化目录结构)
# 使用方法: ./start_process_action_server_hierarchical.sh [robot_id]

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取工作空间根目录
WORKSPACE_DIR="$(cd "${SCRIPT_DIR}/../../../.." && pwd)"

# 默认机器人ID
ROBOT_ID=${1:-Robot1}

echo "🚀 启动 RPCS ProcessActionServer (层次化目录结构)"
echo "   工作空间: $WORKSPACE_DIR"
echo "   机器人ID: $ROBOT_ID"

# 检查可执行文件是否存在
EXECUTABLE="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/lib/rpcs_s_behaviors_workflow/process_action_server"
if [ ! -f "$EXECUTABLE" ]; then
    echo "❌ 错误: 可执行文件不存在: $EXECUTABLE"
    echo "   请先运行: colcon build --packages-select rpcs_s_behaviors_workflow"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_action_mapping.yaml"
HIERARCHICAL_CONFIG_FILE="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_action_mapping_hierarchical.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    echo "   配置文件应该位于: $CONFIG_FILE"
    exit 1
fi

# 检查层次化配置文件是否存在，如果不存在则复制
if [ ! -f "$HIERARCHICAL_CONFIG_FILE" ]; then
    echo "⚠️ 警告: 层次化配置文件不存在: $HIERARCHICAL_CONFIG_FILE"
    echo "   将从源代码目录复制配置文件..."
    
    SRC_CONFIG="$WORKSPACE_DIR/src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/config/process_action_mapping_hierarchical.yaml"
    if [ -f "$SRC_CONFIG" ]; then
        cp "$SRC_CONFIG" "$HIERARCHICAL_CONFIG_FILE"
        echo "✅ 已复制配置文件"
    else
        echo "❌ 错误: 源配置文件不存在: $SRC_CONFIG"
        exit 1
    fi
fi

# 检查工艺树目录是否存在
TREE_PATH="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_trees"
if [ ! -d "$TREE_PATH" ]; then
    echo "❌ 错误: 工艺树目录不存在: $TREE_PATH"
    echo "   请确保已安装配置文件"
    exit 1
fi

# 设置库路径，让进程能找到插件库
PLUGIN_PATH="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/lib/rpcs_s_behaviors_workflow"
export LD_LIBRARY_PATH="$PLUGIN_PATH:$LD_LIBRARY_PATH"

echo "✅ 环境检查完成"
echo "   LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

# 启动服务器
echo "🎯 启动 ProcessActionServer (使用层次化配置)..."
source "$WORKSPACE_DIR/install/setup.bash"
exec "$EXECUTABLE" \
  --ros-args \
  -r __node:=${ROBOT_ID}_process_action_server \
  -p robot_id:="$ROBOT_ID" \
  -p config_file:="$CONFIG_FILE" \
  -p tree_file_path:="$TREE_PATH" \
  -p max_concurrent_actions:=2 \
  -p default_timeout:=300 \
  -p groot2_port:=1670 \
  -p hierarchical_config_file:="$HIERARCHICAL_CONFIG_FILE" 