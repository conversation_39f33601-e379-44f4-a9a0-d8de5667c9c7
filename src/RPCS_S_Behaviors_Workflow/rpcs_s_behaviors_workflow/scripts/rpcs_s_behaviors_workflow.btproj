<?xml version="1.0" ?>
<root BTCPP_format="4" project_name="RPCS_Behavior_Tree_Project">
    <!-- Auto-generated Node Models for Groot2 Editor -->
    <TreeNodesModel>
        <!-- Action Nodes -->
        <Action ID="AgvGoPoint" editable="true">
            <input_port name="intTimeoutMs" default="30000" description="超时时间(毫秒)"/>
            <input_port name="strGoPointName" default="" description="目标点名称"/>
            <input_port name="strNamespace" default="&quot;/Robot1&quot;" description="/Robot1"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strOutputResponse" description="完整响应数据"/>
        </Action>
        <Action ID="AgvMaterial" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strMaterialType" default="" description="物料类型：LIGHT_BOARD-灯板，PCBA，PROTECT_SHELL-保护壳"/>
            <output_port name="boolExist" description="物料是否存在"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="BoardAlign" editable="true">
            <input_port name="intBoardNum" default="0" description="贴砖号"/>
            <input_port name="strParamOverrides" default="&quot;{" description="字符串: strParamOverrides"/>
        </Action>
        <Action ID="CleanStatus" editable="true">
            <output_port name="strAllstatus" description="字符串: strAllstatus"/>
        </Action>
        <Action ID="CommonAlign" editable="true">
            <input_port name="doublePixelDimensions" default="0.1" description="像素尺寸"/>
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strHWC" default="&quot;test&quot;" description="test"/>
            <input_port name="strModelPath" default="&quot;/test&quot;" description="/test"/>
            <input_port name="strParamOverrides" default="&quot;&quot;" description="字符串: strParamOverrides"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleRZ" description="旋转角度"/>
            <output_port name="doubleX" description="x坐标"/>
            <output_port name="doubleY" description="y坐标"/>
            <output_port name="strOutputMessage" description="状态消息"/>
        </Action>
        <Action ID="Conveyor" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="CorrectionPosition" editable="true">
            <input_port name="doubleAbsR" default="0.0" description="R轴目标位置"/>
            <input_port name="doubleAbsX" default="0.0" description="X轴目标位置"/>
            <input_port name="doubleAbsY" default="0.0" description="Y轴目标位置"/>
            <input_port name="doubleAbsZ" default="0.0" description="Z轴目标位置"/>
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strCommandType" default="" description="移动类型: ABS/RELA"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="DepthFusion" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strCameraNamespaces" default="&quot;&quot;" description="字符串: strCameraNamespaces"/>
            <input_port name="strInputDirectory" default="&quot;&quot;" description="字符串: strInputDirectory"/>
            <input_port name="strOutputPath" default="" description="融合后图像的保存路径"/>
            <input_port name="strParamOverrides" default="&quot;&quot;" description="字符串: strParamOverrides"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strOutputMessage" description="状态消息"/>
            <output_port name="strOutputPath" description="融合后图像的保存路径"/>
        </Action>
        <Action ID="DigitalInputRead" editable="true">
            <input_port name="boolContinuousRead" default="false" description="是否连续读取模式"/>
            <input_port name="boolExpectedValue" default="false" description="单个期望的输入值"/>
            <input_port name="boolExpectedValues" default="false" description="期望的输入值列表，与地址列表一一对应"/>
            <input_port name="doubleDuration" default="0.0" description="连续读取持续时间(秒)"/>
            <input_port name="doubleReadInterval" default="0.1" description="连续读取间隔(秒)"/>
            <input_port name="intInputAddress" default="0" description="单个要读取的输入地址"/>
            <input_port name="intInputAddresses" default="0" description="对应的地址列表"/>
            <input_port name="intTimeoutMs" default="5000" description="超时时间(毫秒)"/>
            <input_port name="strBlackboardKey" default="&quot;&quot;" description="黑板变量名"/>
            <input_port name="strDeviceId" default="&quot;robot1&quot;" description="robot1"/>
            <output_port name="boolCurrentValue" description="单个地址的当前值"/>
            <output_port name="boolCurrentValues" description="当前读取到的输入值列表"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolValueMatched" description="每个地址的值是否匹配期望值"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="intInputAddresses" description="对应的地址列表"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="DigitalOutputRead" editable="true">
            <input_port name="boolContinuousRead" default="false" description="是否连续读取模式"/>
            <input_port name="boolExpectedValues" default="false" description="期望的输出值列表，与地址列表一一对应"/>
            <input_port name="doubleDuration" default="0.0" description="连续读取持续时间(秒)"/>
            <input_port name="doubleReadInterval" default="0.1" description="连续读取间隔(秒)"/>
            <input_port name="intOutputAddresses" default="0" description="对应的地址列表"/>
            <input_port name="intTimeoutMs" default="5000" description="超时时间(毫秒)"/>
            <input_port name="strBlackboardKey" default="&quot;&quot;" description="黑板变量名"/>
            <input_port name="strDeviceId" default="&quot;robot1&quot;" description="robot1"/>
            <output_port name="boolCurrentValue" description="单个地址的当前值"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolOutputValues" description="读取到的输出值列表"/>
            <output_port name="boolValueMatched" description="每个地址的值是否匹配期望值"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="intOutputAddresses" description="对应的地址列表"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="DigitalOutputWrite" editable="true">
            <input_port name="boolBatchOperation" default="false" description="是否使用批量操作模式"/>
            <input_port name="boolOutputValues" default="false" description="要写入的输出值列表，必须与地址数量一致"/>
            <input_port name="boolVerifyWrite" default="true" description="是否验证写入结果"/>
            <input_port name="intOutputAddresses" default="0" description="对应的地址列表"/>
            <input_port name="intTimeoutMs" default="5000" description="超时时间(毫秒)"/>
            <input_port name="strDeviceId" default="&quot;robot1&quot;" description="robot1"/>
            <output_port name="boolFinalValues" description="最终的输出值列表"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolWriteSuccesses" description="每个地址的写入是否成功"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="intOutputAddresses" description="对应的地址列表"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="FPCAdapterNumber" editable="true">
            <input_port name="strType" default="&quot;&quot;" description="字符串: strType"/>
            <output_port name="intFPCAdapterNumber" description="获取当前需要操作的FPC转接线是第几个"/>
        </Action>
        <Action ID="FpcOperate" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strOperation" default="" description="FPC操作类型"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="GetActionParameters" editable="true">
            <input_port name="intExtendIndex" default="0" description="要获取的扩展信息索引 (0-based)"/>
            <input_port name="intParameterIndex" default="0" description="要获取的参数索引 (0-based)"/>
            <input_port name="strExtendKey" default="" description="要获取的扩展信息键名"/>
            <input_port name="strParameterKey" default="" description="要获取的特定参数键名"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolPreemptCurrent" description="是否抢占当前任务"/>
            <output_port name="intExtendCount" description="扩展信息总数"/>
            <output_port name="intParameterCount" description="参数总数"/>
            <output_port name="intTimeoutSeconds" description="超时时间"/>
            <output_port name="strExtendKeyOut" description="获取到的扩展信息键名"/>
            <output_port name="strExtendValue" description="获取到的扩展信息值"/>
            <output_port name="strOutputMessage" description="输出消息"/>
            <output_port name="strParameterList" description="完整的参数列表"/>
            <output_port name="strParameterValue" description="获取到的参数值"/>
            <output_port name="strProcessId" description="工艺流程ID"/>
            <output_port name="strProcessType" description="工艺动作类型"/>
            <output_port name="strRobotId" description="机器人ID"/>
        </Action>
        <Action ID="ImageDetection" editable="true">
            <input_port name="intTimeoutMs" default="10000" description="超时时间(毫秒)"/>
            <input_port name="strCameraGroup" default="&quot;&quot;" description="字符串: strCameraGroup"/>
            <input_port name="strCameraIp" default="&quot;&quot;" description="字符串: strCameraIp"/>
            <input_port name="strDetectType" default="&quot;ROBOT_ALIGN&quot;" description="ROBOT_ALIGN"/>
            <input_port name="strNamespace" default="&quot;/Robot1&quot;" description="/Robot1"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleResultRz" description="检测到的旋转角度偏差"/>
            <output_port name="doubleResultX" description="检测到的X坐标偏差"/>
            <output_port name="doubleResultY" description="检测到的Y坐标偏差"/>
            <output_port name="strDetectionSummary" description="检测结果摘要"/>
            <output_port name="strOutputErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strResultCameraIp" description="结果相机IP地址"/>
        </Action>
        <Action ID="ImageDetectionConversion" editable="true">
            <input_port name="compensateX" default="3.402823e+38" description="x坐标(double)"/>
            <input_port name="compensateY" default="3.402823e+38" description="y坐标(double)"/>
            <input_port name="doubleRZ" default="3.402823e+38" description="旋转角度(double)"/>
            <input_port name="doubleX" default="3.402823e+38" description="x坐标(double)"/>
            <input_port name="doubleY" default="3.402823e+38" description="y坐标(double)"/>
            <input_port name="floatRZ" default="3.402823e+38f" description="旋转角度(float)"/>
            <input_port name="floatX" default="3.402823e+38f" description="x坐标(float)"/>
            <input_port name="floatY" default="3.402823e+38f" description="y坐标(float)"/>
            <input_port name="strType" default="" description="操作类型，格式：\"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="image_detection_rz" description="处理后的旋转角度(float)"/>
            <output_port name="image_detection_x" description="处理后的X坐标(float)"/>
            <output_port name="image_detection_y" description="处理后的Y坐标(float)"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strOutputResponse" description="完整响应数据"/>
        </Action>
        <Action ID="ImageDetectionToZero" editable="true">
            <input_port name="camera_group" default="&quot;&quot;" description="参数: camera_group"/>
            <input_port name="camera_ip" default="&quot;&quot;" description="参数: camera_ip"/>
            <input_port name="detect_type" default="&quot;ROBOT_ALIGN&quot;" description="ROBOT_ALIGN"/>
            <input_port name="intTimeoutMs" default="60000" description="超时时间(毫秒)"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="image_detection_rz" description="旋转角度"/>
            <output_port name="image_detection_x" description="X坐标"/>
            <output_port name="image_detection_y" description="Y坐标"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strOutputResponse" description="完整响应数据"/>
        </Action>
        <Action ID="MaterialState" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strMaterialType" default="" description="物料类型：LIGHT_BOARD-玻璃板到位，FPC-FPC托盘到位，EMPTY_FPC-空FPC托盘到位，NAIL-钉子到位"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="MaterialUpload" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strUploadType" default="" description="上料类型：LIGHT_BOARD-玻璃板上料，FPC-FPC转接线上料，PRODUCT_TRAY-成品Tray上料，EMPTY_FPC_CONVERTER_TRAY-空FPC转换器Tray下料"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="MotorHoming" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(s)"/>
            <input_port name="floatSpeedSwitch" default="20.0" description="开关搜索速度(rpm)"/>
            <input_port name="floatSpeedZero" default="20.0" description="零点搜索速度(rpm)"/>
            <input_port name="intHomeOffset" default="0" description="原点偏移量(mm)"/>
            <input_port name="intHomingMethod" default="17" description="寻找原点方法(1-35)"/>
            <input_port name="intMotorId" default="0" description="电机ID编号"/>
            <input_port name="intPositionWindow" default="100" description="位置窗口(inc)"/>
            <input_port name="intPositionWindowTime" default="1000" description="位置窗口时间(ms)"/>
            <input_port name="strDeviceId" default="&quot;Robot1&quot;" description="Robot1"/>
            <input_port name="strMotorBrand" default="&quot;Kinco&quot;" description="Kinco"/>
            <output_port name="boolOutputSuccess" description="回零是否成功"/>
            <output_port name="floatCurrentPosition" description="当前位置(mm)"/>
            <output_port name="floatFinalPosition" description="最终位置(mm)"/>
            <output_port name="intCurrentStatus" description="当前状态字"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strStatusDescription" description="状态描述"/>
        </Action>
        <Action ID="MotorPositionControl" editable="true">
            <input_port name="boolAbsolutePosition" default="true" description="位置模式：true=绝对位置，false=相对位置"/>
            <input_port name="doubleAcceleration" default="100.0" description="加速度(mm/s²)"/>
            <input_port name="doubleDeceleration" default="100.0" description="减速度(mm/s²)"/>
            <input_port name="doubleDwellTime" default="1.0" description="到达后停留时间(s)"/>
            <input_port name="doubleMaxVelocity" default="50.0" description="最大速度(mm/s)"/>
            <input_port name="doubleTargetPosition" default="0.0" description="目标位置(mm)"/>
            <input_port name="doubleTimeout" default="15.0" description="超时时间(s)"/>
            <input_port name="intMotorId" default="0" description="电机ID编号"/>
            <input_port name="strDeviceId" default="&quot;Robot1&quot;" description="Robot1"/>
            <input_port name="strMotorBrand" default="&quot;Kinco&quot;" description="Kinco"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleCurrentPosition" description="当前位置(mm)"/>
            <output_port name="doubleFinalPosition" description="最终位置(mm)"/>
            <output_port name="doublePositionError" description="位置误差(mm)"/>
            <output_port name="doubleProgress" description="完成进度(0.0-1.0)"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="MotorTorqueControl" editable="true">
            <input_port name="boolUsePositionLimits" default="false" description="是否使用位置限制"/>
            <input_port name="doubleDuration" default="0.0" description="运行时间(s)，0表示持续运行"/>
            <input_port name="doubleMaxPosition" default="1000.0" description="最大位置限制(mm)"/>
            <input_port name="doubleMinPosition" default="-1000.0" description="最小位置限制(mm)"/>
            <input_port name="doubleTargetTorque" default="0.0" description="目标转矩(Nm)"/>
            <input_port name="doubleTimeout" default="10.0" description="超时时间(s)"/>
            <input_port name="doubleTorqueSlope" default="1.0" description="转矩变化斜率(Nm/s)"/>
            <input_port name="doubleVelocityLimit" default="30.0" description="最大速度限制(mm/s)"/>
            <input_port name="intMotorId" default="0" description="电机ID编号"/>
            <input_port name="strDeviceId" default="&quot;Robot1&quot;" description="Robot1"/>
            <input_port name="strMotorBrand" default="&quot;Kinco&quot;" description="Kinco"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleCurrentTorque" description="当前转矩(Nm)"/>
            <output_port name="doubleElapsedTime" description="已运行时间(s)"/>
            <output_port name="doubleFinalPosition" description="最终位置(mm)"/>
            <output_port name="doubleFinalTorque" description="最终转矩(Nm)"/>
            <output_port name="doubleFinalVelocity" description="最终速度(mm/s)"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="MotorVelocityControl" editable="true">
            <input_port name="boolStopSignal" default="false" description="停止信号，当为true时立即停止"/>
            <input_port name="boolUsePositionLimits" default="false" description="是否使用位置限制"/>
            <input_port name="doubleAcceleration" default="100.0" description="加速度(rps/s)"/>
            <input_port name="doubleDeceleration" default="100.0" description="减速度(rps/s)"/>
            <input_port name="doubleDuration" default="0.0" description="运行时间(s)，0表示持续运行"/>
            <input_port name="doubleMaxPosition" default="1000.0" description="最大位置限制(mm)"/>
            <input_port name="doubleMinPosition" default="-1000.0" description="最小位置限制(mm)"/>
            <input_port name="doubleTargetCurrentLimit" default="20.0" description="目标电流限制(A)，转换系数42.67"/>
            <input_port name="doubleTargetVelocity" default="0.0" description="目标速度(rpm)"/>
            <input_port name="doubleTimeout" default="10.0" description="超时时间(s)"/>
            <input_port name="intMotorId" default="0" description="电机ID编号"/>
            <input_port name="strDeviceId" default="&quot;Robot1&quot;" description="Robot1"/>
            <input_port name="strMotorBrand" default="&quot;Kinco&quot;" description="Kinco"/>
            <input_port name="strStopSignalKey" default="&quot;&quot;" description="字符串: strStopSignalKey"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleCurrentVelocity" description="当前速度(rpm)"/>
            <output_port name="doubleElapsedTime" description="已运行时间(s)"/>
            <output_port name="doubleFinalPosition" description="最终位置(mm)"/>
            <output_port name="doubleFinalVelocity" description="最终速度(rpm)"/>
            <output_port name="intErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
        </Action>
        <Action ID="PetStrip" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strOperation" default="" description="PET条带操作类型"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="PressHoldBoard" editable="true">
            <input_port name="doublePressure" default="0.0" description="压力值(0-50)"/>
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="ProductPowerOnDetect" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strStep" default="" description="检测步骤：ON_CHECK-移动到检测台，PLUGIN-插接线，PULLOUT-拔出，NG_UNLOAD-ng品下料，OK_UNLOAD-成品下料，GET_RESULT-获取结果，AWAIT_RESULT-等待结果"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolResult" description="成品检测结果：true-ok，false-ng"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="ProtectShell" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="PubPrintMessage" editable="true">
            <input_port name="strPrintMessage" default="" description="消息内容"/>
            <input_port name="strTopicName" default="" description="字符串: strTopicName"/>
        </Action>
        <Action ID="PubProcessFeedback" editable="true">
            <input_port name="doubleProgress" default="0.0" description="工艺执行进度百分比 (0.0-100.0)"/>
            <input_port name="strMessage" default="" description="状态描述信息"/>
            <input_port name="strOperation" default="&quot;当前操作描述&quot;)" description="当前操作描述"/>
            <input_port name="strProcessStep" default="" description="当前执行的工艺步骤名称"/>
            <input_port name="strStatus" default="" description="当前状态 (RUNNING, SUCCESS, FAILURE)"/>
        </Action>
        <Action ID="RobotAlign" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strParamOverrides" default="&quot;&quot;" description="字符串: strParamOverrides"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="doubleRZ" description="旋转角度"/>
            <output_port name="doubleX" description="x坐标"/>
            <output_port name="doubleY" description="y坐标"/>
            <output_port name="strOutputMessage" description="状态消息"/>
        </Action>
        <Action ID="RobotArmControl" editable="true">
            <input_port name="floatPositionX" default="0.0" description="X坐标"/>
            <input_port name="floatPositionY" default="0.0" description="Y坐标"/>
            <input_port name="floatPositionZ" default="0.0" description="Z坐标"/>
            <input_port name="floatRotationRx" default="0.0" description="Rx旋转角度"/>
            <input_port name="floatRotationRy" default="0.0" description="Ry旋转角度"/>
            <input_port name="floatRotationRz" default="0.0" description="Rz旋转角度"/>
            <input_port name="intFunctionData0" default="0" description="功能数据0(夹爪控制: 0=开启, 1=关闭)"/>
            <input_port name="intFunctionData1" default="0" description="功能数据1(工具激活: 0=禁用, 1=启用)"/>
            <input_port name="intFunctionData2" default="0" description="功能数据2(传送带控制: 0=停止, 1=启动)"/>
            <input_port name="intFunctionData3" default="0" description="功能数据3(检测功能: 0=关闭, 1=开启)"/>
            <input_port name="intFunctionData4" default="0" description="功能数据4(自定义功能1)"/>
            <input_port name="intFunctionData5" default="0" description="功能数据5(自定义功能2)"/>
            <input_port name="intProjectId" default="1" description="工程ID"/>
            <input_port name="intSpeedMultiplier" default="100" description="速度倍率(1-100)"/>
            <input_port name="intTimeoutMs" default="60000" description="超时时间(毫秒)"/>
            <input_port name="strDeviceName" default="&quot;/robot1&quot;" description="/robot1"/>
            <output_port name="boolEmergencyStop" description="急停状态"/>
            <output_port name="boolErrorStatus" description="错误状态"/>
            <output_port name="boolMotorPowerStatus" description="电机电源状态"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="boolProgramClearRequest" description="程序清求状态"/>
            <output_port name="boolProgramPaused" description="程序暂停状态"/>
            <output_port name="boolProgramRunning" description="程序运行状态"/>
            <output_port name="boolRemoteModeEnabled" description="远程模式启用状态"/>
            <output_port name="doubleExecutionTime" description="执行时间(秒)"/>
            <output_port name="doubleProgressPercentage" description="执行进度百分比"/>
            <output_port name="floatCurrentRx" description="当前Rx角度"/>
            <output_port name="floatCurrentRy" description="当前Ry角度"/>
            <output_port name="floatCurrentRz" description="当前Rz角度"/>
            <output_port name="floatCurrentX" description="当前X坐标"/>
            <output_port name="floatCurrentY" description="当前Y坐标"/>
            <output_port name="floatCurrentZ" description="当前Z坐标"/>
            <output_port name="intProjectIdStatus" description="工程ID状态"/>
            <output_port name="strCurrentStatus" description="当前状态描述"/>
            <output_port name="strOutputErrorMessage" description="错误信息"/>
        </Action>
        <Action ID="SemiFinishedProduct" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strOperation" default="" description="半成品操作类型"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="SetProgress" editable="true">
            <input_port name="progress" default="&quot;Progress value (0-100)&quot;)" description="Progress value (0-100)"/>
        </Action>
        <Action ID="Sucker" editable="true">
            <input_port name="boolSuckerState" default="false" description="吸盘控制状态，true=开启，false=关闭"/>
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="intSuckerId" default="0" description="吸盘序号"/>
            <input_port name="strSuckerIds" default="" description="吸盘序号列表，逗号分隔"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <Action ID="VehiclesAlign" editable="true">
            <input_port name="strImagePathA" default="" description="第一张图像的文件路径"/>
            <input_port name="strImagePathB" default="" description="第二张图像的文件路径"/>
            <input_port name="strParamOverrides" default="&quot;{" description="字符串: strParamOverrides"/>
        </Action>
        <Action ID="Wait" editable="true">
            <input_port name="intMsec" default="&quot;等待时间(毫秒)&quot;)" description="等待时间(毫秒)"/>
        </Action>
        <Action ID="Workbench" editable="true">
            <input_port name="doubleTimeout" default="30.0" description="超时时间(秒)"/>
            <input_port name="strOperation" default="" description="工作台操作类型"/>
            <output_port name="boolOutputSuccess" description="操作是否成功"/>
            <output_port name="strErrorCode" description="错误代码"/>
            <output_port name="strOutputMessage" description="结果消息"/>
            <output_port name="strPlcFeedback" description="PLC反馈信息"/>
        </Action>
        <!-- Condition Nodes -->
        <Condition ID="SensorValueCheck" editable="true">
            <input_port name="expected_value" default="true" description="期望值"/>
            <input_port name="sensor_value" default="" description="传感器值"/>
        </Condition>
        <Condition ID="SensorValueCheckInt" editable="true">
            <input_port name="expected_value" default="1" description="期望值"/>
            <input_port name="sensor_value" default="" description="传感器值"/>
        </Condition>
    </TreeNodesModel>
</root>