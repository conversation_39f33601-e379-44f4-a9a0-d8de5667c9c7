#!/bin/bash
# 自动生成的环境设置脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# source ROS2环境（如果还没有）
if [ -z "$ROS_DISTRO" ]; then
    echo "正在设置ROS2环境..."
    source /opt/ros/humble/setup.bash
fi

# source工作空间环境
if [ -f "$SCRIPT_DIR/install/setup.bash" ]; then
    echo "正在设置工作空间环境..."
    source "$SCRIPT_DIR/install/setup.bash"
    
    # 确保AGV接口库路径被包含
    if [ -d "$HOME/workspace/RPCS_M_Controllers_interfaces/install/rpcs_s_interfaces_agv" ]; then
        echo "正在添加AGV接口库路径..."
        export LD_LIBRARY_PATH="$HOME/workspace/RPCS_M_Controllers_interfaces/install/rpcs_s_interfaces_agv/lib:$SCRIPT_DIR/install/rpcs_s_interfaces_agv/lib:$LD_LIBRARY_PATH"
    fi
    
    echo "✅ 环境设置完成！"
    echo "🤖 可用的包:"
    ros2 pkg list | grep -E "(rpcs|RPCS)" | sort
else
    echo "❌ 未找到install/setup.bash，请先编译项目"
fi
