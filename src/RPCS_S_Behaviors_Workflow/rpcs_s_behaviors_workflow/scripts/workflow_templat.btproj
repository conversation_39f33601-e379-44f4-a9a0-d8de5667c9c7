<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4" project_name="Project">
    <!-- Description of Node Models (used by Groot) -->
    <TreeNodesModel>
        <condition ID="CheckSignalLineInsertion" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="HasBackPanel" editable="true"/>
        <condition ID="CheckLightBoardAssembly" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="CheckPowerLineInsertion" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="HasLightBoard" editable="true"/>
        <condition ID="HasPCBBoard" editable="true"/>
        <condition ID="CheckPCBBoardInstallation" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="CheckFPCLineInsertion" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="HasSignalLine" editable="true"/>
        <condition ID="HasPowerLine" editable="true"/>
        <condition ID="CheckBackPanelScrewing" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="HasFPCLine" editable="true"/>
        <condition ID="HasGlassSubstrate" editable="true"/>
        <condition ID="CheckPCBBoardScrewing" editable="true">
            <input_port name="boolChecked" default=""/>
        </condition>
        <condition ID="CheckGlassSubstrateAttachment" editable="true">
            <output_port name="boolChecked" default=""/>
        </condition>
        <condition ID="HasScrews" editable="true"/>
        <control ID="Retry" editable="true"/>
        <base ID="BaseStatefulActionNode" editable="true"/>
        <action ID="CleanStatus" editable="true">
            <output_port name="strAllstatus" default=""/>
        </action>
        <action ID="pubRobotStatus" editable="true">
            <input_port name="strTopicName" default=""/>
            <input_port name="strStatusMessage" default=""/>
        </action>
        <action ID="DoLightBoardAssembly" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="DoSignalLineInsertion" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="PubPrintMessage" editable="true">
            <input_port name="strTopicName" default=""/>
            <input_port name="strPrintMessage" default=""/>
        </action>
        <action ID="DoPowerLineInsertion" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="DoBackPanelScrewing" editable="true">
            <output_port name="strStrdoen" default=""/>
        </action>
        <action ID="DoPCBBoardInstallation" editable="true">
            <input_port name="boolDone" default=""/>
        </action>
        <action ID="SubMaterialStatus" editable="true">
            <input_port name="strTopicName" default=""/>
            <output_port name="strBackcovermaterialNum" default=""/>
            <output_port name="strDoublesidedadhesivematerialNum" default=""/>
            <output_port name="strFpcmaterialNum" default=""/>
            <output_port name="strLedboardmaterialNum" default=""/>
            <output_port name="strPcbmaterialNum" default=""/>
            <output_port name="strScrewmaterialNum" default=""/>
        </action>
        <action ID="SetProgress" editable="true">
            <input_port name="progress" default=""/>
        </action>
        <action ID="DoGlassSubstrateAttachment" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="DoPCBBoardScrewing" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="DoFPCLineInsertion" editable="true">
            <output_port name="boolDone" default=""/>
        </action>
        <action ID="SubRobotProperties" editable="true">
            <input_port name="strTopicName" default=""/>
            <output_port name="strPropertiesMessage" default=""/>
        </action>
    </TreeNodesModel>
</root>
