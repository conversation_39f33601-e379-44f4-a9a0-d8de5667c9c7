#!/bin/bash

# Action扩展信息简化测试脚本

echo "=== Action扩展信息简化测试 ==="
echo "使用简化版本的行为树进行测试"
echo ""

# 测试场景: 基本扩展信息
echo "--- 发送包含3个扩展信息的测试请求 ---"
ros2 action send_goal /R03001010002/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'Test',
        robot_id: 'R03001010002',
        process_id: 'SIMPLE_TEST_001',
        timeout_seconds: 60,
        preempt_current: true,
        extends: [
            {key: 'product_type', value: 'TYPE_SIMPLE'},
            {key: 'quality_level', value: 'HIGH'},
            {key: 'station', value: 'STATION_TEST'}
        ]
    }"

echo ""
echo "测试请求已发送，请查看process_action_server的输出日志"
