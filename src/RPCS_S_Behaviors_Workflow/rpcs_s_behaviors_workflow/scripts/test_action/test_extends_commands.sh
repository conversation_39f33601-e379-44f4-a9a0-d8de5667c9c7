#!/bin/bash

# Action扩展信息自动识别测试命令脚本
# 使用ros2 action命令行工具进行快速测试

echo "=== Action扩展信息自动识别测试 ==="
echo "请确保process_action_server已启动："
echo "ros2 run rpcs_s_behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1"
echo ""

# 测试场景1: 基本产品信息
echo "--- 测试场景1: 基本产品信息 ---"
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'TEST_BASIC_001',
        timeout_seconds: 120,
        preempt_current: true,
        extends: [
            {key: 'product_type', value: 'TYPE_A'},
            {key: 'quality_level', value: 'HIGH'},
            {key: 'station', value: 'STATION_001'}
        ]
    }"

echo ""
echo "等待5秒后开始下一个测试..."
sleep 5

# 测试场景2: 完整工艺信息
echo "--- 测试场景2: 完整工艺信息 ---"
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'TEST_FULL_002',
        timeout_seconds: 120,
        preempt_current: true,
        extends: [
            {key: 'product_type', value: 'TYPE_B'},
            {key: 'quality_level', value: 'MEDIUM'},
            {key: 'station', value: 'STATION_002'},
            {key: 'batch_id', value: 'BATCH_20240729_001'},
            {key: 'operator', value: 'OPERATOR_ZHANG'}
        ]
    }"

echo ""
echo "等待5秒后开始下一个测试..."
sleep 5

# 测试场景3: 数值类型扩展信息
echo "--- 测试场景3: 数值类型扩展信息 ---"
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'TEST_NUMERIC_003',
        timeout_seconds: 120,
        preempt_current: true,
        extends: [
            {key: 'temperature', value: '25.5'},
            {key: 'pressure', value: '1013.25'},
            {key: 'humidity', value: '45'},
            {key: 'cycle_count', value: '100'},
            {key: 'is_calibrated', value: 'true'}
        ]
    }"

echo ""
echo "等待5秒后开始下一个测试..."
sleep 5

# 测试场景4: 单个扩展信息
echo "--- 测试场景4: 单个扩展信息 ---"
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'TEST_SINGLE_004',
        timeout_seconds: 120,
        preempt_current: true,
        extends: [
            {key: 'test_mode', value: 'DEBUG'}
        ]
    }"

echo ""
echo "等待5秒后开始下一个测试..."
sleep 5

# 测试场景5: 空扩展信息
echo "--- 测试场景5: 空扩展信息 ---"
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'TEST_EMPTY_005',
        timeout_seconds: 120,
        preempt_current: true,
        extends: []
    }"

echo ""
echo "=== 所有测试场景已发送完成 ==="
echo "请查看process_action_server的日志输出以验证测试结果"
