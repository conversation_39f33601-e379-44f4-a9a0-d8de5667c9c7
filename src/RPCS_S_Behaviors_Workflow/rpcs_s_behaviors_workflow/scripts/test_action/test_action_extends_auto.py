#!/usr/bin/env python3
"""
Action扩展信息自动识别测试脚本
测试不同数量和类型的扩展信息，验证行为树的自动识别能力
"""

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
from rpcs_s_interfaces_behavior_tree.msg import Extend
import time
import sys

class ActionExtendsAutoTester(Node):
    def __init__(self):
        super().__init__('action_extends_auto_tester')
        
        # 创建Action客户端
        self.action_client = ActionClient(
            self, 
            ExecuteProcessAction, 
            '/R03001010002/ExecuteProcessAction'
        )
        
        self.get_logger().info("Action扩展信息自动识别测试器已启动")
    
    def create_test_scenarios(self):
        """创建不同的测试场景"""
        scenarios = []
        
        # 场景1: 基本产品信息
        scenario1 = {
            'name': '基本产品信息',
            'extends': [
                {'key': 'product_type', 'value': 'TYPE_A'},
                {'key': 'quality_level', 'value': 'HIGH'},
                {'key': 'station', 'value': 'STATION_001'}
            ]
        }
        scenarios.append(scenario1)
        
        # 场景2: 完整工艺信息
        scenario2 = {
            'name': '完整工艺信息',
            'extends': [
                {'key': 'product_type', 'value': 'TYPE_B'},
                {'key': 'quality_level', 'value': 'MEDIUM'},
                {'key': 'station', 'value': 'STATION_002'},
                {'key': 'batch_id', 'value': 'BATCH_20240729_001'},
                {'key': 'operator', 'value': 'OPERATOR_ZHANG'}
            ]
        }
        scenarios.append(scenario2)
        
        # 场景3: 数值类型扩展信息
        scenario3 = {
            'name': '数值类型扩展信息',
            'extends': [
                {'key': 'temperature', 'value': '25.5'},
                {'key': 'pressure', 'value': '1013.25'},
                {'key': 'humidity', 'value': '45'},
                {'key': 'cycle_count', 'value': '100'},
                {'key': 'is_calibrated', 'value': 'true'}
            ]
        }
        scenarios.append(scenario3)
        
        # 场景4: 单个扩展信息
        scenario4 = {
            'name': '单个扩展信息',
            'extends': [
                {'key': 'test_mode', 'value': 'DEBUG'}
            ]
        }
        scenarios.append(scenario4)
        
        # 场景5: 空扩展信息
        scenario5 = {
            'name': '空扩展信息',
            'extends': []
        }
        scenarios.append(scenario5)
        
        return scenarios
    
    def create_extend_message(self, key, value):
        """创建Extend消息"""
        extend = Extend()
        extend.key = key
        extend.value = value
        return extend
    
    async def send_goal_and_wait(self, scenario):
        """发送目标并等待结果"""
        self.get_logger().info(f"\n=== 开始测试场景: {scenario['name']} ===")
        
        # 等待Action服务器
        if not self.action_client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error("Action服务器不可用")
            return False
        
        # 创建目标消息
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = 'Test'
        goal_msg.robot_id = 'R03001010002'
        goal_msg.process_id = f'AUTO_TEST_{int(time.time())}'
        goal_msg.timeout_seconds = 120
        goal_msg.preempt_current = True
        
        # 设置扩展信息
        goal_msg.extends = []
        for extend_data in scenario['extends']:
            extend_msg = self.create_extend_message(extend_data['key'], extend_data['value'])
            goal_msg.extends.append(extend_msg)
        
        self.get_logger().info(f"发送扩展信息数量: {len(goal_msg.extends)}")
        for i, extend in enumerate(goal_msg.extends):
            self.get_logger().info(f"  [{i}] {extend.key} = {extend.value}")
        
        # 发送目标
        send_goal_future = self.action_client.send_goal_async(
            goal_msg,
            feedback_callback=self.feedback_callback
        )
        
        try:
            goal_handle = await send_goal_future
            if not goal_handle.accepted:
                self.get_logger().error("目标被拒绝")
                return False
            
            self.get_logger().info("目标已接受，等待结果...")
            
            # 等待结果
            result_future = goal_handle.get_result_async()
            result = await result_future
            
            if result.result.success:
                self.get_logger().info(f"✅ 场景 '{scenario['name']}' 测试成功")
                self.get_logger().info(f"执行时间: {result.result.execution_time:.2f}秒")
                return True
            else:
                self.get_logger().error(f"❌ 场景 '{scenario['name']}' 测试失败: {result.result.result_message}")
                return False
                
        except Exception as e:
            self.get_logger().error(f"❌ 场景 '{scenario['name']}' 执行异常: {str(e)}")
            return False
    
    def feedback_callback(self, feedback_msg):
        """反馈回调函数"""
        feedback = feedback_msg.feedback
        self.get_logger().info(
            f"进度: {feedback.progress_percent:.1f}% | "
            f"步骤: {feedback.current_process_step} | "
            f"状态: {feedback.current_status} | "
            f"操作: {feedback.current_operation}"
        )
    
    async def run_all_tests(self):
        """运行所有测试场景"""
        scenarios = self.create_test_scenarios()
        
        self.get_logger().info(f"\n🚀 开始运行 {len(scenarios)} 个测试场景")
        
        success_count = 0
        total_count = len(scenarios)
        
        for i, scenario in enumerate(scenarios, 1):
            self.get_logger().info(f"\n--- 测试场景 {i}/{total_count} ---")
            
            success = await self.send_goal_and_wait(scenario)
            if success:
                success_count += 1
            
            # 场景间等待
            if i < total_count:
                self.get_logger().info("等待3秒后开始下一个场景...")
                time.sleep(3.0)
        
        # 输出测试总结
        self.get_logger().info(f"\n📊 测试总结:")
        self.get_logger().info(f"总场景数: {total_count}")
        self.get_logger().info(f"成功场景: {success_count}")
        self.get_logger().info(f"失败场景: {total_count - success_count}")
        self.get_logger().info(f"成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            self.get_logger().info("🎉 所有测试场景均通过！")
        else:
            self.get_logger().warning("⚠️  部分测试场景失败，请检查日志")

def main():
    rclpy.init()
    
    tester = ActionExtendsAutoTester()
    
    try:
        # 使用同步方式运行测试
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(tester.run_all_tests())
    except KeyboardInterrupt:
        tester.get_logger().info("测试被用户中断")
    except Exception as e:
        tester.get_logger().error(f"测试过程中发生错误: {str(e)}")
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
