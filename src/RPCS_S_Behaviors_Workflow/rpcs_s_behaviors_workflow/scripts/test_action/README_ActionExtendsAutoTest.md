# Action扩展信息自动识别测试

本目录包含用于验证Action扩展信息自动识别能力的测试文件，可以在不提前知道扩展信息key的情况下，自动识别并验证多种读取方式。

## 文件说明

### 1. `action_extends_auto_test.xml`
- **功能**: 完整版扩展信息测试行为树
- **特点**:
  - 自动获取扩展信息总数
  - 动态遍历所有扩展信息（支持0-4个扩展信息）
  - 验证索引访问、直接访问、GetActionParameters节点三种读取方式
  - 提供详细的测试报告和日志输出
- **状态**: 已修复XML语法问题

### 1.1. `action_extends_simple_test.xml` ⭐ **推荐首次使用**
- **功能**: 简化版本的扩展信息测试行为树
- **特点**:
  - 更简单的逻辑，更容易调试
  - 测试前3个扩展信息的获取
  - 验证三种读取方式
  - 适合快速验证功能
- **推荐**: 首次测试时使用此版本

### 2. `test_action_extends_auto.py`
- **功能**: Python测试脚本，发送包含不同类型扩展信息的Action请求
- **测试场景**:
  - 基本产品信息（3个扩展信息）
  - 完整工艺信息（5个扩展信息）
  - 数值类型扩展信息（5个扩展信息）
  - 单个扩展信息（1个扩展信息）
  - 空扩展信息（0个扩展信息）

### 3. `simple_test.sh` ⭐ **推荐快速测试**
- **功能**: 简化版命令行测试脚本
- **优点**: 使用简化版行为树，快速验证基本功能
- **用途**: 发送包含3个扩展信息的测试请求

### 4. `quick_test.sh`
- **功能**: 快速单次测试脚本
- **用途**: 使用完整版行为树进行快速测试

## 使用方法

### 方法1: 简化测试（推荐首次使用）

1. **启动Action服务器**
   ```bash
   ros2 run rpcs_s_behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1
   ```

2. **运行简化测试脚本**
   ```bash
   cd src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/config/process_trees/R03001010002/Testing
   ./simple_test.sh
   ```

### 方法2: 完整测试

1. **启动Action服务器**
   ```bash
   ros2 run rpcs_s_behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1
   ```

2. **运行Python测试脚本**
   ```bash
   cd src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/config/process_trees/R03001010002/Testing
   python3 test_action_extends_auto.py
   ```

### 方法3: 手动单个测试

```bash
# 启动Action服务器
ros2 run rpcs_s_behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1

# 发送简化测试请求
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsSimpleTest',
        robot_id: 'Robot1',
        process_id: 'MANUAL_TEST_001',
        timeout_seconds: 60,
        preempt_current: true,
        extends: [
            {key: 'product_type', value: 'TYPE_A'},
            {key: 'quality_level', value: 'HIGH'},
            {key: 'station', value: 'STATION_001'}
        ]
    }"
```

## 测试验证内容

### 1. 自动识别能力
- ✅ 自动获取扩展信息总数
- ✅ 动态遍历所有扩展信息，无需提前知道key名称
- ✅ 处理不同数量的扩展信息（0-5个）

### 2. 多种读取方式
- ✅ **索引访问**: 通过`action_extend_N_key`和`action_extend_N_value`访问
- ✅ **直接访问**: 通过`extend_<key>`直接访问（如`extend_product_type`）
- ✅ **GetActionParameters节点**: 通过节点的`strExtendKey`和`intExtendIndex`参数获取

### 3. 数据类型支持
- ✅ 字符串类型
- ✅ 数值类型（作为字符串传输，可在节点中转换）
- ✅ 布尔类型（作为字符串传输）
- ✅ JSON格式数据
- ✅ 中文和特殊字符

## 预期输出示例

运行简化测试后，你应该看到类似以下的输出：

```
=== Action扩展信息简化测试 ===
检测到扩展信息数量: 3
--- 尝试获取扩展信息[0] ---
扩展信息[0]: product_type = TYPE_SIMPLE
  → 通过键名验证成功: TYPE_SIMPLE
--- 尝试获取扩展信息[1] ---
扩展信息[1]: quality_level = HIGH
  → 通过键名验证成功: HIGH
--- 尝试获取扩展信息[2] ---
扩展信息[2]: station = STATION_TEST
  → 通过键名验证成功: STATION_TEST

=== 验证直接访问方式 ===
extend_product_type: TYPE_SIMPLE
extend_quality_level: HIGH
extend_station: STATION_TEST

=== 验证索引访问方式 ===
action_extend_0_key: product_type
action_extend_0_value: TYPE_SIMPLE
action_extend_1_key: quality_level
action_extend_1_value: HIGH

=== 扩展信息简化测试报告 ===
✓ 扩展信息总数: 3
✓ 索引访问方式: 已验证
✓ 直接访问方式: 已验证
✓ GetActionParameters节点: 已验证
✓ 自动识别功能: 正常工作
```

## 故障排除

### 1. Action服务器未启动
**错误**: `Action服务器不可用`
**解决**: 确保process_action_server已启动并且robot_id参数正确

### 2. 行为树文件未找到
**错误**: `找不到行为树文件`
**解决**: 确保XML文件在正确的路径下，推荐先使用`ActionExtendsSimpleTest`

### 3. XML语法错误
**错误**: `port with name [comparison] is found in the XML`
**解决**: 已修复，使用更新后的XML文件

### 4. 权限问题
**错误**: `Permission denied`
**解决**: 给脚本文件添加执行权限：`chmod +x simple_test.sh`

## 测试建议

1. **首次测试**: 使用`simple_test.sh`和`ActionExtendsSimpleTest`
2. **功能验证**: 使用`test_action_extends_auto.py`进行全面测试
3. **调试问题**: 查看process_action_server的详细日志输出
4. **扩展测试**: 修改测试脚本添加更多扩展信息

这个测试套件提供了完整的扩展信息功能验证，确保系统能够正确处理各种情况下的扩展信息传递和访问。
