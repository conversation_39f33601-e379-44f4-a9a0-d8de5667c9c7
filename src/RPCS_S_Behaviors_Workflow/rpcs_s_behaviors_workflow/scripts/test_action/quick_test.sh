#!/bin/bash

# 快速测试Action扩展信息功能
# 发送一个简单的测试请求

echo "=== Action扩展信息快速测试 ==="
echo "发送包含3个扩展信息的测试请求..."

ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{
        process_action_type: 'ActionExtendsAutoTest',
        robot_id: 'Robot1',
        process_id: 'QUICK_TEST',
        timeout_seconds: 60,
        preempt_current: true,
        extends: [
            {key: 'product_type', value: 'TYPE_QUICK'},
            {key: 'quality_level', value: 'HIGH'},
            {key: 'test_id', value: 'QUICK_001'}
        ]
    }"

echo ""
echo "测试请求已发送，请查看process_action_server的输出日志"
