#!/usr/bin/env python3

"""
测试 Action 扩展信息功能的脚本
"""

import rclpy
from rclpy.action import ActionClient
from rclpy.node import Node
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
from rpcs_s_interfaces_behavior_tree.msg import Extend


class ActionExtendsTestClient(Node):
    def __init__(self):
        super().__init__('action_extends_test_client')
        self.action_client = ActionClient(
            self, 
            ExecuteProcessAction, 
            '/R03001010002/ExecuteProcessAction'
        )

    def send_test_goal(self):
        """发送带有扩展信息的测试目标"""
        
        # 等待 action 服务器
        if not self.action_client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error('Action server not available!')
            return False

        # 创建目标消息
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = 'Test'
        goal_msg.robot_id = ''
        goal_msg.process_id = 'EXTEND_TEST_001'
        goal_msg.timeout_seconds = 120
        goal_msg.preempt_current = False
        
        # 设置传统参数
        goal_msg.process_parameters = [
            'param1_value',
            'param2_value', 
            'param3_value'
        ]
        
        # 设置扩展信息
        extends = []
        
        # 产品类型
        extend1 = Extend()
        extend1.key = 'product_type'
        extend1.value = 'TYPE_A'
        extends.append(extend1)
        
        # 质量等级
        extend2 = Extend()
        extend2.key = 'quality_level'
        extend2.value = 'HIGH'
        extends.append(extend2)
        
        # 工作站
        extend3 = Extend()
        extend3.key = 'station'
        extend3.value = 'STATION_001'
        extends.append(extend3)
        
        # 批次号
        extend4 = Extend()
        extend4.key = 'batch_number'
        extend4.value = 'BATCH_20240101_001'
        extends.append(extend4)
        
        # 操作员
        extend5 = Extend()
        extend5.key = 'operator'
        extend5.value = 'OPERATOR_001'
        extends.append(extend5)
        
        goal_msg.extends = extends
        
        self.get_logger().info('发送测试目标...')
        self.get_logger().info(f'工艺类型: {goal_msg.process_action_type}')
        self.get_logger().info(f'参数数量: {len(goal_msg.process_parameters)}')
        self.get_logger().info(f'扩展信息数量: {len(goal_msg.extends)}')
        
        for i, extend in enumerate(goal_msg.extends):
            self.get_logger().info(f'  扩展[{i}]: {extend.key} = {extend.value}')
        
        # 发送目标
        send_goal_future = self.action_client.send_goal_async(
            goal_msg,
            feedback_callback=self.feedback_callback
        )
        
        send_goal_future.add_done_callback(self.goal_response_callback)
        return True

    def goal_response_callback(self, future):
        """目标响应回调"""
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected')
            return

        self.get_logger().info('Goal accepted')
        
        # 等待结果
        get_result_future = goal_handle.get_result_async()
        get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        """结果回调"""
        result = future.result().result
        self.get_logger().info('Result received:')
        self.get_logger().info(f'  Success: {result.success}')
        self.get_logger().info(f'  Message: {result.result_message}')
        self.get_logger().info(f'  Final Status: {result.final_status}')
        self.get_logger().info(f'  Execution Time: {result.execution_time:.2f}s')
        
        # 关闭节点
        rclpy.shutdown()

    def feedback_callback(self, feedback_msg):
        """反馈回调"""
        feedback = feedback_msg.feedback
        self.get_logger().info(
            f'Feedback: [{feedback.current_process_step}] '
            f'{feedback.progress_percent:.1f}% - {feedback.status_message}'
        )


def main():
    rclpy.init()
    
    client = ActionExtendsTestClient()
    
    try:
        if client.send_test_goal():
            rclpy.spin(client)
        else:
            client.get_logger().error('Failed to send goal')
    except KeyboardInterrupt:
        client.get_logger().info('Test interrupted by user')
    finally:
        client.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
