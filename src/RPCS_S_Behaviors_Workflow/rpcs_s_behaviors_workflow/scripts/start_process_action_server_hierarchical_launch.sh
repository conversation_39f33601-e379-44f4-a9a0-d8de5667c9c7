#!/bin/bash

# RPCS ProcessActionServer 启动脚本 (使用ROS2 launch方式，支持层次化目录结构)
# 使用方法: ./start_process_action_server_hierarchical_launch.sh [robot_id] [--background]
# 参数说明:
#   robot_id: 机器人ID (默认: Robot1)
#   --background: 后台运行模式

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取工作空间根目录
WORKSPACE_DIR="$(cd "${SCRIPT_DIR}/../../../.." && pwd)"

# 解析参数
ROBOT_ID=${1:-Robot1}
BACKGROUND_MODE=false

# 显示帮助信息
show_help() {
    echo "RPCS ProcessActionServer 启动脚本 (ROS2 launch方式，支持层次化目录结构)"
    echo ""
    echo "使用方法:"
    echo "  $0 [robot_id] [--background]"
    echo "  $0 --help"
    echo ""
    echo "参数说明:"
    echo "  robot_id      机器人ID (默认: Robot1)"
    echo "                可选值: Robot1, Robot2, Robot3"
    echo "  --background  后台运行模式"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 前台启动Robot1"
    echo "  $0 Robot2            # 前台启动Robot2"
    echo "  $0 --background      # 后台启动Robot1"
    echo "  $0 Robot3 --background # 后台启动Robot3"
    echo ""
    echo "后台运行说明:"
    echo "  - 日志文件: /var/logs/rpcs/process_action_server_[robot_id].log"
    echo "  - PID文件: /tmp/rpcs_process_action_server_[robot_id].pid"
    echo "  - 查看日志: tail -f /var/logs/rpcs/process_action_server_[robot_id].log"
    echo "  - 停止服务: kill \$(cat /tmp/rpcs_process_action_server_[robot_id].pid)"
    echo ""
    echo "注意事项:"
    echo "  - 脚本会自动检查并停止所有相关的老进程"
    echo "  - 包括通过PID文件、进程名、ROS2 launch等方式查找的进程"
    echo "  - 确保工作空间已构建: colcon build --packages-select rpcs_s_behaviors_workflow"
    echo "  - 确保配置文件存在且正确"
}

# 检查帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ] || [ "$2" = "--help" ] || [ "$2" = "-h" ]; then
    show_help
    exit 0
fi

# 检查是否有后台运行参数
if [ "$2" = "--background" ] || [ "$1" = "--background" ]; then
    BACKGROUND_MODE=true
    # 如果第一个参数是--background，则使用默认Robot1
    if [ "$1" = "--background" ]; then
        ROBOT_ID="Robot1"
    fi
else
    # 如果没有后台参数，第一个参数就是机器人ID
    if [ -n "$1" ]; then
        ROBOT_ID="$1"
    fi
fi

echo "🚀 启动 RPCS ProcessActionServer (ROS2 launch方式，层次化目录结构)"
echo "   工作空间: $WORKSPACE_DIR"
echo "   机器人ID: $ROBOT_ID"
echo "   运行模式: $([ "$BACKGROUND_MODE" = true ] && echo "后台运行" || echo "前台运行")"

# 检查工作空间是否已构建
if [ ! -d "$WORKSPACE_DIR/install" ]; then
    echo "❌ 错误: 工作空间未构建，请先运行: colcon build"
    exit 1
fi

# 检查launch文件是否存在
LAUNCH_FILE="$WORKSPACE_DIR/src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/launch/process_action_server_hierarchical.launch.py"
if [ ! -f "$LAUNCH_FILE" ]; then
    echo "❌ 错误: Launch文件不存在: $LAUNCH_FILE"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_action_mapping.yaml"
HIERARCHICAL_CONFIG_FILE="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_action_mapping_hierarchical.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    echo "   请先构建项目: colcon build --packages-select rpcs_s_behaviors_workflow"
    exit 1
fi

# 检查层次化配置文件是否存在，如果不存在则复制
if [ ! -f "$HIERARCHICAL_CONFIG_FILE" ]; then
    echo "⚠️ 警告: 层次化配置文件不存在: $HIERARCHICAL_CONFIG_FILE"
    echo "   将从源代码目录复制配置文件..."
    
    SRC_CONFIG="$WORKSPACE_DIR/src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/config/process_action_mapping_hierarchical.yaml"
    if [ -f "$SRC_CONFIG" ]; then
        cp "$SRC_CONFIG" "$HIERARCHICAL_CONFIG_FILE"
        echo "✅ 已复制配置文件"
    else
        echo "❌ 错误: 源配置文件不存在: $SRC_CONFIG"
        exit 1
    fi
fi

# 检查工艺树目录是否存在
TREE_PATH="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/share/rpcs_s_behaviors_workflow/process_trees"
if [ ! -d "$TREE_PATH" ]; then
    echo "❌ 错误: 工艺树目录不存在: $TREE_PATH"
    echo "   请确保已安装配置文件"
    exit 1
fi

# 设置环境变量
source "$WORKSPACE_DIR/install/setup.bash"

# 确保插件库路径正确设置
export LD_LIBRARY_PATH="$WORKSPACE_DIR/install/rpcs_s_behaviors_workflow/lib/rpcs_s_behaviors_workflow:$LD_LIBRARY_PATH"

# 创建日志目录
LOG_DIR="/var/logs/rpcs"
mkdir -p "$LOG_DIR"

# 函数：停止进程
stop_process() {
    local pid=$1
    local process_name=$2

    if ps -p "$pid" > /dev/null 2>&1; then
        echo "🔄 发现已运行的$process_name (PID: $pid)，正在停止..."

        # 先尝试优雅停止
        kill -TERM "$pid" 2>/dev/null

        # 等待进程停止
        TIMEOUT=5
        COUNTER=0
        while ps -p "$pid" > /dev/null 2>&1 && [ $COUNTER -lt $TIMEOUT ]; do
            echo "⏳ 等待进程优雅停止... ($((TIMEOUT - COUNTER))秒)"
            sleep 1
            COUNTER=$((COUNTER + 1))
        done

        # 如果进程仍在运行，强制终止
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "⚠️ 进程未在超时时间内停止，强制终止..."
            kill -9 "$pid" 2>/dev/null
            sleep 1
        fi

        echo "✅ 已停止 $process_name (PID: $pid)"
        return 0
    fi
    return 1
}

# 检查并停止已运行的ProcessActionServer
echo "🔍 检查并停止已运行的相关进程..."

# 1. 通过PID文件停止
PID_FILE="/tmp/rpcs_process_action_server_${ROBOT_ID}.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if stop_process "$PID" "ProcessActionServer (来自PID文件)"; then
        echo "   从PID文件停止成功"
    fi
    # 清理PID文件
    rm -f "$PID_FILE"
fi

# 2. 通过进程名查找并停止所有相关进程
echo "🔍 查找所有相关的进程..."

# 查找包含特定关键词的进程
PROCESS_PATTERNS=(
    "process_action_server"
    "rpcs_s_behaviors_workflow.*${ROBOT_ID}"
    "process_action_server_hierarchical"
)

FOUND_PROCESSES=false
for pattern in "${PROCESS_PATTERNS[@]}"; do
    # 使用pgrep查找进程，排除当前脚本进程
    PIDS=$(pgrep -f "$pattern" | grep -v "$$")

    if [ -n "$PIDS" ]; then
        FOUND_PROCESSES=true
        echo "📋 发现匹配模式 '$pattern' 的进程:"

        for pid in $PIDS; do
            # 获取进程详细信息
            if ps -p "$pid" > /dev/null 2>&1; then
                PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
                echo "   PID: $pid - $PROCESS_INFO"
                stop_process "$pid" "匹配进程"
            fi
        done
    fi
done

# 3. 特别检查ROS2相关进程
echo "🔍 检查ROS2相关进程..."
ROS2_PIDS=$(pgrep -f "ros2.*launch.*process_action_server" | grep -v "$$")
if [ -n "$ROS2_PIDS" ]; then
    FOUND_PROCESSES=true
    echo "📋 发现ROS2 launch进程:"
    for pid in $ROS2_PIDS; do
        if ps -p "$pid" > /dev/null 2>&1; then
            PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
            echo "   PID: $pid - $PROCESS_INFO"
            stop_process "$pid" "ROS2 launch进程"
        fi
    done
fi

# 4. 检查可能的僵尸进程或相关子进程
echo "🔍 清理可能的相关子进程..."
CHILD_PIDS=$(pgrep -f "${ROBOT_ID}" | grep -v "$$")
if [ -n "$CHILD_PIDS" ]; then
    for pid in $CHILD_PIDS; do
        if ps -p "$pid" > /dev/null 2>&1; then
            PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
            # 只处理明确相关的进程，避免误杀
            if echo "$PROCESS_INFO" | grep -q -E "(process_action|rpcs_s_behaviors|behavior.*tree)"; then
                echo "   发现相关子进程: PID $pid - $PROCESS_INFO"
                stop_process "$pid" "相关子进程"
                FOUND_PROCESSES=true
            fi
        fi
    done
fi

if [ "$FOUND_PROCESSES" = false ]; then
    echo "✅ 未发现运行中的相关进程"
else
    echo "✅ 所有相关进程已停止"
    # 额外等待确保进程完全清理
    echo "⏳ 等待进程完全清理..."
    sleep 2
fi

echo "✅ 环境检查完成"
echo "   使用ROS2 launch启动ProcessActionServer..."

# 使用ROS2 launch启动服务器
echo "🎯 启动 ProcessActionServer (ROS2 launch方式)..."

if [ "$BACKGROUND_MODE" = true ]; then
    # 后台运行模式
    LOG_FILE="$LOG_DIR/process_action_server_${ROBOT_ID}.log"
    echo "📝 日志将输出到: $LOG_FILE"
    nohup ros2 launch rpcs_s_behaviors_workflow process_action_server_hierarchical.launch.py \
      robot_id:="$ROBOT_ID" \
      config_file:="$CONFIG_FILE" \
      hierarchical_config_file:="$HIERARCHICAL_CONFIG_FILE" \
      tree_file_path:="$TREE_PATH" \
      max_concurrent_actions:=2 \
      default_timeout:=300 \
      groot2_port:=1670 > "$LOG_FILE" 2>&1 &
    
    # 获取后台进程PID
    PID=$!
    echo "✅ ProcessActionServer 已在后台启动 (PID: $PID)"
    echo "📋 查看日志: tail -f $LOG_FILE"
    echo "🛑 停止服务: kill $PID"
    
    # 保存PID到文件，方便后续管理
    echo "$PID" > "/tmp/rpcs_process_action_server_${ROBOT_ID}.pid"
    echo "💾 PID已保存到: /tmp/rpcs_process_action_server_${ROBOT_ID}.pid"
else
    # 前台运行模式
    ros2 launch rpcs_s_behaviors_workflow process_action_server_hierarchical.launch.py \
      robot_id:="$ROBOT_ID" \
      config_file:="$CONFIG_FILE" \
      hierarchical_config_file:="$HIERARCHICAL_CONFIG_FILE" \
      tree_file_path:="$TREE_PATH" \
      max_concurrent_actions:=2 \
      default_timeout:=300 \
      groot2_port:=1670
fi 