#!/bin/bash

# =============================================================================
# 快速编译脚本 - RPCS_S_Behaviors_workflow
# 用于日常开发的简化编译脚本
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取工作空间根目录（当前终端的工作目录）
WORKSPACE_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../.." && pwd)"

# 解析命令行参数
FORCE_BUILD=false
for arg in "$@"; do
  case $arg in
    -f|--force)
      FORCE_BUILD=true
      shift
      ;;
  esac
done

echo -e "${GREEN}🚀 快速编译 RPCS_S_Behaviors_workflow${NC}"
echo "脚本目录: $SCRIPT_DIR"
echo "工作空间根目录: $WORKSPACE_ROOT"

# 检查ROS2环境
if [ -z "$ROS_DISTRO" ]; then
    echo -e "${RED}❌ ROS2环境未设置，请先运行:${NC}"
    echo "source /opt/ros/humble/setup.bash"
    exit 1
fi

echo -e "${GREEN}✅ ROS2环境已设置: $ROS_DISTRO${NC}"

# 切换到工作空间根目录
cd "$WORKSPACE_ROOT"

# 执行编译
echo -e "${YELLOW}🔨 开始编译...${NC}"

# 使用--packages-up-to参数一次性编译所有依赖包
echo -e "${BLUE}📦 编译 rpcs_s_behaviors_workflow 及其所有依赖${NC}"
if $FORCE_BUILD; then
    colcon build --packages-up-to rpcs_s_behaviors_workflow --parallel-workers $(nproc) --cmake-clean-cache
else
    colcon build --packages-up-to rpcs_s_behaviors_workflow --parallel-workers $(nproc)
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 编译成功！${NC}"
    
    # 自动source环境
    if [ -f "$WORKSPACE_ROOT/install/setup.bash" ]; then
        echo -e "${GREEN}🔧 设置环境...${NC}"
        source "$WORKSPACE_ROOT/install/setup.bash"
        echo -e "${GREEN}✅ 环境设置完成！${NC}"
        
        echo ""
        echo -e "${GREEN}🎉 编译完成！可以运行以下命令测试:${NC}"
        echo "  ./src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/scripts/start_process_action_server.sh Robot1"
        echo "  python3 ./src/RPCS_S_Behaviors_Workflow/rpcs_s_behaviors_workflow/scripts/enhanced_test_client.py"
    fi
else
    echo -e "${RED}❌ rpcs_s_behaviors_workflow 编译失败！${NC}"
    exit 1
fi 