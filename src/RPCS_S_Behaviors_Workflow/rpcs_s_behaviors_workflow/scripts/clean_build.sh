#!/bin/bash

# =============================================================================
# 清理编译脚本 - RPCS_S_Behaviors_workflow
# 清理所有编译生成的文件和目录
# =============================================================================

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${YELLOW}🧹 清理编译目录${NC}"
echo "工作目录: $SCRIPT_DIR"

cd "$SCRIPT_DIR"

# 清理build目录
if [ -d "build" ]; then
    echo -e "${YELLOW}删除 build/ 目录...${NC}"
    rm -rf build/
    echo -e "${GREEN}✅ build/ 目录已删除${NC}"
else
    echo -e "${GREEN}ℹ️  build/ 目录不存在${NC}"
fi

# 清理install目录
if [ -d "install" ]; then
    echo -e "${YELLOW}删除 install/ 目录...${NC}"
    rm -rf install/
    echo -e "${GREEN}✅ install/ 目录已删除${NC}"
else
    echo -e "${GREEN}ℹ️  install/ 目录不存在${NC}"
fi

# 清理log目录
if [ -d "log" ]; then
    echo -e "${YELLOW}删除 log/ 目录...${NC}"
    rm -rf log/
    echo -e "${GREEN}✅ log/ 目录已删除${NC}"
else
    echo -e "${GREEN}ℹ️  log/ 目录不存在${NC}"
fi

# 清理build_logs目录
if [ -d "build_logs" ]; then
    echo -e "${YELLOW}删除 build_logs/ 目录...${NC}"
    rm -rf build_logs/
    echo -e "${GREEN}✅ build_logs/ 目录已删除${NC}"
else
    echo -e "${GREEN}ℹ️  build_logs/ 目录不存在${NC}"
fi

# 清理自动生成的环境设置脚本
if [ -f "setup_env.sh" ]; then
    echo -e "${YELLOW}删除 setup_env.sh 文件...${NC}"
    rm -f setup_env.sh
    echo -e "${GREEN}✅ setup_env.sh 文件已删除${NC}"
fi

echo ""
echo -e "${GREEN}🎉 清理完成！${NC}"
echo -e "${GREEN}现在可以运行以下命令重新编译:${NC}"
echo "  ./quick_build.sh           # 快速编译"
echo "  ./build_project.sh         # 完整编译"
echo "  ./build_project.sh -f      # 完整编译（等同于清理+编译）" 