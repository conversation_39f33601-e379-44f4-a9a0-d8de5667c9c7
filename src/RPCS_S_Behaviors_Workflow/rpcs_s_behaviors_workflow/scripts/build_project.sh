#!/bin/bash

# =============================================================================
# RPCS_S_Behaviors_workflow 项目编译脚本
# 作者: Assistant
# 日期: 2024-01-24
# 描述: 自动化编译ROS2工作空间，支持多种编译选项
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="$PROJECT_ROOT"

# 日志文件
LOG_DIR="$PROJECT_ROOT/build_logs"
BUILD_LOG="$LOG_DIR/build_$(date +%Y%m%d_%H%M%S).log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$BUILD_LOG"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$BUILD_LOG"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$BUILD_LOG"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$BUILD_LOG"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1" | tee -a "$BUILD_LOG"
}

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    cat << 'EOF'
╔════════════════════════════════════════════════════════════════╗
║                  RPCS_S_Behaviors_workflow                    ║
║                     项目编译脚本                               ║
║                                                                ║
║  🤖 支持完整编译、增量编译、清理编译等多种模式                ║
║  📦 自动处理依赖关系和环境设置                                 ║
║  📝 详细的编译日志和错误报告                                   ║
╚════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 显示使用帮助
show_help() {
    cat << EOF
使用方法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --clean             清理编译（删除build和install目录）
    -f, --full              完整编译（等同于clean + build）
    -p, --package <名称>    只编译指定包
    -j, --jobs <数量>       并行编译任务数量（默认：自动检测）
    -v, --verbose           详细输出模式
    -r, --release           发布模式编译
    -d, --debug             调试模式编译
    --cmake-args <参数>     传递给cmake的额外参数
    --no-deps               不编译依赖包
    --continue-on-error     遇到错误继续编译其他包

示例:
    $0                      # 标准编译
    $0 -c                   # 清理编译
    $0 -f                   # 完整编译
    $0 -p RPCS_S_Behaviors_workflow  # 只编译指定包
    $0 -v -j 4              # 详细输出，4个并行任务
    $0 -r --cmake-args="-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"  # 发布模式编译
EOF
}

# 检查ROS2环境
check_ros2_environment() {
    print_step "检查ROS2环境..."
    
    if [ -z "$ROS_DISTRO" ]; then
        print_error "ROS2环境未设置，请先source ROS2环境:"
        print_error "  source /opt/ros/humble/setup.bash  # 或其他版本"
        return 1
    fi
    
    print_info "检测到ROS2版本: $ROS_DISTRO"
    
    # 检查colcon是否可用
    if ! command -v colcon &> /dev/null; then
        print_error "colcon未找到，请安装:"
        print_error "  sudo apt install python3-colcon-common-extensions"
        return 1
    fi
    
    print_success "ROS2环境检查通过"
    return 0
}

# 检查工作空间结构
check_workspace_structure() {
    print_step "检查工作空间结构..."
    
    if [ ! -d "$WORKSPACE_ROOT/src" ]; then
        print_error "src目录不存在: $WORKSPACE_ROOT/src"
        return 1
    fi
    
    if [ ! -d "$WORKSPACE_ROOT/src/RPCS_S_Behaviors_workflow" ]; then
        print_error "RPCS_S_Behaviors_workflow包不存在"
        return 1
    fi
    
    print_success "工作空间结构检查通过"
    return 0
}

# 清理编译目录
clean_build() {
    print_step "清理编译目录..."
    
    if [ -d "$WORKSPACE_ROOT/build" ]; then
        print_info "删除build目录..."
        rm -rf "$WORKSPACE_ROOT/build"
    fi
    
    if [ -d "$WORKSPACE_ROOT/install" ]; then
        print_info "删除install目录..."
        rm -rf "$WORKSPACE_ROOT/install"
    fi
    
    if [ -d "$WORKSPACE_ROOT/log" ]; then
        print_info "删除log目录..."
        rm -rf "$WORKSPACE_ROOT/log"
    fi
    
    print_success "清理完成"
}

# 检查依赖
check_dependencies() {
    print_step "检查依赖..."
    
    print_info "运行rosdep检查..."
    cd "$WORKSPACE_ROOT"
    
    if ! rosdep check --from-paths src --ignore-src -r &>> "$BUILD_LOG"; then
        print_warning "存在未满足的依赖，尝试安装..."
        if rosdep install --from-paths src --ignore-src -r -y &>> "$BUILD_LOG"; then
            print_success "依赖安装完成"
        else
            print_error "依赖安装失败，请查看日志: $BUILD_LOG"
            return 1
        fi
    else
        print_success "所有依赖都已满足"
    fi
    
    return 0
}

# 执行编译
perform_build() {
    local colcon_args=()
    
    print_step "开始编译..."
    cd "$WORKSPACE_ROOT"
    
    # 基本参数
    colcon_args+=("build")
    
    # 添加包选择
    if [ -n "$PACKAGE_NAME" ]; then
        colcon_args+=("--packages-select" "$PACKAGE_NAME")
        print_info "只编译包: $PACKAGE_NAME"
    fi
    
    # 添加并行任务数
    if [ -n "$JOBS" ]; then
        colcon_args+=("--parallel-workers" "$JOBS")
        print_info "使用 $JOBS 个并行任务"
    fi
    
    # 添加cmake参数
    if [ -n "$CMAKE_ARGS" ]; then
        colcon_args+=("--cmake-args" $CMAKE_ARGS)
        print_info "CMAKE参数: $CMAKE_ARGS"
    fi
    
    # 添加其他选项
    if [ "$NO_DEPS" = true ]; then
        colcon_args+=("--packages-skip-build-finished")
    fi
    
    if [ "$CONTINUE_ON_ERROR" = true ]; then
        colcon_args+=("--continue-on-error")
    fi
    
    # 构建模式
    if [ "$BUILD_TYPE" = "Release" ]; then
        colcon_args+=("--cmake-args" "-DCMAKE_BUILD_TYPE=Release")
        print_info "使用Release模式编译"
    elif [ "$BUILD_TYPE" = "Debug" ]; then
        colcon_args+=("--cmake-args" "-DCMAKE_BUILD_TYPE=Debug")
        print_info "使用Debug模式编译"
    fi
    
    # 输出模式
    if [ "$VERBOSE" = true ]; then
        colcon_args+=("--event-handlers" "console_direct+")
        print_info "使用详细输出模式"
    fi
    
    # 执行编译
    print_info "执行命令: colcon ${colcon_args[*]}"
    
    if colcon "${colcon_args[@]}" 2>&1 | tee -a "$BUILD_LOG"; then
        print_success "编译完成"
        return 0
    else
        print_error "编译失败，请查看日志: $BUILD_LOG"
        return 1
    fi
}

# 设置环境
setup_environment() {
    print_step "设置编译后环境..."
    
    if [ -f "$WORKSPACE_ROOT/install/setup.bash" ]; then
        print_info "创建环境设置脚本..."
        cat > "$WORKSPACE_ROOT/setup_env.sh" << 'EOL'
#!/bin/bash
# 自动生成的环境设置脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# source ROS2环境（如果还没有）
if [ -z "$ROS_DISTRO" ]; then
    echo "正在设置ROS2环境..."
    source /opt/ros/humble/setup.bash
fi

# source工作空间环境
if [ -f "$SCRIPT_DIR/install/setup.bash" ]; then
    echo "正在设置工作空间环境..."
    source "$SCRIPT_DIR/install/setup.bash"
    echo "✅ 环境设置完成！"
    echo "🤖 可用的包:"
    ros2 pkg list | grep -E "(rpcs|RPCS)" | sort
else
    echo "❌ 未找到install/setup.bash，请先编译项目"
fi
EOL
        chmod +x "$WORKSPACE_ROOT/setup_env.sh"
        print_success "环境设置脚本已创建: setup_env.sh"
        print_info "使用方法: source ./setup_env.sh"
    else
        print_warning "未找到install/setup.bash文件"
    fi
}

# 生成编译报告
generate_report() {
    print_step "生成编译报告..."
    
    local report_file="$LOG_DIR/build_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
========================================
RPCS_S_Behaviors_workflow 编译报告
========================================
编译时间: $(date)
工作空间: $WORKSPACE_ROOT
编译日志: $BUILD_LOG

编译参数:
- 包名: ${PACKAGE_NAME:-"全部包"}
- 并行任务: ${JOBS:-"自动"}
- 编译类型: ${BUILD_TYPE:-"默认"}
- 详细输出: ${VERBOSE:-false}
- 清理编译: ${CLEAN_BUILD:-false}

编译结果:
EOF
    
    if [ -d "$WORKSPACE_ROOT/install" ]; then
        echo "✅ 编译成功" >> "$report_file"
        echo "" >> "$report_file"
        echo "已安装的包:" >> "$report_file"
        ls -la "$WORKSPACE_ROOT/install/" >> "$report_file" 2>/dev/null
    else
        echo "❌ 编译失败" >> "$report_file"
    fi
    
    print_info "编译报告已生成: $report_file"
}

# 主函数
main() {
    local CLEAN_BUILD=false
    local FULL_BUILD=false
    local PACKAGE_NAME=""
    local JOBS=""
    local VERBOSE=false
    local BUILD_TYPE=""
    local CMAKE_ARGS=""
    local NO_DEPS=false
    local CONTINUE_ON_ERROR=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                CLEAN_BUILD=true
                shift
                ;;
            -f|--full)
                FULL_BUILD=true
                shift
                ;;
            -p|--package)
                PACKAGE_NAME="$2"
                shift 2
                ;;
            -j|--jobs)
                JOBS="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -r|--release)
                BUILD_TYPE="Release"
                shift
                ;;
            -d|--debug)
                BUILD_TYPE="Debug"
                shift
                ;;
            --cmake-args)
                CMAKE_ARGS="$2"
                shift 2
                ;;
            --no-deps)
                NO_DEPS=true
                shift
                ;;
            --continue-on-error)
                CONTINUE_ON_ERROR=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置自动并行任务数
    if [ -z "$JOBS" ]; then
        JOBS=$(nproc)
        print_info "自动设置并行任务数: $JOBS"
    fi
    
    # 显示横幅
    show_banner
    
    print_info "开始编译流程..."
    print_info "工作空间: $WORKSPACE_ROOT"
    print_info "日志文件: $BUILD_LOG"
    
    # 检查环境
    if ! check_ros2_environment; then
        exit 1
    fi
    
    if ! check_workspace_structure; then
        exit 1
    fi
    
    # 执行清理（如果需要）
    if [ "$CLEAN_BUILD" = true ] || [ "$FULL_BUILD" = true ]; then
        clean_build
    fi
    
    # 检查依赖
    if [ "$NO_DEPS" != true ]; then
        if ! check_dependencies; then
            print_warning "依赖检查失败，但继续编译..."
        fi
    fi
    
    # 执行编译
    local build_start_time=$(date +%s)
    if perform_build; then
        local build_end_time=$(date +%s)
        local build_duration=$((build_end_time - build_start_time))
        print_success "编译成功完成！耗时: ${build_duration}秒"
        
        # 设置环境
        setup_environment
        
        print_success "🎉 全部完成！"
        print_info "下一步:"
        print_info "  1. source ./setup_env.sh"
        print_info "  2. ./start_process_action_server.sh Robot1"
        print_info "  3. python3 enhanced_test_client.py"
        
    else
        print_error "编译失败！"
        exit 1
    fi
    
    # 生成报告
    generate_report
}

# 捕获退出信号
trap 'print_error "编译被中断"; exit 1' INT TERM

# 运行主函数
main "$@" 