import re

def update_version(major=False, minor=False, patch=True):
    with open('include/rpcs_s_behaviors_workflow/base/version.h', 'r') as file:
        content = file.read()

    major_version = int(re.search(r'#define VERSION_MAJOR (\d+)', content).group(1))
    minor_version = int(re.search(r'#define VERSION_MINOR (\d+)', content).group(1))
    patch_version = int(re.search(r'#define VERSION_PATCH (\d+)', content).group(1))

    if major:
        major_version += 1
        minor_version = 0
        patch_version = 0
    elif minor:
        minor_version += 1
        patch_version = 0
    elif patch:
        patch_version += 1

    new_content = re.sub(r'#define VERSION_MAJOR \d+', f'#define VERSION_MAJOR {major_version}', content)
    new_content = re.sub(r'#define VERSION_MINOR \d+', f'#define VERSION_MINOR {minor_version}', new_content)
    new_content = re.sub(r'#define VERSION_PATCH \d+', f'#define VERSION_PATCH {patch_version}', new_content)

    with open('include/rpcs_s_behaviors_workflow/base/version.h', 'w') as file:
        file.write(new_content)

if __name__ == "__main__":
    update_version(patch=True)