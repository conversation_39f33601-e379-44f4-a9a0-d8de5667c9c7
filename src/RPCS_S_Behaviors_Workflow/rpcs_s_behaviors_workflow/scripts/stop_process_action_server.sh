#!/bin/bash

# RPCS ProcessActionServer 停止脚本
# 使用方法: ./stop_process_action_server.sh [robot_id]
# 参数说明:
#   robot_id: 机器人ID (默认: Robot1)

# 解析参数
ROBOT_ID=${1:-Robot1}

# 显示帮助信息
show_help() {
    echo "RPCS ProcessActionServer 停止脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [robot_id]"
    echo "  $0 --help"
    echo ""
    echo "参数说明:"
    echo "  robot_id      机器人ID (默认: Robot1)"
    echo "                可选值: Robot1, Robot2, Robot3"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 停止Robot1相关进程"
    echo "  $0 Robot2            # 停止Robot2相关进程"
    echo "  $0 --all             # 停止所有相关进程"
    echo ""
    echo "功能说明:"
    echo "  - 通过PID文件查找并停止进程"
    echo "  - 通过进程名模式匹配查找并停止进程"
    echo "  - 停止所有相关的ROS2 launch进程"
    echo "  - 清理相关的子进程"
}

# 检查帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# 检查是否停止所有进程
STOP_ALL=false
if [ "$1" = "--all" ]; then
    STOP_ALL=true
    echo "🛑 停止所有RPCS ProcessActionServer相关进程"
else
    echo "🛑 停止 RPCS ProcessActionServer (机器人ID: $ROBOT_ID)"
fi

# 函数：停止进程
stop_process() {
    local pid=$1
    local process_name=$2
    
    if ps -p "$pid" > /dev/null 2>&1; then
        echo "🔄 发现运行中的$process_name (PID: $pid)，正在停止..."
        
        # 先尝试优雅停止
        kill -TERM "$pid" 2>/dev/null
        
        # 等待进程停止
        TIMEOUT=5
        COUNTER=0
        while ps -p "$pid" > /dev/null 2>&1 && [ $COUNTER -lt $TIMEOUT ]; do
            echo "⏳ 等待进程优雅停止... ($((TIMEOUT - COUNTER))秒)"
            sleep 1
            COUNTER=$((COUNTER + 1))
        done
        
        # 如果进程仍在运行，强制终止
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "⚠️ 进程未在超时时间内停止，强制终止..."
            kill -9 "$pid" 2>/dev/null
            sleep 1
        fi
        
        echo "✅ 已停止 $process_name (PID: $pid)"
        return 0
    fi
    return 1
}

echo "🔍 查找并停止相关进程..."

FOUND_PROCESSES=false

# 1. 通过PID文件停止
if [ "$STOP_ALL" = false ]; then
    PID_FILE="/tmp/rpcs_process_action_server_${ROBOT_ID}.pid"
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if stop_process "$PID" "ProcessActionServer (来自PID文件)"; then
            FOUND_PROCESSES=true
        fi
        # 清理PID文件
        rm -f "$PID_FILE"
        echo "🗑️ 已清理PID文件: $PID_FILE"
    fi
else
    # 停止所有机器人的PID文件
    for pid_file in /tmp/rpcs_process_action_server_*.pid; do
        if [ -f "$pid_file" ]; then
            PID=$(cat "$pid_file")
            if stop_process "$PID" "ProcessActionServer (来自$(basename $pid_file))"; then
                FOUND_PROCESSES=true
            fi
            rm -f "$pid_file"
            echo "🗑️ 已清理PID文件: $pid_file"
        fi
    done
fi

# 2. 通过进程名查找并停止
echo "🔍 通过进程名查找相关进程..."

if [ "$STOP_ALL" = true ]; then
    PROCESS_PATTERNS=(
        "process_action_server"
        "rpcs_s_behaviors_workflow"
        "process_action_server_hierarchical"
    )
else
    PROCESS_PATTERNS=(
        "process_action_server"
        "rpcs_s_behaviors_workflow.*${ROBOT_ID}"
        "process_action_server_hierarchical"
    )
fi

for pattern in "${PROCESS_PATTERNS[@]}"; do
    # 使用pgrep查找进程，排除当前脚本进程
    PIDS=$(pgrep -f "$pattern" | grep -v "$$")
    
    if [ -n "$PIDS" ]; then
        FOUND_PROCESSES=true
        echo "📋 发现匹配模式 '$pattern' 的进程:"
        
        for pid in $PIDS; do
            if ps -p "$pid" > /dev/null 2>&1; then
                PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
                echo "   PID: $pid - $PROCESS_INFO"
                stop_process "$pid" "匹配进程"
            fi
        done
    fi
done

# 3. 检查ROS2相关进程
echo "🔍 检查ROS2相关进程..."
ROS2_PIDS=$(pgrep -f "ros2.*launch.*process_action_server" | grep -v "$$")
if [ -n "$ROS2_PIDS" ]; then
    FOUND_PROCESSES=true
    echo "📋 发现ROS2 launch进程:"
    for pid in $ROS2_PIDS; do
        if ps -p "$pid" > /dev/null 2>&1; then
            PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
            echo "   PID: $pid - $PROCESS_INFO"
            stop_process "$pid" "ROS2 launch进程"
        fi
    done
fi

# 4. 清理相关子进程
echo "🔍 清理相关子进程..."
if [ "$STOP_ALL" = true ]; then
    CHILD_PIDS=$(pgrep -f "rpcs_s_behaviors" | grep -v "$$")
else
    CHILD_PIDS=$(pgrep -f "${ROBOT_ID}" | grep -v "$$")
fi

if [ -n "$CHILD_PIDS" ]; then
    for pid in $CHILD_PIDS; do
        if ps -p "$pid" > /dev/null 2>&1; then
            PROCESS_INFO=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
            # 只处理明确相关的进程
            if echo "$PROCESS_INFO" | grep -q -E "(process_action|rpcs_s_behaviors|behavior.*tree)"; then
                echo "   发现相关子进程: PID $pid - $PROCESS_INFO"
                stop_process "$pid" "相关子进程"
                FOUND_PROCESSES=true
            fi
        fi
    done
fi

if [ "$FOUND_PROCESSES" = false ]; then
    echo "✅ 未发现运行中的相关进程"
else
    echo "✅ 所有相关进程已停止"
    echo "⏳ 等待进程完全清理..."
    sleep 2
fi

echo "🎯 停止操作完成"
