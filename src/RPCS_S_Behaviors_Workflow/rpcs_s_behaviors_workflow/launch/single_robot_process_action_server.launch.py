#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    # 声明启动参数
    robot_id_arg = DeclareLaunchArgument(
        'robot_id',
        default_value='Robot1',
        description='Robot ID (Robot1, Robot2, Robot3)'
    )
    
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('RPCS_S_Behaviors_workflow'),
            'config',
            'process_action_mapping.yaml'
        ]),
        description='Path to the process action mapping configuration file'
    )
    
    tree_file_path_arg = DeclareLaunchArgument(
        'tree_file_path',
        default_value=PathJoinSubstitution([
            FindPackageShare('RPCS_S_Behaviors_workflow'),
            'config',
            'process_trees'
        ]),
        description='Path to the directory containing process tree XML files'
    )
    
    max_concurrent_actions_arg = DeclareLaunchArgument(
        'max_concurrent_actions',
        default_value='2',
        description='Maximum number of concurrent actions'
    )
    
    default_timeout_arg = DeclareLaunchArgument(
        'default_timeout',
        default_value='300',
        description='Default timeout in seconds'
    )
    
    groot2_port_arg = DeclareLaunchArgument(
        'groot2_port',
        default_value='1670',
        description='Port for Groot2 publisher'
    )

    # ProcessActionServer Node
    process_action_server = Node(
        package='RPCS_S_Behaviors_workflow',
        executable='process_action_server',
        name=[LaunchConfiguration('robot_id'), '_process_action_server'],
        namespace=LaunchConfiguration('robot_id'),
        parameters=[{
            'config_file': LaunchConfiguration('config_file'),
            'tree_file_path': LaunchConfiguration('tree_file_path'),
            'max_concurrent_actions': LaunchConfiguration('max_concurrent_actions'),
            'default_timeout': LaunchConfiguration('default_timeout'),
            'groot2_port': LaunchConfiguration('groot2_port')
        }],
        arguments=[LaunchConfiguration('robot_id')],
        output='screen',
        emulate_tty=True,
        respawn=True,
        respawn_delay=5.0
    )

    return LaunchDescription([
        robot_id_arg,
        config_file_arg,
        tree_file_path_arg,
        max_concurrent_actions_arg,
        default_timeout_arg,
        groot2_port_arg,
        process_action_server
    ]) 