#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import os

def generate_launch_description():
    # 声明启动参数
    test_type_arg = DeclareLaunchArgument(
        'test_type',
        default_value='basic',
        description='选择测试类型: basic (基础IO测试) 或 comprehensive (综合IO测试)'
    )
    
    device_id_arg = DeclareLaunchArgument(
        'device_id',
        default_value='robot1',
        description='设备ID，默认为robot1'
    )
    
    # 获取参数值
    test_type = LaunchConfiguration('test_type')
    device_id = LaunchConfiguration('device_id')
    
    # 获取包路径
    package_path = FindPackageShare('rpcs_s_behaviors_workflow')
    
    # 根据测试类型选择配置文件
    basic_config_file = PathJoinSubstitution([
        package_path,
        'config',
        'process_trees',
        'Robot1_io_test.xml'
    ])
    
    comprehensive_config_file = PathJoinSubstitution([
        package_path,
        'config', 
        'process_trees',
        'Robot1_io_comprehensive_test.xml'
    ])
    
    # 行为树执行节点
    behavior_tree_node = Node(
        package='rpcs_s_behaviors_workflow',
        executable='behavior_tree_executor',  # 假设有这个可执行文件
        name='io_test_behavior_tree',
        output='screen',
        parameters=[{
            'bt_xml_file': basic_config_file,  # 默认使用基础测试
            'device_id': device_id,
            'loop_rate': 10.0,
            'auto_start': True
        }],
        remappings=[
            ('/behavior_tree/status', '/io_test/bt_status'),
            ('/behavior_tree/feedback', '/io_test/bt_feedback')
        ]
    )
    
    # 创建启动描述
    ld = LaunchDescription()
    
    # 添加参数
    ld.add_action(test_type_arg)
    ld.add_action(device_id_arg)
    
    # 添加信息输出
    ld.add_action(LogInfo(msg=['启动IO测试行为树...']))
    ld.add_action(LogInfo(msg=['测试类型: ', test_type]))
    ld.add_action(LogInfo(msg=['设备ID: ', device_id]))
    
    # 添加节点
    ld.add_action(behavior_tree_node)
    
    return ld 