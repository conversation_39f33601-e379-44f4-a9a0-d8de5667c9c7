import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node

def generate_launch_description():
    """
    生成启动描述文件。

    本函数用于配置并生成ROS2的启动描述文件，以启动指定的节点并配置其参数。
    """
    # 获取行为树配置文件目录
    bt_config_dir = os.path.join(get_package_share_directory('RPCS_S_Behaviors_workflow'), 'config')

    # 定义启动配置参数，包括比赛风格和是否使用模拟时间
    style = LaunchConfiguration('style', default='full')
    use_sim_time = LaunchConfiguration('use_sim_time', default='False')

    # 根据比赛风格动态获取行为树的XML配置文件路径
    bt_xml_dir = PathJoinSubstitution([bt_config_dir, style]), ".xml"

    # 创建行为树节点
    rm_behavior_tree_node = Node(
        package='RPCS_S_Behaviors_workflow',
        executable='rpcs_s_behaviors_workflow',
        respawn=True,
        respawn_delay=3,
        parameters=[
            {
              'style': bt_xml_dir,
              'use_sim_time': use_sim_time,
            }
        ]
    )

    # 返回包含行为树节点的启动描述文件
    return LaunchDescription([rm_behavior_tree_node])

# ros2 launch RPCS_S_Behaviors_workflow rpcs_s_behaviors_workflow.launch.py style:=Workflow SimulationManager use_sim_time:=True

# colcon build
#  source install/setup.bash 
# ros2 launch RPCS_S_Behaviors_workflow rpcs_s_behaviors_workflow.launch.py style:=workflow_v0.3