#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    # 声明启动参数
    robot_id_arg = DeclareLaunchArgument(
        'robot_id',
        default_value='Robot1',
        description='Robot ID (Robot1, Robot2, Robot3)'
    )
    
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('rpcs_s_behaviors_workflow'),
            'config',
            'process_action_mapping.yaml'
        ]),
        description='Path to the process action mapping configuration file'
    )
    
    hierarchical_config_file_arg = DeclareLaunchArgument(
        'hierarchical_config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('rpcs_s_behaviors_workflow'),
            'config',
            'process_action_mapping_hierarchical.yaml'
        ]),
        description='Path to the hierarchical process action mapping configuration file'
    )
    
    tree_file_path_arg = DeclareLaunchArgument(
        'tree_file_path',
        default_value=PathJoinSubstitution([
            FindPackageShare('rpcs_s_behaviors_workflow'),
            'config',
            'process_trees'
        ]),
        description='Path to the directory containing process tree XML files'
    )
    
    max_concurrent_actions_arg = DeclareLaunchArgument(
        'max_concurrent_actions',
        default_value='2',
        description='Maximum number of concurrent actions'
    )
    
    default_timeout_arg = DeclareLaunchArgument(
        'default_timeout',
        default_value='300',
        description='Default timeout in seconds'
    )
    
    groot2_port_arg = DeclareLaunchArgument(
        'groot2_port',
        default_value='1670',
        description='Port for Groot2 publisher'
    )

    # ProcessActionServer Node (支持层次化配置)
    process_action_server = Node(
        package='rpcs_s_behaviors_workflow',
        executable='process_action_server',
        name='process_action_server',  # 使用固定的节点名称
        # 移除namespace，因为cpp代码中已经在action名称中包含了robot_id
        parameters=[{
            'robot_id': LaunchConfiguration('robot_id'),  # 添加robot_id参数
            'config_file': LaunchConfiguration('config_file'),
            'tree_file_path': LaunchConfiguration('tree_file_path'),
            'max_concurrent_actions': LaunchConfiguration('max_concurrent_actions'),
            'default_timeout': LaunchConfiguration('default_timeout'),
            'groot2_port': LaunchConfiguration('groot2_port'),
            'hierarchical_config_file': LaunchConfiguration('hierarchical_config_file')
        }],
        arguments=[LaunchConfiguration('robot_id')],
        output='screen',
        emulate_tty=True,
        respawn=True,
        respawn_delay=5.0
    )

    return LaunchDescription([
        robot_id_arg,
        config_file_arg,
        hierarchical_config_file_arg,
        tree_file_path_arg,
        max_concurrent_actions_arg,
        default_timeout_arg,
        groot2_port_arg,
        process_action_server
    ]) 