#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, GroupAction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from launch.conditions import IfCondition

def generate_launch_description():
    # 声明启动参数
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('RPCS_S_Behaviors_workflow'),
            'config',
            'process_action_mapping.yaml'
        ]),
        description='Path to the process action mapping configuration file'
    )
    
    tree_file_path_arg = DeclareLaunchArgument(
        'tree_file_path',
        default_value=PathJoinSubstitution([
            FindPackageShare('RPCS_S_Behaviors_workflow'),
            'config',
            'process_trees'
        ]),
        description='Path to the directory containing process tree XML files'
    )
    
    groot2_port_arg = DeclareLaunchArgument(
        'groot2_port',
        default_value='1670',
        description='Port for Groot2 publisher'
    )
    
    enable_Robot1_arg = DeclareLaunchArgument(
        'enable_Robot1',
        default_value='true',
        description='Enable Robot1 ProcessActionServer'
    )
    
    enable_Robot2_arg = DeclareLaunchArgument(
        'enable_Robot2',
        default_value='true',
        description='Enable Robot2 ProcessActionServer'
    )
    
    enable_Robot3_arg = DeclareLaunchArgument(
        'enable_Robot3',
        default_value='true',
        description='Enable Robot3 ProcessActionServer'
    )

    # Robot1 ProcessActionServer
    Robot1_server = GroupAction(
        condition=IfCondition(LaunchConfiguration('enable_Robot1')),
        actions=[
            Node(
                package='RPCS_S_Behaviors_workflow',
                executable='process_action_server',
                name='Robot1_process_action_server',
                namespace='Robot1',
                parameters=[{
                    'config_file': LaunchConfiguration('config_file'),
                    'tree_file_path': LaunchConfiguration('tree_file_path'),
                    'max_concurrent_actions': 2,
                    'default_timeout': 300,
                    'groot2_port': 1670
                }],
                arguments=['Robot1'],
                output='screen',
                emulate_tty=True,
                respawn=True,
                respawn_delay=5.0
            )
        ]
    )

    # Robot2 ProcessActionServer
    Robot2_server = GroupAction(
        condition=IfCondition(LaunchConfiguration('enable_Robot2')),
        actions=[
            Node(
                package='RPCS_S_Behaviors_workflow',
                executable='process_action_server',
                name='Robot2_process_action_server',
                namespace='Robot2',
                parameters=[{
                    'config_file': LaunchConfiguration('config_file'),
                    'tree_file_path': LaunchConfiguration('tree_file_path'),
                    'max_concurrent_actions': 2,
                    'default_timeout': 300,
                    'groot2_port': 1671
                }],
                arguments=['Robot2'],
                output='screen',
                emulate_tty=True,
                respawn=True,
                respawn_delay=5.0
            )
        ]
    )

    # Robot3 ProcessActionServer
    Robot3_server = GroupAction(
        condition=IfCondition(LaunchConfiguration('enable_Robot3')),
        actions=[
            Node(
                package='RPCS_S_Behaviors_workflow',
                executable='process_action_server',
                name='Robot3_process_action_server',
                namespace='Robot3',
                parameters=[{
                    'config_file': LaunchConfiguration('config_file'),
                    'tree_file_path': LaunchConfiguration('tree_file_path'),
                    'max_concurrent_actions': 2,
                    'default_timeout': 300,
                    'groot2_port': 1672
                }],
                arguments=['Robot3'],
                output='screen',
                emulate_tty=True,
                respawn=True,
                respawn_delay=5.0
            )
        ]
    )

    return LaunchDescription([
        config_file_arg,
        tree_file_path_arg,
        groot2_port_arg,
        enable_Robot1_arg,
        enable_Robot2_arg,
        enable_Robot3_arg,
        Robot1_server,
        Robot2_server,
        Robot3_server
    ]) 