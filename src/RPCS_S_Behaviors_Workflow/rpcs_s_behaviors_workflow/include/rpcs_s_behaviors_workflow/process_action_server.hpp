#ifndef RPCS_BEHAVIOR_TREE__PROCESS_ACTION_SERVER_HPP_
#define RPCS_BEHAVIOR_TREE__PROCESS_ACTION_SERVER_HPP_

#include <chrono>
#include <memory>
#include <string>
#include <thread>
#include <mutex>
#include <atomic>
#include <map>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_action/rclcpp_action.hpp"
#include "behaviortree_cpp/bt_factory.h"
#include "behaviortree_cpp/loggers/groot2_publisher.h"
#include "rpcs_s_interfaces_behavior_tree/action/execute_process_action.hpp"
#include "yaml-cpp/yaml.h"

#include "rpcs_s_behaviors_workflow/process_tree_path_resolver.hpp"

namespace rpcs_s_behaviors_workflow
{

using ExecuteProcessAction = rpcs_s_interfaces_behavior_tree::action::ExecuteProcessAction;
using GoalHandleExecuteProcessAction = rclcpp_action::ServerGoalHandle<ExecuteProcessAction>;

// 定义状态常量
extern const std::string STATUS_WAITING;
extern const std::string STATUS_INITIALIZING;
extern const std::string STATUS_RUNNING;
extern const std::string STATUS_PAUSED;
extern const std::string STATUS_FINISHED;
extern const std::string STATUS_FAILED;

class ProcessActionServer : public rclcpp::Node
{
public:
    explicit ProcessActionServer(const std::string& robot_id, const rclcpp::NodeOptions& options = rclcpp::NodeOptions());
    ~ProcessActionServer();

private:
    // 机器人ID
    std::string robot_id_;
    
    // Action Server
    rclcpp_action::Server<ExecuteProcessAction>::SharedPtr action_server_;
    
    // 工艺动作树工厂和执行器
    BT::BehaviorTreeFactory factory_;
    std::unique_ptr<BT::Tree> current_process_tree_;
    std::unique_ptr<BT::Groot2Publisher> groot2_publisher_;
    
    // 行为树路径解析器
    std::unique_ptr<ProcessTreePathResolver> path_resolver_;
    
    // 工艺动作类型到文件的映射（旧版映射，兼容性保留）
    std::map<std::string, std::string> process_action_mapping_;
    
    // 配置参数
    std::string config_file_path_;
    std::string hierarchical_config_file_path_; // 新增：层次化配置文件路径
    std::string tree_file_path_;
    int max_concurrent_actions_;
    int default_timeout_;
    unsigned int groot2_port_;
    
    // 执行状态管理
    std::mutex execution_mutex_;
    std::atomic<bool> is_executing_;
    std::atomic<bool> preempt_requested_;  // 抢占请求标志
    std::thread execution_thread_;
    std::shared_ptr<GoalHandleExecuteProcessAction> current_goal_handle_;
    
    // Action Server 回调函数
    rclcpp_action::GoalResponse handle_goal(
        const rclcpp_action::GoalUUID & uuid,
        std::shared_ptr<const ExecuteProcessAction::Goal> goal);
    
    rclcpp_action::CancelResponse handle_cancel(
        const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    void handle_accepted(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    // 核心执行函数
    void execute_process_action(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    // 辅助函数
    bool load_configuration();
    bool load_process_action_mapping();
    std::string get_process_tree_file_path(const std::string& process_action_type);
    bool setup_behavior_tree_factory();
    bool load_and_setup_tree(const std::string& tree_file_path,
                              const std::shared_ptr<const ExecuteProcessAction::Goal>& goal = nullptr);
    /**
     * @brief 发布反馈信息
     * @param goal_handle 目标句柄
     * @param current_step 当前执行步骤
     * @param current_operation 当前操作
     * @param progress_percent 进度百分比
     * @param status_message 状态消息
     * @param current_status 当前状态
     */
    void publish_feedback(
        const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle,
        const std::string& current_step,
        const std::string& current_operation,
        double progress_percent,
        const std::string& status_message,
        const std::string& current_status = "RUNNING");
    void cleanup_current_execution();
    
    // 插件注册
    void register_ros_nodes();
    void register_behavior_tree_plugins();
    
    // 参数声明和获取
    void declare_parameters();
    void get_parameters();
};

} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PROCESS_ACTION_SERVER_HPP_ 