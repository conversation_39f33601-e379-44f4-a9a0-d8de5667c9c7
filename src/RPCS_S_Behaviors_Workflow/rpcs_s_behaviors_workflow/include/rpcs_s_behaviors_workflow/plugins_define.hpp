#pragma once
#include "rpcs_s_behaviors_workflow/base/macros.h"
#include "rclcpp/rclcpp.hpp"
#include "rpcs_s_behaviors_workflow/base/StringUtils.h"
#include "rpcs_s_behaviors_workflow/base/util.h"

// 定义插件的名称空间
namespace rpcs_s_behaviors_workflow
{
    // 插件action类型的默认延时时间 (1秒)
    const int DEFAULT_DELAY_TIME = 1;  // 暂时改回整数，避免编译错误

    // 创建带特定domain_id的节点
    // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样
    // 注意: 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node，一律使用create_node_with_domain函数创建
    //       如果不是与电机节点RPCS_S_Controller_Motor通讯, 就不要使用create_node_with_domain函数
    rclcpp::Node::SharedPtr create_node_with_domain(const std::string& node_name, int domain_id);
}

