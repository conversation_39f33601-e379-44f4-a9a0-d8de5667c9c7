#ifndef RPCS_BEHAVIOR_TREE__PROCESS_TREE_PATH_RESOLVER_HPP_
#define RPCS_BEHAVIOR_TREE__PROCESS_TREE_PATH_RESOLVER_HPP_

#include <string>
#include <map>
#include <filesystem>
#include "yaml-cpp/yaml.h"
#include "rclcpp/rclcpp.hpp"

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 行为树路径解析器
 * 
 * 负责根据设备ID和动作类型，解析行为树文件的路径。
 * 支持新的层次化目录结构和旧的扁平化目录结构。
 */
class ProcessTreePathResolver
{
public:
    /**
     * @brief 构造函数
     * @param logger ROS日志记录器
     * @param config_path 配置文件路径
     * @param tree_base_path 行为树文件基础路径
     */
    ProcessTreePathResolver(
        rclcpp::Logger logger, 
        const std::string& config_path = "",
        const std::string& tree_base_path = "");
    
    /**
     * @brief 加载配置文件
     * @param config_path 配置文件路径
     * @return 加载是否成功
     */
    bool loadConfig(const std::string& config_path);

    /**
     * @brief 获取行为树文件路径
     * @param robot_id 设备ID
     * @param process_action_type 动作类型
     * @return 行为树文件的完整路径，如果找不到则返回空字符串
     */
    std::string getTreePath(
        const std::string& robot_id, 
        const std::string& process_action_type);

    /**
     * @brief 设置行为树文件基础路径
     * @param base_path 基础路径
     */
    void setTreeBasePath(const std::string& base_path) { tree_base_path_ = base_path; }

    /**
     * @brief 获取行为树文件基础路径
     * @return 基础路径
     */
    std::string getTreeBasePath() const { return tree_base_path_; }

    /**
     * @brief 检查是否使用新的目录结构
     * @return 是否使用新的目录结构
     */
    bool isUsingNewStructure() const { return using_new_structure_; }

    /**
     * @brief 清除缓存
     */
    void clearCache() { path_cache_.clear(); }

private:
    // ROS日志记录器
    rclcpp::Logger logger_;
    
    // 配置文件路径
    std::string config_path_;
    
    // 行为树文件基础路径
    std::string tree_base_path_;
    
    // 是否使用新的目录结构
    bool using_new_structure_ = false;

    // 旧结构的配置映射 {robot_id -> {action_type -> file_path}}
    std::map<std::string, std::map<std::string, std::string>> old_structure_mapping_;

    // 新结构的配置映射 {robot_id -> {process_type -> {action_type -> file_path}}}
    std::map<std::string, std::map<std::string, std::map<std::string, std::string>>> new_structure_mapping_;

    // 路径缓存 {robot_id_action_type -> full_path}
    std::map<std::string, std::string> path_cache_;

    /**
     * @brief 尝试加载旧格式的配置文件
     * @param config YAML配置节点
     * @return 加载是否成功
     */
    bool tryLoadOldFormat(const YAML::Node& config);

    /**
     * @brief 尝试加载新格式的配置文件
     * @param config YAML配置节点
     * @return 加载是否成功
     */
    bool tryLoadNewFormat(const YAML::Node& config);

    /**
     * @brief 在旧的目录结构中查找行为树文件
     * @param robot_id 设备ID
     * @param process_action_type 动作类型
     * @return 行为树文件的完整路径，如果找不到则返回空字符串
     */
    std::string findPathInOldStructure(
        const std::string& robot_id, 
        const std::string& process_action_type);

    /**
     * @brief 在新的目录结构中查找行为树文件
     * @param robot_id 设备ID
     * @param process_action_type 动作类型
     * @return 行为树文件的完整路径，如果找不到则返回空字符串
     */
    std::string findPathInNewStructure(
        const std::string& robot_id, 
        const std::string& process_action_type);

    /**
     * @brief 获取缓存键
     * @param robot_id 设备ID
     * @param process_action_type 动作类型
     * @return 缓存键
     */
    std::string getCacheKey(
        const std::string& robot_id, 
        const std::string& process_action_type) const;
};

} // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PROCESS_TREE_PATH_RESOLVER_HPP_ 