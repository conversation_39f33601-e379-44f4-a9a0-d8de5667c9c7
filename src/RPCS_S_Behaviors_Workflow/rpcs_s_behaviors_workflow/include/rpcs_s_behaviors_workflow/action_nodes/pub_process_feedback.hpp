#ifndef PUB_PROCESS_FEEDBACK_HPP_
#define PUB_PROCESS_FEEDBACK_HPP_

#include <string>
#include <memory>

#include "behaviortree_cpp/behavior_tree.h"
#include "behaviortree_cpp/bt_factory.h"

#include "rpcs_s_interfaces_behavior_tree/action/execute_process_action.hpp"
#include "rclcpp/rclcpp.hpp"

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 发布工艺执行反馈的节点
 * 
 * 该节点用于向action服务器发送工艺执行过程中的反馈信息
 */
class PubProcessFeedback : public BT::SyncActionNode
{
public:
  /**
   * @brief 构造函数
   * @param name 节点名称
   * @param config 节点配置
   */
  PubProcessFeedback(
    const std::string & name,
    const BT::NodeConfig & config)
  : BT::SyncActionNode(name, config)
  {
  }

  /**
   * @brief 定义节点的输入和输出端口
   * @return 节点配置
   */
  static BT::PortsList providedPorts()
  {
    return {
      BT::InputPort<std::string>("process_step", "当前执行的工艺步骤名称"),
      BT::InputPort<std::string>("status", "当前状态 (RUNNING, SUCCESS, FAILURE)"),
      BT::InputPort<double>("progress", "工艺执行进度百分比 (0.0-100.0)"),
      BT::InputPort<std::string>("message", "状态描述信息"),
      BT::InputPort<std::string>("operation", "当前操作描述")
    };
  }

  /**
   * @brief 执行节点逻辑
   * @return 节点执行结果
   */
  BT::NodeStatus tick() override
  {
    std::string process_step;
    std::string status;
    double progress = 0.0;
    std::string message;
    std::string operation;

    // 获取输入端口的值
    if (!getInput("process_step", process_step)) {
      process_step = "未知步骤";
    }

    if (!getInput("status", status)) {
      status = "RUNNING";
    }

    if (!getInput("progress", progress)) {
      progress = 0.0;
    }

    if (!getInput("message", message)) {
      message = "";
    }

    if (!getInput("operation", operation)) {
      operation = "";
    }

    // 确保进度值在有效范围内
    progress = std::min(std::max(progress, 0.0), 100.0);

    // 发布反馈信息到黑板，供action服务器使用
    auto * blackboard = this->config().blackboard;
    
    // 这里假设blackboard中有这些键值用于存储反馈信息
    // action服务器会从blackboard中获取这些信息并发布
    blackboard->set("current_process_step", process_step);
    blackboard->set("current_status", status);
    blackboard->set("progress_percent", progress);
    blackboard->set("status_message", message);
    blackboard->set("current_operation", operation);

    // 记录反馈信息
    RCLCPP_INFO(
      rclcpp::get_logger("PubProcessFeedback"),
      "发布工艺执行反馈: [步骤: %s] [状态: %s] [进度: %.1f%%] [操作: %s] [消息: %s]",
      process_step.c_str(), status.c_str(), progress, operation.c_str(), message.c_str());

    // 总是返回成功
    return BT::NodeStatus::SUCCESS;
  }
};

}  // namespace rpcs_s_behaviors_workflow

#endif  // PUB_PROCESS_FEEDBACK_HPP_ 