#pragma once

#include <string>
#include <vector>

namespace rpcs_s_behaviors_workflow
{

/**
 * @brief 获取所有行为树插件库的名称列表
 * 
 * @return 插件库名称列表
 */
inline const std::vector<std::string>& get_bt_plugin_libs()
{
    static const std::vector<std::string> bt_plugin_libs = {
        // 条件节点插件
        "condition_plugins",
        "SensorValueCheckInt",
        
        // ROS 通信插件
        "PubPrintMessage", 
        
        // 控制插件
        "ReturnFailure",
        "Retry",
        
        // 动作节点
        "AgvGoPoint",
        "ImageDetectionToZero",
        "SetProgress",
        "PubProcessFeedback",
        "GetActionParameters",
        "CleanStatus",
        "Wait",
        "FPCAdapterNumber",
        "ImageDetectionConversion",
        
        // 虚拟机器人控制节点
        "CorrectionPosition",
        "Conveyor",
        "FpcOperate",
        "MaterialState",
        "MaterialUpload",
        "PetStrip",
        "PressHoldBoard",
        // "ProductHoldBoard",  // 移除不存在的节点
        "ProductPowerOnDetect",
        "ProtectShell",
        "SemiFinishedProduct",
        "Sucker",
        "Workbench",
        
        // 算法接口节点
        "BoardAlign",
        "CommonAlign",
        "RobotAlign",
        "DepthFusion",
        
        // 相机控制节点
        "CameraCapture",
        
        // 电机控制节点
        "MotorPositionControl",
        "MotorVelocityControl",
        "MotorTorqueControl",
        "MotorHoming",
        "RobotArmControl",
        
        // IO操作节点
        "DigitalInputRead",
        "DigitalOutputRead", 
        "DigitalOutputWrite",
        
        // 视觉识别节点
        "ImageDetection"
    };
    return bt_plugin_libs;
}

}  // namespace rpcs_s_behaviors_workflow 

