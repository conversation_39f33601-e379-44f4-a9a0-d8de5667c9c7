#pragma once
/*****************************************************************************
 * @copyright Copyright (c) 2023.11 软件组
 * @file      macros.h
 * @brief     自定义宏
 * @details   方便阅读，让代码更加突出业务内容，不要一眼让人看到catch、else等异常处理逻辑而忽略函数的主要的业务功能。
 *
 * <AUTHOR>
 * @date      2023/11/23
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
// 指明函数参数是输入参数
#define PIN
// 指明函数参数是输出参数
#define POUT
// 指明函数参数即是输入参数也是输出参数
#define PINOUT

#define CASEWHEN(a, b, c) ((a) ? (b) : (c))
#define IF_FALSE_RETURN(status)  if (!(status)) { return; }
#define IF_TRUE_RETURN(status)   if (status) { return; }
#define IF_FALSE_RETURNVALUE(status, value)   if (!(status)) { return value; }
#define IF_TRUE_RETURNVALUE(status, value)   if (status) { return value; }
#define IF_CONTINUE(status)   if (status) { continue; }
#define IF_TRUE_BREAK(status)   if (status) { break; }
#define IF_TRUE_EXECUTE(status, expression)   if (status) { expression; }
#define IF_TRUE_EXECUTE_AND_RETURN(status, expression)   if (status) { expression; return; }
#define IF_TRUE_EXECUTE_AND_RETURNVALUE(status, expression, value)   if (status) { expression; return (value); }
#define WHILE_TRUE_EXECUTE(status, expression)   while (status) { expression; }
#define IF_FALSE_EXECUTE(status, expression)   if (!(status)) { expression; }
#define IF_TRUE_EXPRESSION_ELSE_EXPRESSION(status, a, b)   if (status) { a; } else { b; }

#define EMPTY_STRING ""
#define IsEmptyString(str) (nullptr==(str) or '\0'==*(str))
// 如果字符串参数str为NULL的话，替换为""空字符串，防止参数打印报错，其中PNRE为PARAM NULL REPLACE EMPRY的缩写
#define PNRE(str) (nullptr==(str) ? "" : (str))

// 是否json对象包含这个成员
#define IS_JSON_CONTAINS_MEMBER(a) j.contains(a)
// 是否json对象包含这个成员，并且成员是对象类型
#define IS_JSON_CONTAINS_OBJECT(a) (j.contains(a) && j[a].is_object())
// 起强调作用，与["weather"][0]["main"]相比，这样写JSON_ARRAY0("weather", "main")更不容易手误
#define JSON_ARRAY0(arr, a) j[arr][0][a]
// 是否json对象包含这个成员，并且成员是数组类型
#define IS_JSON_CONTAINS_ARRAY(a) (j.contains(a) && j[a].is_array() && !j[a].empty())
// 是否json对象包含这个成员，并且成员是整数类型，包含int、long long int类型
#define IS_JSON_CONTAINS_MEMBER_INT(a) (j.contains(a) && j[a].is_number_integer())
// 是否json对象包含这个成员，并且成员是浮点类型类型，包含int、long long int、float、double类型
#define IS_JSON_CONTAINS_MEMBER_DOUBLE(a) (j.contains(a) && (j[a].is_number_integer() || j[a].is_number_float()))
// 是否json对象包含这个成员，并且成员是浮点类型类型，包含int、long long int、float、double类型
#define IS_JSON_CONTAIN_MEMBER_DOUBLE(obj, a) (j[obj].contains(a) && (j[obj][a].is_number_integer() || j[obj][a].is_number_float()))
// 是否json对象包含这个成员，并且成员是浮点类型类型，包含int、long long int、float、double类型
#define IS_JSON_OBJECT_CONTAINS_MEMBER_DOUBLE(obj, a) (j.contains(obj) && j[obj].is_object() && IS_JSON_CONTAIN_MEMBER_DOUBLE(obj, a))
// 是否json对象包含这个成员，并且成员是bool类型
#define IS_JSON_CONTAINS_MEMBER_BOOL(a) (j.contains(a) && j[a].is_boolean())
// 是否json对象包含这个成员，并且成员是字符串类型
#define IS_JSON_CONTAINS_MEMBER_STRING(a) (j.contains(a) && j[a].is_string())
// 是否json对象包含这个成员，并且成员是字符串类型，并且字符串不为空
#define IS_JSON_CONTAINS_MEMBER_VALID_STRING(a) (j.contains(a) && j[a].is_string() && !j[a].get<std::string>().empty())
// 是否json对象包含这个成员，并且成员是字符串类型，并且字符串不为空
#define IS_JSON_CONTAIN_MEMBER_VALID_STRING(obj, a) (j[obj].contains(a) && j[obj][a].is_string() && !j[obj][a].get<std::string>().empty())
// 是否json数组下标为0的对象包含这个成员，并且成员是字符串类型，并且字符串不为空
#define IS_JSON_ARRAY0_CONTAIN_MEMBER_VALID_STRING(obj, a) (j[obj][0].contains(a) && j[obj][0][a].is_string() && !j[obj][0][a].get<std::string>().empty())


