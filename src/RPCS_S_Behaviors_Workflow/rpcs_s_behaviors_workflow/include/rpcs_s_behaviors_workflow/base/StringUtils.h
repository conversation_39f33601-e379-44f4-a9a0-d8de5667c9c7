#pragma once
/*****************************************************************************
 * @copyright Copyright (c) 2023.11 软件组
 * @file      StringUtils.h
 * @brief
 * @details   framework工具库对象string_utils字符串工具
 *
 * <AUTHOR>
 * @date      2023/12/04
 * @version
 * @attention
 * @remark    
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "rpcs_s_behaviors_workflow/base/macros.h"
#include <array>
#include <string>

namespace rpcs_s_behaviors_workflow
{
class StringUtils
{
public:
    // 去除字符串的前导和尾随空白字符，返回值为“修剪”后的字符串
    // 部分类型可以转换为string类型，却不能转为string_view类型，比如json[key]。所以保留了string类型的Trim函数
    static std::string Trim(const std::string & str);

    // 去除字符串的前导和尾随空白字符，返回值为“修剪”后的字符串
    // 由于const char*可以同时转化为string、string_view类型，为了不报ambiguous错误，保留了const char*类型的Trim函数
    static std::string Trim(const char * str);

    // 是否trim之后的字符串是空字符串，返回true，表示是空字符串，返回false表示不是空字符串。
    static bool IsTrimEmpty(const std::string & str);

    // 去除字符串的前导和尾随空白字符，返回值为“修剪”后的视图内容
    // 使用这个trim函数时，原始的std::string_view不会被修改，因为std::string_view不拥有其所指向的字符串数据。
    // 相反，trim函数返回一个新的std::string_view实例，该实例引用原始字符串的一个子串，从而提供了一个视图到“修剪”后的内容。
    static std::string_view Trim(std::string_view sv);

    // 移除字符串中所有空白字符，返回新字符串
    static std::string RemoveWhitespace(const std::string& input);

    // 移除源字符串中所有空白字符，修改源字符串
    static std::string RemoveOrgStrWhitespace(PINOUT std::string & input);

    // 将std::array转化为string
    static std::string ArrayToString(const std::array<std::string, 5> & arr);

    // 是否是有效的十六进制字符串
    static bool IsValidHexString(const std::string &str);

    //将字符串改为大写，但是不改变原有字符串
    static std::string ToUpperCopy(std::string src);

    // 如果sep刚好是两个字符，且字符串是以sep的两个字符开头和结尾，那么将原始字符串中的开头、结尾字符删除。
    // 使用场景：有些https返回的报文是json数组，且数组中只有一组数据，那么把[]剔除后，直接用json解析更为方便。
    static std::string RemoveStartEndCharacters(const std::string & str, const std::string & sep);

    // 判断两个字符串是否相等
    static bool IsStringsEqual(const char* str1, const char* str2);

    // 判断两个字符串是否相等，忽略大小写
    static bool IsStringsEqualIgnoreCase(const std::string & str1, const std::string & str2);

    // 判断字符串str1是否包含字符串str2
    static bool IsContainStr(const std::string & str1, const std::string & str2);

    // 判断字符串str1是否包含字符串str2
    static bool IsContainStr(const std::string & str1, const char* str2);

    // 判断字符串str1是否包含字符串str2，忽略大小写
    static bool IsContainStrIgnoreCase(const std::string & str1_org, std::string str2_org);

    //判断一个字符串是否以指定字符串开头
    static bool CheckStrStart(const std::string & str, const char *comStr);

    //判断一个字符串是否以指定字符串开头
    static bool CheckStrStart(const char *str, const char *comStr);

    //判断一个字符串是否以指定字符串结尾
    static bool CheckStrEnd(const std::string & str, const char *comStr);

    // 如果一个字符串是double转化的字符串，移除字符串小数点之后，结尾的0
    // 比如转114.031600为114.0316
    static void RemoveDoubleStrTailZero(PINOUT std::string & str);

    // 获取最后一个分隔符最后的数据
    // 比如，由 /upload/face/ranvoo 获取到 ranvoo
    static std::string SubstrEndSeq(const std::string & str, const char seq);

    // 从字符串中提取子串，比如从12345[678]9中根据[]可以提取到678子串
    // 此函数一般结合StringUtils::IsFindSubstringMultiple一起使用，精确保障业务场景，避免多个子串出现的场景。
    static std::string ExtractContent(const std::string& str, const char* seq);

    // 用于SQL语句拼接，比如VALUES(13)可以得到 VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) 
    static std::string SqlValuesConcat(int n);

    // 剔除字符串结尾的几个字符，number代表剔除几个字符
    static std::string RemoveLastChars(const std::string& input, int number);

    // 将一段内存转为十六进制字符串，方便spdlog打印，主要用于打印日志、问题定位。
    static std::string ToHexString(const void *content, const size_t len);

    // 将一段内存转为十六进制字符串，方便spdlog打印，主要用于打印日志、问题定位。
    static std::string ToHexString(const void *content, const int len);

    // 将地址转为十六进制字符串
    static std::string ToHexAddress(const void * addr);
};

} // namespace rpcs_s_behaviors_workflow


