#pragma once
/*****************************************************************************
 * @copyright Copyright (c) 2023.11 软件组
 * @file      util.h
 * @brief
 * @details   framework工具库对象集合
 *
 * <AUTHOR>
 * @date      2023/11/23
 * @version
 * @attention
 * @remark    
             【需求背景】
                   util工具库，函数默认返回0是成功，返回其他是发生错误
 * SDK版本： 1.0.0
 * @par 修改日志:
 *****************************************************************************/
#include "rpcs_s_behaviors_workflow/base/macros.h"
#include <array>
#include <unordered_set>
#include <vector>
#include <string>

namespace rpcs_s_behaviors_workflow
{
class Utils
{
public:
    // 判断集合中是否包含某个数据，全部都用contains函数，可以重载，做定制化开发
    static bool contains(const std::array<std::string, 5> & arr, const std::string & value);

    // std::string转为long long int 即long long int的函数，转换失败会抛异常
    static long long int atoll(const std::string & str);

    // std::string转为int的函数，转换失败会抛异常
    static int atoi(const std::string & str);

    // 判断字符串std::string是否可以转换为long long int 即long long int类型
    static bool can_atoll(const std::string & str);

    // std::string转为double类型，转换失败会抛异常
    static double atof(const std::string & str);

    // 判断字符串std::string是否可以转换为double类型
    static bool can_atof(const std::string & str);

    // 从文件路径中提取文件名，类似basename命令
    static std::string basename(const std::string& path);

    // 检查文件是否存在， 目录存在时返回true 目录不存在时返回false
    // 保险起见，建议此处使用全路径
    static bool IsFileExist(const std::string & path);

    // 检查目录是否存在， 目录存在时返回true 目录不存在时返回false
    // 保险起见，建议此处使用全路径
    static bool IsPathExist(const std::string & filePath);

    // 如果目录不存在则创建目录
    static bool CreateDirectories(const std::string & path);

    // 移除文件
    static bool RemoveFile(const std::string & fileFullPath);

    // 拷贝文件
    static bool CopyFile(const std::string & srcFullPath, const std::string & destFullPath);

    // 移动文件
    static bool MoveFile(const std::string & srcFullPath, const std::string & destFullPath);

    // 读取文件大小
    // 调用此函数前，最好检查并确保文件存在
    static long long int GetFileSize(const std::string & filename);

    // 获取system函数的输出内容
    static std::string GetSystemEchoContent(const std::string & cmd);

    // 读取文件内容，文件成功打开，返回true； 否则返回false； isTrim默认为true，因为大部分实际应用，还是需要trim的。
    static bool ReadFileContent(const std::string & fileFullPath, POUT std::string & fileContent, bool isTrim = true);

    // 读取文件内容，文件成功打开 并且为整型，返回true； 否则返回false
    static bool ReadFileContent(const std::string & fileFullPath, POUT long long int & fileContent);

    // 写入文件成功，返回true； 否则返回false
    static bool WriteFile(PIN const std::string & fileContent, const std::string & fileFullPath);

    // 获取可执行文件全路径
    static std::string GetExeFileFullPath();

    // 获取可执行文件路径
    static std::string GetExeFilePath();
    static std::string ToString(const std::vector<std::string> & arr);
    static std::string ToString(const std::vector<long long int> & arr);
    static std::string ToString(const std::unordered_set<std::string> & set);

    // 获取行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id
    // 获取RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID
    // 行为树中与电机节点RPCS_S_Controller_Motor通讯的所有rclcpp::Node的自定义上下文中的domain_id 与 RPCS_S_Controller_Motor工程使用的ROS_DOMAIN_ID一模一样
    static int GetControllerMotorNodeRosDomainId();
};

} // namespace rpcs_s_behaviors_workflow


