<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 机械臂控制示例行为树 -->
    <BehaviorTree ID="RobotArmControlExample">
        <Sequence name="机械臂控制序列">
            <!-- 第一步：移动到初始位置 -->
            <RobotArmControl 
                strDeviceName="/robot1" 
                intProjectId="1"
                intSpeedMultiplier="50"
                floatPositionX="100.0"
                floatPositionY="200.0"
                floatPositionZ="300.0"
                floatRotationRx="0.0"
                floatRotationRy="0.0"
                floatRotationRz="0.0"
                intFunctionData0="1"
                intFunctionData1="0"
                intFunctionData2="0"
                intFunctionData3="0"
                intFunctionData4="0"
                intFunctionData5="0"
                intTimeoutMs="30000"
                boolOutputSuccess="{init_success}"
                strOutputErrorMessage="{init_message}"
                doubleExecutionTime="{init_time}"
                floatCurrentX="{current_x}"
                floatCurrentY="{current_y}"
                floatCurrentZ="{current_z}"
                doubleProgressPercentage="{progress}"
                strCurrentStatus="{status}" />
            
            <!-- 检查第一步是否成功 -->
            <Script code="init_success == true" />
            
            <!-- 第二步：执行抓取操作 -->
            <RobotArmControl 
                strDeviceName="/robot1" 
                intProjectId="2"
                intSpeedMultiplier="30"
                floatPositionX="150.0"
                floatPositionY="250.0"
                floatPositionZ="280.0"
                floatRotationRx="10.0"
                floatRotationRy="0.0"
                floatRotationRz="45.0"
                intFunctionData0="1"
                intFunctionData1="1"
                intFunctionData2="0"
                intFunctionData3="0"
                intFunctionData4="0"
                intFunctionData5="0"
                intTimeoutMs="45000"
                boolOutputSuccess="{grasp_success}"
                strOutputErrorMessage="{grasp_message}"
                doubleExecutionTime="{grasp_time}"
                boolProgramRunning="{program_running}"
                boolEmergencyStop="{emergency_stop}"
                boolMotorPowerStatus="{motor_power}" />
            
            <!-- 检查第二步是否成功 -->
            <Script code="grasp_success == true" />
            
            <!-- 第三步：移动到目标位置 -->
            <RobotArmControl 
                strDeviceName="/robot1" 
                intProjectId="3"
                intSpeedMultiplier="80"
                floatPositionX="300.0"
                floatPositionY="400.0"
                floatPositionZ="350.0"
                floatRotationRx="0.0"
                floatRotationRy="0.0"
                floatRotationRz="90.0"
                intFunctionData0="1"
                intFunctionData1="1"
                intFunctionData2="0"
                intFunctionData3="0"
                intFunctionData4="0"
                intFunctionData5="0"
                intTimeoutMs="60000"
                boolOutputSuccess="{move_success}"
                strOutputErrorMessage="{move_message}"
                doubleExecutionTime="{move_time}" />
            
            <!-- 检查第三步是否成功 -->
            <Script code="move_success == true" />
            
            <!-- 第四步：释放物体 -->
            <RobotArmControl 
                strDeviceName="/robot1" 
                intProjectId="4"
                intSpeedMultiplier="50"
                floatPositionX="300.0"
                floatPositionY="400.0"
                floatPositionZ="350.0"
                floatRotationRx="0.0"
                floatRotationRy="0.0"
                floatRotationRz="90.0"
                intFunctionData0="0"
                intFunctionData1="0"
                intFunctionData2="0"
                intFunctionData3="0"
                intFunctionData4="0"
                intFunctionData5="0"
                intTimeoutMs="30000"
                boolOutputSuccess="{release_success}"
                strOutputErrorMessage="{release_message}"
                doubleExecutionTime="{release_time}" />
            
            <!-- 最终检查 -->
            <Script code="release_success == true" />
            
            <!-- 输出最终状态 -->
            <Script code="
                std::cout &lt;&lt; '机械臂控制完成' &lt;&lt; std::endl;
                std::cout &lt;&lt; '初始化时间: ' &lt;&lt; init_time &lt;&lt; 's' &lt;&lt; std::endl;
                std::cout &lt;&lt; '抓取时间: ' &lt;&lt; grasp_time &lt;&lt; 's' &lt;&lt; std::endl;
                std::cout &lt;&lt; '移动时间: ' &lt;&lt; move_time &lt;&lt; 's' &lt;&lt; std::endl;
                std::cout &lt;&lt; '释放时间: ' &lt;&lt; release_time &lt;&lt; 's' &lt;&lt; std::endl;
            " />
        </Sequence>
    </BehaviorTree>
    
    <!-- 多机械臂协调示例 -->
    <BehaviorTree ID="MultiArmCoordinationExample">
        <Parallel success_threshold="2" failure_threshold="1">
            <!-- 机械臂1执行任务 -->
            <Sequence name="Arm1任务">
                <RobotArmControl 
                    strDeviceName="/robot1" 
                    intProjectId="10"
                    intSpeedMultiplier="70"
                    floatPositionX="100.0"
                    floatPositionY="100.0"
                    floatPositionZ="200.0"
                    intFunctionData0="1"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{arm1_success}" />
                <Script code="arm1_success == true" />
            </Sequence>
            
            <!-- 机械臂2执行任务 -->
            <Sequence name="Arm2任务">
                <RobotArmControl 
                    strDeviceName="/RobotArm2" 
                    intProjectId="20"
                    intSpeedMultiplier="70"
                    floatPositionX="200.0"
                    floatPositionY="200.0"
                    floatPositionZ="250.0"
                    intFunctionData0="1"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="30000"
                    boolOutputSuccess="{arm2_success}" />
                <Script code="arm2_success == true" />
            </Sequence>
        </Parallel>
    </BehaviorTree>
    
    <!-- 错误处理示例 -->
    <BehaviorTree ID="ErrorHandlingExample">
        <Fallback name="带错误处理的机械臂控制">
            <!-- 尝试主要操作 -->
            <Sequence name="主要操作">
                <RobotArmControl 
                    strDeviceName="/robot1" 
                    intProjectId="5"
                    intSpeedMultiplier="100"
                    floatPositionX="200.0"
                    floatPositionY="300.0"
                    floatPositionZ="400.0"
                    intFunctionData0="1"
                    intFunctionData1="1"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="45000"
                    boolOutputSuccess="{main_success}"
                    strOutputErrorMessage="{main_error}"
                    boolEmergencyStop="{emergency}" />
                <Script code="main_success == true" />
            </Sequence>
            
            <!-- 检查是否是急停错误 -->
            <Sequence name="急停处理">
                <Script code="emergency == true" />
                <Script code="
                    std::cout &lt;&lt; '检测到急停，执行安全停止程序' &lt;&lt; std::endl;
                " />
                <!-- 执行安全回零 -->
                <RobotArmControl 
                    strDeviceName="/robot1" 
                    intProjectId="0"
                    intSpeedMultiplier="20"
                    floatPositionX="0.0"
                    floatPositionY="0.0"
                    floatPositionZ="0.0"
                    intFunctionData0="0"
                    intFunctionData1="0"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="60000" />
            </Sequence>
            
            <!-- 其他错误的重试处理 -->
            <Sequence name="重试处理">
                <Script code="
                    std::cout &lt;&lt; '操作失败，错误信息: ' &lt;&lt; main_error &lt;&lt; std::endl;
                    std::cout &lt;&lt; '尝试重新执行...' &lt;&lt; std::endl;
                " />
                <!-- 重试相同操作，但降低速度 -->
                <RobotArmControl 
                    strDeviceName="/robot1" 
                    intProjectId="5"
                    intSpeedMultiplier="50"
                    floatPositionX="200.0"
                    floatPositionY="300.0"
                    floatPositionZ="400.0"
                    intFunctionData0="1"
                    intFunctionData1="1"
                    intFunctionData2="0"
                    intFunctionData3="0"
                    intFunctionData4="0"
                    intFunctionData5="0"
                    intTimeoutMs="60000" />
            </Sequence>
        </Fallback>
    </BehaviorTree>
</root> 