<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 视觉识别示例：机械臂归零识别 -->
    <BehaviorTree ID="ImageDetectionExample">
        <Sequence name="视觉识别流程">
            
            <!-- 示例1：基本机械臂归零识别 -->
            <Sequence name="基本归零识别">
                <ImageDetection 
                    strDetectType="ROBOT_ALIGN"
                    strNamespace="/Robot1"
                    intTimeoutMs="5000"
                    boolOutputSuccess="{detection_success}"
                    strOutputErrorCode="{error_code}"
                    strOutputMessage="{message}"
                    doubleResultX="{offset_x}"
                    doubleResultY="{offset_y}"
                    doubleResultRz="{offset_rz}"
                    strDetectionSummary="{summary}" />
                
                <!-- 检查检测是否成功 -->
                <Fallback>
                    <!-- 成功分支 -->
                    <Sequence>
                        <Script code="detection_success == true" />
                        <PubPrintMessage strTopicName="✅ 归零识别成功: {summary}" />
                    </Sequence>
                    
                    <!-- 失败分支 -->
                    <Sequence>
                        <PubPrintMessage strTopicName="❌ 归零识别失败: {message}" />
                        <ReturnFailure />
                    </Sequence>
                </Fallback>
            </Sequence>
            
            <!-- 示例2：指定相机IP的识别 -->
            <Sequence name="指定相机识别">
                <ImageDetection 
                    strDetectType="ROBOT_ALIGN"
                    strCameraIp="*************"
                    strNamespace="/Robot1"
                    intTimeoutMs="8000"
                    boolOutputSuccess="{detection_success2}"
                    strOutputErrorCode="{error_code2}"
                    strOutputMessage="{message2}"
                    doubleResultX="{offset_x2}"
                    doubleResultY="{offset_y2}"
                    doubleResultRz="{offset_rz2}"
                    strDetectionSummary="{summary2}" />
                
                <Fallback>
                    <Sequence>
                        <Script code="detection_success2 == true" />
                        <PubPrintMessage strTopicName="✅ 指定相机识别成功: {summary2}" />
                    </Sequence>
                    <PubPrintMessage strTopicName="❌ 指定相机识别失败: {message2}" />
                </Fallback>
            </Sequence>
            
            <!-- 示例3：使用相机组的识别 -->
            <Sequence name="相机组识别">
                <ImageDetection 
                    strDetectType="ROBOT_ALIGN"
                    strCameraGroup="arm_cameras"
                    strNamespace="/Robot1"
                    intTimeoutMs="6000"
                    boolOutputSuccess="{detection_success3}"
                    strOutputErrorCode="{error_code3}"
                    strOutputMessage="{message3}"
                    doubleResultX="{offset_x3}"
                    doubleResultY="{offset_y3}"
                    doubleResultRz="{offset_rz3}"
                    strDetectionSummary="{summary3}" />
                
                <Fallback>
                    <Sequence>
                        <Script code="detection_success3 == true" />
                        <PubPrintMessage strTopicName="✅ 相机组识别成功: {summary3}" />
                    </Sequence>
                    <PubPrintMessage strTopicName="❌ 相机组识别失败: {message3}" />
                </Fallback>
            </Sequence>
            
            <!-- 示例4：综合使用识别结果进行机械臂补偿 -->
            <Sequence name="识别结果应用">
                <!-- 先进行识别 -->
                <ImageDetection 
                    strDetectType="ROBOT_ALIGN"
                    strNamespace="/Robot1"
                    intTimeoutMs="5000"
                    boolOutputSuccess="{final_success}"
                    doubleResultX="{final_x}"
                    doubleResultY="{final_y}"
                    doubleResultRz="{final_rz}"
                    strDetectionSummary="{final_summary}" />
                
                <!-- 判断识别结果并应用补偿 -->
                <Fallback>
                    <!-- 识别成功：应用补偿 -->
                    <Sequence>
                        <Script code="final_success == true" />
                        <PubPrintMessage strTopicName="📍 准备应用偏差补偿: {final_summary}" />
                        
                        <!-- 这里可以连接机械臂控制节点，使用偏差值进行位置补偿 -->
                        <RobotArmControl 
                            strDeviceName="/robot1"
                            intProjectId="999"
                            intSpeedMultiplier="50"
                            floatPositionX="{final_x}"
                            floatPositionY="{final_y}"
                            floatRotationRz="{final_rz}"
                            intFunctionData0="0"
                            intFunctionData1="0"
                            intFunctionData2="0"
                            intFunctionData3="0"
                            intFunctionData4="0"
                            intFunctionData5="0"
                            intTimeoutMs="30000"
                            boolOutputSuccess="{arm_success}"
                            strOutputMessage="{arm_message}" />
                        
                        <Fallback>
                            <Sequence>
                                <Script code="arm_success == true" />
                                <PubPrintMessage strTopicName="✅ 机械臂补偿动作完成" />
                            </Sequence>
                            <PubPrintMessage strTopicName="❌ 机械臂补偿动作失败: {arm_message}" />
                        </Fallback>
                    </Sequence>
                    
                    <!-- 识别失败：使用默认位置 -->
                    <Sequence>
                        <PubPrintMessage strTopicName="⚠️ 识别失败，使用默认位置" />
                        
                        <RobotArmControl 
                            strDeviceName="/robot1"
                            intProjectId="998"
                            intSpeedMultiplier="30"
                            floatPositionX="0.0"
                            floatPositionY="0.0"
                            floatRotationRz="0.0"
                            intFunctionData0="0"
                            intFunctionData1="0"
                            intFunctionData2="0"
                            intFunctionData3="0"
                            intFunctionData4="0"
                            intFunctionData5="0"
                            intTimeoutMs="30000"
                            boolOutputSuccess="{default_arm_success}"
                            strOutputMessage="{default_arm_message}" />
                        
                        <PubPrintMessage strTopicName="📍 默认位置动作完成" />
                    </Sequence>
                </Fallback>
            </Sequence>
            
            <PubPrintMessage strTopicName="🎯 视觉识别流程全部完成" />
        </Sequence>
    </BehaviorTree>
</root> 