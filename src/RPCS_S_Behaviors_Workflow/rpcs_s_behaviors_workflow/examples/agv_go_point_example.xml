<root BTCPP_format="4">
    <BehaviorTree ID="AgvGoPointExample">
        <Sequence>
            <!-- 初始化消息 -->
            <SetBlackboard output_key="current_status" value="RUNNING" />
            <SetBlackboard output_key="current_process_step" value="AGV移动测试" />
            <SetBlackboard output_key="current_operation" value="准备AGV移动" />
            <SetBlackboard output_key="status_message" value="正在准备AGV移动操作" />
            <SetBlackboard output_key="progress_percent" value="0.0" />
            
            <!-- AGV移动到安全位置 -->
            <Sequence>
                <SetBlackboard output_key="current_operation" value="移动AGV到安全位置" />
                <SetBlackboard output_key="status_message" value="正在请求AGV移动到安全位置" />
                <SetProgress doubleProgress="30.0" />
                
                <!-- 使用AgvGoPoint节点，并存储返回结果 -->
                <AgvGoPoint strGoPointName="ToSafePoint" 
                           strNamespace="/Robot1" 
                           intTimeoutMs="10000"
                           boolOutputSuccess="{agv_success}"
                           strOutputMessage="{agv_message}"
                           strOutputResponse="{agv_response}" />
                
                <!-- 根据返回结果更新状态 -->
                <SetBlackboard output_key="status_message" value="{agv_message}" />
                <SetProgress doubleProgress="50.0" />
            </Sequence>
            
            <!-- AGV移动到货架位置 -->
            <Sequence>
                <SetBlackboard output_key="current_operation" value="移动AGV到货架位置" />
                <SetBlackboard output_key="status_message" value="正在请求AGV移动到货架位置" />
                <SetProgress doubleProgress="60.0" />
                
                <!-- 使用AgvGoPoint节点，并存储返回结果 -->
                <AgvGoPoint strGoPointName="ToRacks" 
                           strNamespace="/Robot1" 
                           intTimeoutMs="15000"
                           boolOutputSuccess="{agv_success}"
                           strOutputMessage="{agv_message}"
                           strOutputResponse="{agv_response}" />
                
                <!-- 根据返回结果更新状态 -->
                <SetBlackboard output_key="status_message" value="{agv_message}" />
                <SetProgress doubleProgress="100.0" />
            </Sequence>
            
            <!-- 完成状态设置 -->
            <SetBlackboard output_key="current_status" value="SUCCESS" />
            <SetBlackboard output_key="status_message" value="AGV移动任务完成" />
        </Sequence>
    </BehaviorTree>
</root> 