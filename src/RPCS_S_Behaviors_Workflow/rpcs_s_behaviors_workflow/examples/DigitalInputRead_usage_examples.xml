<?xml version="1.0"?>
<!-- DigitalInputRead 使用示例 - 展示灵活的黑板变量名配置 -->
<!-- 注意：strBlackboardKey存储的是匹配状态（当前值 == 期望值时为true，否则为false） -->
<root BTCPP_format="4">
    
    <!-- 示例1：电机停止信号监控 -->
    <!-- 当输入地址4的值为HIGH时，motor5_stop_signal为true，触发电机停止 -->
    <BehaviorTree ID="MotorStopSignal">
        <Parallel success_count="1" failure_count="1">
            <MotorVelocityControl
                strDeviceId="robot1"
                intMotorId="5"
                doubleTargetVelocity="50.0"
                boolStopSignal="{motor5_stop_signal}"
                boolOutputSuccess="{motor5_success}" />
                
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="4"
                boolContinuousRead="true"
                doubleReadInterval="0.1"
                boolExpectedValue="true"
                strBlackboardKey="motor5_stop_signal"
                boolCurrentValue="{io4_current_value}" />
        </Parallel>
    </BehaviorTree>
    
    <!-- 示例2：多个传感器监控，各自的匹配状态 -->
    <BehaviorTree ID="MultipleSensors">
        <Parallel success_count="-1" failure_count="1">
            <!-- 料盘1位置检测：期望HIGH，匹配时tray1_detected为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="0"
                boolContinuousRead="true"
                doubleReadInterval="0.2"
                boolExpectedValue="true"
                strBlackboardKey="tray1_detected"
                boolCurrentValue="{tray1_raw_value}" />
                
            <!-- 料盘2位置检测：期望HIGH，匹配时tray2_detected为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="5"
                boolContinuousRead="true"
                doubleReadInterval="0.2"
                boolExpectedValue="true"
                strBlackboardKey="tray2_detected"
                boolCurrentValue="{tray2_raw_value}" />
                
            <!-- 料盘3位置检测：期望HIGH，匹配时tray3_detected为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="6"
                boolContinuousRead="true"
                doubleReadInterval="0.2"
                boolExpectedValue="true"
                strBlackboardKey="tray3_detected"
                boolCurrentValue="{tray3_raw_value}" />
        </Parallel>
    </BehaviorTree>
    
    <!-- 示例3：单次读取，不使用黑板匹配状态 -->
    <BehaviorTree ID="SimpleRead">
        <Sequence>
            <!-- 不指定strBlackboardKey，只通过输出端口返回原始值 -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="7"
                boolContinuousRead="false"
                boolExpectedValue="true"
                boolOutputSuccess="{read_success}"
                boolInputValue="{pressure_sensor_raw_value}" />
                
            <!-- 根据读取结果执行不同操作 -->
            <Switch case_1="read_success" case_2="!read_success">
                <SequenceNode name="case_1">
                    <AlwaysSuccess />
                </SequenceNode>
                <SequenceNode name="case_2">
                    <AlwaysFailure />
                </SequenceNode>
            </Switch>
        </Sequence>
    </BehaviorTree>
    
    <!-- 示例4：反向逻辑检测 - 期望LOW状态 -->
    <BehaviorTree ID="ReverseLogic">
        <Parallel success_count="-1" failure_count="1">
            <!-- 安全开关检测：期望LOW（安全状态），匹配时safety_ok为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="8"
                boolContinuousRead="true"
                doubleReadInterval="0.1"
                boolExpectedValue="false"
                strBlackboardKey="safety_ok"
                boolCurrentValue="{safety_switch_raw}" />
                
            <!-- 故障指示器：期望LOW（无故障），匹配时no_fault为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="9"
                boolContinuousRead="true"
                doubleReadInterval="0.2"
                boolExpectedValue="false"
                strBlackboardKey="no_fault"
                boolCurrentValue="{fault_indicator_raw}" />
        </Parallel>
    </BehaviorTree>
    
    <!-- 示例5：多设备相同功能的状态监控 -->
    <BehaviorTree ID="MultiDeviceStatus">
        <Parallel success_count="-1" failure_count="1">
            <!-- Robot1工位完成信号：期望HIGH，完成时robot1_workstation_ready为true -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="10"
                boolContinuousRead="true"
                doubleReadInterval="0.5"
                boolExpectedValue="true"
                strBlackboardKey="robot1_workstation_ready"
                boolCurrentValue="{robot1_workstation_raw}" />
                
            <!-- Robot2工位完成信号：期望HIGH，完成时robot2_workstation_ready为true -->
            <DigitalInputRead
                strDeviceId="robot2"
                intInputAddress="10"
                boolContinuousRead="true"
                doubleReadInterval="0.5"
                boolExpectedValue="true"
                strBlackboardKey="robot2_workstation_ready"
                boolCurrentValue="{robot2_workstation_raw}" />
        </Parallel>
    </BehaviorTree>
    
    <!-- 示例6：智能匹配逻辑示例 -->
    <BehaviorTree ID="SmartMatching">
        <Sequence>
            <!-- 检测按钮是否被按下（期望HIGH） -->
            <DigitalInputRead
                strDeviceId="robot1"
                intInputAddress="15"
                boolContinuousRead="false"
                boolExpectedValue="true"
                strBlackboardKey="button_pressed"
                boolCurrentValue="{button_raw_state}" />
            
            <!-- 根据匹配状态执行不同分支 -->
            <Switch case_1="button_pressed" case_2="!button_pressed">
                <SequenceNode name="case_1">
                    <!-- 按钮已按下，执行相应操作 -->
                    <AlwaysSuccess />
                </SequenceNode>
                <SequenceNode name="case_2">
                    <!-- 按钮未按下，等待或执行其他操作 -->
                    <AlwaysFailure />
                </SequenceNode>
            </Switch>
        </Sequence>
    </BehaviorTree>
    
</root> 