<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="Motor5IO4Linkage">
        <Sequence name="Main">
            <!-- 并行执行：电机速度控制 和 IO输入监控 -->
            <Parallel success_count="1" failure_count="1">
                
                <!-- 电机5速度控制任务 -->
                <MotorVelocityControl
                    strDeviceId="robot1"
                    intMotorId="5"
                    doubleTargetVelocity="50.0"
                    doubleAcceleration="30.0"
                    doubleDeceleration="30.0"
                    doubleDuration="0.0"
                    doubleTargetCurrentLimit="15.0"
                    boolUsePositionLimits="false"
                    doubleTimeout="30.0"
                    boolStopSignal="{motor5_stop_signal}"
                    boolOutputSuccess="{motor5_success}"
                    strOutputMessage="{motor5_message}"
                    intErrorCode="{motor5_error_code}"
                    doubleCurrentVelocity="{current_velocity}"
                    doubleElapsedTime="{elapsed_time}"
                />
                
                <!-- IO输入4监控任务 - 直接使用DigitalInputRead的期望值匹配功能 -->
                <DigitalInputRead
                    strDeviceId="robot1"
                    intInputAddress="4"
                    intTimeoutMs="10000"
                    boolContinuousRead="true"
                    doubleReadInterval="0.1"
                    doubleDuration="30.0"
                    boolExpectedValue="true"
                    strBlackboardKey="motor5_stop_signal"
                    boolOutputSuccess="{io4_success}"
                    boolCurrentValue="{motor5_stop_signal}"
                    strOutputMessage="{io4_message}"
                    intErrorCode="{io4_error_code}"
                />
            </Parallel>
        </Sequence>
    </BehaviorTree>
</root>
