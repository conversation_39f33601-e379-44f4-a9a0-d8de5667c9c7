<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 与用户示例命令匹配的机械臂控制测试 -->
    <BehaviorTree ID="RobotArmMatchTest">
        <Sequence name="匹配示例命令的测试">
            <!-- 完全匹配用户提供的示例命令参数 -->
            <RobotArmControl 
                strDeviceName="/robot1"
                intProjectId="2"
                intSpeedMultiplier="80"
                floatPositionX="400.0"
                floatPositionY="200.0"
                floatPositionZ="300.0"
                floatRotationRx="0.0"
                floatRotationRy="0.0"
                floatRotationRz="0.0"
                intFunctionData0="1"
                intFunctionData1="0"
                intFunctionData2="0"
                intFunctionData3="0"
                intFunctionData4="0"
                intFunctionData5="0"
                intTimeoutMs="30000"
                boolOutputSuccess="{success}"
                strOutputErrorMessage="{error_message}"
                doubleExecutionTime="{exec_time}"
                doubleProgressPercentage="{progress}"
                strCurrentStatus="{status}" />
            
            <!-- 检查执行结果 -->
            <Script code="
                if (success) {
                    std::cout << '机械臂控制成功完成' << std::endl;
                    std::cout << '执行时间: ' << exec_time << '秒' << std::endl;
                    std::cout << '最终状态: ' << status << std::endl;
                } else {
                    std::cout << '机械臂控制失败: ' << error_message << std::endl;
                }
            " />
        </Sequence>
    </BehaviorTree>
</root> 