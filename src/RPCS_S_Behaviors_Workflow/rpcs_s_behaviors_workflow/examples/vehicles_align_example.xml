<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="VehiclesAlignExample">
        <Sequence name="载板贴合算法示例">
            <!-- 载板贴合算法执行 -->
            <VehiclesAlign name="执行载板贴合算法"
                          strImagePathA="/path/to/image_a.jpg"
                          strImagePathB="/path/to/image_b.jpg"
                          strParamOverrides="{&quot;threshold&quot;: &quot;0.8&quot;, &quot;max_iterations&quot;: &quot;100&quot;}"
                          doubleTimeout="15.0"
                          boolOutputSuccess="{success}"
                          strOutputMessage="{message}"
                          boolIsFinish="{is_finish}"
                          strMoveCommands="{move_commands}"
                          intMoveCommandsCount="{move_count}"
                          doubleOutputX="{x_value}"
                          doubleOutputY="{y_value}"
                          doubleOutputR="{r_value}"/>
            
            <!-- 检查结果 -->
            <Switch name="检查载板贴合结果" variable="{success}" case_eq="1">
                <Sequence name="成功处理">
                    <PubPrintMessage strMessage="载板贴合算法执行成功"/>
                    <PubPrintMessage strMessage="X偏移: {x_value}, Y偏移: {y_value}, 旋转: {r_value}"/>
                    <PubPrintMessage strMessage="移动命令数量: {move_count}"/>
                </Sequence>
                <Sequence name="失败处理">
                    <PubPrintMessage strMessage="载板贴合算法执行失败: {message}"/>
                </Sequence>
            </Switch>
        </Sequence>
    </BehaviorTree>
</root> 