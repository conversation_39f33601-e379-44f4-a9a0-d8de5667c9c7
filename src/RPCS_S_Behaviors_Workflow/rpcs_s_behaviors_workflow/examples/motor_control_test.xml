<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- 电机控制测试行为树 -->
    <BehaviorTree ID="MotorControlTest">
        <Sequence name="电机控制测试序列">
            <!-- 电机回零 -->
            <MotorHoming 
                strDeviceId="Robot1" 
                intMotorId="1"
                intHomingMethod="17"
                floatSpeedSwitch="20.0"
                floatSpeedZero="20.0" 
                intHomeOffset="0"
                doubleTimeout="30.0"
                boolOutputSuccess="{homing_success}"
                strOutputMessage="{homing_message}" />
            
            <!-- 检查回零是否成功 -->
            <Script code="homing_success == true" />
            
            <!-- 位置控制 -->
            <MotorPositionControl 
                strDeviceId="Robot1"
                intMotorId="1"
                doubleTargetPosition="100.0"
                boolAbsolutePosition="true"
                doubleMaxVelocity="50.0"
                doubleAcceleration="100.0"
                doubleDeceleration="100.0"
                doubleDwellTime="1.0"
                doubleTimeout="15.0"
                boolOutputSuccess="{position_success}"
                strOutputMessage="{position_message}" />
            
            <!-- 检查位置控制是否成功 -->
            <Script code="position_success == true" />
            
            <!-- 速度控制 -->
            <MotorVelocityControl 
                strDeviceId="Robot1"
                intMotorId="1"
                doubleTargetVelocity="30.0"
                doubleAcceleration="100.0"
                doubleDeceleration="100.0"
                doubleDuration="3.0"
                boolUsePositionLimits="true"
                doubleMinPosition="0.0"
                doubleMaxPosition="200.0"
                doubleTimeout="10.0"
                boolOutputSuccess="{velocity_success}"
                strOutputMessage="{velocity_message}" />
            
            <!-- 检查速度控制是否成功 -->
            <Script code="velocity_success == true" />
            
            <!-- 转矩控制 -->
            <MotorTorqueControl 
                strDeviceId="Robot1"
                intMotorId="1"
                doubleTargetTorque="0.5"
                doubleVelocityLimit="30.0"
                doubleTorqueSlope="1.0"
                doubleDuration="2.0"
                boolUsePositionLimits="true"
                doubleMinPosition="0.0"
                doubleMaxPosition="200.0"
                doubleTimeout="10.0"
                boolOutputSuccess="{torque_success}"
                strOutputMessage="{torque_message}" />
            
            <!-- 检查转矩控制是否成功 -->
            <Script code="torque_success == true" />
            
            <!-- 测试完成 -->
            <PubPrintMessage strMessage="电机控制测试完成!" />
        </Sequence>
    </BehaviorTree>
</root> 