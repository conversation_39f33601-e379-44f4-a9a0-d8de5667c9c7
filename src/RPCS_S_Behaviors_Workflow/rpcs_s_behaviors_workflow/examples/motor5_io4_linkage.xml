<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="Motor5IO4Linkage">
        <Sequence name="Main">
            <!-- 初始化变量 -->
            <Script code="io4_current_value := false; motor5_stop_signal := false; motor5_success := false; motor5_message := ''; motor5_error_code := 0" />
            
            <!-- 并行执行：电机速度控制 和 IO输入监控 -->
            <Parallel success_count="1" failure_count="1">
                
                <!-- 电机5速度控制任务 -->
                <MotorVelocityControl
                    strDeviceId="robot1"
                    intMotorId="5"
                    doubleTargetVelocity="50.0"
                    doubleAcceleration="30.0"
                    doubleDeceleration="30.0"
                    doubleDuration="0.0"
                    doubleTargetCurrentLimit="15.0"
                    boolUsePositionLimits="false"
                    doubleTimeout="30.0"
                    boolStopSignal="{motor5_stop_signal}"
                    boolOutputSuccess="{motor5_success}"
                    strOutputMessage="{motor5_message}"
                    intErrorCode="{motor5_error_code}"
                    doubleCurrentVelocity="{current_velocity}"
                    doubleElapsedTime="{elapsed_time}"
                />
                
                <!-- IO输入4监控任务 -->
                <Sequence name="IO4Monitor">
                    <!-- 连续读取IO输入4，期望值为1 -->
                    <DigitalInputRead
                        strDeviceId="robot1"
                        intInputAddress="4"
                        boolContinuousRead="true"
                        doubleReadInterval="0.1"
                        doubleDuration="30.0"
                        boolExpectedValue="true"
                        boolOutputSuccess="{io4_success}"
                        boolCurrentValue="{io4_current_value}"
                        strOutputMessage="{io4_message}"
                        intErrorCode="{io4_error_code}"
                    />
                    
                    <!-- 当IO4读取到期望值1时，设置电机5停止信号 -->
                    <Script code="if (io4_current_value === true) { motor5_stop_signal := true }" />
                </Sequence>
            </Parallel>
            
            <!-- 显示最终结果 -->
            <Script code="console.log('Motor5 result: ', motor5_success, ', IO4 value: ', io4_current_value)" />
        </Sequence>
    </BehaviorTree>
</root>
