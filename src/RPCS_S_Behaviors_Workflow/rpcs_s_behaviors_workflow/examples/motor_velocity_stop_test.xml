<?xml version="1.0"?>
<root BTCPP_format="4">
    <BehaviorTree ID="MotorVelocityStopTest">
        <Sequence name="Main">
            <!-- 设置停止信号为false -->
            <Script code="stop_signal := false" />
            
            <!-- 并行执行：速度控制 和 停止信号检测 -->
            <Parallel success_threshold="1" failure_threshold="1">
                <!-- 速度控制任务 -->
                <MotorVelocityControl
                    strDeviceId="Robot1"
                    intMotorId="1"
                    doubleTargetVelocity="100.0"
                    doubleAcceleration="50.0"
                    doubleDeceleration="50.0"
                    doubleDuration="10.0"
                    doubleTargetCurrentLimit="15.0"
                    boolUsePositionLimits="false"
                    doubleTimeout="15.0"
                    boolStopSignal="{stop_signal}"
                    boolOutputSuccess="{motor_success}"
                    strOutputMessage="{motor_message}"
                    intErrorCode="{motor_error_code}"
                    doubleCurrentVelocity="{current_velocity}"
                    doubleElapsedTime="{elapsed_time}"
                />
                
                <!-- 停止信号触发器 - 3秒后设置停止信号 -->
                <Sequence>
                    <Sleep msec="3000" />
                    <Script code="stop_signal := true; console.log('设置停止信号为true')" />
                </Sequence>
            </Parallel>
            
            <!-- 显示结果 -->
            <Script code="console.log('电机控制结果: ', motor_success, ', 消息: ', motor_message, ', 错误代码: ', motor_error_code)" />
        </Sequence>
    </BehaviorTree>
</root>
