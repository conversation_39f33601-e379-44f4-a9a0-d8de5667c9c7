# 电机控制节点使用说明

## 概述

本文档介绍RPCS行为树系统中新增的4个电机控制节点的使用方法。这些节点通过ROS2 Action接口与电机控制器通信，支持多设备控制。

## 节点列表

### 1. MotorPositionControl - 位置控制
- **功能**: 控制电机运动到指定位置
- **支持**: 绝对位置和相对位置控制
- **Action接口**: `/motor_X/position_control` 或 `/{device_id}/motor_X/position_control`

### 2. MotorVelocityControl - 速度控制  
- **功能**: 控制电机以指定速度运行
- **支持**: 时间限制和持续运行模式
- **Action接口**: `/motor_X/velocity_control` 或 `/{device_id}/motor_X/velocity_control`

### 3. MotorTorqueControl - 转矩控制
- **功能**: 控制电机输出指定转矩
- **支持**: 力控制和安全限制
- **Action接口**: `/motor_X/torque_control` 或 `/{device_id}/motor_X/torque_control`

### 4. MotorHoming - 回零控制
- **功能**: 执行电机回零操作
- **支持**: 多种回零方法（1-35）
- **Action接口**: `/motor_X/homing` 或 `/{device_id}/motor_X/homing`

## 使用方法

### 基本使用示例

```xml
<!-- 电机回零 -->
<MotorHoming 
    strDeviceId="Robot1" 
    intMotorId="1"
    intHomingMethod="17"
    floatSpeedSwitch="20.0"
    floatSpeedZero="20.0" />

<!-- 位置控制 -->
<MotorPositionControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetPosition="100.0"
    boolAbsolutePosition="true"
    doubleMaxVelocity="50.0"
    doubleAcceleration="100.0" />
```

### 多设备控制

```xml
<!-- 设备1的电机1 -->
<MotorPositionControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetPosition="100.0" />

<!-- 设备2的电机1 -->  
<MotorPositionControl 
    strDeviceId="Robot2"
    intMotorId="1"
    doubleTargetPosition="150.0" />
```

### 并行控制

```xml
<Parallel success_count="2" failure_count="1">
    <MotorPositionControl 
        strDeviceId="Robot1"
        intMotorId="1"
        doubleTargetPosition="100.0" />
    
    <MotorPositionControl 
        strDeviceId="Robot1"
        intMotorId="2"
        doubleTargetPosition="200.0" />
</Parallel>
```

## 主要参数说明

### 通用参数
- `strDeviceId`: 设备ID，默认"Robot1"
- `intMotorId`: 电机ID编号（必需）
- `doubleTimeout`: 超时时间（秒）

### 位置控制特有参数
- `doubleTargetPosition`: 目标位置（mm）
- `boolAbsolutePosition`: 位置模式（true=绝对，false=相对）
- `doubleMaxVelocity`: 最大速度（mm/s）
- `doubleAcceleration/doubleDeceleration`: 加减速度（mm/s²）

### 速度控制特有参数
- `doubleTargetVelocity`: 目标速度（mm/s）
- `doubleDuration`: 运行时间（s），0表示持续运行
- `boolUsePositionLimits`: 是否使用位置限制

### 转矩控制特有参数
- `doubleTargetTorque`: 目标转矩（Nm）
- `doubleVelocityLimit`: 最大速度限制（mm/s）
- `doubleTorqueSlope`: 转矩变化斜率（Nm/s）

### 回零控制特有参数
- `intHomingMethod`: 寻找原点方法（1-35），默认17
- `floatSpeedSwitch/floatSpeedZero`: 搜索速度（rpm），默认20.0
- `intHomeOffset`: 原点偏移量（mm）

## 输出端口

所有节点都提供以下通用输出：
- `boolOutputSuccess`: 操作是否成功
- `strOutputMessage`: 结果消息

各节点还提供特定的输出端口，如当前位置、速度、转矩等实时状态信息。

## 错误处理

节点实现了完整的错误处理机制：
- 参数验证
- 服务可用性检查
- 超时控制
- Action取消支持

## 测试

参考 `examples/motor_control_test.xml` 文件了解完整的测试用例。

## 编译

确保已安装 `rpcs_interfaces_motor` 包，然后在工作空间中执行：

```bash
colcon build --packages-select RPCS_S_Behaviors_workflow
```

## 注意事项

1. 使用前确保电机控制器服务正在运行
2. 检查Action接口名称是否与实际配置匹配
3. 注意多设备控制时的命名空间规则
4. 建议先进行回零操作再执行其他控制
5. 使用位置限制确保安全操作 