# 机器人工艺动作类型

本文档记录了三个机器人的工艺动作列表，包括所属工艺、动作名称、动作类型和动作参数。

## Robot1 动作列表

| 所属工艺 | 动作名称 | 动作类型 | 动作参数 |
|---------|---------|---------|---------|
| 灯板取送料工艺 | 状态自检 | LightBoardSelfCheck | 0 |
| 灯板取送料工艺 | 取送料 | LightBoardPickSendStock | 0 |
| 灯板码放工艺 | 重置码放 | StackLightBoards | 0 |
| 薄膜撕膜工艺 | 执行薄膜撕膜清洁 | RemoveReleaseFilm | 0 |
| 载板贴合工艺 | 执行载板贴合 | BondCarrier | 0 |

## Robot2 动作列表

| 所属工艺 | 动作名称 | 动作类型 | 动作参数 |
|---------|---------|---------|---------|
| PCBA取送料工艺 | 状态自检 | PcbaSelfCheck | 0 |
| PCBA取送料工艺 | 取送料 | PcbaPickSendStock | 0 |
| 载板贴合工艺 | 半成品转移 | MoveWIP | 0 |
| FPC插接+PCBA固定工艺 | 半成品连放 | PlaceWIP | 0 |
| FPC插接+PCBA固定工艺 | PCBA固放 | PlacePCBA | 0 |
| FPC插接+PCBA固定工艺 | 执行FPC转接头贴放 | DoPCAdapterPaste | 0 |
| FPC插接+PCBA固定工艺 | FPC开盖 | OpenFPC | 0 |
| 上盖检测 | 成品连放 | TransferFixture | 0 |
| 上盖检测 | 吸附成品放置OK区 | PlaceProduct | {"result": "OK"} |
| 上盖检测 | 吸附成品放置NG区 | PlaceProduct | {"result": "NG"} |

## Robot3 动作列表

| 所属工艺 | 动作名称 | 动作类型 | 动作参数 |
|---------|---------|---------|---------|
| 保护壳取送料工艺 | 状态自检 | ProCaseSelfCheck | 0 |
| 保护壳取送料工艺 | 到取料点 | ProCaseRouteToPickPoint | {"waypoints": [...]} |
| 保护壳取送料工艺 | 取送料 | ProCasePickSendStock | 0 |
| 保护壳取送料工艺 | 到安全位 | ProCaseRouteToSafePos | {"waypoints": [...]} |
| FPC插接+PCBA固定工艺 | PCBA螺丝锁付 | ScrewPCBA | 0 |
| FPC插接+PCBA固定工艺 | 执行FPC插接 | ConnectFPC | 0 |
| 保护壳固定工艺 | 执行保护壳固定 | FastenProCase | 0 |

## 动作类型说明

### 基础动作类型（按工艺区分）
- **LightBoardSelfCheck**: 灯板取送料工艺状态自检
- **LightBoardPickSendStock**: 灯板取送料操作
- **PcbaSelfCheck**: PCBA取送料工艺状态自检
- **PcbaPickSendStock**: PCBA取送料操作
- **ProCaseSelfCheck**: 保护壳取送料工艺状态自检
- **ProCasePickSendStock**: 保护壳取送料操作
- **ProCaseRouteToPickPoint**: 保护壳工艺路径规划移动到取料点
- **ProCaseRouteToSafePos**: 保护壳工艺路径规划移动到安全位

### 装配动作类型
- **StackLightBoards**: 灯板码放
- **RemoveReleaseFilm**: 薄膜撕膜清洁
- **BondCarrier**: 载板贴合
- **MoveWIP**: 半成品转移
- **PlaceWIP**: 半成品放置
- **PlacePCBA**: PCBA放置
- **DoPCAdapterPaste**: FPC转接头贴放
- **OpenFPC**: FPC开盖
- **TransferFixture**: 成品转移
- **PlaceProduct**: 成品放置（支持OK/NG区分）
- **ScrewPCBA**: PCBA螺丝锁付
- **ConnectFPC**: FPC插接
- **FastenProCase**: 保护壳固定

## 参数格式说明

- **0**: 无参数或使用默认参数
- **{"result": "OK"}**: JSON格式参数，指定结果为OK
- **{"result": "NG"}**: JSON格式参数，指定结果为NG  
- **{"waypoints": [...]}**: JSON格式参数，包含路径点数组

## 工艺流程概述

1. **Robot1**: 主要负责灯板相关工艺，包括取送料、码放、薄膜处理和载板贴合
2. **Robot2**: 主要负责PCBA相关工艺，包括取送料、FPC插接、PCBA固定和最终检测
3. **Robot3**: 主要负责保护壳相关工艺，包括取送料、螺丝锁付、FPC插接和保护壳固定

三个机器人协同工作，完成完整的产品装配流程。

## 命名规范说明

为了避免同名动作类型的冲突，本文档采用以下命名规范：

1. **大驼峰命名**: 所有动作类型名称采用大驼峰（PascalCase）格式，每个单词首字母大写，无下划线连接：
   - `LightBoard` - 灯板相关工艺前缀
   - `Pcba` - PCBA相关工艺前缀
   - `ProCase` - 保护壳相关工艺前缀

2. **功能描述命名**: 对于需要进一步区分的动作，在前缀后直接连接具体功能描述：
   - `RouteToPickPoint` - 移动到取料点
   - `RouteToSafePos` - 移动到安全位

3. **唯一性原则**: 确保每个动作类型名称在整个系统中唯一，避免命名冲突。
