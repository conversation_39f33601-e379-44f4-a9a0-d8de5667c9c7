# 原料柜上下料流程详细说明文档

## 流程概述

本文档详细描述原料柜上下料过程中的具体动作、执行时序和相关对象。流程适用于灯板、PCBA和保护壳三种物料类型的处理，提供精确的操作步骤和执行主体信息。

## 时序流程表

| 序号 | 时间点 | 执行对象 | 具体动作 | 输入/前置条件 | 输出/结果 |
|-----|-------|---------|---------|------------|----------|
| 1 | T0 | 用户 | 发起上料请求 | 指定物料类型(灯板/PCBA/保护壳) | 请求信息发送至控制系统 |
| 2 | T0+1s | 控制系统 | 接收请求并验证 | 用户请求信息 | 请求验证结果(通过/拒绝) |
| 3 | T0+2s | 控制系统 | 规划任务路径 | 验证通过的请求 | AGV路径规划、机械臂操作序列 |
| 4 | T0+3s | 控制系统 | 发送移动指令 | 完成的任务规划 | AGV移动指令 |
| 5 | T0+4s | AGV | 接收移动指令 | 控制系统发送的指令 | 确认接收 |
| 6 | T0+5s | AGV | 开始移动至原料柜 | 移动指令 | 实时位置信息 |
| 7 | T0+Xs | AGV | 到达指定位置 | 移动路径 | 位置到达信号 |
| 8 | T0+X+1s | 控制系统 | 接收位置信号并验证 | AGV位置信号 | 位置确认 |
| 9 | T0+X+2s | 控制系统 | 发送开门指令 | AGV到位确认 | 原料柜开门指令 |
| 10 | T0+X+3s | 原料柜 | 接收开门指令 | 控制系统指令 | 确认接收 |
| 11 | T0+X+4s | 原料柜 | 执行开门操作 | 开门指令 | 门状态变更为"开启" |
| 12 | T0+X+6s | 原料柜 | 发送门已开启信号 | 门完全打开 | 开门完成信号 |
| 13 | T0+X+7s | 控制系统 | 接收门状态并发送机械臂初始化指令 | 开门完成信号 | 机械臂初始化指令 |
| 14 | T0+X+8s | 机械臂 | 接收初始化指令并执行 | 控制系统指令 | 初始化完成信号 |
| 15 | T0+X+10s | 控制系统 | 发送取料指令 | 机械臂初始化完成 | 机械臂取料指令 |
| 16 | T0+X+11s | 机械臂 | 移动至取料位置 | 取料指令 | 到达取料位置 |
| 17 | T0+X+13s | 机械臂 | 执行抓取物料动作 | 到达取料位置 | 物料抓取状态(成功/失败) |
| 18 | T0+X+15s | 机械臂 | 移动至目标放置位置 | 物料抓取成功 | 到达放置位置 |
| 19 | T0+X+17s | 机械臂 | 执行放置物料动作 | 到达放置位置 | 物料放置状态(成功/失败) |
| 20 | T0+X+19s | 机械臂 | 返回安全位置 | 物料放置完成 | 到达安全位置 |
| 21 | T0+X+20s | 机械臂 | 发送操作完成信号 | 返回安全位置 | 操作完成状态报告 |
| 22 | T0+X+21s | 控制系统 | 接收操作状态并发送关门指令 | 机械臂操作完成信号 | 原料柜关门指令 |
| 23 | T0+X+22s | 原料柜 | 接收关门指令 | 控制系统指令 | 确认接收 |
| 24 | T0+X+23s | 原料柜 | 执行关门操作 | 关门指令 | 门状态变更为"关闭" |
| 25 | T0+X+25s | 原料柜 | 发送门已关闭信号 | 门完全关闭 | 关门完成信号 |
| 26 | T0+X+26s | 控制系统 | 接收门状态并发送AGV返回指令 | 关门完成信号 | AGV返回指令 |
| 27 | T0+X+27s | AGV | 接收返回指令 | 控制系统指令 | 确认接收 |
| 28 | T0+X+28s | AGV | 执行返回安全位置 | 返回指令 | AGV位置更新 |
| 29 | T0+X+Ys | AGV | 到达安全位置 | 返回路径 | 位置到达信号 |
| 30 | T0+X+Y+1s | 控制系统 | 生成任务完成报告 | 全部子任务状态 | 任务报告 |
| 31 | T0+X+Y+2s | 控制系统 | 发送任务完成通知 | 完成的任务报告 | 用户通知 |
| 32 | T0+X+Y+3s | 用户 | 接收任务完成反馈 | 控制系统通知 | 任务结束确认 |

*注: X表示AGV移动时间(视距离而定)，Y表示AGV返回时间(视距离而定)*

## 详细操作说明

### 1. 物料类型差异处理

**灯板取料操作**：
- 执行对象：机械臂
- 具体动作：
  1. 机械臂调整抓取器至灯板专用模式(T0+X+12s)
  2. 定位灯板边缘位置(T0+X+12.5s)
  3. 执行平行抓取动作(T0+X+13s)
  4. 提升灯板10cm(T0+X+14s)
  5. 确认抓取牢固度(T0+X+14.5s)

**PCBA取料操作**：
- 执行对象：机械臂
- 具体动作：
  1. 机械臂调整抓取器至PCBA专用模式(T0+X+12s)
  2. 定位PCBA四角位置(T0+X+12.5s)
  3. 执行多点抓取动作(T0+X+13s)
  4. 提升PCBA8cm(T0+X+14s)
  5. 确认抓取稳定性(T0+X+14.5s)

**保护壳取料操作**：
- 执行对象：机械臂
- 具体动作：
  1. 机械臂调整抓取器至保护壳专用模式(T0+X+12s)
  2. 定位保护壳内侧边缘(T0+X+12.5s)
  3. 执行侧面夹取动作(T0+X+13s)
  4. 提升保护壳12cm(T0+X+14s)
  5. 确认抓取压力值(T0+X+14.5s)

### 2. 关键参数规范

**AGV移动参数**：
- 执行对象：AGV
- 参数值：
  - 最大速度：1.2m/s
  - 加速度：0.5m/s²
  - 减速度：0.8m/s²
  - 接近原料柜时速度：0.3m/s
  - 精确定位误差：±5mm

**机械臂操作参数**：
- 执行对象：机械臂
- 参数值：
  - 取料移动速度：0.5m/s
  - 抓取力度：灯板(10N)、PCBA(8N)、保护壳(15N)
  - 放置精度：±2mm
  - 安全高度：物料上方15cm

**原料柜参数**：
- 执行对象：原料柜
- 参数值：
  - 开门速度：15cm/s
  - 开门角度：105°
  - 门完全开启识别延迟：0.5s
  - 关门速度：12cm/s

### 3. 异常情况处理时序

**物料抓取失败**：
- 执行对象：机械臂 → 控制系统
- 具体动作：
  1. 机械臂检测抓取失败(T0+X+14s)
  2. 发送错误代码至控制系统(T0+X+14.5s)
  3. 控制系统接收错误信息(T0+X+15s)
  4. 控制系统决定重试策略(T0+X+15.5s)
  5. 发送重试指令至机械臂(T0+X+16s)
  6. 机械臂重新执行抓取(T0+X+17s)
  7. 若三次失败，控制系统终止任务(T0+X+22s)

**AGV路径阻断**：
- 执行对象：AGV → 控制系统
- 具体动作：
  1. AGV检测到路径阻断(任意时间点)
  2. 立即停止移动并发送异常信号(检测后0.5s内)
  3. 控制系统接收异常信号(发送后0.5s内)
  4. 发送备用路径或等待指令(接收后1s内)
  5. AGV执行新指令或等待(接收新指令后1s内)
  6. 若15分钟内阻断未解除，终止任务

**原料柜开门故障**：
- 执行对象：原料柜 → 控制系统
- 具体动作：
  1. 原料柜检测到开门阻力异常(T0+X+5s)
  2. 停止开门并发送错误代码(T0+X+5.5s)
  3. 控制系统接收错误信息(T0+X+6s)
  4. 控制系统发送重试指令(T0+X+6.5s)
  5. 若重试失败，发送人工干预请求(T0+X+9s)

## 状态转换图

```
用户请求 → 控制系统验证 → AGV移动 → 原料柜开门 → 机械臂操作 → 
原料柜关门 → AGV返回 → 任务完成通知
```

每个主要状态包含子状态和状态转换条件：

1. **用户请求** → **控制系统验证**
   - 转换条件：完整的请求信息提交

2. **控制系统验证** → **AGV移动**
   - 转换条件：验证通过且规划完成

3. **AGV移动** → **原料柜开门**
   - 转换条件：AGV到达指定位置且位置误差在±5mm内

4. **原料柜开门** → **机械臂操作**
   - 转换条件：门完全开启(开启角度≥100°)且信号已发送

5. **机械臂操作** → **原料柜关门**
   - 转换条件：机械臂完成物料操作并返回安全位置

6. **原料柜关门** → **AGV返回**
   - 转换条件：门完全关闭且锁定确认

7. **AGV返回** → **任务完成通知**
   - 转换条件：AGV到达安全位置且状态正常

## 执行成功标准

1. **整体任务成功条件**：
   - 所有子任务按时序正确执行
   - 物料成功从原料柜取出/放入
   - 所有设备返回安全状态
   - 任务报告生成并发送至用户

2. **单步操作成功条件**：
   - AGV到位：位置误差≤5mm
   - 开门完成：开门角度≥100°
   - 物料抓取：传感器确认抓取成功，力度在规定范围内
   - 物料放置：位置误差≤2mm
   - 关门完成：门完全关闭且锁定

---

通过本文档，操作人员、维护人员和系统开发者可以准确理解每个动作的具体内容、精确执行时序和责任对象，确保原料柜上下料过程的有效实施和问题诊断。 