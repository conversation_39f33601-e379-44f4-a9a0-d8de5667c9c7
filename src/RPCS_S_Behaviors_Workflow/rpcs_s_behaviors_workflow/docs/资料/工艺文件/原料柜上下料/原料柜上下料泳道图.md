# 原料柜上下料泳道图

下面是原料柜上下料（灯板/PCBA/保护壳）流程的泳道图：

```mermaid
flowchart TD
    %% 泳道定义
    subgraph 用户
        User_Start([开始]) --> User_Request[发起上料请求]
        User_Request --> User_Wait[等待任务完成]
        User_Wait --> User_Receive[接收完成反馈]
        User_Receive --> User_End([结束])
    end

    subgraph 控制系统
        System_Receive[接收请求] --> System_Verify{验证任务}
        System_Verify -->|有效| System_Plan[规划任务]
        System_Verify -->|无效| System_Reject[拒绝任务]
        System_Plan --> System_Send[发送AGV指令]
        System_Monitor[监控执行状态]
        System_Process[处理执行结果]
        System_Report[生成完成报告]
    end

    subgraph AGV
        AGV_Receive[接收移动指令] --> AGV_Move[移动至原料柜]
        AGV_Move --> AGV_Arrive[到达指定位置]
        AGV_Arrive --> AGV_Signal[发送到达信号]
        AGV_Wait[等待操作完成]
        AGV_Return[返回初始位置]
    end

    subgraph 原料柜
        Cabinet_Receive[接收开门指令] --> Cabinet_Open[开门]
        Cabinet_Open --> Cabinet_Ready[准备就绪]
        Cabinet_Close[关门]
    end

    subgraph 机械臂
        Arm_Init[初始化] --> Arm_Wait[等待指令]
        Arm_Wait --> Arm_Move[移动至取料位]
        Arm_Move --> Arm_Grab[抓取物料]
        Arm_Grab --> Arm_Place[放置物料]
        Arm_Place --> Arm_Return[回到安全位置]
        Arm_Return --> Arm_Report[报告状态]
    end

    %% 对象间的交互连接（时序流）
    User_Request --> System_Receive
    System_Reject --> User_Receive
    System_Send --> AGV_Receive
    AGV_Signal --> System_Monitor
    System_Monitor --> Cabinet_Receive
    Cabinet_Ready --> System_Monitor
    System_Monitor --> Arm_Init
    Arm_Report --> System_Process
    System_Process --> Cabinet_Close
    Cabinet_Close --> AGV_Wait
    AGV_Wait --> AGV_Return
    AGV_Return --> System_Report
    System_Report --> User_Receive

    %% 错误处理路径
    Arm_Grab -->|失败| Arm_Error[报告错误]
    Arm_Error --> System_Process
    System_Process -->|错误| System_Error[错误处理]
    System_Error -->|重试| Arm_Init
    System_Error -->|终止| System_Abort[终止任务]
    System_Abort --> System_Report
```

## 图表说明

此泳道图展示了原料柜上下料流程中各系统之间的交互和时序关系：

1. **用户泳道**：展示用户操作流程，从发起请求到接收反馈
2. **控制系统泳道**：作为中央协调者，负责验证请求、分发指令和监控执行
3. **AGV泳道**：负责移动至原料柜位置和返回安全位置
4. **原料柜泳道**：负责执行开关门操作
5. **机械臂泳道**：负责物料的抓取和放置操作

图中箭头清晰地展示了各个组件之间的交互关系和时序，包括正常执行路径和错误处理路径。

## 另一种可视化方式

如果上面的Mermaid图无法正确渲染，可以使用以下简化版泳道图：

```
+------------+  +---------------+  +---------+  +----------+  +----------+
|    用户     |  |    控制系统    |  |   AGV   |  |  原料柜   |  |  机械臂   |
+------------+  +---------------+  +---------+  +----------+  +----------+
      |                |               |             |             |
      | 发起上料请求     |               |             |             |
      |--------------->|               |             |             |
      |                | 验证任务       |             |             |
      |                |-------------->|             |             |
      |                | 发送AGV指令    |             |             |
      |                |--------------->             |             |
      |                |               | 移动至原料柜  |             |
      |                |               |------------>|             |
      |                |               | 到达并发信号  |             |
      |                |<--------------|             |             |
      |                | 发送开门指令    |             |             |
      |                |------------------------------>             |
      |                |               |             | 开门操作     |
      |                |               |             |------------>|
      |                |               |             | 准备就绪     |
      |                |<------------------------------|             |
      |                | 初始化机械臂    |             |             |
      |                |------------------------------------------>|
      |                |               |             |             | 取料操作
      |                |               |             |             |-------->
      |                |               |             |             | 放置操作
      |                |               |             |             |-------->
      |                |               |             |             | 返回安全位置
      |                |<------------------------------------------|
      |                | 发送关门指令    |             |             |
      |                |------------------------------>             |
      |                |               |             | 关门操作     |
      |                |               |<------------|             |
      |                | 发送返回指令    |             |             |
      |                |--------------->             |             |
      |                |               | 返回安全位置  |             |
      |                |<--------------|             |             |
      |                | 生成完成报告    |             |             |
      | 接收完成反馈     |               |             |             |
      |<---------------|               |             |             |
      |                |               |             |             |
```

这个ASCII版本的泳道图可以作为备选方案，确保即使在不支持Mermaid语法的环境中也能查看流程。 