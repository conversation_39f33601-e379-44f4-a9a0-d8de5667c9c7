# 灯板料柜操作行为树说明

## 概述

本文档描述了灯板料柜操作相关的四个主要行为树的功能和它们之间的关系。这些行为树共同实现了原料柜上下料的完整流程，包括AGV移动、机械臂取放料、AGV返回安全位置自动充电和料仓电机归位等功能。

## 行为树清单

本系统包含以下四个主要行为树：

1. **Robot1_light_board_to_racks.xml** - AGV移动到料柜
2. **Robot1_pick_send_stock.xml** - 灯板拾取与送料
3. **Robot1_light_board_to_safe_point.xml** - AGV返回安全位置并自动充电
4. **Robot1_light_board_reset_bin_position.xml** - 料仓电机归位

## 行为树详细说明

### 1. Robot1_light_board_to_racks.xml（AGV移动到料柜）

**功能**：控制AGV移动到料柜位置，并等待AGV完成信号。

**主要步骤**：
1. 初始化任务
2. 准备AGV移动
3. AGV移动到料柜
4. 等待料柜操作完成
5. AGV返回安全位置
6. 完成任务

**核心节点**：
- `AgvGoPoint` - 控制AGV移动到指定位置
- `WaitForEvent` - 等待料柜操作完成事件

**输入输出**：
- 输入：AGV目标位置名称
- 输出：AGV移动状态反馈

### 2. Robot1_pick_send_stock.xml（灯板拾取与送料）

**功能**：控制机械臂拾取灯板并将其送到指定位置。

**主要步骤**：
1. 初始化与准备
2. 移动到拾取位置
3. 抓取灯板
4. 送料过程
5. 返回安全位置
6. 完成任务

**关键操作**：
- 灯板定位与视觉伺服
- 灯板精确抓取
- 安全运输灯板
- 精确放置灯板

**错误处理**：
- 移动失败处理
- 抓取失败处理
- 送料失败处理
- 返回失败处理

### 3. Robot1_light_board_to_safe_point.xml（AGV返回安全位置并自动充电）

**功能**：控制AGV从当前位置返回预定义的安全位置，并执行自动充电操作。

**主要步骤**：
1. 初始化返回任务
2. 准备返回阶段
3. AGV移动到安全位置
4. 对接充电桩并开始充电
5. 验证AGV状态与充电状态
6. 完成任务

**安全考虑**：
- AGV位置精确校准
- 充电桩对接精度控制
- 充电状态监控
- 失败情况下的异常处理

**核心节点**：
- `AgvGoPoint` - 控制AGV移动到安全位置
- `AgvDockCharger` - AGV对接充电桩并开始充电

### 4. Robot1_light_board_reset_bin_position.xml（料仓电机归位）

**功能**：将料仓电机从当前位置重置到标准初始位置。

**主要步骤**：
1. 初始化归位任务
2. 准备电机归位
3. 执行电机归位操作（粗调和精调）
4. 验证归位精度
5. 完成任务

**归位特性**：
- 两阶段归位（粗调和精调）
- 精度验证机制
- 归位数据记录

## 行为树执行流程

典型的执行顺序如下：

1. **初始阶段**：执行`Robot1_light_board_to_racks.xml`，AGV移动到料柜位置。

2. **操作阶段**：执行`Robot1_pick_send_stock.xml`，机械臂完成灯板拾取与送料。

3. **返回阶段**：执行`Robot1_light_board_to_safe_point.xml`，AGV返回安全位置并自动充电。

4. **归位阶段**：执行`Robot1_light_board_reset_bin_position.xml`，料仓电机重置到初始位置。

## 行为树执行图

```
┌─────────────────────┐
│ 用户发起上料请求    │
└──────────┬──────────┘
           ▼
┌─────────────────────┐
│ AGV移动到料柜       │◄─── Robot1_light_board_to_racks.xml
└──────────┬──────────┘
           ▼
┌─────────────────────┐
│ 机械臂拾取与送料    │◄─── Robot1_pick_send_stock.xml
└──────────┬──────────┘
           ▼
┌─────────────────────┐
│ AGV返回并自动充电   │◄─── Robot1_light_board_to_safe_point.xml
└──────────┬──────────┘
           ▼
┌─────────────────────┐
│ 料仓电机归位        │◄─── Robot1_light_board_reset_bin_position.xml
└──────────┬──────────┘
           ▼
┌─────────────────────┐
│ 任务完成            │
└─────────────────────┘
```

## 错误处理策略

所有行为树均采用类似的错误处理策略：

1. **故障检测**：通过条件节点和传感器反馈检测故障
2. **故障报告**：使用`PubProcessFeedback`和`LogError`报告错误
3. **故障处理**：根据错误类型执行相应的恢复操作
4. **故障升级**：当错误无法恢复时，通过`ReturnFailure`向上传递失败状态

## 交互与反馈

所有行为树均使用以下机制提供实时反馈：

1. **进度反馈**：通过`SetProgress`和`PubProcessFeedback`节点更新执行进度
2. **状态消息**：通过`PubPrintMessage`节点输出可读的状态信息
3. **操作日志**：记录关键操作和事件
4. **错误通知**：特定错误状态的通知

## 结论

这四个行为树共同构成了灯板料柜操作的完整工作流程，每个行为树负责流程中的特定阶段。这种模块化设计使系统能够灵活处理不同场景，同时简化了维护和故障排除。 