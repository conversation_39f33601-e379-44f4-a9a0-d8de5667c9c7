# RobotArmControl 机械臂控制节点说明

## 概述

`RobotArmControl` 是一个基于 ROS2 Action 的行为树节点，用于控制机械臂执行各种操作。该节点支持位置控制、旋转控制、功能控制等多种机械臂操作模式，并提供实时反馈和状态监控。

## 功能特性

- **异步Action控制**: 基于ROS2 Action框架，支持长时间运行的机械臂操作
- **多设备支持**: 通过设备名称参数支持多机械臂场景
- **实时反馈**: 提供位置、状态、进度等实时反馈信息
- **错误处理**: 完善的超时、急停、错误状态处理机制
- **状态监控**: 支持程序运行状态、电机状态、远程模式等多种状态监控

## 输入端口 (Input Ports)

### 基本控制参数
| 端口名称 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `strDeviceName` | string | "/robot1" | 设备名称/命名空间，用于区分不同的机械臂 |
| `intProjectId` | int | 1 | 工程ID，指定要执行的机械臂程序 |
| `intSpeedMultiplier` | int | 100 | 速度倍率，自动限制在1-100范围内 |
| `intTimeoutMs` | int | 60000 | 超时时间(毫秒) |

### 位置和旋转参数
| 端口名称 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `floatPositionX` | float | 0.0 | X坐标 (mm) |
| `floatPositionY` | float | 0.0 | Y坐标 (mm) |
| `floatPositionZ` | float | 0.0 | Z坐标 (mm) |
| `floatRotationRx` | float | 0.0 | Rx旋转角度 (度) |
| `floatRotationRy` | float | 0.0 | Ry旋转角度 (度) |
| `floatRotationRz` | float | 0.0 | Rz旋转角度 (度) |

### 功能数据参数
| 端口名称 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `intFunctionData0` | int | 0 | 功能数据0(夹爪控制: 0=开启, 1=关闭) |
| `intFunctionData1` | int | 0 | 功能数据1(工具激活: 0=禁用, 1=启用) |
| `intFunctionData2` | int | 0 | 功能数据2(传送带控制: 0=停止, 1=启动) |
| `intFunctionData3` | int | 0 | 功能数据3(检测功能: 0=关闭, 1=开启) |
| `intFunctionData4` | int | 0 | 功能数据4(自定义功能1) |
| `intFunctionData5` | int | 0 | 功能数据5(自定义功能2) |

## 输出端口 (Output Ports)

### 基本结果输出
| 端口名称 | 类型 | 描述 |
|---------|------|------|
| `boolOutputSuccess` | bool | 操作是否成功 |
| `strOutputErrorMessage` | string | 错误信息 |
| `doubleExecutionTime` | double | 执行时间(秒) |

### 程序状态输出
| 端口名称 | 类型 | 描述 |
|---------|------|------|
| `boolProgramRunning` | bool | 程序运行状态 |
| `boolProgramPaused` | bool | 程序暂停状态 |
| `boolRemoteModeEnabled` | bool | 远程模式启用状态 |
| `boolEmergencyStop` | bool | 急停状态 |
| `boolProgramClearRequest` | bool | 程序清求状态 |
| `boolErrorStatus` | bool | 错误状态 |
| `boolMotorPowerStatus` | bool | 电机电源状态 |
| `intProjectIdStatus` | int | 工程ID状态 |

### 当前位置输出
| 端口名称 | 类型 | 描述 |
|---------|------|------|
| `floatCurrentX` | float | 当前X坐标 |
| `floatCurrentY` | float | 当前Y坐标 |
| `floatCurrentZ` | float | 当前Z坐标 |
| `floatCurrentRx` | float | 当前Rx角度 |
| `floatCurrentRy` | float | 当前Ry角度 |
| `floatCurrentRz` | float | 当前Rz角度 |

### 进度和状态输出
| 端口名称 | 类型 | 描述 |
|---------|------|------|
| `doubleProgressPercentage` | double | 执行进度百分比 |
| `strCurrentStatus` | string | 当前状态描述 |

## 使用示例

### 基本位置控制

```xml
<RobotArmControl 
    strDeviceName="/robot1" 
    intProjectId="1"
    intSpeedMultiplier="80"
    floatPositionX="100.0"
    floatPositionY="200.0"
    floatPositionZ="300.0"
    floatRotationRx="0.0"
    floatRotationRy="0.0"
    floatRotationRz="45.0"
    intFunctionData0="1"
    intFunctionData1="0"
    intFunctionData2="0"
    intFunctionData3="0"
    intFunctionData4="0"
    intFunctionData5="0"
    intTimeoutMs="30000"
    boolOutputSuccess="{success}"
    strOutputErrorMessage="{error_msg}" />
```

### 抓取操作

```xml
<RobotArmControl 
    strDeviceName="/robot1" 
    intProjectId="2"
    intSpeedMultiplier="50"
    floatPositionX="150.0"
    floatPositionY="250.0"
    floatPositionZ="280.0"
    intFunctionData0="1"
    intFunctionData1="1"
    intFunctionData2="0"
    intFunctionData3="0"
    intFunctionData4="0"
    intFunctionData5="0"
    intTimeoutMs="45000"
    boolOutputSuccess="{grasp_success}" />
```

### 多机械臂协调

```xml
<Parallel success_threshold="2" failure_threshold="1">
    <RobotArmControl 
        strDeviceName="/robot1" 
        intProjectId="10"
        floatPositionX="100.0"
        floatPositionY="100.0"
        floatPositionZ="200.0" />
    
    <RobotArmControl 
        strDeviceName="/robot2" 
        intProjectId="20"
        floatPositionX="200.0"
        floatPositionY="200.0"
        floatPositionZ="250.0" />
</Parallel>
```

## 错误处理

### 超时处理
- 节点内置超时检查机制
- 超时后自动取消Action并返回FAILURE状态
- 通过`intTimeoutMs`参数配置超时时间

### 急停检测
- 实时监控急停状态
- 检测到急停时立即停止操作
- 可以通过`boolEmergencyStop`输出端口获取急停状态

### 重试机制
```xml
<Fallback>
    <!-- 主要操作 -->
    <RobotArmControl strDeviceName="/RobotArm1" ... />
    
    <!-- 重试操作，降低速度 -->
    <RobotArmControl strDeviceName="/RobotArm1" 
                    intSpeedMultiplier="30" ... />
</Fallback>
```

## 功能数据说明

各个功能数据参数的用途说明：

- **intFunctionData0**: 夹爪控制 (0=开启, 1=关闭)
- **intFunctionData1**: 工具激活 (0=禁用, 1=启用)
- **intFunctionData2**: 传送带控制 (0=停止, 1=启动)
- **intFunctionData3**: 检测功能 (0=关闭, 1=开启)
- **intFunctionData4**: 自定义功能1
- **intFunctionData5**: 自定义功能2

示例配置：
```xml
<!-- 工序1 -->
<RobotArmControl 
    intFunctionData0="1"
    intFunctionData1="0"
    intFunctionData2="0"
    intFunctionData3="0"
    intFunctionData4="0"
    intFunctionData5="0" ... />

```

## 状态监控

节点提供多种状态监控功能：

### 实时状态检查
```xml
<RobotArmControl ... 
    boolProgramRunning="{running}"
    boolEmergencyStop="{emergency}"
    boolMotorPowerStatus="{power}" />

<!-- 检查状态 -->
<Script code="
    if (emergency) {
        std::cout << '检测到急停' << std::endl;
    } else if (!power) {
        std::cout << '电机未上电' << std::endl;
    }
" />
```

### 位置监控
```xml
<RobotArmControl ... 
    floatCurrentX="{x}"
    floatCurrentY="{y}"
    floatCurrentZ="{z}" />

<Script code="
    std::cout << '当前位置: (' << x << ',' << y << ',' << z << ')' << std::endl;
" />
```

## 性能优化建议

1. **合理设置超时时间**: 根据实际操作复杂度设置合适的超时时间
2. **速度倍率调节**: 在精度要求高的操作中降低速度倍率
3. **状态监控**: 利用实时状态监控及时发现问题
4. **并行操作**: 对于独立的机械臂，可以使用并行操作提高效率

## 注意事项

1. **Action服务依赖**: 节点依赖对应的机械臂Action服务，确保服务正常运行
2. **设备名称**: 不同机械臂必须使用不同的设备名称以避免冲突
3. **坐标系**: 确保位置坐标与机械臂的坐标系一致
4. **安全考虑**: 在操作前检查机械臂工作空间和碰撞检测
5. **功能数据**: 合理配置功能数据以匹配实际的机械臂功能

## 完整示例文件

参考 `examples/robot_arm_control_example.xml` 文件获取更多完整的使用示例。 