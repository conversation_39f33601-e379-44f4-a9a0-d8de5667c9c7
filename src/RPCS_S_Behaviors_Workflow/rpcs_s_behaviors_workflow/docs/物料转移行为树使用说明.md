# 物料转移行为树使用说明

## 概述

本文档介绍物料转移系统的行为树实现，包含三个主要的XML文件，用于执行完整的物料转移工作流程。

## 文件列表

### 1. material_transfer_preparation.xml - 物料转移前置准备
**功能**: 执行物料转移前的系统初始化和准备工作
**流程**:
1. **并行初始化**: 电机5、6、7回零(方法17，速度100) + 存储池转移气缸原位
2. **气缸动作**: 并行执行存储池气缸1、2动作
3. **气缸复位**: 并行执行存储池气缸1、2原位
4. **位置控制**: 并行执行电机5、6位置-350，电机7位置330(速度100)

### 2. material_transfer_execution.xml - 物料转移执行
**功能**: 执行实际的物料转移操作
**前置条件**: Tray盘到位感应3为true，否则流程结束
**流程**:
1. **检查条件**: 验证Tray盘到位感应3(DI6)状态
2. **转移序列**: 
   - 存储池转移气缸原位
   - 电机7位置模式100(速度100)
   - 存储池转移气缸动作 → 转移真空 → 存储池转移气缸原位 → 电机7位置模式330
   - 重复转移动作序列
3. **最终调整**: 
   - 电机6位置模式相对-30(速度20)
   - 条件执行：如果Tray盘到位感应2和感应1都为true，则电机5位置模式相对-30(速度20)

### 3. material_transfer_workflow.xml - 完整工作流程
**功能**: 包含完整的物料转移工作流程，包括前置准备和执行两个阶段
**流程**:
1. **阶段1**: 调用MaterialTransferPreparation子树
2. **阶段2**: 调用MaterialTransferExecution子树

## IO映射说明

根据IO板配置文档，使用的IO地址映射如下：

### 数字输出 (DO)
- **DO0**: 存储池气缸1动作
- **DO1**: 存储池气缸1原位  
- **DO2**: 存储池气缸2动作
- **DO3**: 存储池气缸2原位
- **DO4**: 存储池转移气缸动作
- **DO5**: 存储池转移气缸原位
- **DO6**: 转移真空

### 数字输入 (DI)
- **DI0**: Tray盘到位感应1
- **DI5**: Tray盘到位感应2  
- **DI6**: Tray盘到位感应3

## 电机控制说明

### 使用的电机
- **电机5**: 料仓电机5
- **电机6**: 料仓电机6  
- **电机7**: 转移电机7

### 控制参数
- **回零方法**: 17
- **回零速度**: 100 rpm
- **位置控制速度**: 100 mm/s (准备阶段), 20 mm/s (精细调整)
- **加速度/减速度**: 100 mm/s² (准备阶段), 50 mm/s² (精细调整)

## 使用方法

### 1. 单独执行准备阶段
```bash
# 调用工艺动作
curl -X POST http://localhost:8080/api/process \
  -H "Content-Type: application/json" \
  -d '{"process_type": "MaterialTransferPreparation", "robot_id": "Robot1"}'
```

### 2. 单独执行转移阶段  
```bash
# 调用工艺动作
curl -X POST http://localhost:8080/api/process \
  -H "Content-Type: application/json" \
  -d '{"process_type": "MaterialTransferExecution", "robot_id": "Robot1"}'
```

### 3. 执行完整工作流程
```bash
# 调用完整工作流程
curl -X POST http://localhost:8080/api/process \
  -H "Content-Type: application/json" \
  -d '{"process_type": "MaterialTransferWorkflow", "robot_id": "Robot1"}'
```

## 错误处理

### 前置准备阶段
- 电机回零失败: 系统停止，报告具体电机故障
- 气缸动作失败: 系统停止，报告IO操作失败
- 位置控制失败: 系统停止，报告电机位置控制失败

### 执行阶段  
- Tray感应器检测失败: 流程立即终止，避免误操作
- 转移动作失败: 系统停止，保持当前状态
- 条件检查失败: 跳过可选动作，继续执行必要操作

## 监控和反馈

系统提供详细的进度反馈:
- **进度百分比**: 0-100%的完成度指示
- **状态信息**: INITIALIZING, RUNNING, COMPLETED, FAILED
- **操作描述**: 当前执行的具体操作
- **错误信息**: 详细的故障描述和错误代码

## 安全注意事项

1. **前置检查**: 确保所有传感器工作正常
2. **急停机制**: 支持紧急停止所有电机和气缸动作
3. **状态验证**: 每个操作完成后验证实际状态
4. **超时保护**: 所有操作都有超时保护机制
5. **错误恢复**: 提供详细的错误信息用于故障排除

## 配置参数调整

如需调整参数，可以修改XML文件中的相应值:
- **速度参数**: doubleMaxVelocity, floatSpeedSwitch, floatSpeedZero
- **位置参数**: doubleTargetPosition, intHomeOffset
- **时间参数**: doubleTimeout, doubleDwellTime, intTimeoutMs
- **IO地址**: intOutputAddress, intInputAddress

## 依赖服务

确保以下ROS2服务正常运行:
- **电机控制服务**: `/robot1/Kinco_{motor_id}/homing_control`, `/robot1/Kinco_{motor_id}/position_control`
- **IO板服务**: `/robot1/io_board_1/digital_output_write`, `/robot1/io_board_1/digital_input_read`
- **行为树服务**: PubProcessFeedback, PubPrintMessage节点

## 故障排除

### 常见问题
1. **电机服务不可用**: 检查电机控制器是否启动
2. **IO服务不可用**: 检查IO板控制器连接状态  
3. **传感器读取失败**: 检查传感器接线和电源
4. **位置控制超时**: 检查电机负载和机械限位

### 调试方法
1. 使用Groot2监控行为树执行状态
2. 查看ROS2日志获取详细错误信息
3. 单独测试电机和IO操作验证硬件状态
4. 使用仿真模式测试逻辑流程 