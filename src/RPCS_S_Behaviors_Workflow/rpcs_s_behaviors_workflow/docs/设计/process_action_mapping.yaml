# 动作类型到行为树文件的映射配置
Robot1:
  LightBoardSelfCheck: "Robot1_self_check.xml"
  LightBoardPickSendStock: "Robot1_pick_send_stock.xml"
  StackLightBoards: "Robot1_stack_light_boards.xml"
  RemoveReleaseFilm: "Robot1_remove_release_film.xml"
  BondCarrier: "Robot1_bond_carrier.xml"

Robot2:
  PcbaSelfCheck: "robot2_self_check.xml"
  PcbaPickSendStock: "Robot2_pick_send_stock.xml"
  MoveWIP: "Robot2_move_wip.xml"
  PlaceWIP: "Robot2_place_wip.xml"
  PlacePCBA: "Robot2_place_pcba.xml"
  DoPCAdapterPaste: "Robot2_pc_adapter_paste.xml"
  OpenFPC: "Robot2_open_fpc.xml"
  TransferFixture: "Robot2_transfer_fixture.xml"
  PlaceProduct: "Robot2_place_product.xml"

Robot3:
  ProCaseSelfCheck: "robot3_self_check.xml"
  ProCaseRouteToPickPoint: "Robot3_route_to_pick_point.xml"
  ProCasePickSendStock: "Robot3_pick_send_stock.xml"
  ProCaseRouteToSafePos: "Robot3_route_to_safe_pos.xml"
  ScrewPCBA: "Robot3_screw_pcba.xml"
  ConnectFPC: "Robot3_connect_fpc.xml"
  FastenProCase: "Robot3_fasten_pro_case.xml"

# 全局配置
global:
  tree_file_path: "/opt/rpcs/behavior_trees/"
  default_timeout: 300
  max_concurrent_actions: 3
  enable_preemption: true
  log_level: "INFO"
  
# 机器人特定配置
robot_configs:
  Robot1:
    namespace: "/Robot1"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot1/ExecuteProcessAction
    node_name: "Robot1_process_action_server"
    max_execution_time: 600
    retry_count: 3
    hardware_interface: "/Robot1/hardware"
  Robot2:
    namespace: "/Robot2"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot2/ExecuteProcessAction
    node_name: "Robot2_process_action_server"
    max_execution_time: 900
    retry_count: 2
    hardware_interface: "/Robot2/hardware"
  Robot3:
    namespace: "/Robot3"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot3/ExecuteProcessAction
    node_name: "Robot3_process_action_server"
    max_execution_time: 450
    retry_count: 3
    hardware_interface: "/Robot3/hardware"

# 网络配置 - 防冲突设置
network_config:
  domain_id: 0  # ROS2 DDS Domain ID
  discovery_timeout: 30.0
  enable_multicast: true
  # 如果需要进一步隔离不同机器人组，可以使用不同的 domain_id
  robot_groups:
    production_line_1:
      domain_id: 0
      robots: ["Robot1", "Robot2", "Robot3"]
    production_line_2:
      domain_id: 1
      robots: ["robot4", "robot5", "robot6"] 