@startuml SystemArchitecture

!theme vibrant

package "开发与调试工具" {
    actor "开发者" as Dev
    artifact "AutoCreateRPCS.py" as CodeGen
    artifact "Groot2" as Groot
    artifact "RPSConfig.btproj" as Btproj
    artifact "workflow.xml" as Xml
}

package "RPCS_S_Behaviors_workflow" {
    node "ROS2 Middleware" as ROS2 {
        package "rpcs_s_behaviors_workflow" as Core {
            [自定义节点插件\n(plugins)] as Plugins
            [核心逻辑\n(src)] as Src
        }

        package "rpcs_s_interfaces_behavior_tree" as Interfaces {
            [消息定义\n(msg)] as Msgs
        }
        
        package "BehaviorTree.ROS2" as BtRos2 {
            [ROS2节点基类] as RosNodeBase
        }
    }
    
    artifact "BehaviorTree.CPP" as BtCpp
}

node "机器人硬件" as Robot {
    [传感器]
    [执行器 (机械臂)]
}

Dev --> CodeGen : 使用
Dev --> Groot : 使用

CodeGen -> Btproj : 读取
CodeGen --> Plugins : 生成/更新 C++ 骨架代码
Groot <--> Btproj : 编辑
Groot <--> Xml : 编辑/可视化

Core -up-> BtCpp : 依赖
Plugins ..> BtCpp : 继承节点基类
Core --> Interfaces : 使用消息
Plugins ..> RosNodeBase : 继承ROS节点基类
BtRos2 -up-> BtCpp : 桥接
Interfaces -> Msgs

Core ..> ROS2 : 通过Topic/Action通信
Robot <--> ROS2 : 通过Topic/Action通信

@enduml 