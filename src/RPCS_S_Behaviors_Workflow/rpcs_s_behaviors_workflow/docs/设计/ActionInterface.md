# ROS2 Action 工艺动作树调用接口设计

## 概述

本文档设计了一个通过 ROS2 DDS Action 消息调用工艺动作树的接口系统。服务器通过"工艺动作类型"参数决定运行哪一个工艺动作树文件，实现灵活的工艺流程控制。

## Action 接口定义

### ExecuteProcessAction.action

```yaml
# Goal - 执行工艺动作的请求
string process_action_type    # 工艺动作类型，对应具体的工艺动作树文件
string robot_id              # 机器人ID (Robot1, Robot2, Robot3)
string[] process_parameters  # 工艺参数列表
int32 timeout_seconds        # 超时时间（秒），0表示无超时
bool preempt_current         # 是否抢占当前正在执行的工艺动作
string process_id            # 工艺流程ID，用于追踪
---
# Result - 工艺执行结果
bool success                 # 工艺执行是否成功
string result_message        # 结果描述信息
string final_status          # 最终状态 (SUCCESS, FAILURE, ABORTED)
float64 execution_time       # 工艺执行时间（秒）
string[] process_output_data # 工艺输出数据
string quality_status        # 质量状态 (OK, NG, UNKNOWN)
---
# Feedback - 工艺执行反馈
string current_process_step  # 当前执行的工艺步骤名称
string current_status        # 当前状态 (RUNNING, SUCCESS, FAILURE)
float64 progress_percent     # 工艺执行进度百分比 (0.0-100.0)
string status_message        # 状态描述信息
string current_operation     # 当前操作描述
builtin_interfaces/Time timestamp  # 时间戳
```

## 工艺动作类型映射表

基于配置文件的工艺动作类型，建立工艺动作类型到工艺动作树文件的映射关系：

### Robot1 工艺动作映射

| 工艺动作类型 | 工艺动作树文件 | 描述 |
|-------------|---------------|------|
| LightBoardSelfCheck | Robot1_self_check.xml | 机器人状态自检 |
| LightBoardPickSendStock | Robot1_pick_send_stock.xml | 灯板取送料工艺 |
| StackLightBoards | Robot1_stack_light_boards.xml | 灯板码放工艺 |
| RemoveReleaseFilm | Robot1_remove_release_film.xml | 薄膜撕膜清洁工艺 |
| BondCarrier | Robot1_bond_carrier.xml | 载板贴合工艺 |

### Robot2 工艺动作映射

| 工艺动作类型 | 工艺动作树文件 | 描述 |
|-------------|---------------|------|
| PcbaSelfCheck | robot2_self_check.xml | 机器人状态自检 |
| PcbaPickSendStock | Robot2_pick_send_stock.xml | PCBA取送料工艺 |
| MoveWIP | Robot2_move_wip.xml | 半成品转移工艺 |
| PlaceWIP | Robot2_place_wip.xml | 半成品放置工艺 |
| PlacePCBA | Robot2_place_pcba.xml | PCBA放置工艺 |
| DoPCAdapterPaste | Robot2_pc_adapter_paste.xml | FPC转接头贴放工艺 |
| OpenFPC | Robot2_open_fpc.xml | FPC开盖工艺 |
| TransferFixture | Robot2_transfer_fixture.xml | 成品转移工艺 |
| PlaceProduct | Robot2_place_product.xml | 成品放置工艺 |

### Robot3 工艺动作映射

| 工艺动作类型 | 工艺动作树文件 | 描述 |
|-------------|---------------|------|
| ProCaseSelfCheck | robot3_self_check.xml | 机器人状态自检 |
| ProCaseRouteToPickPoint | Robot3_route_to_pick_point.xml | 路径规划到取料点工艺 |
| ProCasePickSendStock | Robot3_pick_send_stock.xml | 保护壳取送料工艺 |
| ProCaseRouteToSafePos | Robot3_route_to_safe_pos.xml | 路径规划到安全位置工艺 |
| ScrewPCBA | Robot3_screw_pcba.xml | PCBA螺丝锁付工艺 |
| ConnectFPC | Robot3_connect_fpc.xml | FPC插接工艺 |
| FastenProCase | Robot3_fasten_pro_case.xml | 保护壳固定工艺 |

## 命名空间和防冲突设计

### Action 服务命名规范

为了防止同一局域网中多个相同节点的冲突，采用以下命名规范：

- **Action 服务名称**: `/{robot_id}/ExecuteProcessAction`
- **节点名称**: `{robot_id}_process_action_server`
- **命名空间**: `{robot_id}`

### 实际 Action 服务列表

| 机器人ID | Action 服务名称 | 节点名称 | 命名空间 |
|---------|----------------|----------|----------|
| Robot1 | `/Robot1/ExecuteProcessAction` | `Robot1_process_action_server` | `Robot1` |
| Robot2 | `/Robot2/ExecuteProcessAction` | `Robot2_process_action_server` | `Robot2` |
| Robot3 | `/Robot3/ExecuteProcessAction` | `Robot3_process_action_server` | `Robot3` |

### 优势

1. **避免冲突**: 不同机器人的 Action 服务名称完全独立
2. **易于识别**: 从服务名称就能明确知道是哪个机器人
3. **扩展性好**: 可以轻松添加更多机器人（robot4, robot5...）
4. **调试友好**: 使用 `ros2 node list` 和 `ros2 action list` 能清晰看到各机器人的服务

## 系统架构

```
┌─────────────────┐    Action Call    ┌──────────────────────────────────────┐
│   上层控制系统   │ ──────────────→  │      ProcessAction Servers         │
│   (MES/WMS)     │                   │  ┌─────────────────────────────────┐  │
└─────────────────┘                   │  │ /Robot1/ExecuteProcessAction  │  │
                                      │  │ /Robot2/ExecuteProcessAction  │  │
                                      │  │ /Robot3/ExecuteProcessAction  │  │
                                      │  └─────────────────────────────────┘  │
                                      └──────────────────────────────────────┘
                                                │
                                                │ Load & Execute
                                                ▼
┌─────────────────────────────────────────────────────────────┐
│                  工艺动作树文件库                            │
│  Robot1_*.xml  │  Robot2_*.xml  │  Robot3_*.xml           │
└─────────────────────────────────────────────────────────────┘
                                                │
                                                │ ROS2 Topics/Actions
                                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   机器人硬件层                              │
│     Robot1        │     Robot2        │     Robot3        │
└─────────────────────────────────────────────────────────────┘
```

## 服务器实现架构

### ProcessActionServer 类设计

```cpp
class ProcessActionServer : public rclcpp::Node
{
public:
    ProcessActionServer(const std::string& robot_id);
    
private:
    std::string robot_id_;
    
    // Action Server
    rclcpp_action::Server<rpcs_s_interfaces_behavior_tree::action::ExecuteProcessAction>::SharedPtr action_server_;
    
    // 工艺动作树工厂和执行器
    BT::BehaviorTreeFactory factory_;
    std::unique_ptr<BT::Tree> current_process_tree_;
    
    // 工艺动作类型到文件的映射
    std::map<std::string, std::map<std::string, std::string>> process_action_mapping_;
    
    // 回调函数
    rclcpp_action::GoalResponse handle_goal(
        const rclcpp_action::GoalUUID & uuid,
        std::shared_ptr<const ExecuteProcessAction::Goal> goal);
    
    rclcpp_action::CancelResponse handle_cancel(
        const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    void handle_accepted(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    // 核心执行函数
    void ExecuteProcessAction(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
    
    // 辅助函数
    std::string get_process_tree_file_path(const std::string& robot_id, const std::string& process_action_type);
    void load_process_action_mapping();
    void publish_feedback(const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle);
};
```

## 使用示例

### 1. 调用 Robot1 执行灯板取送料工艺

```cpp
// C++ 客户端示例
auto action_client = rclcpp_action::create_client<ExecuteProcessAction>(node, "Robot1/ExecuteProcessAction");

ExecuteProcessAction::Goal goal;
goal.process_action_type = "LightBoardPickSendStock";
goal.robot_id = "Robot1";
goal.timeout_seconds = 30;
goal.preempt_current = false;
goal.process_id = "PROCESS_001";

auto future = action_client->async_send_goal(goal);
```

### 2. Python 客户端示例

```python
import rclpy
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction

class ProcessActionClient:
    def __init__(self, robot_id):
        self.robot_id = robot_id
        self.node = rclpy.create_node(f'process_action_client_{robot_id}')
        self.client = ActionClient(self.node, ExecuteProcessAction, f'{robot_id}/ExecuteProcessAction')
    
    def ExecuteProcessAction(self, robot_id, process_action_type, process_parameters=None, timeout=30, process_id=None):
        goal = ExecuteProcessAction.Goal()
        goal.robot_id = robot_id
        goal.process_action_type = process_action_type
        goal.process_parameters = process_parameters or []
        goal.timeout_seconds = timeout
        goal.preempt_current = False
        goal.process_id = process_id or f"PROCESS_{int(time.time())}"
        
        future = self.client.send_goal_async(goal, feedback_callback=self.feedback_callback)
        return future
    
    def feedback_callback(self, feedback):
        print(f"Current process step: {feedback.current_process_step}")
        print(f"Current operation: {feedback.current_operation}")
        print(f"Progress: {feedback.progress_percent}%")
```

### 3. 命令行调用示例

```bash
# 使用 ros2 action 命令行工具调用不同机器人工艺动作
# 调用 Robot1 执行灯板取送料工艺
ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
  "{robot_id: 'Robot1', process_action_type: 'LightBoardPickSendStock', process_parameters: [], timeout_seconds: 30, process_id: 'LIGHTBOARD_001'}"

# 调用 Robot2 执行成品放置工艺
ros2 action send_goal /Robot2/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
  "{robot_id: 'Robot2', process_action_type: 'PlaceProduct', process_parameters: ['{\"result\": \"OK\"}'], timeout_seconds: 60, process_id: 'PRODUCT_PLACE_001'}"

# 调用 Robot3 执行路径规划移动工艺
ros2 action send_goal /Robot3/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
  "{robot_id: 'Robot3', process_action_type: 'ProCaseRouteToPickPoint', process_parameters: ['{\"waypoints\": [...]}'], timeout_seconds: 45, process_id: 'ROUTE_001'}"
```

## 配置文件设计

### process_action_mapping.yaml

```yaml
# 动作类型到行为树文件的映射配置
Robot1:
  LightBoardSelfCheck: "Robot1_self_check.xml"
  LightBoardPickSendStock: "Robot1_pick_send_stock.xml"
  StackLightBoards: "Robot1_stack_light_boards.xml"
  RemoveReleaseFilm: "Robot1_remove_release_film.xml"
  BondCarrier: "Robot1_bond_carrier.xml"

Robot2:
  PcbaSelfCheck: "robot2_self_check.xml"
  PcbaPickSendStock: "Robot2_pick_send_stock.xml"
  MoveWIP: "Robot2_move_wip.xml"
  PlaceWIP: "Robot2_place_wip.xml"
  PlacePCBA: "Robot2_place_pcba.xml"
  DoPCAdapterPaste: "Robot2_pc_adapter_paste.xml"
  OpenFPC: "Robot2_open_fpc.xml"
  TransferFixture: "Robot2_transfer_fixture.xml"
  PlaceProduct: "Robot2_place_product.xml"

Robot3:
  ProCaseSelfCheck: "robot3_self_check.xml"
  ProCaseRouteToPickPoint: "Robot3_route_to_pick_point.xml"
  ProCasePickSendStock: "Robot3_pick_send_stock.xml"
  ProCaseRouteToSafePos: "Robot3_route_to_safe_pos.xml"
  ScrewPCBA: "Robot3_screw_pcba.xml"
  ConnectFPC: "Robot3_connect_fpc.xml"
  FastenProCase: "Robot3_fasten_pro_case.xml"

# 全局配置
global:
  tree_file_path: "/opt/rpcs/behavior_trees/"
  default_timeout: 300
  max_concurrent_actions: 3
  enable_preemption: true
  log_level: "INFO"
  
# 机器人特定配置
robot_configs:
  Robot1:
    namespace: "/Robot1"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot1/ExecuteProcessAction
    node_name: "Robot1_process_action_server"
    max_execution_time: 600
    retry_count: 3
    hardware_interface: "/Robot1/hardware"
  Robot2:
    namespace: "/Robot2"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot2/ExecuteProcessAction
    node_name: "Robot2_process_action_server"
    max_execution_time: 900
    retry_count: 2
    hardware_interface: "/Robot2/hardware"
  Robot3:
    namespace: "/Robot3"
    action_server_name: "ExecuteProcessAction"  # 完整路径: /Robot3/ExecuteProcessAction
    node_name: "Robot3_process_action_server"
    max_execution_time: 450
    retry_count: 3
    hardware_interface: "/Robot3/hardware"

# 网络配置 - 防冲突设置
network_config:
  domain_id: 0  # ROS2 DDS Domain ID
  discovery_timeout: 30.0
  enable_multicast: true
  # 如果需要进一步隔离不同机器人组，可以使用不同的 domain_id
  robot_groups:
    production_line_1:
      domain_id: 0
      robots: ["Robot1", "Robot2", "Robot3"]
    production_line_2:
      domain_id: 1
      robots: ["robot4", "robot5", "robot6"]
```

## 错误处理机制

### 错误类型定义

```cpp
enum class BehaviorTreeError {
    UNKNOWN_ACTION_TYPE,
    INVALID_ROBOT_ID,
    TREE_FILE_NOT_FOUND,
    TREE_LOAD_FAILED,
    EXECUTION_TIMEOUT,
    EXECUTION_FAILED,
    PREEMPTION_FAILED,
    PARAMETER_INVALID
};
```

### 错误处理流程

1. **参数验证**：检查 robot_id 和 action_type 的有效性
2. **文件存在性检查**：验证对应的行为树文件是否存在
3. **加载验证**：确保行为树文件能够正确加载
4. **执行监控**：监控执行过程，处理超时和异常
5. **资源清理**：确保执行完成后正确释放资源

## 性能考虑

1. **并发控制**：支持多个 Action 同时执行，但限制最大并发数
2. **资源管理**：合理管理行为树实例的生命周期
3. **缓存机制**：缓存已加载的行为树文件，避免重复加载
4. **监控指标**：记录执行时间、成功率等性能指标

## 部署配置

### launch 文件示例

```xml
<launch>
  <!-- Robot1 ProcessAction Server -->
  <node pkg="rpcs_s_behaviors_workflow" exec="process_action_server" name="Robot1_process_action_server" namespace="Robot1">
    <param name="robot_id" value="Robot1"/>
    <param name="config_file" value="$(find-pkg-share rpcs_s_behaviors_workflow)/config/process_action_mapping.yaml"/>
    <param name="tree_file_path" value="/opt/rpcs/behavior_trees/"/>
    <param name="max_concurrent_actions" value="3"/>
    <param name="default_timeout" value="300"/>
    <param name="max_execution_time" value="600"/>
    <param name="retry_count" value="3"/>
    <param name="log_level" value="INFO"/>
  </node>

  <!-- Robot2 ProcessAction Server -->
  <node pkg="rpcs_s_behaviors_workflow" exec="process_action_server" name="Robot2_process_action_server" namespace="Robot2">
    <param name="robot_id" value="Robot2"/>
    <param name="config_file" value="$(find-pkg-share rpcs_s_behaviors_workflow)/config/process_action_mapping.yaml"/>
    <param name="tree_file_path" value="/opt/rpcs/behavior_trees/"/>
    <param name="max_concurrent_actions" value="3"/>
    <param name="default_timeout" value="300"/>
    <param name="max_execution_time" value="900"/>
    <param name="retry_count" value="2"/>
    <param name="log_level" value="INFO"/>
  </node>

  <!-- Robot3 ProcessAction Server -->
  <node pkg="rpcs_s_behaviors_workflow" exec="process_action_server" name="Robot3_process_action_server" namespace="Robot3">
    <param name="robot_id" value="Robot3"/>
    <param name="config_file" value="$(find-pkg-share rpcs_s_behaviors_workflow)/config/process_action_mapping.yaml"/>
    <param name="tree_file_path" value="/opt/rpcs/behavior_trees/"/>
    <param name="max_concurrent_actions" value="3"/>
    <param name="default_timeout" value="300"/>
    <param name="max_execution_time" value="450"/>
    <param name="retry_count" value="3"/>
    <param name="log_level" value="INFO"/>
  </node>
</launch>
```

## 系统交互流程

下图展示了通过 ROS2 Action 调用行为树的完整交互流程：

```mermaid
sequenceDiagram
    participant MES as 上层控制系统<br/>(MES/WMS)
    participant ActionServer as BehaviorTree<br/>ActionServer
    participant TreeFactory as BehaviorTree<br/>Factory
    participant TreeFile as 行为树文件库
    participant ROS2 as ROS2通信层
    participant Robot as 机器人硬件

    MES->>ActionServer: ExecuteBehaviorTree Goal<br/>{robot_id: "Robot1", action_type: "LightBoardPickSendStock"}
    activate ActionServer
    
    ActionServer->>ActionServer: 验证参数<br/>(robot_id, action_type)
    
    ActionServer->>TreeFile: 查找映射文件<br/>Robot1_pick_send_stock.xml
    TreeFile-->>ActionServer: 返回文件路径
    
    ActionServer->>TreeFactory: 加载行为树文件
    activate TreeFactory
    TreeFactory-->>ActionServer: 返回Tree实例
    deactivate TreeFactory
    
    ActionServer->>MES: Feedback: 开始执行
    
    loop 行为树执行循环
        ActionServer->>ActionServer: tick() 行为树
        ActionServer->>ROS2: 执行节点动作<br/>(Topic/Action调用)
        activate ROS2
        ROS2->>Robot: 控制指令
        activate Robot
        Robot-->>ROS2: 执行状态反馈
        deactivate Robot
        ROS2-->>ActionServer: 节点执行结果
        deactivate ROS2
        ActionServer->>MES: Feedback: 执行进度<br/>{current_node, progress_percent}
    end
    
    ActionServer->>MES: Result: 执行完成<br/>{success: true, execution_time: 30.5}
    deactivate ActionServer
```

### 流程步骤说明

1. **请求发起**: 上层控制系统（MES/WMS）通过 ROS2 Action 发送执行请求
2. **参数验证**: ActionServer 验证 robot_id 和 action_type 参数的有效性
3. **文件映射**: 根据配置文件查找对应的行为树 XML 文件
4. **树加载**: 使用 BehaviorTreeFactory 加载并创建行为树实例
5. **执行监控**: 在执行循环中持续 tick 行为树，并发送进度反馈
6. **硬件交互**: 通过 ROS2 与机器人硬件进行通信
7. **结果返回**: 执行完成后返回最终结果

## 调试和监控

### 查看可用的 Action 服务

```bash
# 列出所有 Action 服务
ros2 action list

# 查看特定机器人的 Action 服务
ros2 action list | grep Robot1
ros2 action list | grep Robot2
ros2 action list | grep Robot3

# 查看 Action 接口定义
ros2 action info /Robot1/ExecuteProcessAction
```

### 监控 Action 执行状态

```bash
# 监控特定 Action 的反馈
ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
  "{robot_id: 'Robot1', process_action_type: 'LightBoardSelfCheck', process_id: 'CHECK_001'}" --feedback

# 查看所有活跃的节点
ros2 node list | grep process_action

# 查看节点信息
ros2 node info /Robot1/Robot1_process_action_server
```

### 网络诊断

```bash
# 检查 ROS2 网络连接
ros2 doctor

# 查看 DDS 发现的节点
ros2 daemon stop
ros2 daemon start
ros2 node list

# 检查特定命名空间下的话题和服务
ros2 topic list | grep Robot1
ros2 service list | grep Robot1
```

### 故障排除

1. **Action 服务不可用**
   - 检查对应的 BehaviorTree Server 是否启动
   - 验证命名空间配置是否正确
   - 确认 ROS2 Domain ID 设置一致

2. **多个相同服务名冲突**
   - 确保每个机器人使用不同的命名空间
   - 检查 launch 文件中的 namespace 参数
   - 考虑使用不同的 DDS Domain ID

3. **Action 执行失败**
   - 查看行为树文件是否存在
   - 检查动作类型映射配置
   - 验证机器人硬件连接状态

这个设计提供了一个完整的、灵活的 ROS2 Action 接口，能够根据动作类型动态加载和执行相应的行为树文件，支持多机器人协同工作，并通过命名空间机制有效防止了服务名称冲突。 