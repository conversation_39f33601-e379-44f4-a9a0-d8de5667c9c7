### 工作流程 (Mermaid 泳道图)

下图详细说明了在 `MainTree` 中执行单个装配步骤（例如 `LightBoardAssembly`）时的核心交互流程。

```mermaid
sequenceDiagram
    participant MainTree as 主树 (ReactiveSequence)
    participant SubTree as 装配子树 (e.g., LightBoardAssembly)
    participant Blackboard as 黑板
    participant ROS2 as ROS2 通信层
    participant Robot as 机器人硬件

    MainTree->>SubTree: 执行装配步骤
    activate SubTree

    SubTree->>Blackboard: 检查 'done' 标志 (CheckLightBoardAssembly)
    activate Blackboard
    Blackboard-->>SubTree: 返回: false (未完成)
    deactivate Blackboard

    SubTree->>Blackboard: 检查物料 (HasLightBoard)
    activate Blackboard
    Blackboard-->>SubTree: 返回: true (有物料)
    deactivate Blackboard
    
    SubTree->>SubTree: 执行动作 (DoLightBoardAssembly)
    activate SubTree

    SubTree->>ROS2: 发送指令 (e.g., 调用机械臂Action)
    activate ROS2
    ROS2->>Robot: 控制硬件执行
    activate Robot
    Robot-->>ROS2: 硬件执行完毕
    deactivate Robot
    ROS2-->>SubTree: 动作完成
    deactivate ROS2

    SubTree->>Blackboard: 更新 'done' 标志为 true
    activate Blackboard
    Blackboard-->>SubTree: 
    deactivate Blackboard
    
    SubTree-->>MainTree: 步骤成功
    deactivate SubTree
    deactivate SubTree

    MainTree->>MainTree: 移至下一个装配步骤...
```

#### 流程说明

1.  **启动步骤**: `MainTree` 作为一个 `ReactiveSequence`，按顺序启动其子节点。它调用第一个装配任务，例如 `LightBoardAssembly` 子树。
2.  **状态检查**: `LightBoardAssembly` 是一个 `ReactiveFallback`。它的第一个子节点是 `CheckLightBoardAssembly` 条件节点，该节点从黑板读取 `done` 标志。在第一次运行时，该标志为 `false`，因此 `Check` 节点失败。
3.  **前置条件检查**: 由于 `Check` 节点失败，`Fallback` 继续执行其下一个子节点，即一个 `Sequence`。该 `Sequence` 首先运行 `HasLightBoard` 条件节点，该节点也从黑板（或通过 ROS 消息）检查是否有必要的物料。如果为 `true`，则 `Sequence` 继续。
4.  **执行动作**: `Sequence` 运行 `DoLightBoardAssembly` 动作节点。该节点被激活，`onStart()` 被调用。
5.  **与硬件交互**: 在 `onRunning()` 状态中，该节点通过 ROS2 通信层（例如，调用一个 action server）向机器人硬件（例如，机械臂控制器）发送指令。
6.  **更新状态**: 动作完成后（例如，action server 返回成功），`DoLightBoardAssembly` 节点将黑板上的 `done` 标志更新为 `true`，并返回 `SUCCESS`。
7.  **循环**: `MainTree` 现在继续执行下一个装配任务的子树，整个过程重复进行，直到所有步骤完成。如果在任何时候重新 `tick` `LightBoardAssembly` 子树，`CheckLightBoardAssembly` 节点现在会成功，`Fallback` 会立即返回 `SUCCESS`，从而跳过动作的重新执行。 