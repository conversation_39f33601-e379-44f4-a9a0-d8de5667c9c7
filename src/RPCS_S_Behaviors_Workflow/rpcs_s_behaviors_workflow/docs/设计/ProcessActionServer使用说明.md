# ProcessActionServer 使用说明

## 概述

ProcessActionServer 是一个基于 ROS2 Action 服务的工艺动作树执行系统，它取代了原来在 main 函数中固化启动行为树的方式，实现了动态选择和执行不同工艺动作树的功能。

## 系统架构

```
┌─────────────────┐    Action Call    ┌─────────────────┐    YAML Config    ┌─────────────────┐
│   Client Node   │ ─────────────────→ │ProcessActionServ│ ─────────────────→ │ Process Tree    │
│                 │                    │     er          │                    │   XML Files     │
└─────────────────┘                    └─────────────────┘                    └─────────────────┘
                                               │
                                               ▼
                                       ┌─────────────────┐
                                       │ BehaviorTree    │
                                       │   Factory       │
                                       └─────────────────┘
```

## 主要组件

### 1. ProcessActionServer 类
- **功能**: 核心服务器，处理 Action 请求并执行相应的工艺动作树
- **文件**: `src/process_action_server.cpp`
- **头文件**: `include/rpcs_s_behaviors_workflow/process_action_server.hpp`

### 2. 主程序
- **功能**: 启动 ProcessActionServer 实例
- **文件**: `src/process_action_server_main.cpp`
- **可执行文件**: `process_action_server`

### 3. 客户端示例
- **功能**: 演示如何调用 ProcessActionServer
- **文件**: `src/process_action_client_example.cpp`
- **可执行文件**: `process_action_client_example`

## 配置文件

### 工艺动作映射配置 (`config/process_action_mapping.yaml`)

```yaml
Robot1:
  SelfCheck: "Robot1_self_check.xml"
  PickSendStock: "Robot1_pick_send_stock.xml"
  StackLightBoards: "Robot1_stack_light_boards.xml"
  RemoveReleaseFilm: "Robot1_remove_release_film.xml"
  BondCarrier: "Robot1_bond_carrier.xml"

Robot2:
  SelfCheck: "robot2_self_check.xml"
  PickSendStock: "Robot2_pick_send_stock.xml"
  MoveWIP: "Robot2_move_wip.xml"
  PlaceWIP: "Robot2_place_wip.xml"
  PlacePCBA: "Robot2_place_pcba.xml"
  # ... 更多工艺动作类型

Robot3:
  SelfCheck: "robot3_self_check.xml"
  Route: "Robot3_route.xml"
  # ... 更多工艺动作类型
```

## 启动方式

### 1. 启动单个机器人的 ProcessActionServer

```bash
# 方法1: 直接运行可执行文件
ros2 run rpcs_s_behaviors_workflow process_action_server Robot1

# 方法2: 使用 launch 文件
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1

# 方法3: 使用环境变量
export ROBOT_ID=Robot1
ros2 run rpcs_s_behaviors_workflow process_action_server
```

### 2. 启动多个机器人的 ProcessActionServer

```bash
# 启动所有机器人
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py

# 只启动特定机器人
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py \
    enable_Robot1:=true \
    enable_Robot2:=false \
    enable_Robot3:=true
```

### 3. 自定义配置启动

```bash
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 \
    config_file:=/path/to/your/config.yaml \
    tree_file_path:=/path/to/your/trees \
    groot2_port:=1670
```

## 使用 Action 客户端

### 1. 使用命令行工具

```bash
# 基本调用
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'SelfCheck', robot_id: 'Robot1', timeout_seconds: 60}"

# 带参数调用
ros2 action send_goal /Robot2/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'PlaceProduct', robot_id: 'Robot2', 
      process_parameters: ['PRODUCT_001', 'STATION_A'], 
      timeout_seconds: 120, process_id: 'TASK_001'}"
```

### 2. 使用示例客户端

```bash
# 基本用法
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 SelfCheck

# 带参数用法
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 PickSendStock LIGHTBOARD_001 60
```

### 3. 编程调用 (C++)

```cpp
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include "rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction.hpp"

using ExecuteProcessAction = rpcs_s_interfaces_behavior_tree::action::ExecuteProcessAction;

auto client = rclcpp_action::create_client<ExecuteProcessAction>(node, "Robot1/ExecuteProcessAction");

auto goal_msg = ExecuteProcessAction::Goal();
goal_msg.process_action_type = "SelfCheck";
goal_msg.robot_id = "Robot1";
goal_msg.timeout_seconds = 60;

client->async_send_goal(goal_msg);
```

### 4. 编程调用 (Python)

```python
import rclpy
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction

class ProcessActionClient:
    def __init__(self):
        self.node = rclpy.create_node('process_action_client')
        self.client = ActionClient(self.node, ExecuteProcessAction, 'Robot1/ExecuteProcessAction')
    
    def send_goal(self, process_action_type):
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = process_action_type
        goal_msg.robot_id = 'Robot1'
        goal_msg.timeout_seconds = 60
        
        return self.client.send_goal_async(goal_msg)
```

## 监控和调试

### 1. 查看 Action 服务状态

```bash
# 列出所有 Action 服务
ros2 action list

# 查看特定 Action 的信息
ros2 action info /Robot1/ExecuteProcessAction

# 查看 Action 的接口定义
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction
```

### 2. 监控执行过程

```bash
# 监控反馈信息
ros2 topic echo /Robot1/ExecuteProcessAction/_action/feedback

# 监控结果信息
ros2 topic echo /Robot1/ExecuteProcessAction/_action/result
```

### 3. 使用 Groot2 可视化

ProcessActionServer 集成了 Groot2 发布器，可以实时监控行为树执行状态：

```bash
# 启动 Groot2 (需要单独安装)
groot2

# 连接到对应端口
# Robot1: localhost:1670
# Robot2: localhost:1671  
# Robot3: localhost:1672
```

## 参数配置

### 启动参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `config_file` | `config/process_action_mapping.yaml` | 工艺动作映射配置文件路径 |
| `tree_file_path` | `config/process_trees` | 工艺动作树文件目录路径 |
| `max_concurrent_actions` | `2` | 最大并发动作数量 |
| `default_timeout` | `300` | 默认超时时间(秒) |
| `groot2_port` | `1670` | Groot2 发布器端口 |

### Action 接口字段

**Goal 字段:**
- `process_action_type`: 工艺动作类型
- `robot_id`: 机器人ID
- `process_parameters`: 工艺参数列表
- `timeout_seconds`: 超时时间
- `preempt_current`: 是否抢占当前任务
- `process_id`: 工艺流程ID

**Result 字段:**
- `success`: 执行成功状态
- `result_message`: 结果描述
- `final_status`: 最终状态
- `execution_time`: 执行时间
- `process_output_data`: 工艺输出数据
- `quality_status`: 质量状态

**Feedback 字段:**
- `current_process_step`: 当前工艺步骤
- `current_status`: 当前状态
- `progress_percent`: 进度百分比
- `status_message`: 状态描述
- `current_operation`: 当前操作描述

## 故障排除

### 1. 常见错误

**配置文件找不到:**
```
ERROR: Config file does not exist: /path/to/config.yaml
```
解决方案: 检查配置文件路径是否正确

**工艺动作类型未找到:**
```
ERROR: Unknown process action type: InvalidAction
```
解决方案: 检查配置文件中是否定义了该工艺动作类型

**工艺动作树文件不存在:**
```
ERROR: Process tree file does not exist: /path/to/tree.xml
```
解决方案: 检查工艺动作树文件是否存在且路径正确

### 2. 调试方法

**启用详细日志:**
```bash
ros2 run rpcs_s_behaviors_workflow process_action_server Robot1 --ros-args --log-level DEBUG
```

**检查节点状态:**
```bash
ros2 node list
ros2 node info /Robot1/Robot1_process_action_server
```

**检查话题通信:**
```bash
ros2 topic list
ros2 topic hz /Robot1/ExecuteProcessAction/_action/feedback
```

## 扩展开发

### 1. 添加新的工艺动作类型

1. 在配置文件中添加映射关系
2. 创建对应的工艺动作树 XML 文件
3. 如需要，开发新的行为树节点插件

### 2. 自定义客户端

参考 `process_action_client_example.cpp` 开发自定义客户端应用

### 3. 集成到现有系统

ProcessActionServer 可以作为独立服务运行，通过 ROS2 Action 接口与其他系统集成

## 版本历史

- v1.0.0: 初始版本，支持基本的工艺动作树执行
- 后续版本将根据实际使用需求进行功能扩展 