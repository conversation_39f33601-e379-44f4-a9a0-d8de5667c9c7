# 行为树节点数据传递处理

在行为树中，节点间的数据传递主要通过**黑板（Blackboard）机制**实现。让我详细解释几种常用的数据传递方法：

## 🔄 1. 黑板变量传递（最常用）

### 基本原理
黑板是一个全局共享的键值存储，所有节点都可以读写。使用 `{变量名}` 语法来引用黑板变量。

### 示例：位置校正后获取结果用于下一步操作

```xml
<Sequence name="位置校正链式操作">
    
    <!-- 第一步：执行位置校正 -->
    <CorrectionPosition 
        doubleAbsX="100.0"
        doubleAbsY="200.0"
        doubleAbsZ="50.0"
        doubleAbsR="90.0"
        boolOutputSuccess="{correction_success}"
        strErrorCode="{correction_error}"
        strOutputMessage="{correction_msg}"
        strPlcFeedback="{plc_feedback}" />
    
    <!-- 第二步：检查校正结果，决定下一步动作 -->
    <Fallback name="根据结果执行不同动作">
        <!-- 成功分支 -->
        <Sequence name="校正成功后续动作">
            <Script code="correction_success == true" />
            <PubPrintMessage strPrintMessage="✅ 位置校正成功: {correction_msg}" />
            
            <!-- 使用校正结果继续下一个动作 -->
            <MotorPositionControl 
                strDeviceId="Robot1"
                intMotorId="5"
                doubleTargetPosition="150.0"
                boolOutputSuccess="{motor_success}"
                strOutputMessage="{motor_msg}" />
        </Sequence>
        
        <!-- 失败分支 -->
        <Sequence name="校正失败处理">
            <PubPrintMessage strPrintMessage="❌ 位置校正失败: {correction_msg}, 错误: {correction_error}" />
            <ReturnFailure />
        </Sequence>
    </Fallback>
    
    <!-- 第三步：综合处理多个节点的输出 -->
    <Script code="
        if (correction_success && motor_success) {
            final_result := 'SUCCESS';
            final_message := '位置校正和电机控制都成功完成';
        } else {
            final_result := 'FAILURE';
            final_message := '操作中有步骤失败: 校正=' + correction_msg + ', 电机=' + motor_msg;
        }
    " />
    
    <PubPrintMessage strPrintMessage="🎯 最终结果: {final_result} - {final_message}" />
    
</Sequence>
```

## 🔀 2. 条件分支处理

### 根据前一个节点的输出决定执行路径

```xml
<ReactiveFallback name="智能决策流程">
    
    <!-- 尝试位置校正 -->
    <Sequence name="位置校正尝试">
        <CorrectionPosition 
            doubleAbsX="{target_x}"
            doubleAbsY="{target_y}"
            doubleAbsZ="{target_z}"
            doubleAbsR="{target_r}"
            boolOutputSuccess="{correction_result}"
            strErrorCode="{error_code}"
            strPlcFeedback="{plc_data}" />
            
        <!-- 检查是否成功 -->
        <Script code="correction_result == true" />
    </Sequence>
    
    <!-- 失败后的重试逻辑 -->
    <Sequence name="重试机制">
        <Script code="
            retry_count := retry_count + 1;
            console.log('位置校正失败，第' + retry_count + '次重试');
        " />
        
        <!-- 调整参数后重试 -->
        <CorrectionPosition 
            doubleAbsX="{target_x}"
            doubleAbsY="{target_y}"
            doubleAbsZ="{target_z + 5.0}"
            doubleAbsR="{target_r}"
            doubleTimeout="45.0"
            boolOutputSuccess="{retry_result}"
            strErrorCode="{retry_error}" />
            
        <Script code="retry_result == true" />
    </Sequence>
    
    <!-- 最终失败处理 -->
    <Sequence name="最终失败处理">
        <PubPrintMessage strPrintMessage="⚠️ 位置校正多次重试失败，错误码: {error_code}" />
        <Script code="
            system_status := 'ERROR';
            error_description := '位置校正失败: ' + error_code;
        " />
    </Sequence>
    
</ReactiveFallback>
```

## 🔄 3. 数据转换和处理

### 使用Script节点处理和转换数据

```xml
<Sequence name="数据处理链">
    
    <!-- 步骤1：获取初始数据 -->
    <CorrectionPosition 
        doubleAbsX="100.0"
        doubleAbsY="200.0"
        doubleAbsZ="50.0"
        doubleAbsR="0.0"
        boolOutputSuccess="{step1_success}"
        strPlcFeedback="{plc_raw_data}" />
    
    <!-- 步骤2：处理PLC反馈数据 -->
    <Script code="
        // 解析PLC反馈数据
        if (step1_success) {
            // 假设PLC返回位置信息
            actual_x := extractValue(plc_raw_data, 'actual_x');
            actual_y := extractValue(plc_raw_data, 'actual_y');
            actual_z := extractValue(plc_raw_data, 'actual_z');
            
            // 计算偏差
            deviation_x := actual_x - 100.0;
            deviation_y := actual_y - 200.0;
            deviation_z := actual_z - 50.0;
            
            // 判断是否需要二次校正
            needs_correction := (abs(deviation_x) > 0.1 || abs(deviation_y) > 0.1 || abs(deviation_z) > 0.1);
            
            console.log('位置偏差: X=' + deviation_x + ', Y=' + deviation_y + ', Z=' + deviation_z);
        } else {
            needs_correction := false;
        }
    " />
    
    <!-- 步骤3：条件性二次校正 -->
    <Fallback name="二次校正决策">
        <!-- 不需要校正 -->
        <Sequence name="精度满足">
            <Script code="needs_correction == false" />
            <PubPrintMessage strPrintMessage="✅ 位置精度满足要求，无需二次校正" />
        </Sequence>
        
        <!-- 需要二次校正 -->
        <Sequence name="二次校正">
            <PubPrintMessage strPrintMessage="🔧 检测到位置偏差，执行二次校正" />
            <CorrectionPosition 
                doubleAbsX="{100.0 - deviation_x}"
                doubleAbsY="{200.0 - deviation_y}"
                doubleAbsZ="{50.0 - deviation_z}"
                doubleAbsR="0.0"
                boolOutputSuccess="{step3_success}"
                strOutputMessage="{step3_message}" />
        </Sequence>
    </Fallback>
    
</Sequence>
```

## 🎭 4. 复杂工作流示例

### 多节点输出整合的完整工作流

```xml
<BehaviorTree ID="ComplexWorkflow">
    <Sequence name="复杂装配工作流">
        
        <!-- 初始化工作变量 -->
        <Script code="
            workflow_step := 1;
            total_steps := 4;
            error_count := 0;
            success_count := 0;
        " />
        
        <!-- 工作流步骤1：位置校正 -->
        <Sequence name="步骤1_位置校正">
            <PubProcessFeedback 
                strProcessStep="位置校正"
                strStatus="RUNNING"
                doubleProgress="{workflow_step / total_steps * 100}"
                strMessage="正在执行位置校正..." />
                
            <CorrectionPosition 
                doubleAbsX="100.0"
                doubleAbsY="200.0"
                doubleAbsZ="75.0"
                doubleAbsR="45.0"
                boolOutputSuccess="{correction_success}"
                strErrorCode="{correction_error}"
                strOutputMessage="{correction_msg}" />
                
            <!-- 记录步骤结果 -->
            <Script code="
                workflow_step := 2;
                if (correction_success) {
                    success_count := success_count + 1;
                    step1_result := 'SUCCESS: ' + correction_msg;
                } else {
                    error_count := error_count + 1;
                    step1_result := 'FAILED: ' + correction_error + ' - ' + correction_msg;
                }
            " />
        </Sequence>
        
        <!-- 工作流步骤2：AGV移动（基于步骤1结果） -->
        <Sequence name="步骤2_AGV移动">
            <Script code="correction_success == true" />  <!-- 前置条件检查 -->
            
            <PubProcessFeedback 
                strProcessStep="AGV移动"
                strStatus="RUNNING"
                doubleProgress="{workflow_step / total_steps * 100}"
                strMessage="位置校正成功，开始AGV移动..." />
                
            <AgvGoPoint 
                strGoPointName="ToWorkstation"
                strNamespace="/Robot1"
                boolOutputSuccess="{agv_success}"
                strOutputMessage="{agv_msg}" />
                
            <Script code="
                workflow_step := 3;
                if (agv_success) {
                    success_count := success_count + 1;
                    step2_result := 'SUCCESS: ' + agv_msg;
                } else {
                    error_count := error_count + 1;
                    step2_result := 'FAILED: ' + agv_msg;
                }
            " />
        </Sequence>
        
        <!-- 工作流步骤3：电机控制（基于前面步骤结果） -->
        <Sequence name="步骤3_电机控制">
            <Script code="correction_success && agv_success" />  <!-- 需要前面都成功 -->
            
            <MotorPositionControl 
                strDeviceId="Robot1"
                intMotorId="5"
                doubleTargetPosition="150.0"
                doubleMaxVelocity="50.0"
                boolOutputSuccess="{motor_success}"
                strOutputMessage="{motor_msg}" />
                
            <Script code="
                workflow_step := 4;
                if (motor_success) {
                    success_count := success_count + 1;
                    step3_result := 'SUCCESS: ' + motor_msg;
                } else {
                    error_count := error_count + 1;
                    step3_result := 'FAILED: ' + motor_msg;
                }
            " />
        </Sequence>
        
        <!-- 工作流总结 -->
        <Script code="
            overall_success := (error_count == 0);
            success_rate := success_count / total_steps * 100;
            
            if (overall_success) {
                workflow_status := 'COMPLETED';
                workflow_message := '所有步骤成功完成，成功率: ' + success_rate + '%';
            } else {
                workflow_status := 'PARTIAL_FAILURE';
                workflow_message := '工作流部分失败，成功率: ' + success_rate + '%, 失败数: ' + error_count;
            }
            
            // 生成详细报告
            detailed_report := '步骤1(位置校正): ' + step1_result + 
                             '； 步骤2(AGV移动): ' + step2_result + 
                             '； 步骤3(电机控制): ' + step3_result;
        " />
        
        <!-- 最终状态反馈 -->
        <PubProcessFeedback 
            strProcessStep="工作流完成"
            strStatus="{workflow_status}"
            doubleProgress="100.0"
            strMessage="{workflow_message}"
            strOperation="生成最终报告" />
            
        <PubPrintMessage strPrintMessage="📊 工作流执行报告: {detailed_report}" />
        
    </Sequence>
</BehaviorTree>
```

## 📋 5. 最佳实践

### 5.1 命名约定
```xml
<!-- 使用描述性的变量名 -->
<CorrectionPosition 
    boolOutputSuccess="{correction_step1_success}"      <!-- 而不是 {success} -->
    strErrorCode="{correction_step1_error_code}"        <!-- 而不是 {error} -->
    strOutputMessage="{correction_step1_message}" />
```

### 5.2 错误处理链
```xml
<Sequence name="带完整错误处理的操作链">
    <!-- 尝试操作 -->
    <CorrectionPosition 
        doubleAbsX="100.0"
        doubleAbsY="200.0"
        doubleAbsZ="50.0"
        doubleAbsR="0.0"
        boolOutputSuccess="{op_success}"
        strErrorCode="{op_error}"
        strOutputMessage="{op_message}" />
    
    <!-- 立即检查结果 -->
    <Fallback name="结果处理">
        <!-- 成功路径 -->
        <Sequence name="成功处理">
            <Script code="op_success == true" />
            <Script code="next_step_input := op_message" />  <!-- 传递成功信息 -->
        </Sequence>
        
        <!-- 失败路径 -->
        <Sequence name="失败处理">
            <Script code="
                error_log := '操作失败: ' + op_error + ' - ' + op_message;
                console.log(error_log);
                next_step_input := 'SKIP';  // 标记跳过下一步
            " />
        </Sequence>
    </Fallback>
    
    <!-- 下一个操作（基于前面的结果） -->
    <Fallback name="条件性下一步">
        <!-- 跳过分支 -->
        <Sequence name="跳过下一步">
            <Script code="next_step_input == 'SKIP'" />
            <PubPrintMessage strPrintMessage="⏭️ 由于前步失败，跳过当前操作" />
        </Sequence>
        
        <!-- 执行分支 -->
        <NextOperation input_from_previous="{next_step_input}" />
    </Fallback>
</Sequence>
```

## 🎯 关键要点

1. **黑板变量** 是主要的数据传递机制
2. **Script节点** 用于数据处理和逻辑判断  
3. **条件检查** 确保数据流的正确性
4. **错误处理** 让数据传递更加健壮
5. **命名规范** 提高可读性和维护性

这样就可以实现灵活的节点间数据传递和工作流控制！🚀