# 通用行为树节点类型及用法（BehaviorTree.CPP）

本节补充BehaviorTree.CPP常用节点类型及其功能、用法，便于开发者查阅和设计行为树。

---

## 1. Action（动作节点）
- **AlwaysFailure**：无条件返回FAILURE。
- **AlwaysSuccess**：无条件返回SUCCESS。
- **Script**：执行脚本（如Lua/Python等），常用于灵活扩展。
- **SetBlackboard**：设置黑板变量。
- **Sleep**：延时一段时间后返回SUCCESS，常用于流程等待。

## 2. Condition（条件节点）
- **ScriptCondition**：通过脚本判断条件，返回SUCCESS或FAILURE。

## 3. Control（控制节点/组合节点）
- **AsyncFallback / AsyncSequence**：异步版本的Fallback/Sequence，支持并发执行子节点。
- **Fallback**：又称Selector，依次执行子节点，任一成功即返回SUCCESS，否则FAILURE。
- **IfThenElse**：条件分支节点，根据条件执行不同分支。
- **Parallel / ParallelAll**：并行执行所有子节点，通常全部成功才返回SUCCESS。
- **ReactiveFallback / ReactiveSequence**：每次tick都从第一个子节点开始，适合高响应场景。
- **Sequence**：顺序执行子节点，全部成功才返回SUCCESS，否则遇到FAILURE立即返回。
- **SequenceWithMemory**：带记忆的顺序节点，跳过已完成的子节点。
- **Switch2/3/4/5/6**：多分支选择节点，根据输入值选择对应分支。
- **WhileDoElse**：条件循环节点，条件为真时执行主分支，否则执行else分支。

## 4. Decorator（装饰器节点）
- **Delay**：延迟一段时间后再tick子节点。
- **ForceFailure / ForceSuccess**：无论子节点结果如何，强制返回FAILURE/SUCCESS。
- **Inverter**：反转子节点结果（SUCCESS<->FAILURE，RUNNING不变）。
- **KeepRunningUntilFailure**：子节点返回FAILURE前一直返回RUNNING。
- **LoopDouble / LoopString**：循环执行子节点指定次数或根据字符串参数。
- **Precondition**：前置条件节点，条件不满足时不执行子节点。
- **Repeat**：重复执行子节点指定次数。
- **RetryUntilSuccessful**：失败时重试子节点，直到成功或达到最大次数。
- **RunOnce**：子节点只执行一次，后续tick直接返回上次结果。
- **Timeout**：对子节点设置超时时间，超时返回FAILURE。

## 5. SubTree（子树节点）
- **SubTree**：将一个行为树作为子节点复用，便于模块化和结构清晰。

---

## 典型用法示例

### 1. Sequence节点
```xml
<Sequence>
  <ActionA/>
  <ActionB/>
  <ActionC/>
</Sequence>
```

### 2. Fallback节点
```xml
<Fallback>
  <ConditionA/>
  <ActionB/>
</Fallback>
```

### 3. Repeat装饰器
```xml
<Repeat num_cycles="3">
  <ActionA/>
</Repeat>
```

### 4. Timeout装饰器
```xml
<Timeout msec="1000">
  <ActionA/>
</Timeout>
```

### 5. Switch多分支
```xml
<Switch3 variable="mode">
  <Case value="0">
    <ActionA/>
  </Case>
  <Case value="1">
    <ActionB/>
  </Case>
  <Case value="2">
    <ActionC/>
  </Case>
</Switch3>
```

---

## 参考资料
- [BehaviorTree.CPP 官方文档](https://www.behaviortree.dev/docs/)
- [Nodes Library](https://www.behaviortree.dev/docs/nodes_library/)

如需详细参数和自定义节点实现方式，请参考官方文档和源码。
