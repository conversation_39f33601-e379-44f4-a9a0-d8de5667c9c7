# Action 扩展信息功能实现总结

## 功能概述

基于你的需求，我已经完成了 Action 接口扩展信息 (`Extend[]`) 的完整实现，使行为树能够访问和使用这些键值对数据。

## 实现的功能

### 1. **接口扩展**
- ✅ 支持 `rpcs_s_interfaces_behavior_tree/Extend[]` 类型
- ✅ 每个 `Extend` 包含 `string key` 和 `string value`
- ✅ 在 `ExecuteProcessAction.action` 中新增 `extends` 字段

### 2. **黑板写入机制**
- ✅ 自动将扩展信息写入行为树黑板
- ✅ 支持两种访问方式：
  - 直接访问：`extend_<key>` (如 `extend_product_type`)
  - 索引访问：`action_extend_N_key` 和 `action_extend_N_value`

### 3. **GetActionParameters 节点增强**
- ✅ 新增扩展信息获取功能
- ✅ 支持通过键名获取：`strExtendKey`
- ✅ 支持通过索引获取：`intExtendIndex`
- ✅ 输出扩展信息数量：`intExtendCount`

### 4. **黑板变量命名规范**

| 变量类型 | 命名格式 | 示例 | 说明 |
|---------|---------|------|------|
| 直接访问 | `extend_<key>` | `extend_product_type` | 最便捷的访问方式 |
| 索引键名 | `action_extend_N_key` | `action_extend_0_key` | 第N个扩展信息的键名 |
| 索引值 | `action_extend_N_value` | `action_extend_0_value` | 第N个扩展信息的值 |
| 数量 | `action_extends_count` | `action_extends_count` | 扩展信息总数 |
| 原始数据 | `action_extends` | `action_extends` | 完整的扩展信息数组 |

## 使用方式

### 1. **在行为树 XML 中直接使用**
```xml
<!-- 直接使用扩展信息 -->
<PubPrintMessage strPrintMessage="产品类型: {extend_product_type}" />
<SomeNode strQualityLevel="{extend_quality_level}" />

<!-- 条件判断 -->
<Condition name="检查产品类型" if="{extend_product_type} == 'TYPE_A'" />
```

### 2. **使用 GetActionParameters 节点**
```xml
<!-- 通过键名获取 -->
<GetActionParameters 
    strExtendKey="product_type"
    strExtendValue="{product_type}"
    boolOutputSuccess="{success}" />

<!-- 通过索引获取 -->
<GetActionParameters 
    intExtendIndex="0"
    strExtendValue="{extend_0_value}"
    strExtendKeyOut="{extend_0_key}"
    boolOutputSuccess="{success}" />
```

### 3. **在自定义节点中访问**
```cpp
auto blackboard = this->config().blackboard;

// 直接获取扩展信息
std::string product_type;
if (blackboard->get("extend_product_type", product_type)) {
    // 使用产品类型
}

// 获取扩展信息数量
int extend_count = 0;
blackboard->get("action_extends_count", extend_count);
```

### 4. **客户端调用示例**

**Python:**
```python
from rpcs_s_interfaces_behavior_tree.msg import Extend

# 创建扩展信息
extends = []
extend1 = Extend()
extend1.key = 'product_type'
extend1.value = 'TYPE_A'
extends.append(extend1)

goal_msg.extends = extends
```

**命令行:**
```bash
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{extends: [{key: 'product_type', value: 'TYPE_A'}]}"
```

## 文件修改清单

### 1. **核心实现文件**
- ✅ `process_action_server.cpp` - 扩展信息写入黑板逻辑
- ✅ `GetActionParameters.hpp` - 节点接口扩展
- ✅ `GetActionParameters.cpp` - 节点实现扩展

### 2. **示例和文档**
- ✅ `action_parameters_test.xml` - 完整测试示例
- ✅ `Action参数传递机制.md` - 详细使用文档
- ✅ `test_action_extends.py` - Python 测试脚本

### 3. **配置文件**
- ✅ `bt_plugins_list.hpp` - 插件列表更新

## 测试验证

### 1. **编译验证**
```bash
cd /path/to/workspace
colcon build --packages-select rpcs_s_behaviors_workflow
```

### 2. **功能测试**
```bash
# 启动 action 服务器
ros2 run rpcs_s_behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1

# 运行测试脚本
python3 test_action_extends.py

# 或使用命令行测试
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'ActionParametersTest', 
      extends: [{key: 'product_type', value: 'TYPE_A'}]}"
```

## 优势特点

1. **向后兼容** - 不影响现有的 `process_parameters` 功能
2. **类型安全** - 结构化的键值对，避免解析错误
3. **灵活访问** - 支持直接访问和索引访问两种方式
4. **易于使用** - 在行为树中可直接引用 `{extend_key}`
5. **完整日志** - 详细的参数和扩展信息日志记录

## 应用场景

- **产品信息传递** - 产品类型、批次号、规格等
- **工艺参数配置** - 质量等级、工作站、操作员等
- **动态配置** - 根据扩展信息动态调整行为树执行逻辑
- **数据追踪** - 完整的工艺执行上下文信息

这个实现完全满足了你的需求，将 `Extend[]` 扩展信息的每个键值对都写入黑板，供行为树节点方便地访问和使用。
