# 行为树目录结构设计

## 背景

随着行为树文件数量的增加，现有的扁平文件结构变得臃肿难以管理。本设计旨在对行为树文件进行层次化组织，提高代码的可维护性和可扩展性。

## 当前结构

目前行为树文件结构是扁平的：
```
config/process_trees/
  ├── Robot1_light_board_reset_bin_position.xml
  ├── Robot2_material_transfer_preparation.xml
  └── ...
```

通过`process_action_mapping.yaml`将动作类型映射到特定的XML文件：
```yaml
# 示例映射
MaterialTransferPreparation: Robot2_material_transfer_preparation.xml
LightBoardResetBinPosition: Robot1_light_board_reset_bin_position.xml
```

## 新结构设计

### 1. 目录层级

新的三层目录结构如下：

```
config/process_trees/
  ├── Robot1/                         # 设备层
  │   ├── LightPanelSplicing/         # 工艺层
  │   │   ├── light_board_reset_bin_position.xml
  │   │   ├── light_board_to_racks.xml
  │   │   └── ...
  │   ├── MaterialTransfer/           # 另一个工艺层
  │   │   ├── material_transfer_preparation.xml
  │   │   └── ...
  │   └── ...
  ├── Robot2/                         # 另一个设备层
  │   ├── MaterialTransfer/           # 工艺层
  │   │   ├── material_transfer_preparation.xml
  │   │   └── ...
  │   └── ...
  └── ...
```

### 2. 配置文件设计

修改`process_action_mapping.yaml`的格式，增加设备和工艺的层次结构：

```yaml
# 新格式
mapping:
  Robot1:
    LightPanelSplicing:
      LightBoardResetBinPosition: light_board_reset_bin_position.xml
      LightBoardToRacks: light_board_to_racks.xml
    MaterialTransfer:
      MaterialTransferPreparation: material_transfer_preparation.xml
      
  Robot2:
    MaterialTransfer:
      MaterialTransferPreparation: material_transfer_preparation.xml
      MaterialTransferExecution: material_transfer_execution.xml
```

### 3. 路径解析设计

1. **动态路径构建**：
   - 输入：设备ID (如"Robot1")、动作类型 (如"LightBoardResetBinPosition")
   - 查找流程：通过配置文件找到对应的工艺类型和文件名
   - 输出：完整路径如 `process_trees/Robot1/LightPanelSplicing/light_board_reset_bin_position.xml`

2. **路径缓存**：
   - 首次加载时，构建一个设备+动作类型到完整路径的映射缓存
   - 缓存格式：`{设备ID}_{动作类型} -> 完整路径`
   - 例如：`Robot1_LightBoardResetBinPosition -> process_trees/Robot1/LightPanelSplicing/light_board_reset_bin_position.xml`

### 4. 向后兼容设计

为保证现有系统可用，可以设计一个向后兼容层：
- 如果在新结构中找不到文件，回退到查找旧的扁平结构
- 逐步迁移现有文件到新结构，同时保持旧文件可用
- 在过渡期间维护双重映射关系

## 系统设计考虑

### 1. 工艺与设备解耦

- 相同工艺可以在不同机器人上执行
- 通过将工艺逻辑与设备ID分离，可以实现更好的模块化
- 特定设备的配置和参数可以单独管理

### 2. 参数化设计

- 在XML文件中使用参数化的设备ID，而不是硬编码
- 运行时根据实际设备ID替换参数
- 促进代码复用，减少重复编写相似行为树

### 3. 动态发现机制

**自动发现**：
- 系统启动时自动扫描目录结构，构建工艺树映射
- 无需手动维护映射配置
- 支持热加载新增的行为树文件

**元数据管理**：
- 为每个XML文件添加元数据头，包含工艺类型、适用设备等信息
- 通过解析元数据自动构建映射关系

## 迁移计划

1. **开发阶段**：
   - 实现新的目录结构和配置格式
   - 保留向后兼容功能
   - 单元测试验证新旧系统的一致性

2. **迁移阶段**：
   - 创建迁移脚本，自动将旧文件转移到新结构
   - 更新配置文件格式
   - 逐步迁移各个模块，确保系统持续可用

3. **稳定阶段**：
   - 完成所有文件迁移
   - 移除旧结构支持
   - 全面采用新的路径解析机制

## 预期优势

1. **更好的组织结构**：通过层次化目录结构，使文件组织更加清晰
2. **提高可维护性**：简化文件管理，降低维护成本
3. **增强扩展性**：更容易添加新的设备和工艺类型
4. **模块化设计**：促进代码复用，减少重复工作
5. **更好的文档化**：目录结构本身提供了系统架构的清晰视图
