robot2@robot2-pc:~/jacky/RPCS_M_BehaviorsTree$ ros2 action send_goal /R03001010002/io_board_1/digital_output_write rpcs_interfaces_motor/action/DigitalOutputWrite  "{output_addresses: [4,5], output_values: [false,true], verify_write: true}"
Waiting for an action server to become available...
Sending goal:
     output_addresses:
- 4
- 5
output_values:
- false
- true
verify_write: true
batch_operation: false
write_timeout: 0.0

Goal accepted with ID: 5db1e9fd2153414fac1e6aff239e2553

Result:
    final_values:
- false
- true
addresses:
- 4
- 5
write_success:
- true
- true
success: true
error_code: 0
error_message: ''
write_operations: 2
average_write_time: 32.134

Goal finished with status: SUCCEEDED
robot2@robot2-pc:~/jacky/RPCS_M_BehaviorsTree$ 




ros2 action send_goal /R03001010002/io_board_1/digital_input_read rpcs_interfaces_motor/action/DigitalInputRead "{input_addresses: [8,9], continuous_read: true, read_interval: 0.5, duration: 3}"
Waiting for an action server to become available...
Sending goal:
     input_addresses:
- 8
- 9
continuous_read: true
read_interval: 0.5
duration: 3.0

Goal accepted with ID: 6ed98809c6e74eacb683486f6a2443e1

Result:
    input_values:
- false
- true
addresses:
- 8
- 9
success: true
error_code: 0
error_message: ''
total_reads: 7
average_read_time: 69.83071428571431

Goal finished with status: SUCCEEDED
robot2@robot2-pc:~/jacky/RPCS_M_BehaviorsTree$ 






