# 远程IO控制器API使用文档

本文档详细介绍了`rpcs_controller_remote_io`包提供的API，包括高层控制器话题和底层硬件服务，并提供了使用示例。

## 目录

1. [概述](#概述)
2. [高层控制器API (话题)](#高层控制器api-话题)
   - [状态话题 (发布)](#状态话题-发布)
   - [命令话题 (订阅)](#命令话题-订阅)
3. [底层硬件API (服务)](#底层硬件api-服务)
   - [功能服务](#功能服务)
   - [调试服务](#调试服务)
4. [通用管理服务](#通用管理服务)
5. [参数列表](#参数列表)
6. [使用示例](#使用示例)
   - [示例1: 使用话题控制IO](#示例1-使用话题控制io)
   - [示例2: 使用服务控制IO](#示例2-使用服务控制io)
   - [示例3: Python脚本交互](#示例3-python脚本交互)
7. [消息和服务定义](#消息和服务定义)


## 概述

`rpcs_controller_remote_io`是一个基于ROS2 Control框架开发的包，用于控制和监控远程IO设备。它提供两层API：

- **高层控制器API**：由`remote_io_controller`提供，通过ROS话题进行交互，使用标准消息类型，易于与其它ROS节点集成。
- **底层硬件API**：由硬件接口直接提供，通过ROS服务进行交互，可以对硬件进行更精细的控制和调试。

通常情况下，推荐使用**高层控制器API**。

**注意**: 本包使用`rpcs_s_interfaces_io_board`包提供的服务接口定义，不再包含自己的消息和服务定义。

---

## 高层控制器API (话题)

这些话题由`remote_io_controller`发布和订阅，话题的命名空间前缀由`device_name`参数决定，默认为`remote_io`。

### 状态话题 (发布)

控制器以一定频率发布IO和设备状态。

| 话题名称 | 消息类型 | 描述 |
|---------|--------|------|
| `/{device_name}/io/digital_inputs` | `std_msgs/msg/Float64MultiArray` | 数字输入状态数组，1.0为ON, 0.0为OFF。 |
| `/{device_name}/io/analog_inputs` | `std_msgs/msg/Float64MultiArray` | 模拟输入值数组。 |
| `/{device_name}/io/digital_outputs` | `std_msgs/msg/Float64MultiArray` | 数字输出状态反馈数组，1.0为ON, 0.0为OFF。 |
| `/{device_name}/io/analog_outputs` | `std_msgs/msg/Float64MultiArray` | 模拟输出值反馈数组。 |
| `/{device_name}/status` | `std_msgs/msg/Float64MultiArray` | 设备状态。`data[0]`为设备状态码, `data[1]`为错误码。 |

### 命令话题 (订阅)

控制器接收这些话题来控制输出。

| 话题名称 | 消息类型 | 描述 |
|---------|--------|------|
| `/{device_name}/cmd/set_digital_outputs` | `std_msgs/msg/Float64MultiArray` | 设置多个数字输出。数组中的每个元素对应一个通道，大于0.5为ON，否则为OFF。 |
| `/{device_name}/cmd/set_analog_outputs` | `std_msgs/msg/Float64MultiArray` | 设置多个模拟输出。数组中的每个元素对应一个通道。 |

---

## 底层硬件API (服务)

这些服务由硬件接口提供，服务名称前缀也由`device_name`参数决定，默认为`/{device_name}/{device_name}`。它们提供了对硬件的直接访问。

### 功能服务

| 服务名称 | 服务类型 | 描述 |
|---------|--------|------|
| `/{device_name}/{device_name}/set_output` | `rpcs_s_interfaces_io_board/srv/SetOutput` | 设置单个数字或模拟输出。 |
| `/{device_name}/{device_name}/get_io_status` | `rpcs_s_interfaces_io_board/srv/GetIOStatus` | 获取所有IO的当前状态和设备状态。 |

### 调试服务

| 服务名称 | 服务类型 | 描述 |
|---------|--------|------|
| `/{device_name}/{device_name}/read_register` | `rpcs_s_interfaces_io_board/srv/ReadRegister` | 读取Modbus寄存器的值。 |
| `/{device_name}/{device_name}/write_register` | `rpcs_s_interfaces_io_board/srv/WriteRegister` | 写入Modbus寄存器的值。 |

---

## 通用管理服务

这些是由`ros2_control`框架提供的标准服务，用于管理控制器和硬件的生命周期。

| 服务名称 | 描述 |
|---------|------|
| `/{device_name}/controller_manager/list_controllers` | 列出所有控制器及其状态。 |
| `/{device_name}/controller_manager/switch_controller` | 启动或停止控制器。 |
| `/{device_name}/controller_manager/set_hardware_component_state` | 设置硬件组件的生命周期状态 (e.g., 'active', 'inactive')。 |

*(注：为简洁起见，此处未列出所有管理服务。服务名称可能因启动配置而异。)*

---

## 参数列表

| 参数名称 | 适用节点 | 类型 | 描述 |
|---------|--------|-----|------|
| `device_name` | `remote_io_controller` | 字符串 | 设置控制器使用的话题命名空间，必须与硬件定义匹配。 |
| `publish_rate` | `remote_io_controller` | 浮点数 | IO状态话题的发布频率 (Hz)。 |
| `update_rate` | `controller_manager` | 浮点数 | `ros2_control`的控制循环更新频率 (Hz)。 |

---

## 使用示例

### 示例1: 使用话题控制IO

```bash
# 查看数字输入状态 (假设 device_name 为 remote_io)
ros2 topic echo /remote_io/io/digital_inputs

# 设置前4个数字输出，1、3为ON，2、4为OFF
ros2 topic pub --once /remote_io/cmd/set_digital_outputs std_msgs/msg/Float64MultiArray "{data: [1.0, 0.0, 1.0, 0.0]}"

# 设置前2个模拟输出的值
ros2 topic pub --once /remote_io/cmd/set_analog_outputs std_msgs/msg/Float64MultiArray "{data: [5.2, 8.8]}"
```

### 示例2: 使用服务控制IO

```bash
# 使用服务设置2号数字输出为 ON (假设 device_name 为 remote_io)
ros2 service call /remote_io/remote_io/set_output rpcs_s_interfaces_io_board/srv/SetOutput "{output_type: 0, output_index: 2, value: 1.0}"

# 使用服务设置1号模拟输出为 4.5V
ros2 service call /remote_io/remote_io/set_output rpcs_s_interfaces_io_board/srv/SetOutput "{output_type: 1, output_index: 1, value: 4.5}"

# 获取完整的IO状态
ros2 service call /remote_io/remote_io/get_io_status rpcs_s_interfaces_io_board/srv/GetIOStatus "{}"

# 读取状态寄存器
ros2 service call /remote_io/remote_io/read_register rpcs_s_interfaces_io_board/srv/ReadRegister "{register_type: 2, address: 0, count: 1}"

# 写入线圈寄存器
ros2 service call /remote_io/remote_io/write_register rpcs_s_interfaces_io_board/srv/WriteRegister "{register_type: 0, address: 0, bool_values: [true, false, true]}"
```

### 示例3: Python脚本交互

```python
#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from rpcs_s_interfaces_io_board.srv import SetOutput, GetIOStatus
import time

class RemoteIOClient(Node):
    def __init__(self, device_name='remote_io'):
        super().__init__('remote_io_client')
        
        # --- 高层话题API ---
        self.digital_out_pub = self.create_publisher(
            Float64MultiArray, f'/{device_name}/cmd/set_digital_outputs', 10)
        self.digital_in_sub = self.create_subscription(
            Float64MultiArray, f'/{device_name}/io/digital_inputs', self.digital_in_callback, 10)
            
        # --- 底层服务API ---
        service_prefix = f'/{device_name}/{device_name}'
        self.set_output_client = self.create_client(SetOutput, f'{service_prefix}/set_output')
        while not self.set_output_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info(f'等待 {service_prefix}/set_output 服务启动...')

        self.get_io_status_client = self.create_client(GetIOStatus, f'{service_prefix}/get_io_status')
        while not self.get_io_status_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info(f'等待 {service_prefix}/get_io_status 服务启动...')

        self.get_logger().info('远程IO控制器客户端就绪')

    def digital_in_callback(self, msg):
        self.get_logger().info(f"接收到数字输入状态: {msg.data}", throttle_duration_sec=2)

    def set_outputs_by_topic(self, values):
        """通过话题设置多个数字输出"""
        msg = Float64MultiArray()
        msg.data = [float(v) for v in values]
        self.digital_out_pub.publish(msg)
        self.get_logger().info(f'通过话题发布了数字输出命令: {msg.data}')

    def set_output_by_service(self, out_type, index, value):
        """通过服务设置单个输出 (0:digital, 1:analog)"""
        request = SetOutput.Request()
        request.output_type = out_type
        request.output_index = index
        request.value = float(value)
        
        future = self.set_output_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result():
            self.get_logger().info(f'服务调用成功: {future.result().message}')
            return future.result().success
        else:
            self.get_logger().error('服务调用失败')
            return False

    def get_io_status(self):
        """获取IO状态"""
        request = GetIOStatus.Request()
        
        future = self.get_io_status_client.call_async(request)
        rclpy.spin_until_future_complete(self, future)
        
        if future.result():
            result = future.result()
            self.get_logger().info(f'IO状态获取成功: {result.message}')
            self.get_logger().info(f'数字输入: {result.digital_inputs}')
            self.get_logger().info(f'数字输出: {result.digital_outputs}')
            return result
        else:
            self.get_logger().error('获取IO状态失败')
            return None

def main():
    rclpy.init()
    client = RemoteIOClient()
    
    try:
        # 示例1：使用话题API
        client.set_outputs_by_topic([True, False, True, False])
        time.sleep(1)

        # 示例2：使用服务API
        client.set_output_by_service(0, 3, True) # 设置第4个数字输出为ON
        time.sleep(1)
        client.set_output_by_service(0, 3, False) # 设置第4个数字输出为OFF
        
        # 示例3：获取IO状态
        client.get_io_status()
        
        # 持续运行以接收回调
        rclpy.spin(client)

    except KeyboardInterrupt:
        pass
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
```

---

## 消息和服务定义

以下是在`rpcs_s_interfaces_io_board`包中定义的核心服务。

### 服务

**`rpcs_s_interfaces_io_board/srv/SetOutput`**
```
# 设置输出的请求
uint8 output_type        # 输出类型: 0=数字, 1=模拟
uint8 output_index       # 输出索引
float32 value            # 输出值 (数字: 0.0=关闭, 非0=打开, 模拟: 实际值)

# 常量定义
uint8 OUTPUT_TYPE_DIGITAL=0
uint8 OUTPUT_TYPE_ANALOG=1
---
# 响应
bool success             # 操作是否成功
string message           # 状态消息
```

**`rpcs_s_interfaces_io_board/srv/GetIOStatus`**
```
# 请求 - 为保持兼容性的空字段
---
# 响应
# 替换外部依赖，使用自定义的IO状态字段
bool[] digital_inputs     # 数字输入状态数组
float32[] analog_inputs   # 模拟输入值数组
bool[] digital_outputs    # 数字输出状态数组
float32[] analog_outputs  # 模拟输出值数组
bool success              # 操作是否成功
string message            # 状态消息
```

**`rpcs_s_interfaces_io_board/srv/ReadRegister`**
```
# 请求
uint8 register_type     # 寄存器类型: 0=线圈, 1=离散输入, 2=保持寄存器, 3=输入寄存器
uint16 address          # 寄存器起始地址
uint16 count            # 要读取的寄存器数量

# 常量定义
uint8 REGISTER_TYPE_COIL=0
uint8 REGISTER_TYPE_INPUT=1
uint8 REGISTER_TYPE_HOLDING=2
uint8 REGISTER_TYPE_INPUT_REG=3
---
# 响应
uint8 register_type     # 寄存器类型
uint16 address          # 起始地址
uint16 count            # 读取数量
bool[] bool_values      # 布尔值 (用于线圈和离散输入)
uint16[] register_values # 寄存器值 (用于保持寄存器和输入寄存器)
bool success            # 操作是否成功
string message          # 状态消息
```

**`rpcs_s_interfaces_io_board/srv/WriteRegister`**
```
# 请求
uint8 register_type      # 寄存器类型: 0=线圈, 2=保持寄存器 (1和3是只读类型)
uint16 address           # 寄存器起始地址
bool[] bool_values       # 布尔值 (用于线圈)
uint16[] register_values # 寄存器值 (用于保持寄存器)

# 常量定义
uint8 REGISTER_TYPE_COIL=0
uint8 REGISTER_TYPE_INPUT=1  # 只读
uint8 REGISTER_TYPE_HOLDING=2
uint8 REGISTER_TYPE_INPUT_REG=3  # 只读
---
# 响应
bool success             # 操作是否成功
string message           # 状态消息
```

---

*最后更新: 2025-07-02* 