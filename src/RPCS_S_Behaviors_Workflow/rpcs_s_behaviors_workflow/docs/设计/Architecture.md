### 项目总结: `RPCS_S_Behaviors_workflow`

该项目是一个用于**工业装配机器人**的、基于 ROS2 的控制系统。它使用行为树来定义和执行复杂的装配工作流。

#### 整体架构

该系统分为三个主要的 ROS2 包，并与外部工具和硬件进行交互：

1.  **`rpcs_s_interfaces_behavior_tree`**:
    *   **目的**: 定义机器人系统不同部分之间通信的"语言"或数据契约。
    *   **功能**: 包含用于 ROS2 topic 的自定义 `.msg` 文件。这些消息描述了机器人的状态 (`RobotStatus.msg`)、物料的状态（暗示了物料相关的消息类型）和指令 (`RobotControl.msg`)。

2.  **`BehaviorTree.ROS2`**:
    *   **目的**: 提供 `BehaviorTree.CPP` 和 ROS2 之间的基础集成。
    *   **功能**: 它提供了通用的、可复用的行为树节点，可以发布/订阅 topic、调用 service 以及与 action server 交互。`rpcs_s_behaviors_workflow` 中的自定义节点基于此包提供的类构建。

3.  **`rpcs_s_behaviors_workflow`**:
    *   **目的**: 这是操作的"大脑"，包含了装配工作流的具体逻辑。
    *   **功能**:
        *   **行为树定义 (`config/`)**: 包含定义行为树结构的 `.xml` 文件。主树 (`workflow_v0.3.xml`) 是一系列装配步骤的序列。
        *   **自定义节点实现 (`plugins/`)**: 包含 XML 树中使用的每个自定义节点的 C++ 源代码。代码按类型（action, condition）组织。
        *   **代码生成**: 该项目严重依赖一个自动代码生成脚本 (`AutoCreateRPCS.py`)。该工具根据 `.btproj` 文件和严格的命名约定 (`Do...`, `Check...`, `Sub...` 等) 为节点生成 C++ 骨架代码，极大地加快了开发速度并强制执行了一致的架构。
        *   **执行**: `launch` 文件用于启动行为树，然后行为树运行定义好的工作流。

#### 工作流和设计模式

核心设计是一个**顺序执行、状态驱动的工作流**。

1.  `MainTree` 按顺序迭代一系列装配任务。
2.  每个任务都是一个子树，通常是一个 `ReactiveFallback`。
3.  这个 `Fallback` 首先检查任务是否已经完成 (使用 `Check...` 条件节点)。
4.  如果没有，它会尝试执行该任务。这通常包括：
    a.  检查必要的先决条件，例如是否拥有所需的部件 (使用 `Has...` 条件节点)。
    b.  执行主要动作 (使用 `Do...` 动作节点)。
5.  状态在行为树的"黑板 (Blackboard)"上进行管理。例如，当一个 `Do...` 动作完成时，它会将一个 `done` 变量设置为 `true`。对应的 `Check...` 节点会读取此变量。
6.  ROS2 通信被封装在自己的子树 (`RosMessage`) 和特定的 ROS 节点 (`Sub...`, `Pub...`) 中，从而使核心逻辑保持清晰，并与通信层分离。

#### 类和功能总结

*   **类**: 主要的类是位于 `rpcs_s_behaviors_workflow/plugins/` 中的自定义行为树节点。每个类代表一个单一、可复用的逻辑片段。
    *   **动作节点 (例如 `DoLightBoardAssembly`)**: 继承自 `BT::StatefulActionNode`。负责执行耗时任务。
    *   **条件节点 (例如 `CheckLightBoardAssembly`)**: 继承自 `BT::SimpleConditionNode`。负责检查状态并立即返回成功/失败。
    *   **ROS 节点 (例如 `SubMaterialStatus`)**: 继承自 `BehaviorTree.ROS2` 的基类，用于处理 ROS2 通信。

*   **函数**: 主要的"函数"是这些类中的方法 (`onStart`, `onRunning`, `tick()` 等)。开发人员的任务是在这些方法内部实现与机器人系统其他部分交互的逻辑。 