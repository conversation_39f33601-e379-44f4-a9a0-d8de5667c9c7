# 虚拟机器人控制系统服务API参考

本文档以表格形式详细描述了虚拟机器人控制系统的ROS2服务接口。这些接口定义在`src/vir_robot_interfaces/srv`目录中，用于实现与PLC通信、任务调度和机器人控制等功能。

## 目录

1. [对位校正服务](#1-对位校正服务)
2. [吸盘控制服务](#2-吸盘控制服务)
3. [离型膜剥离服务](#3-离型膜剥离服务)
4. [物料上料服务](#4-物料上料服务)
5. [物料到位服务](#5-物料到位服务)
6. [灯板保压服务](#6-灯板保压服务)
7. [B工台控制服务](#7-b工台控制服务)
8. [半成品操作服务](#8-半成品操作服务)
9. [成品上电检测服务](#9-成品上电检测服务)
10. [FPC操作服务](#10-fpc操作服务)
11. [保护壳操作服务](#11-保护壳操作服务)
12. [AGV物料管理服务](#12-agv物料管理服务)
13. [图像检测服务](#13-图像检测服务)
14. [PLC监控服务](#14-plc监控服务)

## 1. 对位校正服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_position_correction_service` |
| **接口文件** | `CorrectionPosition.srv` |
| **功能描述** | 用于请求对位平台移动 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `abs_x` | `float64` | x坐标 |
| `abs_y` | `float64` | y坐标 |
| `abs_z` | `float64` | z坐标 |
| `abs_r` | `float64` | 旋转角度 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_position_correction_service vir_robot_interfaces/srv/CorrectionPosition "{abs_x: 1, abs_y: 0, abs_z: 0, abs_r: 0}"
```

## 2. 吸盘控制服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_sucker_service` |
| **接口文件** | `Sucker.srv` |
| **功能描述** | 控制机器人吸盘开关 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `suckers` | `SuckerKeyValue[]` | 吸盘控制数组 |

其中 `SuckerKeyValue` 包含:

| 字段名 | 类型 | 描述 |
|-------|------|------|
| `key` | `int8` | 吸盘序号 |
| `value` | `bool` | 是否启动吸盘 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_sucker_service vir_robot_interfaces/srv/Sucker "{suckers: [{key: '1', value: true}, {key: '2', value: true}]}"
```

## 3. 离型膜剥离服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_pet_strip_service` |
| **接口文件** | `PetStrip.srv` |
| **功能描述** | 控制离型膜剥离操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | 执行步骤，可选值为'A'或'B' |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_pet_strip_service vir_robot_interfaces/srv/PetStrip "{step: 'A'}"
ros2 service call rpcs_vrobot_plc_pet_strip_service vir_robot_interfaces/srv/PetStrip "{step: 'B'}"
```

## 4. 物料上料服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_material_upload_manager_service` |
| **接口文件** | `MaterialUpload.srv` |
| **功能描述** | 执行不同类型的物料上料（下料）操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `upload_type` | `string` | 上料类型 |

`upload_type` 可选值:

| 值 | 描述 |
|---|------|
| `LIGHT_BOARD` | 灯板上料 |
| `FPC` | FPC上料 |
| `PRODUCT_TRAY` | 成品tray上料 |
| `EMPTY_FPC_CONVERTER_TRAY` | FPC转接线空tray下料 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_material_upload_manager_service vir_robot_interfaces/srv/MaterialUpload "{upload_type: 'LIGHT_BOARD'}"
```

## 5. 物料到位服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_material_in_place_manager_service` |
| **接口文件** | `MaterialState.srv` |
| **功能描述** | 检查物料是否已到位 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `material_type` | `string` | 物料类型 |

`material_type` 可选值:

| 值 | 描述 |
|---|------|
| `LIGHT_BOARD` | 灯板 |
| `FPC` | FPC |
| `EMPTY_FPC` | FPC空tray |
| `NAIL` | 钉 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `in_place` | `bool` | 对应材料是否到位 |
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_material_in_place_manager_service vir_robot_interfaces/srv/MaterialState "{material_type: 'LIGHT_BOARD'}"
```

## 6. 灯板保压服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_board_pressure_service` |
| **接口文件** | `PressHoldBoard.srv` |
| **功能描述** | 控制灯板保压操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `reset` | `bool` | 是否执行回零操作 |
| `pressure_value` | `float32` | 保压压力值 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_board_pressure_service vir_robot_interfaces/srv/PressHoldBoard "{reset: false, pressure_value: 100.0}"
```

## 7. B工台控制服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_workbench_service` |
| **接口文件** | `Workbench.srv` |
| **功能描述** | 控制B工台的不同步骤 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | 执行步骤，例如'11_2'、'11_8'等 |
| `reset` | `bool` | 是否进行重置 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_workbench_service vir_robot_interfaces/srv/Workbench "{step: '11_2', reset: true}"
```

## 8. 半成品操作服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_semi_finished_product_service` |
| **接口文件** | `SemiFinishedProduct.srv` |
| **功能描述** | 对半成品进行操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | 操作步骤 |

`step` 可选值:

| 值 | 描述 |
|---|------|
| `CORRECT` | 半成品规正 |
| `NG` | 半成品保压AOI+摆放(NG品下料) |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_semi_finished_product_service vir_robot_interfaces/srv/SemiFinishedProduct "{step: 'CORRECT'}"
```

## 9. 成品上电检测服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_product_power_on_detect_service` |
| **接口文件** | `ProductPowerOnDetect.srv` |
| **功能描述** | 控制成品上电检测过程 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | 操作步骤 |

`step` 可选值:

| 值 | 描述 |
|---|------|
| `ON_CHECK` | 移动到检测台 |
| `PLUGIN` | 插接线 |
| `PULLOUT` | 拔出 |
| `NG_UNLOAD` | NG品下料 |
| `OK_UNLOAD` | 成品下料 |
| `GET_RESULT` | 获取结果 |
| `AWAIT_RESULT` | 等待结果 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `detect_result` | `bool` | 成品检测结果（true: OK, false: NG） |
| `detect_finished` | `bool` | 是否检测完成 |
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_product_power_on_detect_service vir_robot_interfaces/srv/ProductPowerOnDetect "{step: 'ON_CHECK'}"
```

## 10. FPC操作服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_fpc_operate_service` |
| **接口文件** | `FpcOperate.srv` |
| **功能描述** | 控制FPC的各种操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | FPC操作步骤 |

`step` 可选值:

| 值 | 描述 |
|---|------|
| `PROVOKE` | 挑起 |
| `DOWN` | 放下 |
| `FLIP` | 翻折 |
| `IN_POS` | 检查是否到位 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_fpc_operate_service vir_robot_interfaces/srv/FpcOperate "{step: 'PROVOKE'}"
```

## 11. 保护壳操作服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_protect_shell_service` |
| **接口文件** | `ProtectShell.srv` |
| **功能描述** | 控制保护壳操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `step` | `string` | 操作步骤 |

`step` 可选值:

| 值 | 描述 |
|---|------|
| `START` | 启动归正 |
| `RESET` | 归零（放开） |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_protect_shell_service vir_robot_interfaces/srv/ProtectShell "{step: 'START'}"
```

## 12. AGV物料管理服务

| 项目 | 说明 |
|------|------|
| **服务名称** | `rpcs_vrobot_plc_agv_material_state_service` |
| **接口文件** | `AgvMaterial.srv` |
| **功能描述** | 查询AGV上物料状态 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `material_type` | `string` | 物料类型 |

`material_type` 可选值:

| 值 | 描述 |
|---|------|
| `LIGHT_BOARD` | 灯板 |
| `PCBA` | PCBA |
| `PROTECT_SHELL` | 保护壳 |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `exist` | `bool` | 对应材料在小车上是否存在 |
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

**调用示例**:
```bash
ros2 service call rpcs_vrobot_plc_agv_material_state_service vir_robot_interfaces/srv/AgvMaterial "{material_type: 'LIGHT_BOARD'}"
```

## 13. 图像检测服务

| 项目 | 说明 |
|------|------|
| **服务名称** | (未在README中指定) |
| **接口文件** | `ImageDetection.srv` |
| **功能描述** | 执行图像检测操作 |

**请求参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `camera_ip` | `string` | 相机IP（可选，优先级最高） |
| `camera_name` | `string` | 相机名称（可选，第二优先级） |
| `camera_group` | `string` | 相机组名称（可选，第三优先级） |

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `reults` | `ImageDetectionResult[]` | 检测结果数组 |
| `success` | `bool` | 操作是否成功 |
| `error_code` | `string` | 错误码 |
| `msg` | `string` | 详细信息 |

其中 `ImageDetectionResult` 包含:

| 字段名 | 类型 | 描述 |
|-------|------|------|
| `camera_ip` | `string` | 相机IP |
| `coordinates` | `Coordinates` | 坐标信息 |

`Coordinates` 包含:

| 字段名 | 类型 | 描述 |
|-------|------|------|
| `abs_x` | `float64` | x坐标 |
| `abs_y` | `float64` | y坐标 |
| `abs_z` | `float64` | z坐标 |
| `abs_r` | `float64` | 旋转角度 |

## 14. PLC监控服务

| 项目 | 说明 |
|------|------|
| **服务名称** | (未在README中指定) |
| **接口文件** | `PlcMonitor.srv` |
| **功能描述** | 获取PLC系统信息 |

**请求参数**: 无

**响应参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| `informations` | `SysInfo[]` | 系统信息数组 |

其中 `SysInfo` 包含:

| 字段名 | 类型 | 描述 |
|-------|------|------|
| `monitor_type` | `string` | 监控类型（PLC、SERVICE、TOPIC） |
| `info_type` | `string` | 信息类型 |
| `references` | `Reference[]` | 引用信息 |
| `description` | `string` | 描述 |
| `level` | `string` | 日志级别（DEBUG、INFO、WARNING、FATAL） |  