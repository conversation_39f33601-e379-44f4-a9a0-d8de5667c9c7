# BehaviorTree.CPP 类型转换示例

## 扩展信息的类型转换

### 1. 黑板中的存储
所有扩展信息在黑板中都以 `std::string` 类型存储：

```cpp
// 在 ProcessActionServer 中
context->set("extend_product_type", extend.value);  // std::string
context->set("extend_product_id", "12345");         // std::string
context->set("extend_price", "99.99");              // std::string
context->set("extend_is_active", "true");           // std::string
```

### 2. 节点中的类型定义和使用

#### 示例节点：ProductProcessor
```cpp
class ProductProcessor : public BT::SyncActionNode
{
public:
    static BT::PortsList providedPorts()
    {
        return {
            // 输入端口 - 定义期望的类型
            BT::InputPort<std::string>("strProductType", "产品类型"),
            BT::InputPort<int>("intProductId", "产品ID"),
            BT::InputPort<double>("doublePrice", "产品价格"),
            BT::InputPort<bool>("boolIsActive", "是否激活"),
            
            // 输出端口
            BT::OutputPort<bool>("boolSuccess", "处理是否成功")
        };
    }

    BT::NodeStatus tick() override
    {
        // BehaviorTree.CPP 会自动进行类型转换
        std::string product_type;
        int product_id;
        double price;
        bool is_active;
        
        // 从黑板获取值，自动类型转换
        if (!getInput("strProductType", product_type)) {
            RCLCPP_ERROR(logger_, "Failed to get product_type");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput("intProductId", product_id)) {
            RCLCPP_ERROR(logger_, "Failed to get product_id");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput("doublePrice", price)) {
            RCLCPP_ERROR(logger_, "Failed to get price");
            return BT::NodeStatus::FAILURE;
        }
        
        if (!getInput("boolIsActive", is_active)) {
            RCLCPP_ERROR(logger_, "Failed to get is_active");
            return BT::NodeStatus::FAILURE;
        }
        
        // 使用转换后的值
        RCLCPP_INFO(logger_, "Processing product: %s (ID: %d, Price: %.2f, Active: %s)",
                   product_type.c_str(), product_id, price, is_active ? "true" : "false");
        
        return BT::NodeStatus::SUCCESS;
    }
};
```

### 3. XML 中的使用

```xml
<!-- 扩展信息传递给节点 -->
<ProductProcessor 
    strProductType="{extend_product_type}"    <!-- string -> string -->
    intProductId="{extend_product_id}"        <!-- string -> int -->
    doublePrice="{extend_price}"              <!-- string -> double -->
    boolIsActive="{extend_is_active}"         <!-- string -> bool -->
    boolSuccess="{process_success}" />
```

### 4. 类型转换规则

#### 字符串到数值类型
```cpp
// 有效的转换
"123" -> int(123)
"3.14" -> double(3.14)
"0" -> int(0)
"-42" -> int(-42)

// 无效的转换会导致 getInput 返回 false
"abc" -> int (转换失败)
"" -> int (转换失败)
```

#### 字符串到布尔类型
```cpp
// 转换为 true 的字符串
"true", "True", "TRUE", "1", "yes", "Yes", "YES"

// 转换为 false 的字符串
"false", "False", "FALSE", "0", "no", "No", "NO"

// 其他字符串会导致转换失败
```

### 5. 错误处理和验证

#### 在节点中进行类型验证
```cpp
BT::NodeStatus tick() override
{
    std::string product_type;
    int product_id;
    
    // 获取并验证字符串类型
    if (!getInput("strProductType", product_type) || product_type.empty()) {
        RCLCPP_ERROR(logger_, "Invalid product type");
        return BT::NodeStatus::FAILURE;
    }
    
    // 获取并验证数值类型
    if (!getInput("intProductId", product_id) || product_id <= 0) {
        RCLCPP_ERROR(logger_, "Invalid product ID: %d", product_id);
        return BT::NodeStatus::FAILURE;
    }
    
    return BT::NodeStatus::SUCCESS;
}
```

#### 在 XML 中使用条件验证
```xml
<Sequence name="ProductProcessingWithValidation">
    <!-- 验证产品类型不为空 -->
    <Condition name="ValidateProductType" 
               if="{extend_product_type} != ''" />
    
    <!-- 验证产品ID大于0 -->
    <Condition name="ValidateProductId" 
               if="{extend_product_id} > '0'" />
    
    <!-- 处理产品 -->
    <ProductProcessor 
        strProductType="{extend_product_type}"
        intProductId="{extend_product_id}"
        boolSuccess="{process_success}" />
</Sequence>
```

### 6. 最佳实践

#### 1. 明确的端口类型定义
```cpp
// 好的做法：明确指定类型
BT::InputPort<int>("intProductId", "产品ID")
BT::InputPort<double>("doublePrice", "价格")

// 避免：使用泛型
BT::InputPort<std::string>("strValue", "某个值")  // 然后手动转换
```

#### 2. 提供默认值
```cpp
static BT::PortsList providedPorts()
{
    return {
        BT::InputPort<int>("intProductId", 0, "产品ID，默认为0"),
        BT::InputPort<bool>("boolIsActive", true, "是否激活，默认为true")
    };
}
```

#### 3. 在客户端确保数据格式正确
```python
# Python 客户端示例
extend1 = Extend()
extend1.key = "product_id"
extend1.value = str(12345)  # 确保是字符串

extend2 = Extend()
extend2.key = "price"
extend2.value = "99.99"     # 数值也转为字符串

extend3 = Extend()
extend3.key = "is_active"
extend3.value = "true"      # 布尔值转为字符串
```

## 总结

1. **存储类型**：所有扩展信息在黑板中都以 `std::string` 存储
2. **自动转换**：BehaviorTree.CPP 根据端口定义自动进行类型转换
3. **类型安全**：`getInput()` 方法会在转换失败时返回 `false`
4. **验证机制**：可以在节点中或 XML 中添加验证逻辑
5. **最佳实践**：明确端口类型、提供默认值、客户端确保格式正确
