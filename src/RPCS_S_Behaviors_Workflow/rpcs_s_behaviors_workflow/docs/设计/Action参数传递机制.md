# Action 参数传递机制

## 概述

本文档介绍如何将 Action 接口的参数传递给行为树节点，使行为树能够根据外部输入动态调整执行逻辑。

## 机制原理

### 1. 参数写入黑板

当 ProcessActionServer 接收到 Action 请求时，会将所有参数写入行为树的黑板：

```cpp
// 在 load_and_setup_tree 方法中
auto context = BT::Blackboard::create();

// 写入基本信息
context->set("action_robot_id", goal->robot_id);
context->set("action_process_id", goal->process_id);
context->set("action_process_type", goal->process_action_type);
context->set("action_timeout_seconds", goal->timeout_seconds);
context->set("action_preempt_current", goal->preempt_current);

// 写入工艺参数列表
context->set("action_process_parameters", goal->process_parameters);

// 按索引单独存储参数
for (size_t i = 0; i < goal->process_parameters.size(); ++i) {
    std::string key = "action_param_" + std::to_string(i);
    context->set(key, goal->process_parameters[i]);
}

// 存储参数数量
context->set("action_param_count", static_cast<int>(goal->process_parameters.size()));

// 写入扩展信息 (Extend[] extends)
context->set("action_extends", goal->extends);
context->set("action_extends_count", static_cast<int>(goal->extends.size()));

// 将每个扩展键值对写入黑板，使用键名作为黑板变量名
for (size_t i = 0; i < goal->extends.size(); ++i) {
    const auto& extend = goal->extends[i];
    // 直接使用 key 作为黑板变量名，添加前缀避免冲突
    std::string blackboard_key = "extend_" + extend.key;
    context->set(blackboard_key, extend.value);

    // 同时按索引存储，方便遍历
    std::string index_key = "action_extend_" + std::to_string(i) + "_key";
    std::string index_value = "action_extend_" + std::to_string(i) + "_value";
    context->set(index_key, extend.key);
    context->set(index_value, extend.value);
}
```

### 2. 黑板变量命名规范

| 变量名 | 类型 | 描述 |
|--------|------|------|
| `action_robot_id` | string | 机器人ID |
| `action_process_id` | string | 工艺流程ID |
| `action_process_type` | string | 工艺动作类型 |
| `action_timeout_seconds` | int | 超时时间(秒) |
| `action_preempt_current` | bool | 是否抢占当前任务 |
| `action_process_parameters` | vector<string> | 完整参数列表 |
| `action_param_count` | int | 参数总数 |
| `action_param_0` | string | 第0个参数 |
| `action_param_1` | string | 第1个参数 |
| `action_param_N` | string | 第N个参数 |
| `action_extends` | vector<Extend> | 完整扩展信息列表 |
| `action_extends_count` | int | 扩展信息总数 |
| `extend_<key>` | string | 扩展信息值 (key为实际键名) |
| `action_extend_0_key` | string | 第0个扩展信息的键名 |
| `action_extend_0_value` | string | 第0个扩展信息的值 |
| `action_extend_N_key` | string | 第N个扩展信息的键名 |
| `action_extend_N_value` | string | 第N个扩展信息的值 |

## 使用方法

### 1. 使用 GetActionParameters 节点

专门的节点用于获取 Action 参数：

```xml
<!-- 获取所有基本信息 -->
<GetActionParameters 
    strRobotId="{robot_id}"
    strProcessId="{process_id}"
    strProcessType="{process_type}"
    intTimeoutSeconds="{timeout_seconds}"
    intParameterCount="{param_count}"
    strParameterList="{param_list}"
    boolOutputSuccess="{get_action_info_success}"
    strOutputMessage="{get_action_info_message}" />

<!-- 获取特定索引的参数 -->
<GetActionParameters 
    intParameterIndex="0"
    strParameterValue="{param_0_value}"
    boolOutputSuccess="{get_param_0_success}" />

<!-- 通过键名获取参数 -->
<GetActionParameters
    strParameterKey="action_robot_id"
    strParameterValue="{robot_id_from_key}"
    boolOutputSuccess="{get_robot_id_success}" />

<!-- 获取扩展信息 -->
<GetActionParameters
    strExtendKey="product_type"
    strExtendValue="{product_type}"
    strExtendKeyOut="{product_type_key}"
    boolOutputSuccess="{get_product_type_success}" />

<!-- 通过索引获取扩展信息 -->
<GetActionParameters
    intExtendIndex="0"
    strExtendValue="{extend_0_value}"
    strExtendKeyOut="{extend_0_key}"
    boolOutputSuccess="{get_extend_0_success}" />
```

### 2. 直接使用黑板变量

在行为树 XML 中直接引用黑板变量：

```xml
<!-- 使用机器人ID参数 -->
<PubPrintMessage strPrintMessage="当前机器人: {action_robot_id}" />

<!-- 使用第一个参数 -->
<SomeNode strInputParam="{action_param_0}" />

<!-- 使用扩展信息 -->
<SomeNode strProductType="{extend_product_type}" />
<SomeNode strQualityLevel="{extend_quality_level}" />

<!-- 条件判断 -->
<Condition name="检查参数数量" if="{action_param_count} > 0" />
<Condition name="检查扩展信息数量" if="{action_extends_count} > 0" />
```

### 3. 在自定义节点中访问

在自定义节点的 tick() 方法中：

```cpp
BT::NodeStatus MyCustomNode::tick()
{
    auto blackboard = this->config().blackboard;
    
    // 获取机器人ID
    std::string robot_id;
    if (blackboard->get("action_robot_id", robot_id)) {
        RCLCPP_INFO(logger_, "机器人ID: %s", robot_id.c_str());
    }
    
    // 获取第一个参数
    std::string param_0;
    if (blackboard->get("action_param_0", param_0)) {
        RCLCPP_INFO(logger_, "参数[0]: %s", param_0.c_str());
    }
    
    // 获取参数数量
    int param_count = 0;
    blackboard->get("action_param_count", param_count);

    // 获取扩展信息
    std::string product_type;
    if (blackboard->get("extend_product_type", product_type)) {
        RCLCPP_INFO(logger_, "产品类型: %s", product_type.c_str());
    }

    // 获取扩展信息数量
    int extend_count = 0;
    blackboard->get("action_extends_count", extend_count);

    return BT::NodeStatus::SUCCESS;
}
```

## 使用示例

### 1. 命令行调用示例

```bash
# 带参数和扩展信息的工艺调用
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'ActionParametersTest',
      robot_id: 'Robot1',
      process_parameters: ['param1_value', 'param2_value', 'param3_value'],
      extends: [
        {key: 'product_type', value: 'TYPE_A'},
        {key: 'quality_level', value: 'HIGH'},
        {key: 'station', value: 'STATION_001'}
      ],
      timeout_seconds: 120,
      process_id: 'TEST_001'}"
```

### 2. 行为树示例

参考 `config/process_trees/Test/Test/action_parameters_test.xml` 文件，展示了完整的参数获取和使用流程。

### 3. Python 客户端示例

```python
goal_msg = ExecuteProcessAction.Goal()
goal_msg.process_action_type = "ActionParametersTest"
goal_msg.robot_id = "Robot1"
goal_msg.process_parameters = [
    "product_id=PROD_001",
    "station=STATION_A",
    "quality_level=HIGH"
]

# 设置扩展信息
from rpcs_s_interfaces_behavior_tree.msg import Extend
extend1 = Extend()
extend1.key = "product_type"
extend1.value = "TYPE_A"

extend2 = Extend()
extend2.key = "quality_level"
extend2.value = "HIGH"

extend3 = Extend()
extend3.key = "station"
extend3.value = "STATION_001"

goal_msg.extends = [extend1, extend2, extend3]
goal_msg.timeout_seconds = 120
goal_msg.process_id = "TEST_001"
```

## 最佳实践

### 1. 参数格式建议

**process_parameters (传统参数):**
- 使用键值对格式：`"key=value"`
- 使用 JSON 格式：`'{"key": "value", "number": 123}'`
- 使用简单字符串：`"simple_value"`

**extends (扩展信息):**
- 推荐使用扩展信息存储结构化的键值对
- 键名使用下划线命名：`product_type`, `quality_level`
- 值可以是字符串、数字或简单的 JSON
- 避免在键名中使用特殊字符

### 2. 错误处理

```xml
<!-- 检查参数获取是否成功 -->
<Fallback name="参数获取容错">
    <Sequence name="正常获取参数">
        <GetActionParameters intParameterIndex="0" 
                           strParameterValue="{param_value}"
                           boolOutputSuccess="{get_param_success}" />
        <Condition name="检查获取成功" if="{get_param_success}" />
        <!-- 使用参数的逻辑 -->
    </Sequence>
    <Sequence name="使用默认值">
        <PubPrintMessage strPrintMessage="使用默认参数值" />
        <!-- 默认逻辑 -->
    </Sequence>
</Fallback>
```

### 3. 参数验证

```xml
<!-- 验证参数数量 -->
<Condition name="检查参数数量足够" if="{action_param_count} >= 2" />

<!-- 验证扩展信息数量 -->
<Condition name="检查扩展信息数量" if="{action_extends_count} > 0" />

<!-- 验证特定参数存在 -->
<GetActionParameters intParameterIndex="0"
                   boolOutputSuccess="{param_0_exists}" />
<Condition name="检查第一个参数存在" if="{param_0_exists}" />

<!-- 验证特定扩展信息存在 -->
<GetActionParameters strExtendKey="product_type"
                   boolOutputSuccess="{product_type_exists}" />
<Condition name="检查产品类型存在" if="{product_type_exists}" />

<!-- 直接检查扩展信息值 -->
<Condition name="检查产品类型为A" if="{extend_product_type} == 'TYPE_A'" />
```

## 注意事项

1. **作用域**：黑板变量仅在当前行为树实例中有效
2. **生命周期**：参数在行为树执行期间一直可用
3. **类型安全**：建议在节点中进行适当的类型检查和转换
4. **性能**：频繁访问黑板变量的性能开销较小，可以放心使用

## 故障排除

### 1. 参数未找到

- 检查 Action 调用时是否正确传递了参数
- 确认黑板变量名称拼写正确
- 查看服务器日志确认参数是否成功写入黑板

### 2. 类型转换错误

- 确保黑板变量类型与节点期望类型匹配
- 使用 try-catch 处理类型转换异常

### 3. 参数索引越界

- 先获取参数数量 `action_param_count`
- 确保索引在有效范围内 `[0, param_count)`
