# 电机控制行为树节点设计方案

## 1. 设计概述

基于RPCS_S_Controller_Motor API接口，设计一套电机控制行为树节点，支持局域网内多设备、多品牌电机的统一控制。节点设计遵循RPCS行为树框架规范，基于Action接口实现异步电机控制。

### 1.1 设计目标

- **统一接口**: 为不同品牌电机提供统一的行为树节点接口
- **多设备支持**: 通过设备ID机制支持局域网内多个控制器
- **异步控制**: 支持长时间运行的电机动作，实时反馈进度
- **安全可靠**: 完善的错误处理、超时控制和安全保护机制
- **易于使用**: 清晰的端口设计和XML配置方式

### 1.2 架构设计

```
┌─────────────────────────────────────────────────────────┐
│                 行为树节点层                              │
├─────────────────────────────────────────────────────────┤
│                   Action节点                            │
│  ·MotorPositionControl  - 位置控制                       │
│  ·MotorVelocityControl  - 速度控制                       │
│  ·MotorTorqueControl    - 转矩控制                       │
│  ·MotorHoming           - 回零控制                       │
├─────────────────────────────────────────────────────────┤
│                   ROS2 Action层                         │
├─────────────────────────────────────────────────────────┤
│              电机控制器 (多设备支持)                        │
│  Robot1/motor_X   │  Robot2/motor_X  │  Station_A/motor_X │
└─────────────────────────────────────────────────────────┘
```

## 2. 节点设计详情

### 2.1 Action节点设计

#### 2.1.1 MotorPositionControl - 位置控制节点

**功能**: 控制电机精确移动到指定位置，支持绝对位置和相对位置两种模式

**端口设计**:
```cpp
static BT::PortsList providedPorts() {
    return {
        // === 输入端口 ===
        // 设备和电机标识
        BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
        BT::InputPort<int>("intMotorId", "电机ID编号"),
        
        // 位置控制参数
        BT::InputPort<double>("doubleTargetPosition", "目标位置(mm)"),
        BT::InputPort<bool>("boolAbsolutePosition", true, "位置模式：true=绝对位置，false=相对位置"),
        
        // 运动参数
        BT::InputPort<double>("doubleMaxVelocity", 50.0, "最大速度(mm/s)"),
        BT::InputPort<double>("doubleAcceleration", 100.0, "加速度(mm/s²)"),
        BT::InputPort<double>("doubleDeceleration", 100.0, "减速度(mm/s²)"),
        BT::InputPort<double>("doubleDwellTime", 1.0, "到达后停留时间(s)"),
        BT::InputPort<double>("doubleTimeout", 15.0, "超时时间(s)"),
        
        // === 输出端口 ===
        // 执行结果
        BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
        BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
        BT::OutputPort<double>("doublePositionError", "位置误差(mm)"),
        BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
        BT::OutputPort<int>("intErrorCode", "错误代码"),
        
        // 实时状态
        BT::OutputPort<double>("doubleCurrentPosition", "当前位置(mm)"),
        BT::OutputPort<double>("doubleProgress", "完成进度(0.0-1.0)")
    };
}
```

**关键特性**:
- 支持绝对位置和相对位置两种模式
- 完整的梯形运动控制参数
- 实时进度反馈和位置监控
- 自定义超时时间控制
- 详细的执行结果输出

#### 2.1.2 MotorVelocityControl - 速度控制节点

**功能**: 控制电机以指定速度运行，支持时间限制和位置限制

**端口设计**:
```cpp
static BT::PortsList providedPorts() {
    return {
        // === 输入端口 ===
        BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
        BT::InputPort<int>("intMotorId", "电机ID编号"),
        
        // 速度控制参数
        BT::InputPort<double>("doubleTargetVelocity", "目标速度(mm/s)"),
        BT::InputPort<double>("doubleAcceleration", 100.0, "加速度(mm/s²)"),
        BT::InputPort<double>("doubleDeceleration", 100.0, "减速度(mm/s²)"),
        BT::InputPort<double>("doubleDuration", 0.0, "运行时间(s)，0表示持续运行"),
        
        // 安全限制
        BT::InputPort<bool>("boolUsePositionLimits", false, "是否使用位置限制"),
        BT::InputPort<double>("doubleMinPosition", -1000.0, "最小位置限制(mm)"),
        BT::InputPort<double>("doubleMaxPosition", 1000.0, "最大位置限制(mm)"),
        BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(s)"),
        
        // === 输出端口 ===
        BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
        BT::OutputPort<double>("doubleFinalVelocity", "最终速度(mm/s)"),
        BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
        BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
        BT::OutputPort<int>("intErrorCode", "错误代码"),
        
        // 实时状态
        BT::OutputPort<double>("doubleCurrentVelocity", "当前速度(mm/s)"),
        BT::OutputPort<double>("doubleElapsedTime", "已运行时间(s)")
    };
}
```

#### 2.1.3 MotorTorqueControl - 转矩控制节点

**功能**: 控制电机输出指定转矩，适用于力控制应用

**端口设计**:
```cpp
static BT::PortsList providedPorts() {
    return {
        // === 输入端口 ===
        BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
        BT::InputPort<int>("intMotorId", "电机ID编号"),
        
        // 转矩控制参数
        BT::InputPort<double>("doubleTargetTorque", "目标转矩(Nm)"),
        BT::InputPort<double>("doubleVelocityLimit", 30.0, "最大速度限制(mm/s)"),
        BT::InputPort<double>("doubleTorqueSlope", 1.0, "转矩变化斜率(Nm/s)"),
        BT::InputPort<double>("doubleDuration", 0.0, "运行时间(s)，0表示持续运行"),
        
        // 安全限制
        BT::InputPort<bool>("boolUsePositionLimits", false, "是否使用位置限制"),
        BT::InputPort<double>("doubleMinPosition", -1000.0, "最小位置限制(mm)"),
        BT::InputPort<double>("doubleMaxPosition", 1000.0, "最大位置限制(mm)"),
        BT::InputPort<double>("doubleTimeout", 10.0, "超时时间(s)"),
        
        // === 输出端口 ===
        BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
        BT::OutputPort<double>("doubleFinalTorque", "最终转矩(Nm)"),
        BT::OutputPort<double>("doubleFinalVelocity", "最终速度(mm/s)"),
        BT::OutputPort<double>("doubleFinalPosition", "最终位置(mm)"),
        BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
        BT::OutputPort<int>("intErrorCode", "错误代码"),
        
        // 实时状态
        BT::OutputPort<double>("doubleCurrentTorque", "当前转矩(Nm)"),
        BT::OutputPort<double>("doubleElapsedTime", "已运行时间(s)")
    };
}
```

#### 2.1.4 MotorHoming - 回零控制节点

**功能**: 执行电机回零操作，建立位置基准

**端口设计**:
```cpp
static BT::PortsList providedPorts() {
    return {
        // === 输入端口 ===
        BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID"),
        BT::InputPort<int>("intMotorId", "电机ID编号"),
        
        // 回零参数
        BT::InputPort<int>("intHomingMethod", 17, "寻找原点方法(1-35)"),
        BT::InputPort<float>("floatSpeedSwitch", 20.0, "开关搜索速度(rpm)"),
        BT::InputPort<float>("floatSpeedZero", 20.0, "零点搜索速度(rpm)"),
        BT::InputPort<int>("intHomeOffset", 0, "原点偏移量(mm)"),
        BT::InputPort<int>("intPositionWindow", 100, "位置窗口(inc)"),
        BT::InputPort<int>("intPositionWindowTime", 1000, "位置窗口时间(ms)"),
        BT::InputPort<double>("doubleTimeout", 30.0, "超时时间(s)"),
        
        // === 输出端口 ===
        BT::OutputPort<bool>("boolOutputSuccess", "回零是否成功"),
        BT::OutputPort<float>("floatFinalPosition", "最终位置(mm)"),
        BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
        
        // 实时状态
        BT::OutputPort<int>("intCurrentStatus", "当前状态字"),
        BT::OutputPort<std::string>("strStatusDescription", "状态描述"),
        BT::OutputPort<float>("floatCurrentPosition", "当前位置(mm)")
    };
}
```

## 2. 技术实现要点

### 2.1 多设备支持实现

```cpp
// 设备ID处理逻辑
std::string createActionName(const std::string& device_id, int motor_id, const std::string& action_type) {
    if (device_id.empty() || device_id == "default") {
        return fmt::format("/Kinco_{}/{}", motor_id, action_type);
    } else {
        return fmt::format("/{}/Kinco_{}/{}", device_id, motor_id, action_type);
    }
}
```

### 2.2 异步操作模式

```cpp
// 继承自AgvGoPoint的异步操作模式
class MotorPositionControl : public BT::StatefulActionNode {
private:
    rclcpp::Node::SharedPtr node_;
    rclcpp_action::Client<rpcs_interfaces_motor::action::PositionControl>::SharedPtr client_;
    std::shared_future<rclcpp_action::ClientGoalHandle<rpcs_interfaces_motor::action::PositionControl>::SharedPtr> goal_future_;
    std::chrono::steady_clock::time_point start_time_;
    
public:
    BT::NodeStatus onStart() override;
    BT::NodeStatus onRunning() override;
    void onHalted() override;
};
```

### 2.3 错误处理策略

```cpp
// 统一错误处理
void handleActionError(int error_code, const std::string& error_message) {
    setOutput<bool>("boolOutputSuccess", false);
    setOutput<int>("intErrorCode", error_code);
    setOutput<std::string>("strOutputMessage", error_message);
    
    // 记录详细日志
    RCLCPP_ERROR(rclcpp::get_logger("MotorControl"), 
                "Motor action failed: [{}] {}", error_code, error_message);
}
```

### 2.4 节点注册更新

```cpp
// 更新bt_plugins_list.hpp
inline const std::vector<std::string>& getBTPluginLibs() {
    static const std::vector<std::string> bt_plugin_libs = {
        // 现有节点...
        
        // 电机控制节点
        "MotorPositionControl",
        "MotorVelocityControl", 
        "MotorTorqueControl",
        "MotorHoming"
    };
    return bt_plugin_libs;
}
```

## 3. 项目结构

```
rpcs_s_behaviors_workflow/
├── include/rpcs_s_behaviors_workflow/plugins/
│   └── action/
│       ├── MotorPositionControl.hpp
│       ├── MotorVelocityControl.hpp
│       ├── MotorTorqueControl.hpp
│       └── MotorHoming.hpp
├── plugins/
│   └── action/
│       ├── MotorPositionControl.cpp
│       ├── MotorVelocityControl.cpp
│       ├── MotorTorqueControl.cpp
│       └── MotorHoming.cpp
├── examples/
│   ├── single_motor_control.xml
│   ├── multi_device_coordination.xml
│   └── complex_motor_sequence.xml
└── tests/
    ├── test_motor_position_control.cpp
    ├── test_motor_velocity_control.cpp
    ├── test_motor_torque_control.cpp
    └── test_motor_homing.cpp
```

## 4. 设计优势

### 4.1 统一性
- 所有电机控制节点遵循相同的设计模式
- 统一的端口命名规范和错误处理机制
- 一致的多设备支持实现

### 4.2 可扩展性
- 基于Action接口的设计便于添加新的控制模式
- 统一的抽象层支持新的电机品牌
- 模块化设计便于功能扩展

### 4.3 易用性
- 清晰的端口设计降低学习成本
- 丰富的输出信息便于调试和监控
- 完整的XML示例便于快速上手

### 4.4 可靠性
- 完善的错误处理和超时控制
- 安全的资源管理和清理机制
- 实时状态监控和反馈

### 4.5 高性能
- 异步操作模式避免阻塞
- 多设备并发控制支持
- 优化的网络通信机制

这个设计方案为RPCS系统提供了基于Action接口的电机控制能力，能够有效支持复杂的自动化控制场景，并为后续的功能扩展奠定了坚实的基础。 