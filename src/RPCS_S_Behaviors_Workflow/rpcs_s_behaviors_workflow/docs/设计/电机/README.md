# 电机控制行为树节点文档

## 文档概览

本目录包含基于RPCS_S_Controller_Motor API接口设计的电机控制行为树节点的完整文档，支持局域网内多设备、多品牌电机的统一控制。

## 文档结构

### 1. [电机控制节点设计方案.md](./电机控制节点设计方案.md)
**主要设计文档** - 包含完整的技术设计方案

**内容包括**:
- 设计目标和架构
- 11个电机控制节点的详细设计
  - 4个Action节点：位置控制、速度控制、转矩控制、回零控制
  - 4个Service节点：使能、禁用、重置、紧急停止
  - 3个Condition节点：使能检查、就绪检查、故障检查
- 端口设计规范和技术实现要点
- 多设备支持和异步操作模式
- 项目结构和设计优势

### 2. [实施计划.md](./实施计划.md)  
**项目实施规划** - 9周详细实施计划

**内容包括**:
- 5个实施阶段的详细规划
- 时间节点和验收标准
- 资源分配和人员安排  
- 质量保证和风险控制
- 交付成果和后续计划

### 3. [XML使用示例.md](./XML使用示例.md)
**使用指南和示例** - 详细的XML配置示例

**内容包括**:
- 基础使用示例（单电机控制）
- 多电机协调控制示例
- 多设备控制示例
- 复杂运动序列示例
- 错误处理和恢复示例
- 高级控制模式示例
- 实际应用场景示例

## 核心特性

### 统一接口设计
- 支持Kinco、UMot等多品牌电机
- 统一的端口命名规范（类型+名称）
- 一致的错误处理机制

### 多设备支持
- 通过设备ID机制支持局域网多控制器
- 命名规则：`/{device_id}/Kinco_{id}/{action}`
- 支持设备冲突检测和隔离

### 异步操作模式
- 继承自`BT::StatefulActionNode`
- 三阶段异步控制：`onStart()` → `onRunning()` → `onHalted()`
- 实时进度反馈和状态监控

### 完整的控制类型
- **位置控制**: 绝对/相对位置，梯形运动曲线
- **速度控制**: 时间限制，位置安全限制
- **转矩控制**: 力控制应用，转矩斜率控制
- **回零控制**: 多种回零方法，精确定位

## 快速开始

### 基本使用示例

```xml
<!-- 单电机位置控制 -->
<MotorPositionControl strDeviceId="Robot1" 
                     intMotorId="5"
                     doubleTargetPosition="100.0" 
                     boolAbsolutePosition="true"
                     doubleMaxVelocity="50.0"
                     boolOutputSuccess="{motor_success}" />
```

### 多设备协调示例

```xml
<!-- 三设备并行控制 -->
<Parallel success_threshold="3" failure_threshold="1">
    <MotorPositionControl strDeviceId="Robot1" intMotorId="5" doubleTargetPosition="50.0" />
    <MotorPositionControl strDeviceId="Robot2" intMotorId="5" doubleTargetPosition="75.0" />
    <MotorPositionControl strDeviceId="Station_A" intMotorId="3" doubleTargetPosition="25.0" />
</Parallel>
```

## 节点清单

### Action节点（异步控制）
| 节点名 | 功能 | 主要参数 |
|--------|------|----------|
| `MotorPositionControl` | 位置控制 | 目标位置、绝对/相对模式、速度参数 |
| `MotorVelocityControl` | 速度控制 | 目标速度、运行时间、位置限制 |
| `MotorTorqueControl` | 转矩控制 | 目标转矩、速度限制、转矩斜率 |
| `MotorHoming` | 回零控制 | 回零方法、搜索速度、偏移量 |

### Service节点（同步控制）
| 节点名 | 功能 | 响应时间 |
|--------|------|----------|
| `MotorEnable` | 电机使能 | < 100ms |
| `MotorDisable` | 电机禁用 | < 100ms |
| `MotorReset` | 电机重置 | < 500ms |
| `MotorEmergencyStop` | 紧急停止 | < 50ms |

### Condition节点（状态检查）
| 节点名 | 功能 | 检查内容 |
|--------|------|----------|
| `IsMotorEnabled` | 使能状态检查 | 电机是否使能 |
| `IsMotorReady` | 就绪状态检查 | 电机是否就绪 |
| `HasMotorFault` | 故障检查 | 故障状态和错误码 |

## 技术要点

### 端口命名规范
```cpp
// 输入端口
BT::InputPort<std::string>("strDeviceId", "Robot1", "设备ID")
BT::InputPort<int>("intMotorId", "电机ID编号")
BT::InputPort<double>("doubleTargetPosition", "目标位置(mm)")

// 输出端口  
BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功")
BT::OutputPort<std::string>("strOutputMessage", "结果消息")
```

### 多设备命名
```cpp
// 单设备模式
/motor_5/position_control

// 多设备模式
/Robot1/motor_5/position_control
/Robot2/motor_5/position_control
/Station_A/motor_3/position_control
```

### 异步操作流程
```cpp
onStart() {
    // 1. 参数获取和验证
    // 2. 创建Action客户端
    // 3. 发送异步请求
    return RUNNING;
}

onRunning() {
    // 1. 检查超时
    // 2. 处理反馈
    // 3. 检查完成状态
    return RUNNING/SUCCESS/FAILURE;
}

onHalted() {
    // 1. 取消请求
    // 2. 清理资源
    // 3. 设置输出状态
}
```

## 应用场景

- **自动装配线**: 多设备协调作业
- **机械加工**: 精确位置控制和力控制
- **物料搬运**: 多轴联动和路径规划
- **质检系统**: 精密定位和检测
- **包装生产**: 高速序列控制

## 项目状态

- ✅ **设计方案**: 已完成
- ✅ **实施计划**: 已制定
- ✅ **使用示例**: 已编写
- ⏳ **代码实现**: 待实施（9周计划）
- ⏳ **测试验证**: 待实施
- ⏳ **文档完善**: 待完成

## 联系信息

如有疑问或建议，请联系RPCS开发团队。

---

> **注意**: 本文档基于RPCS_S_Controller_Motor API接口设计，使用前请确保相关依赖包已正确安装。 