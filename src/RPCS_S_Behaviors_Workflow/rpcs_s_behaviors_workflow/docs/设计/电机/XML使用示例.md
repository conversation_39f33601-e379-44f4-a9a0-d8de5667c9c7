# 电机控制行为树节点XML使用示例

## 1. 基础使用示例

### 1.1 单电机位置控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="SingleMotorPosition">
        <Sequence>
            <!-- 执行位置控制 -->
            <MotorPositionControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetPosition="100.0" 
                                 boolAbsolutePosition="true"
                                 doubleMaxVelocity="50.0"
                                 doubleAcceleration="200.0"
                                 doubleDeceleration="200.0"
                                 doubleDwellTime="1.0"
                                 doubleTimeout="15.0"
                                 boolOutputSuccess="{motor_success}"
                                 strOutputMessage="{motor_message}"
                                 doubleCurrentPosition="{current_pos}" />
            
            <!-- 检查执行结果 -->
            <Condition code="motor_success == true" />
            
            <!-- 输出成功信息 -->
            <LogMessage strMessage="电机移动成功，当前位置: {current_pos}mm" />
        </Sequence>
    </BehaviorTree>
</root>
```

### 1.2 单电机速度控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="SingleMotorVelocity">
        <Sequence>
            <!-- 速度控制：以50mm/s运行5秒 -->
            <MotorVelocityControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetVelocity="50.0" 
                                 doubleAcceleration="100.0"
                                 doubleDeceleration="100.0"
                                 doubleDuration="5.0"
                                 boolUsePositionLimits="true"
                                 doubleMinPosition="0.0"
                                 doubleMaxPosition="200.0"
                                 doubleTimeout="10.0"
                                 boolOutputSuccess="{velocity_success}"
                                 strOutputMessage="{velocity_message}" />
            
            <!-- 检查结果 -->
            <Condition code="velocity_success == true" />
        </Sequence>
    </BehaviorTree>
</root>
```

### 1.3 电机回零操作

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="MotorHoming">
        <Sequence>
            <!-- 回零操作 -->
            <MotorHoming strDeviceId="Robot1" 
                        intMotorId="5"
                        intHomingMethod="1"
                        floatSpeedSwitch="10.0"
                        floatSpeedZero="5.0"
                        intHomeOffset="0"
                        intPositionWindow="100"
                        intPositionWindowTime="1000"
                        doubleTimeout="30.0"
                        boolOutputSuccess="{homing_success}"
                        strOutputMessage="{homing_message}"
                        floatFinalPosition="{final_pos}" />
            
            <!-- 检查回零结果 -->
            <Condition code="homing_success == true" />
            
            <!-- 输出结果 -->
            <LogMessage strMessage="回零完成，最终位置: {final_pos}mm" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 2. 多电机协调控制

### 2.1 并行电机控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="ParallelMotorControl">
        <Sequence>
            <!-- 并行执行位置控制 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <!-- X轴电机 -->
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="5"
                                     doubleTargetPosition="50.0" 
                                     doubleMaxVelocity="30.0"
                                     boolOutputSuccess="{x_success}" />
                
                <!-- Y轴电机 -->
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="6"
                                     doubleTargetPosition="75.0" 
                                     doubleMaxVelocity="35.0"
                                     boolOutputSuccess="{y_success}" />
                
                <!-- Z轴电机 -->
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="7"
                                     doubleTargetPosition="25.0" 
                                     doubleMaxVelocity="20.0"
                                     boolOutputSuccess="{z_success}" />
            </Parallel>
            
            <!-- 检查所有轴都成功 -->
            <Condition code="x_success == true && y_success == true && z_success == true" />
            
            <LogMessage strMessage="三轴联动完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

### 2.2 序列电机控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="SequentialMotorControl">
        <Sequence>
            <!-- 第一步：X轴移动到位 -->
            <Sequence>
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="5"
                                     doubleTargetPosition="100.0" 
                                     doubleMaxVelocity="50.0" />
                <LogMessage strMessage="X轴到位" />
            </Sequence>
            
            <!-- 第二步：Y轴移动到位 -->
            <Sequence>
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="6"
                                     doubleTargetPosition="80.0" 
                                     doubleMaxVelocity="40.0" />
                <LogMessage strMessage="Y轴到位" />
            </Sequence>
            
            <!-- 第三步：Z轴下降 -->
            <Sequence>
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="7"
                                     doubleTargetPosition="10.0" 
                                     doubleMaxVelocity="20.0" />
                <LogMessage strMessage="Z轴下降到位" />
            </Sequence>
            
            <LogMessage strMessage="序列运动完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 3. 多设备控制

### 3.1 多设备协调作业

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="MultiDeviceCoordination">
        <Sequence>
            <!-- 协调运动：多设备同时动作 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <!-- Robot1 任务：移动到取货位置 -->
                <Sequence>
                    <LogMessage strMessage="Robot1 开始移动到取货位置" />
                    <MotorPositionControl strDeviceId="Robot1" 
                                         intMotorId="5"
                                         doubleTargetPosition="50.0" 
                                         doubleMaxVelocity="30.0"
                                         doubleTimeout="20.0"
                                         boolOutputSuccess="{robot1_success}" />
                    <LogMessage strMessage="Robot1 取货位置到达" />
                </Sequence>
                
                <!-- Robot2 任务：移动到放货位置 -->
                <Sequence>
                    <LogMessage strMessage="Robot2 开始移动到放货位置" />
                    <MotorPositionControl strDeviceId="Robot2" 
                                         intMotorId="5"
                                         doubleTargetPosition="75.0" 
                                         doubleMaxVelocity="40.0"
                                         doubleTimeout="18.0"
                                         boolOutputSuccess="{robot2_success}" />
                    <LogMessage strMessage="Robot2 放货位置到达" />
                </Sequence>
                
                <!-- Station_A 任务：升降台准备 -->
                <Sequence>
                    <LogMessage strMessage="Station_A 升降台开始准备" />
                    <MotorPositionControl strDeviceId="Station_A" 
                                         intMotorId="3"
                                         doubleTargetPosition="25.0" 
                                         doubleMaxVelocity="20.0"
                                         doubleTimeout="15.0"
                                         boolOutputSuccess="{station_success}" />
                    <LogMessage strMessage="Station_A 升降台就位" />
                </Sequence>
            </Parallel>
            
            <!-- 检查所有设备完成 -->
            <Condition code="robot1_success == true && robot2_success == true && station_success == true" />
            
            <LogMessage strMessage="多设备协调作业完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 4. 复杂运动序列

### 4.1 完整的工艺流程

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="CompleteProcessFlow">
        <Sequence>
            <LogMessage strMessage="开始工艺流程" />
            
            <!-- 第一阶段：系统初始化 -->
            <SubTree ID="SystemInitialization" />
            
            <!-- 第二阶段：工件取料 -->
            <SubTree ID="PickupSequence" />
            
            <!-- 第三阶段：工件加工 -->
            <SubTree ID="ProcessingSequence" />
            
            <!-- 第四阶段：工件放置 -->
            <SubTree ID="PlaceSequence" />
            
            <!-- 第五阶段：系统复位 -->
            <SubTree ID="SystemReset" />
            
            <LogMessage strMessage="工艺流程完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- 子树：系统初始化 -->
    <BehaviorTree ID="SystemInitialization">
        <Sequence>
            <LogMessage strMessage="系统初始化开始" />
            
            <!-- 所有电机回零 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <Sequence>
                    <MotorEnable strDeviceId="Robot1" intMotorId="5" />
                    <MotorHoming strDeviceId="Robot1" intMotorId="5" 
                                intHomingMethod="1" doubleTimeout="30.0" />
                </Sequence>
                <Sequence>
                    <MotorEnable strDeviceId="Robot1" intMotorId="6" />
                    <MotorHoming strDeviceId="Robot1" intMotorId="6" 
                                intHomingMethod="1" doubleTimeout="30.0" />
                </Sequence>
                <Sequence>
                    <MotorEnable strDeviceId="Robot1" intMotorId="7" />
                    <MotorHoming strDeviceId="Robot1" intMotorId="7" 
                                intHomingMethod="1" doubleTimeout="30.0" />
                </Sequence>
            </Parallel>
            
            <!-- 移动到待机位置 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="0.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="0.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                     doubleTargetPosition="100.0" doubleMaxVelocity="30.0" />
            </Parallel>
            
            <LogMessage strMessage="系统初始化完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- 子树：工件取料 -->
    <BehaviorTree ID="PickupSequence">
        <Sequence>
            <LogMessage strMessage="开始取料操作" />
            
            <!-- 移动到取料位置上方 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="120.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="80.0" doubleMaxVelocity="50.0" />
            </Parallel>
            
            <!-- Z轴下降到取料位置 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="20.0" doubleMaxVelocity="20.0" />
            
            <!-- 模拟取料动作（这里可能是气缸或夹爪控制） -->
            <Delay delay_msec="1000" />
            
            <!-- Z轴抬升 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="100.0" doubleMaxVelocity="30.0" />
            
            <LogMessage strMessage="取料操作完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- 子树：工件加工 -->
    <BehaviorTree ID="ProcessingSequence">
        <Sequence>
            <LogMessage strMessage="开始加工操作" />
            
            <!-- 移动到加工位置 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="200.0" doubleMaxVelocity="40.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="150.0" doubleMaxVelocity="40.0" />
            </Parallel>
            
            <!-- 精确定位 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="50.0" doubleMaxVelocity="10.0" />
            
            <!-- 模拟加工过程 -->
            <Sequence>
                <LogMessage strMessage="开始加工..." />
                <Delay delay_msec="5000" />
                <LogMessage strMessage="加工完成" />
            </Sequence>
            
            <!-- 抬升离开加工位置 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="100.0" doubleMaxVelocity="30.0" />
            
            <LogMessage strMessage="加工操作完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- 子树：工件放置 -->
    <BehaviorTree ID="PlaceSequence">
        <Sequence>
            <LogMessage strMessage="开始放置操作" />
            
            <!-- 移动到放置位置上方 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="300.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="100.0" doubleMaxVelocity="50.0" />
            </Parallel>
            
            <!-- Z轴下降到放置位置 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="25.0" doubleMaxVelocity="20.0" />
            
            <!-- 模拟放置动作 -->
            <Delay delay_msec="1000" />
            
            <!-- Z轴抬升 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="100.0" doubleMaxVelocity="30.0" />
            
            <LogMessage strMessage="放置操作完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- 子树：系统复位 -->
    <BehaviorTree ID="SystemReset">
        <Sequence>
            <LogMessage strMessage="系统复位开始" />
            
            <!-- 返回待机位置 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="0.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="0.0" doubleMaxVelocity="50.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                     doubleTargetPosition="100.0" doubleMaxVelocity="30.0" />
            </Parallel>
            
            <LogMessage strMessage="系统复位完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 5. 错误处理和恢复

### 5.1 带错误处理的电机控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="MotorControlWithErrorHandling">
        <Sequence>
            <!-- 带重试的电机使能 -->
            <Retry num_attempts="3">
                <Sequence>
                    <MotorEnable strDeviceId="Robot1" intMotorId="5" 
                                boolOutputSuccess="{enable_success}"
                                strOutputMessage="{enable_message}" />
                    <Condition code="enable_success == true" />
                </Sequence>
            </Retry>
            
            <!-- 故障检查和处理 -->
            <Fallback>
                <!-- 正常情况：无故障 -->
                <Inverter>
                    <HasMotorFault strDeviceId="Robot1" intMotorId="5" 
                                  boolHasFault="{has_fault}"
                                  strErrorMessage="{fault_message}" />
                </Inverter>
                
                <!-- 故障处理序列 -->
                <Sequence>
                    <LogMessage strMessage="检测到电机故障: {fault_message}" />
                    
                    <!-- 尝试重置电机 -->
                    <MotorReset strDeviceId="Robot1" intMotorId="5" 
                               boolOutputSuccess="{reset_success}" />
                    
                    <!-- 如果重置成功，重新使能 -->
                    <Fallback>
                        <Condition code="reset_success == false" />
                        <MotorEnable strDeviceId="Robot1" intMotorId="5" />
                    </Fallback>
                    
                    <!-- 再次检查故障状态 -->
                    <Inverter>
                        <HasMotorFault strDeviceId="Robot1" intMotorId="5" />
                    </Inverter>
                </Sequence>
            </Fallback>
            
            <!-- 执行位置控制，带超时和错误处理 -->
            <Fallback>
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="5"
                                     doubleTargetPosition="100.0" 
                                     doubleMaxVelocity="50.0"
                                     doubleTimeout="20.0"
                                     boolOutputSuccess="{move_success}"
                                     strOutputMessage="{move_message}" />
                
                <!-- 移动失败处理 -->
                <Sequence>
                    <LogMessage strMessage="位置控制失败: {move_message}" />
                    
                    <!-- 紧急停止 -->
                    <MotorEmergencyStop strDeviceId="Robot1" intMotorId="5" />
                    
                    <!-- 返回错误状态 -->
                    <ForceFailure />
                </Sequence>
            </Fallback>
            
            <LogMessage strMessage="电机控制成功完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

### 5.2 系统级错误恢复

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="SystemErrorRecovery">
        <Fallback>
            <!-- 正常工作流程 -->
            <SubTree ID="NormalWorkflow" />
            
            <!-- 系统级错误恢复 -->
            <Sequence>
                <LogMessage strMessage="系统出现异常，开始错误恢复" />
                
                <!-- 停止所有电机 -->
                <Parallel success_threshold="3" failure_threshold="0">
                    <MotorEmergencyStop strDeviceId="Robot1" intMotorId="5" />
                    <MotorEmergencyStop strDeviceId="Robot1" intMotorId="6" />
                    <MotorEmergencyStop strDeviceId="Robot1" intMotorId="7" />
                </Parallel>
                
                <!-- 等待系统稳定 -->
                <Delay delay_msec="2000" />
                
                <!-- 重置所有电机 -->
                <Parallel success_threshold="3" failure_threshold="1">
                    <MotorReset strDeviceId="Robot1" intMotorId="5" />
                    <MotorReset strDeviceId="Robot1" intMotorId="6" />
                    <MotorReset strDeviceId="Robot1" intMotorId="7" />
                </Parallel>
                
                <!-- 重新初始化系统 -->
                <SubTree ID="SystemInitialization" />
                
                <!-- 再次尝试正常流程 -->
                <SubTree ID="NormalWorkflow" />
            </Sequence>
        </Fallback>
    </BehaviorTree>
    
    <!-- 正常工作流程 -->
    <BehaviorTree ID="NormalWorkflow">
        <Sequence>
            <!-- 这里是正常的工作流程 -->
            <LogMessage strMessage="执行正常工作流程" />
            <!-- ... 具体的工作步骤 ... -->
        </Sequence>
    </BehaviorTree>
</root>
```

## 6. 高级控制模式

### 6.1 转矩控制示例

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="TorqueControlExample">
        <Sequence>
            <LogMessage strMessage="开始力控制操作" />
            
            <!-- 使能电机 -->
            <MotorEnable strDeviceId="Robot1" intMotorId="5" />
            
            <!-- 先移动到接触位置附近 -->
            <MotorPositionControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetPosition="90.0" 
                                 doubleMaxVelocity="30.0" />
            
            <!-- 切换到转矩控制，进行力控制接触 -->
            <MotorTorqueControl strDeviceId="Robot1" 
                               intMotorId="5"
                               doubleTargetTorque="0.5" 
                               doubleVelocityLimit="10.0"
                               doubleTorqueSlope="1.0"
                               doubleDuration="3.0"
                               boolUsePositionLimits="true"
                               doubleMinPosition="90.0"
                               doubleMaxPosition="105.0"
                               doubleTimeout="15.0"
                               boolOutputSuccess="{torque_success}"
                               doubleCurrentTorque="{current_torque}"
                               doubleFinalPosition="{contact_position}" />
            
            <!-- 检查力控制结果 -->
            <Condition code="torque_success == true" />
            
            <LogMessage strMessage="力控制完成，接触位置: {contact_position}mm，接触力: {current_torque}Nm" />
            
            <!-- 保持一段时间 -->
            <Delay delay_msec="2000" />
            
            <!-- 退回 -->
            <MotorPositionControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetPosition="80.0" 
                                 doubleMaxVelocity="20.0" />
        </Sequence>
    </BehaviorTree>
</root>
```

### 6.2 速度控制配合位置检测

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="VelocityWithPositionDetection">
        <Sequence>
            <LogMessage strMessage="开始搜索操作" />
            
            <!-- 使能电机 -->
            <MotorEnable strDeviceId="Robot1" intMotorId="5" />
            
            <!-- 以低速搜索目标位置 -->
            <Parallel success_threshold="1" failure_threshold="2">
                <!-- 速度控制：持续运行直到找到目标 -->
                <MotorVelocityControl strDeviceId="Robot1" 
                                     intMotorId="5"
                                     doubleTargetVelocity="20.0" 
                                     doubleAcceleration="50.0"
                                     doubleDuration="0.0"
                                     boolUsePositionLimits="true"
                                     doubleMinPosition="0.0"
                                     doubleMaxPosition="200.0"
                                     doubleTimeout="30.0"
                                     doubleCurrentPosition="{search_position}" />
                
                <!-- 位置检测：当到达目标位置时停止 -->
                <UntilSuccess>
                    <Condition code="search_position > 150.0" />
                </UntilSuccess>
            </Parallel>
            
            <!-- 停止速度控制（通过发送零速度） -->
            <MotorVelocityControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetVelocity="0.0" 
                                 doubleDuration="0.5" />
            
            <LogMessage strMessage="搜索完成，停止位置: {search_position}mm" />
            
            <!-- 精确定位到目标位置 -->
            <MotorPositionControl strDeviceId="Robot1" 
                                 intMotorId="5"
                                 doubleTargetPosition="150.0" 
                                 doubleMaxVelocity="10.0" />
            
            <LogMessage strMessage="精确定位完成" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 7. 实际应用场景

### 7.1 自动装配线

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="AssemblyLine">
        <Sequence>
            <LogMessage strMessage="自动装配线开始作业" />
            
            <!-- 工位1：Robot1负责零件取料 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <!-- Robot1工作序列 -->
                <Sequence>
                    <LogMessage strMessage="Robot1: 开始零件取料" />
                    <SubTree ID="Robot1_PickupPart" />
                    <LogMessage strMessage="Robot1: 零件取料完成" />
                </Sequence>
                
                <!-- 工位2：Robot2负责预加工 -->
                <Sequence>
                    <LogMessage strMessage="Robot2: 开始预加工" />
                    <SubTree ID="Robot2_PreProcess" />
                    <LogMessage strMessage="Robot2: 预加工完成" />
                </Sequence>
            </Parallel>
            
            <!-- 工位3：Station_A负责最终装配 -->
            <Sequence>
                <LogMessage strMessage="Station_A: 开始最终装配" />
                <SubTree ID="StationA_FinalAssembly" />
                <LogMessage strMessage="Station_A: 最终装配完成" />
            </Sequence>
            
            <LogMessage strMessage="装配线作业完成" />
        </Sequence>
    </BehaviorTree>
    
    <!-- Robot1 取料子流程 -->
    <BehaviorTree ID="Robot1_PickupPart">
        <Sequence>
            <!-- 移动到料仓位置 -->
            <Parallel success_threshold="3" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="50.0" doubleMaxVelocity="100.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="30.0" doubleMaxVelocity="80.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                     doubleTargetPosition="100.0" doubleMaxVelocity="60.0" />
            </Parallel>
            
            <!-- 下降取料 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="20.0" doubleMaxVelocity="30.0" />
            
            <!-- 夹取动作（这里用延时模拟） -->
            <Delay delay_msec="1000" />
            
            <!-- 上升 -->
            <MotorPositionControl strDeviceId="Robot1" intMotorId="7"
                                 doubleTargetPosition="100.0" doubleMaxVelocity="40.0" />
            
            <!-- 移动到传递位置 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot1" intMotorId="5"
                                     doubleTargetPosition="150.0" doubleMaxVelocity="80.0" />
                <MotorPositionControl strDeviceId="Robot1" intMotorId="6"
                                     doubleTargetPosition="100.0" doubleMaxVelocity="80.0" />
            </Parallel>
        </Sequence>
    </BehaviorTree>
    
    <!-- Robot2 预加工子流程 -->
    <BehaviorTree ID="Robot2_PreProcess">
        <Sequence>
            <!-- 移动到加工位置 -->
            <Parallel success_threshold="2" failure_threshold="1">
                <MotorPositionControl strDeviceId="Robot2" intMotorId="5"
                                     doubleTargetPosition="200.0" doubleMaxVelocity="90.0" />
                <MotorPositionControl strDeviceId="Robot2" intMotorId="6"
                                     doubleTargetPosition="150.0" doubleMaxVelocity="90.0" />
            </Parallel>
            
            <!-- 精确定位 -->
            <MotorPositionControl strDeviceId="Robot2" intMotorId="7"
                                 doubleTargetPosition="40.0" doubleMaxVelocity="20.0" />
            
            <!-- 加工动作（转矩控制） -->
            <MotorTorqueControl strDeviceId="Robot2" intMotorId="7"
                               doubleTargetTorque="1.2" 
                               doubleDuration="3.0" />
            
            <!-- 退回 -->
            <MotorPositionControl strDeviceId="Robot2" intMotorId="7"
                                 doubleTargetPosition="100.0" doubleMaxVelocity="50.0" />
        </Sequence>
    </BehaviorTree>
    
    <!-- Station_A 装配子流程 -->
    <BehaviorTree ID="StationA_FinalAssembly">
        <Sequence>
            <!-- 升降台下降，准备接收 -->
            <MotorPositionControl strDeviceId="Station_A" intMotorId="3"
                                 doubleTargetPosition="50.0" doubleMaxVelocity="40.0" />
            
            <!-- 等待零件到位 -->
            <Delay delay_msec="2000" />
            
            <!-- 升降台上升，进行装配 -->
            <MotorPositionControl strDeviceId="Station_A" intMotorId="3"
                                 doubleTargetPosition="120.0" doubleMaxVelocity="30.0" />
            
            <!-- 装配完成，下降到输出位置 -->
            <MotorPositionControl strDeviceId="Station_A" intMotorId="3"
                                 doubleTargetPosition="80.0" doubleMaxVelocity="35.0" />
        </Sequence>
    </BehaviorTree>
</root>
```

## 8. 调试和监控

### 8.1 带详细日志的电机控制

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="MotorControlWithLogging">
        <Sequence>
            <LogMessage strMessage="=== 电机控制开始 ===" />
            
            <!-- 检查初始状态 -->
            <Sequence>
                <LogMessage strMessage="检查电机初始状态..." />
                <IsMotorEnabled strDeviceId="Robot1" intMotorId="5" 
                               boolMotorEnabled="{is_enabled}" />
                <LogMessage strMessage="电机使能状态: {is_enabled}" />
                
                <HasMotorFault strDeviceId="Robot1" intMotorId="5" 
                              boolHasFault="{has_fault}"
                              strErrorMessage="{fault_msg}" />
                <LogMessage strMessage="电机故障状态: {has_fault}, 故障信息: {fault_msg}" />
            </Sequence>
            
            <!-- 使能电机 -->
            <Sequence>
                <LogMessage strMessage="正在使能电机..." />
                <MotorEnable strDeviceId="Robot1" intMotorId="5" 
                            boolOutputSuccess="{enable_result}"
                            strOutputMessage="{enable_msg}" />
                <LogMessage strMessage="使能结果: {enable_result}, 消息: {enable_msg}" />
            </Sequence>
            
            <!-- 执行位置控制 -->
            <Sequence>
                <LogMessage strMessage="开始位置控制到 100.0mm..." />
                <MotorPositionControl strDeviceId="Robot1" 
                                     intMotorId="5"
                                     doubleTargetPosition="100.0" 
                                     doubleMaxVelocity="50.0"
                                     doubleAcceleration="200.0"
                                     doubleTimeout="15.0"
                                     boolOutputSuccess="{pos_result}"
                                     doubleFinalPosition="{final_pos}"
                                     doublePositionError="{pos_error}"
                                     strOutputMessage="{pos_msg}"
                                     doubleProgress="{progress}" />
                
                <LogMessage strMessage="位置控制结果: {pos_result}" />
                <LogMessage strMessage="最终位置: {final_pos}mm" />
                <LogMessage strMessage="位置误差: {pos_error}mm" />
                <LogMessage strMessage="控制消息: {pos_msg}" />
            </Sequence>
            
            <!-- 最终状态检查 -->
            <Sequence>
                <LogMessage strMessage="检查最终电机状态..." />
                <IsMotorReady strDeviceId="Robot1" intMotorId="5" 
                             boolMotorReady="{is_ready}"
                             strMotorState="{motor_state}" />
                <LogMessage strMessage="电机就绪状态: {is_ready}, 状态: {motor_state}" />
            </Sequence>
            
            <LogMessage strMessage="=== 电机控制完成 ===" />
        </Sequence>
    </BehaviorTree>
</root>
```

这些XML示例展示了电机控制行为树节点的各种使用场景，从简单的单电机控制到复杂的多设备协调作业，涵盖了实际应用中的常见需求。用户可以根据具体的应用场景选择合适的模式进行配置和使用。 