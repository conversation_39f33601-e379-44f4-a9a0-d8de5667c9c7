# 行为树节点设计规范

## 1. 概述

本文档定义了RPCS系统中行为树节点的设计规范，包括节点类设计、输入/输出端口命名、节点注册等方面的规则。遵循这些规范可以确保节点设计的一致性、可维护性和可扩展性。

## 2. 节点类设计

### 2.1 基本结构

行为树节点应继承自适当的基类：

- **动作节点(Action Node)**: 继承自`BT::StatefulActionNode`或`BT::SyncActionNode`
- **条件节点(Condition Node)**: 继承自`BT::ConditionNode`或`BT::SimpleConditionNode`
- **控制节点(Control Node)**: 继承自`BT::ControlNode`及其子类

### 2.2 命名规范

- 类名应使用驼峰命名法，首字母大写
- 类名应清晰表达节点功能，避免使用缩写
- 对于动作节点，使用动词开头，如`MoveRobot`、`PickObject`
- 对于条件节点，使用`Has`、`Is`、`Can`等前缀，如`HasMaterial`、`IsRobotReady`

### 2.3 文件组织

- 每个节点类应有对应的`.hpp`和`.cpp`文件
- 头文件放置在`include/rpcs_s_behaviors_workflow/plugins/[类型]/`目录下
- 源文件放置在`plugins/[类型]/`目录下
- 类型目录包括：`action/`、`condition/`、`control/`、`decorator/`

### 2.4 类模板

动作节点类模板：

```cpp
// 头文件 (MyActionNode.hpp)
#ifndef RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MYACTIONNODE_HPP_
#define RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MYACTIONNODE_HPP_

#include "behaviortree_cpp/action_node.h"
#include "rclcpp/rclcpp.hpp"
// 其他必要的包含

namespace rpcs_s_behaviors_workflow
{
    class MyActionNode : public BT::StatefulActionNode
    {
    public:
        MyActionNode(const std::string& name, const BT::NodeConfiguration& config);
        virtual ~MyActionNode() = default;

        static BT::PortsList providedPorts()
        {
            return {
                // 输入/输出端口定义
            };
        }

        // StatefulActionNode接口实现
        BT::NodeStatus onStart() override;
        BT::NodeStatus onRunning() override;
        void onHalted() override;

    private:
        // 私有成员变量和方法
    };
}  // namespace rpcs_s_behaviors_workflow

#endif // RPCS_BEHAVIOR_TREE__PLUGINS__ACTION__MYACTIONNODE_HPP_
```

```cpp
// 源文件 (MyActionNode.cpp)
#include "rpcs_s_behaviors_workflow/plugins/action/MyActionNode.hpp"
#include "rpcs_s_behaviors_workflow/plugins_define.hpp"

namespace rpcs_s_behaviors_workflow
{
    MyActionNode::MyActionNode(const std::string& name, const BT::NodeConfiguration& config)
        : BT::StatefulActionNode(name, config)
    {
        // 初始化代码
    }

    BT::NodeStatus MyActionNode::onStart()
    {
        // 实现节点启动逻辑
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus MyActionNode::onRunning()
    {
        // 实现节点运行逻辑
        return BT::NodeStatus::RUNNING;
    }

    void MyActionNode::onHalted()
    {
        // 实现节点中断逻辑
    }
} // namespace rpcs_s_behaviors_workflow

// 节点注册
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MyActionNode>("MyActionNode");
}
```

## 3. 输入/输出端口设计

### 3.1 命名规范

输入和输出端口采用"数据类型+首字母大写名称"的命名方式：

- 布尔类型：`boolIsEnabled`、`boolHasError`
- 整数类型：`intTimeout`、`intRetryCount`
- 浮点类型：`doubleSpeed`、`floatThreshold`
- 字符串类型：`strName`、`strMessage`
- 其他类型：遵循同样的模式，如`vecPoints`、`mapConfig`

### 3.2 端口定义示例

```cpp
static BT::PortsList providedPorts()
{
    return {
        // 输入端口
        BT::InputPort<std::string>("strGoPointName", "目标点名称"),
        BT::InputPort<std::string>("strNamespace", "/Robot1", "节点命名空间"),
        BT::InputPort<int>("intTimeoutMs", 30000, "超时时间(毫秒)"),
        BT::InputPort<bool>("boolPreemptCurrent", false, "是否抢占当前操作"),
        
        // 输出端口
        BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
        BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
        BT::OutputPort<std::string>("strOutputResponse", "完整响应数据")
    };
}
```

### 3.3 端口访问规范

在节点实现中，使用以下方式访问端口：

```cpp
// 读取输入端口
std::string go_point_name;
if (!getInput<std::string>("strGoPointName", go_point_name)) {
    return BT::NodeStatus::FAILURE;
}

int timeout_ms = 30000;  // 默认值
getInput<int>("intTimeoutMs", timeout_ms);  // 如果端口存在则覆盖默认值

// 写入输出端口
setOutput<bool>("boolOutputSuccess", true);
setOutput<std::string>("strOutputMessage", "操作成功完成");
```

## 4. 节点注册

RPCS系统中使用简化的节点注册机制，通过共享头文件集中管理节点列表，减少维护成本。

### 4.1 节点源文件注册

在每个节点实现文件(.cpp)末尾必须添加以下注册代码：

```cpp
#include "behaviortree_cpp/bt_factory.h"
BT_REGISTER_NODES(factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MyActionNode>("MyActionNode");
}
```

这种方式可以确保节点在被动态加载时能够正确注册到工厂中。

### 4.2 节点类型专用注册函数 (可选)

系统保留了按类型组织的注册函数，但目前这些函数不是必需的：

```cpp
// src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/src/action_plugins.cpp
namespace rpcs_s_behaviors_workflow
{
/**
 * 集中注册所有动作节点。
 * 注意：当前系统中没有直接调用此函数，节点主要通过BT_REGISTER_NODES宏进行注册。
 * 保留此函数是为了文档目的和未来可能的扩展。
 */
void RegisterActionNodes(BT::BehaviorTreeFactory& factory)
{
    factory.registerNodeType<rpcs_s_behaviors_workflow::MyActionNode>("MyActionNode");
    // 其他节点注册...
}
}  // namespace rpcs_s_behaviors_workflow
```

### 4.3 集中式节点列表管理

所有节点名称都集中在共享头文件`bt_plugins_list.hpp`中管理：

```cpp
// include/rpcs_s_behaviors_workflow/bt_plugins_list.hpp
namespace rpcs_s_behaviors_workflow
{
// 集中管理所有节点名称列表
inline const std::vector<std::string>& getBTPluginLibs()
{
    static const std::vector<std::string> bt_plugin_libs = {
        // ROS 通信插件
        "SubRobotProperties",
        "PubPrintMessage", 
        "SubMaterialStatus",
        // ... 其他节点，按类别组织
        
        // 控制插件
        "ReturnFailure",
        "Retry"
    };
    return bt_plugin_libs;
}
} // namespace rpcs_s_behaviors_workflow
```

系统中的其他组件通过导入此头文件获取完整的节点列表：

```cpp
// 在rpcs_behavior_tree.cpp和process_action_server.cpp中
#include "rpcs_s_behaviors_workflow/bt_plugins_list.hpp"

// 获取节点列表
const std::vector<std::string>& bt_plugin_libs = rpcs_s_behaviors_workflow::getBTPluginLibs();

// 使用列表进行节点注册
for (const auto& plugin : bt_plugin_libs) {
    factory.registerFromPlugin(BT::SharedLibrary::getOSName(plugin));
}
```

### 4.4 简化的注册流程

开发新节点时，只需遵循以下简化的注册流程：

1. **源文件注册**：在节点的.cpp文件末尾添加`BT_REGISTER_NODES`注册代码
2. **更新共享节点列表**：在`bt_plugins_list.hpp`文件的`getBTPluginLibs()`函数中添加节点名称

### 4.5 节点注册检查清单

开发新节点时，使用以下简化的检查清单确保正确注册：

- [ ] 在节点源文件末尾使用`BT_REGISTER_NODES`注册
- [ ] 在`bt_plugins_list.hpp`的`getBTPluginLibs()`函数中添加节点名称
- [ ] 确认节点名称在所有位置拼写一致

## 5. ROS2集成规范

### 5.1 节点处理

对于需要ROS2通信的节点，推荐以下处理方式：

```cpp
// 构造函数中
node_ = rclcpp::Node::SharedPtr(std::make_shared<rclcpp::Node>("my_action_node"));

// onRunning中
rclcpp::spin_some(node_);  // 处理ROS2事件
```

### 5.2 命名空间处理

为支持多机器人场景，服务名称应包含命名空间：

```cpp
std::string namespace_prefix = "/Robot1";  // 默认值
try {
    namespace_prefix = config.input_ports.at("strNamespace");
} catch (const std::exception& e) {
    // 使用默认值
}

std::string service_name = namespace_prefix + "/MyService";
client_ = node_->create_client<MyServiceType>(service_name);
```

## 6. 异步操作处理

对于长时间运行的节点，应使用异步模式并正确处理超时：

```cpp
// onStart中
auto result_future = client_->async_send_request(request);
response_future_ = result_future.future.share();
start_time_ = std::chrono::steady_clock::now();
return BT::NodeStatus::RUNNING;

// onRunning中
auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
    std::chrono::steady_clock::now() - start_time_).count();
    
if (elapsed > timeout_ms_) {
    return BT::NodeStatus::FAILURE;
}

auto status = response_future_.wait_for(std::chrono::milliseconds(10));
if (status == std::future_status::ready) {
    // 处理响应...
    return BT::NodeStatus::SUCCESS;  // 或FAILURE
}

return BT::NodeStatus::RUNNING;
```

## 7. 完整示例：AgvGoPoint节点

### 7.1 头文件设计

```cpp
// AgvGoPoint.hpp
class AgvGoPoint : public BT::StatefulActionNode
{
public:
    AgvGoPoint(const std::string& name, const BT::NodeConfiguration& config);

    static BT::PortsList providedPorts()
    {
        return {
            BT::InputPort<std::string>("strGoPointName", "目标点名称"),
            BT::InputPort<std::string>("strNamespace", "/Robot1", "节点命名空间"),
            BT::InputPort<int>("intTimeoutMs", 30000, "超时时间(毫秒)"),
            BT::OutputPort<bool>("boolOutputSuccess", "操作是否成功"),
            BT::OutputPort<std::string>("strOutputMessage", "结果消息"),
            BT::OutputPort<std::string>("strOutputResponse", "完整响应数据")
        };
    }

    BT::NodeStatus onStart() override;
    BT::NodeStatus onRunning() override;
    void onHalted() override;

private:
    void createServiceClient(const std::string& namespace_prefix);
    
    rclcpp::Node::SharedPtr node_;
    rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedPtr client_;
    rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedFuture response_future_;
    
    std::string go_point_name_;
    int timeout_ms_;
    std::chrono::steady_clock::time_point start_time_;
};
```

### 7.2 源文件实现关键部分

```cpp
// AgvGoPoint.cpp
BT::NodeStatus AgvGoPoint::onStart()
{
    // 获取输入参数
    if (!getInput<std::string>("strGoPointName", go_point_name_)) {
        return BT::NodeStatus::FAILURE;
    }

    getInput<int>("intTimeoutMs", timeout_ms_);
    
    // 发送请求
    auto request = std::make_shared<rpcs_s_interfaces_agv::srv::AgvGoPoint::Request>();
    std::string json_str = R"({"go_point_name": ")" + go_point_name_ + R"("})";
    request->request = json_str;
    
    auto result_future = client_->async_send_request(request);
    response_future_ = result_future.future.share();
    
    start_time_ = std::chrono::steady_clock::now();
    
    return BT::NodeStatus::RUNNING;
}

BT::NodeStatus AgvGoPoint::onRunning()
{
    // 处理ROS事件
    rclcpp::spin_some(node_);
    
    // 检查是否超时
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now() - start_time_).count();
        
    if (elapsed > timeout_ms_) {
        setOutput<bool>("boolOutputSuccess", false);
        setOutput<std::string>("strOutputMessage", "服务请求超时");
        setOutput<std::string>("strOutputResponse", "");
        
        return BT::NodeStatus::FAILURE;
    }
    
    // 检查响应是否就绪
    auto status = response_future_.wait_for(std::chrono::milliseconds(10));
    
    if (status == std::future_status::ready) {
        auto response = response_future_.get();
        
        setOutput<bool>("boolOutputSuccess", response->success);
        setOutput<std::string>("strOutputMessage", response->message);
        setOutput<std::string>("strOutputResponse", response->response);
        
        return response->success ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
    
    return BT::NodeStatus::RUNNING;
}
```

## 8. XML中使用节点示例

```xml
<BehaviorTree ID="MyTree">
    <Sequence>
        <AgvGoPoint strGoPointName="SafePoint" 
                   strNamespace="/Robot1" 
                   intTimeoutMs="10000" />
        
        <Fallback>
            <Script code="success == true"/>
            <LogMessage strMessage="AGV移动失败: {message}"/>
        </Fallback>
    </Sequence>
</BehaviorTree>
```

## 9. 最佳实践

1. **职责单一**: 每个节点应专注于单一功能，避免复杂逻辑
2. **错误处理**: 全面处理异常情况，提供有用的错误信息
3. **资源管理**: 在`onHalted()`中释放资源，避免泄漏
4. **超时控制**: 所有外部调用应有超时机制
5. **命名空间**: 使用命名空间参数支持多机器人场景
6. **文档**: 在代码中添加充分的注释，特别是端口描述
7. **测试**: 为每个节点编写单元测试，验证功能正确性
8. **集中注册**: 使用共享头文件`bt_plugins_list.hpp`集中管理节点列表，确保一致性

---

本文档基于AgvGoPoint节点设计模式，定义了RPCS系统行为树节点的设计规范。遵循这些规范可以确保节点设计的一致性和系统的可维护性。所有新开发的节点应符合本规范要求。 