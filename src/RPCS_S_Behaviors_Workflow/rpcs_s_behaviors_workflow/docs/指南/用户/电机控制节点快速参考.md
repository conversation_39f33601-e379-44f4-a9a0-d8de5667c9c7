# RPCS 控制节点快速参考

本文档提供RPCS行为树系统中所有控制节点的快速参考卡片，便于快速查阅和使用。

## 目录

1. [电机控制节点](#电机控制节点)
2. [AGV控制节点](#agv控制节点)
3. [IO板控制节点](#io板控制节点)
4. [通用配置模板](#通用配置模板)

---

## 电机控制节点

### MotorHoming - 电机回零控制

**快速配置卡片**
```xml
<MotorHoming 
    strDeviceId="Robot1"           <!-- 设备ID -->
    intMotorId="1"                 <!-- 电机ID (1-6) -->
    intHomingMethod="17"           <!-- 回零方法 (推荐17) -->
    floatSpeedSwitch="20.0"        <!-- 开关搜索速度 (rpm) -->
    floatSpeedZero="20.0"          <!-- 零点搜索速度 (rpm) -->
    intHomeOffset="0"              <!-- 原点偏移量 -->
    intPositionWindow="10"         <!-- 位置窗口 -->
    intPositionWindowTime="100"    <!-- 位置窗口时间 (ms) -->
    doubleTimeout="60.0"           <!-- 超时时间 (秒) -->
    boolOutputSuccess="{homing_success}"
    floatFinalPosition="{final_position}" />
```

### MotorPositionControl - 电机位置控制

**快速配置卡片**
```xml
<MotorPositionControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetPosition="100.0"    <!-- 目标位置 (mm) -->
    boolAbsolutePosition="true"     <!-- true=绝对位置, false=相对位置 -->
    doubleMaxVelocity="50.0"        <!-- 最大速度 (mm/s) -->
    doubleAcceleration="200.0"      <!-- 加速度 (mm/s²) -->
    doubleDeceleration="200.0"      <!-- 减速度 (mm/s²) -->
    doubleDwellTime="1.0"          <!-- 停留时间 (秒) -->
    doubleTimeout="15.0"
    boolOutputSuccess="{pos_success}"
    doubleFinalPosition="{final_pos}" />
```

### MotorVelocityControl - 电机速度控制

**快速配置卡片**
```xml
<MotorVelocityControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetVelocity="30.0"     <!-- 目标速度 (rpm) -->
    doubleAcceleration="10.0"       <!-- 加速度 (rps/s) -->
    doubleDeceleration="10.0"       <!-- 减速度 (rps/s) -->
    doubleDuration="5.0"           <!-- 运行时间 (秒, 0=持续) -->
    doubleTargetCurrentLimit="15.0" <!-- 电流限制 (A) -->
    boolUsePositionLimits="false"
    doubleTimeout="10.0"
    boolOutputSuccess="{vel_success}"
    doubleFinalVelocity="{final_vel}" />
```

### MotorTorqueControl - 电机转矩控制

**快速配置卡片**
```xml
<MotorTorqueControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetTorque="0.5"        <!-- 目标转矩 (%, 占额定转矩百分比) -->
    doubleVelocityLimit="10.0"      <!-- 速度限制 (rpm) -->
    doubleTorqueSlope="1.0"         <!-- 转矩变化斜率 -->
    doubleDuration="3.0"           <!-- 运行时间 (秒) -->
    boolUsePositionLimits="false"
    doubleTimeout="12.0"
    boolOutputSuccess="{torque_success}"
    doubleFinalTorque="{final_torque}" />
```

---

## AGV控制节点

### AgvGoPoint - AGV移动控制

**快速配置卡片**
```xml
<AgvGoPoint 
    strGoPointName="ToWorkstation"  <!-- 目标点名称 -->
    strNamespace="/Robot1"          <!-- 命名空间 -->
    intTimeoutMs="30000"           <!-- 超时时间 (毫秒) -->
    boolOutputSuccess="{agv_success}"
    strOutputMessage="{agv_message}"
    strOutputResponse="{agv_response}" />
```

**预定义目标点**
- `"ToRacks"` - 到料架位置
- `"ToSafePoint"` - 到安全点位置  
- `"ToWorkstation"` - 到工作站位置
- `"ToCharging"` - 到充电位置

---

## IO板控制节点

### DoSetIoOutput - 数字输出控制

**快速配置卡片**
```xml
<DoSetIoOutput 
    strFunctionName="RedLight"      <!-- IO功能名称 -->
    strNamespace="/remote_io"       <!-- IO控制器命名空间 -->
    boolOutputState="true"          <!-- 输出状态 (true=开启, false=关闭) -->
    intTimeoutMs="2000"            <!-- 超时时间 (毫秒) -->
    boolSuccess="{io_success}"
    strMessage="{io_message}"
    boolFinalState="{io_final_state}" />
```

**预定义DO功能映射**

| 功能名称 | 序号 | 说明 | 功能名称 | 序号 | 说明 |
|----------|------|------|----------|------|------|
| `"StoragePoolCylinder1Action"` | 0 | 料仓气缸1动作 | `"TransferVacuum"` | 6 | 传送真空 |
| `"StoragePoolCylinder1Origin"` | 1 | 料仓气缸1原位 | `"TransferVacuumSuction"` | 7 | 传送真空吸力 |
| `"StoragePoolCylinder2Action"` | 2 | 料仓气缸2动作 | `"RedLight"` | 8 | 红色指示灯 |
| `"StoragePoolCylinder2Origin"` | 3 | 料仓气缸2原位 | `"YellowLight"` | 9 | 黄色指示灯 |
| `"StoragePoolTransferCylinderAction"` | 4 | 传送气缸动作 | `"GreenLight"` | 10 | 绿色指示灯 |
| `"StoragePoolTransferCylinderOrigin"` | 5 | 传送气缸原位 | `"Buzzer"` | 11 | 蜂鸣器 |
| `"VacuumPump1"` | 12 | 真空泵1 | `"VacuumPump2"` | 13 | 真空泵2 |
| `"PressurePump"` | 14 | 压力泵 | `"Undefined15"` | 15 | 未定义输出15 |

### DiGetIoStatus - 数字输入检测

**快速配置卡片**
```xml
<DiGetIoStatus 
    strFunctionName="TrayPositionSensor1"  <!-- IO功能名称 -->
    strNamespace="/remote_io"              <!-- IO控制器命名空间 -->
    boolInvertLogic="false"               <!-- 是否反转逻辑 -->
    intTimeoutMs="2000"                   <!-- 超时时间 (毫秒) -->
    boolCurrentState="{sensor_state}"
    strMessage="{sensor_message}" />
```

**预定义DI功能映射**

| 功能名称 | 序号 | 说明 | 功能名称 | 序号 | 说明 |
|----------|------|------|----------|------|------|
| `"TrayPositionSensor1"` | 0 | 料盘位置传感器1 | `"TrayPositionSensor2"` | 5 | 料盘位置传感器2 |
| `"Undefined1"` | 1 | 未定义输入1 | `"TrayPositionSensor3"` | 6 | 料盘位置传感器3 |
| `"Undefined2"` | 2 | 未定义输入2 | `"NegativePressureDisplay4"` | 7 | 负压显示4 |
| `"Undefined3"` | 3 | 未定义输入3 | `"Undefined8-15"` | 8-15 | 未定义输入8-15 |
| `"TrayPositionSensor1_Duplicate"` | 4 | 料盘传感器1副本 | - | - | - |

---

## 通用配置模板

### 电机并行回零模板

```xml
<Parallel success_count="4" failure_count="1" name="并行回零操作">
    <MotorHoming strDeviceId="Robot1" intMotorId="1" intHomingMethod="17" 
                floatSpeedSwitch="20.0" floatSpeedZero="20.0" doubleTimeout="60.0" />
    <MotorHoming strDeviceId="Robot1" intMotorId="2" intHomingMethod="17" 
                floatSpeedSwitch="20.0" floatSpeedZero="20.0" doubleTimeout="60.0" />
    <MotorHoming strDeviceId="Robot1" intMotorId="3" intHomingMethod="17" 
                floatSpeedSwitch="20.0" floatSpeedZero="20.0" doubleTimeout="60.0" />
    <MotorHoming strDeviceId="Robot1" intMotorId="4" intHomingMethod="17" 
                floatSpeedSwitch="20.0" floatSpeedZero="20.0" doubleTimeout="60.0" />
</Parallel>
```

### AGV与电机协作模板

```xml
<Sequence name="AGV与电机协作">
    <!-- AGV移动到位 -->
    <AgvGoPoint strGoPointName="ToWorkstation" strNamespace="/Robot1" 
                boolOutputSuccess="{agv_ok}" />
    <Script code="agv_ok == true" />
    
    <!-- 电机回零 -->
    <MotorHoming strDeviceId="Robot1" intMotorId="1" intHomingMethod="17" 
                boolOutputSuccess="{motor_ok}" />
    <Script code="motor_ok == true" />
    
    <!-- 执行工作流程 -->
    <MotorPositionControl strDeviceId="Robot1" intMotorId="1" 
                         doubleTargetPosition="100.0" boolAbsolutePosition="true" />
</Sequence>
```

### IO控制与状态检测模板

```xml
<Sequence name="IO控制流程">
    <!-- 检查安全条件 -->
    <DiGetIoStatus strFunctionName="TrayPositionSensor1" 
                   boolCurrentState="{safety_ok}" />
    <Script code="safety_ok == true" />
    
    <!-- 开启工作指示灯 -->
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    
    <!-- 启动工作设备 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
    
    <!-- 工作完成后关闭 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
</Sequence>
```

### 错误处理模板

```xml
<Fallback name="错误处理">
    <!-- 正常操作 -->
    <Sequence name="正常流程">
        <MotorPositionControl strDeviceId="Robot1" intMotorId="1" 
                             doubleTargetPosition="50.0" boolOutputSuccess="{success}" />
        <Script code="success == true" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    </Sequence>
    
    <!-- 错误处理 -->
    <Sequence name="错误处理">
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="true" />
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="true" />
        <Delay delay_msec="3000" />
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="false" />
    </Sequence>
</Fallback>
```

### 重试机制模板

```xml
<Retry num_attempts="3" name="重试操作">
    <Sequence name="可能失败的操作">
        <MotorPositionControl strDeviceId="Robot1" intMotorId="1" 
                             doubleTargetPosition="100.0" boolOutputSuccess="{success}" />
        <Script code="success == true" />
    </Sequence>
</Retry>
```

### 超时控制模板

```xml
<Timeout msec="30000" name="30秒超时">
    <Sequence name="长时间操作">
        <MotorHoming strDeviceId="Robot1" intMotorId="1" />
        <MotorPositionControl strDeviceId="Robot1" intMotorId="1" doubleTargetPosition="200.0" />
    </Sequence>
</Timeout>
```

### 完整工作流程模板

```xml
<Sequence name="完整工作流程">
    <!-- 1. 初始化和自检 -->
    <Sequence name="初始化">
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
        <DiGetIoStatus strFunctionName="TrayPositionSensor1" />
        <MotorHoming strDeviceId="Robot1" intMotorId="1" />
    </Sequence>
    
    <!-- 2. AGV协作 -->
    <Sequence name="AGV协作">
        <AgvGoPoint strGoPointName="ToWorkstation" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    </Sequence>
    
    <!-- 3. 工作执行 -->
    <Sequence name="工作执行">
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
        <MotorPositionControl strDeviceId="Robot1" intMotorId="1" doubleTargetPosition="100.0" />
        <MotorVelocityControl strDeviceId="Robot1" intMotorId="1" doubleTargetVelocity="20.0" />
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
    </Sequence>
    
    <!-- 4. 完成和复位 -->
    <Sequence name="完成复位">
        <MotorPositionControl strDeviceId="Robot1" intMotorId="1" doubleTargetPosition="0.0" />
        <AgvGoPoint strGoPointName="ToSafePoint" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
    </Sequence>
</Sequence>
```

---

## 关键参数速查

### 电机控制关键参数

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `intHomingMethod` | 17 | 推荐的回零方法 |
| `floatSpeedSwitch` | 20.0 | 开关搜索速度 (rpm) |
| `doubleTargetCurrentLimit` | 15.0-20.0 | 电流限制 (A) |
| `doubleMaxVelocity` | 30.0-100.0 | 最大速度 (mm/s 或 rpm) |
| `doubleAcceleration` | 10.0-200.0 | 加速度 |
| `doubleTimeout` | 10.0-60.0 | 超时时间 (秒) |

### AGV控制关键参数

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `intTimeoutMs` | 30000 | 标准超时 (30秒) |
| `strNamespace` | "/Robot1" | 设备命名空间 |

### IO控制关键参数

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `intTimeoutMs` | 2000 | IO操作超时 (2秒) |
| `strNamespace` | "/remote_io" | IO控制器命名空间 |
| `boolInvertLogic` | false | 通常不反转逻辑 |

---

## 命名空间和设备配置

### 多设备命名规范

```xml
<!-- Robot1 设备 -->
<MotorHoming strDeviceId="Robot1" intMotorId="1" />
<AgvGoPoint strNamespace="/Robot1" />

<!-- Robot2 设备 -->  
<MotorHoming strDeviceId="Robot2" intMotorId="1" />
<AgvGoPoint strNamespace="/Robot2" />

<!-- IO设备(共享) -->
<DoSetIoOutput strNamespace="/remote_io" />
<DiGetIoStatus strNamespace="/remote_io" />
```

### 常用变量命名

```xml
<!-- 成功状态变量 -->
{motor1_success}, {motor2_success}
{agv_success}, {io_success}

<!-- 消息变量 -->  
{motor1_message}, {agv_message}
{io_message}, {sensor_message}

<!-- 状态变量 -->
{motor1_position}, {agv_response}
{sensor_state}, {io_final_state}
```

---

本快速参考涵盖了RPCS系统中最常用的控制节点配置。更详细的使用说明请参考各节点的专门用户指南文档。 