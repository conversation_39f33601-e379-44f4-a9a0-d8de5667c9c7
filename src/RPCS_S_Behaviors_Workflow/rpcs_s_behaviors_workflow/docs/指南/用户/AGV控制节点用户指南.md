# RPCS AGV控制节点用户指南

## 概述

本指南详细介绍了RPCS行为树系统中AGV控制节点的使用方法，主要包含AgvGoPoint节点的参数配置、使用示例和最佳实践。AGV控制节点基于ROS2服务接口，提供了异步、可取消的AGV移动控制功能。

### 支持的AGV控制节点

1. **AgvGoPoint** - AGV移动控制，支持目标点位移动

---

## AgvGoPoint - AGV移动控制

### 功能描述

AgvGoPoint节点用于控制AGV移动到指定的目标位置。该节点通过ROS2服务接口与AGV控制系统通信，支持多种预定义的目标点位，并提供实时的移动状态反馈。

### 技术架构

- **通信方式**: ROS2 Service (`rpcs_s_interfaces_agv::srv::AgvGoPoint`)
- **服务名称**: `/{namespace}/AgvGoPointService`
- **消息格式**: JSON格式的请求/响应
- **超时控制**: 可配置的超时机制

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `strGoPointName` | string | 目标点名称 | `"ToRacks"`, `"ToSafePoint"` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strNamespace` | string | "/Robot1" | ROS节点命名空间 | "/Robot1", "/Robot2" |
| `intTimeoutMs` | int | 30000 | 超时时间(毫秒) | 15000-60000 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 移动操作是否成功 |
| `strOutputMessage` | string | 服务返回的状态消息 |
| `strOutputResponse` | string | 完整的JSON响应内容 |

### 预定义目标点位

根据系统配置文件，常用的目标点位包括：

| 目标点名称 | 说明 | 用途场景 |
|------------|------|----------|
| `"ToRacks"` | 移动到料架位置 | 取料/送料操作 |
| `"ToSafePoint"` | 移动到安全位置 | 任务完成后的安全停靠 |
| `"ToWorkstation"` | 移动到工作站 | 工件处理操作 |
| `"ToCharging"` | 移动到充电位置 | 自动充电 |

*注：具体的目标点位需要根据实际的AGV系统配置进行定义*

### 使用示例

#### 基本AGV移动操作

```xml
<AgvGoPoint 
    strGoPointName="ToRacks"
    strNamespace="/Robot1"
    intTimeoutMs="30000"
    boolOutputSuccess="{agv_move_success}"
    strOutputMessage="{agv_move_message}"
    strOutputResponse="{agv_response}" />
```

#### 带错误处理的AGV移动

```xml
<Sequence name="AGV移动带错误处理">
    <AgvGoPoint 
        strGoPointName="ToSafePoint"
        boolOutputSuccess="{agv_success}"
        strOutputMessage="{agv_message}" />
    
    <Fallback>
        <!-- 检查移动是否成功 -->
        <Script code="agv_success == true" />
        
        <!-- 失败处理 -->
        <Sequence>
            <PubPrintMessage strPrintMessage="AGV移动失败: {agv_message}" />
            <SetBlackboard strKey="agv_error" strValue="true" />
        </Sequence>
    </Fallback>
</Sequence>
```

#### 多目标点顺序移动

```xml
<Sequence name="AGV多点位移动">
    <!-- 移动到料架 -->
    <Sequence name="移动到料架">
        <PubProcessFeedback 
            strMessage="AGV开始移动到料架"
            doubleProgress="10.0" />
        
        <AgvGoPoint 
            strGoPointName="ToRacks"
            boolOutputSuccess="{move1_success}" />
        
        <Script code="move1_success == true" />
        
        <PubProcessFeedback 
            strMessage="AGV已到达料架位置"
            doubleProgress="40.0" />
    </Sequence>
    
    <!-- 在料架位置停留 -->
    <Delay delay_msec="5000">
        <AlwaysSuccess/>
    </Delay>
    
    <!-- 返回安全位置 -->
    <Sequence name="返回安全位置">
        <PubProcessFeedback 
            strMessage="AGV开始返回安全位置"
            doubleProgress="70.0" />
        
        <AgvGoPoint 
            strGoPointName="ToSafePoint"
            boolOutputSuccess="{move2_success}" />
        
        <Script code="move2_success == true" />
        
        <PubProcessFeedback 
            strMessage="AGV已返回安全位置"
            doubleProgress="100.0" />
    </Sequence>
</Sequence>
```

#### 超时重试机制

```xml
<Retry num_attempts="3" name="AGV移动重试">
    <Sequence name="AGV移动操作">
        <AgvGoPoint 
            strGoPointName="ToWorkstation"
            intTimeoutMs="20000"
            boolOutputSuccess="{agv_success}" />
        
        <Script code="agv_success == true" />
    </Sequence>
</Retry>
```

### 工作流程集成示例

#### 完整的AGV作业流程

```xml
<Sequence name="AGV完整作业流程">
    <!-- 初始化 -->
    <PubProcessFeedback 
        strProcessStep="AGV作业流程"
        strStatus="开始"
        strMessage="开始AGV移动任务"
        doubleProgress="0.0" />
    
    <!-- 1. 移动到料架取料 -->
    <Sequence name="取料阶段">
        <PubProcessFeedback 
            strProcessStep="取料阶段"
            strMessage="AGV移动到料架位置"
            doubleProgress="20.0" />
        
        <AgvGoPoint 
            strGoPointName="ToRacks"
            boolOutputSuccess="{take_material_move_success}"
            strOutputMessage="{take_material_message}" />
        
        <Script code="take_material_move_success == true" />
        
        <!-- 模拟取料操作 -->
        <PubProcessFeedback 
            strMessage="正在执行取料操作"
            doubleProgress="40.0" />
        
        <Delay delay_msec="3000">
            <AlwaysSuccess/>
        </Delay>
    </Sequence>
    
    <!-- 2. 移动到工作站 -->
    <Sequence name="送料阶段">
        <PubProcessFeedback 
            strProcessStep="送料阶段"
            strMessage="AGV移动到工作站"
            doubleProgress="60.0" />
        
        <AgvGoPoint 
            strGoPointName="ToWorkstation"
            boolOutputSuccess="{deliver_move_success}"
            strOutputMessage="{deliver_message}" />
        
        <Script code="deliver_move_success == true" />
        
        <!-- 模拟送料操作 -->
        <PubProcessFeedback 
            strMessage="正在执行送料操作"
            doubleProgress="80.0" />
        
        <Delay delay_msec="2000">
            <AlwaysSuccess/>
        </Delay>
    </Sequence>
    
    <!-- 3. 返回安全位置 -->
    <Sequence name="归位阶段">
        <PubProcessFeedback 
            strProcessStep="归位阶段"
            strMessage="AGV返回安全位置"
            doubleProgress="90.0" />
        
        <AgvGoPoint 
            strGoPointName="ToSafePoint"
            boolOutputSuccess="{return_success}"
            strOutputMessage="{return_message}" />
        
        <Script code="return_success == true" />
        
        <PubProcessFeedback 
            strProcessStep="任务完成"
            strStatus="成功"
            strMessage="AGV作业流程完成"
            doubleProgress="100.0" />
    </Sequence>
</Sequence>
```

### 与其他节点协作

#### AGV与电机控制协作

```xml
<Sequence name="AGV与电机协作流程">
    <!-- AGV移动到指定位置 -->
    <AgvGoPoint 
        strGoPointName="ToWorkstation"
        boolOutputSuccess="{agv_arrive_success}" />
    
    <Script code="agv_arrive_success == true" />
    
    <!-- AGV到位后，执行电机回零 -->
    <Parallel success_count="4" failure_count="1" name="多电机回零">
        <MotorHoming intMotorId="1" boolOutputSuccess="{motor1_success}" />
        <MotorHoming intMotorId="2" boolOutputSuccess="{motor2_success}" />
        <MotorHoming intMotorId="3" boolOutputSuccess="{motor3_success}" />
        <MotorHoming intMotorId="4" boolOutputSuccess="{motor4_success}" />
    </Parallel>
    
    <!-- 电机操作完成后，AGV离开 -->
    <AgvGoPoint 
        strGoPointName="ToSafePoint"
        boolOutputSuccess="{agv_leave_success}" />
</Sequence>
```

### 错误处理和恢复

#### 通用错误处理模式

```xml
<Fallback name="AGV移动错误处理">
    <!-- 正常移动流程 -->
    <Sequence name="正常移动">
        <AgvGoPoint 
            strGoPointName="ToRacks"
            boolOutputSuccess="{agv_success}"
            strOutputMessage="{agv_message}" />
        
        <Script code="agv_success == true" />
    </Sequence>
    
    <!-- 移动失败处理 -->
    <Sequence name="失败处理">
        <PubProcessFeedback 
            strStatus="ERROR"
            strMessage="AGV移动失败: {agv_message}"
            strOperation="错误处理" />
        
        <!-- 尝试移动到安全位置 -->
        <AgvGoPoint 
            strGoPointName="ToSafePoint"
            intTimeoutMs="15000" />
        
        <!-- 记录错误 -->
        <PubPrintMessage strPrintMessage="⚠️ AGV移动失败，已移动到安全位置" />
        
        <ReturnFailure>
            <AlwaysSuccess/>
        </ReturnFailure>
    </Sequence>
</Fallback>
```

#### 超时和网络问题处理

```xml
<Sequence name="网络问题处理">
    <Fallback name="超时处理">
        <!-- 尝试移动（较短超时） -->
        <AgvGoPoint 
            strGoPointName="ToWorkstation"
            intTimeoutMs="15000"
            boolOutputSuccess="{quick_success}" />
        
        <!-- 超时后重试（较长超时） -->
        <Sequence name="重试机制">
            <PubPrintMessage strPrintMessage="🔄 AGV移动超时，正在重试..." />
            
            <AgvGoPoint 
                strGoPointName="ToWorkstation"
                intTimeoutMs="45000"
                boolOutputSuccess="{retry_success}" />
            
            <Script code="retry_success == true" />
        </Sequence>
    </Fallback>
</Sequence>
```

### 命名空间和多设备支持

#### 多AGV设备管理

```xml
<!-- Robot1的AGV -->
<AgvGoPoint 
    strGoPointName="ToRacks"
    strNamespace="/Robot1"
    boolOutputSuccess="{robot1_agv_success}" />

<!-- Robot2的AGV -->
<AgvGoPoint 
    strGoPointName="ToWorkstation"
    strNamespace="/Robot2"
    boolOutputSuccess="{robot2_agv_success}" />
```

#### 多AGV协调移动

```xml
<Parallel success_count="2" failure_count="1" name="多AGV协调">
    <!-- Robot1 AGV移动 -->
    <Sequence name="Robot1 AGV移动">
        <AgvGoPoint 
            strGoPointName="ToRacks"
            strNamespace="/Robot1"
            boolOutputSuccess="{robot1_move_success}" />
        
        <PubProcessFeedback 
            strMessage="Robot1 AGV已到达料架"
            doubleProgress="50.0" />
    </Sequence>
    
    <!-- Robot2 AGV移动 -->
    <Sequence name="Robot2 AGV移动">
        <AgvGoPoint 
            strGoPointName="ToWorkstation"
            strNamespace="/Robot2"
            boolOutputSuccess="{robot2_move_success}" />
        
        <PubProcessFeedback 
            strMessage="Robot2 AGV已到达工作站"
            doubleProgress="50.0" />
    </Sequence>
</Parallel>
```

### 性能优化建议

#### 1. 超时时间设置

根据不同的移动距离和路径复杂度，合理设置超时时间：

- **短距离移动**: 15-20秒
- **中距离移动**: 30-45秒  
- **长距离或复杂路径**: 60-90秒

#### 2. 状态监控

```xml
<Sequence name="状态监控的AGV移动">
    <PubProcessFeedback 
        strMessage="AGV开始移动到目标位置"
        doubleProgress="10.0" />
    
    <AgvGoPoint 
        strGoPointName="ToRacks"
        boolOutputSuccess="{agv_success}"
        strOutputMessage="{agv_status}" />
    
    <!-- 根据返回状态提供详细反馈 -->
    <Fallback>
        <Script code="agv_success == true" />
        <PubProcessFeedback 
            strStatus="ERROR"
            strMessage="AGV移动失败: {agv_status}" />
    </Fallback>
</Sequence>
```

#### 3. 资源管理

- 避免同时向同一AGV发送多个移动指令
- 合理规划移动路径，减少不必要的往返
- 使用重试机制处理临时性网络问题

### 调试技巧

#### 1. 日志输出

```xml
<Sequence name="调试版AGV移动">
    <PubPrintMessage strPrintMessage="🚗 开始AGV移动，目标: ToRacks" />
    
    <AgvGoPoint 
        strGoPointName="ToRacks"
        boolOutputSuccess="{agv_success}"
        strOutputMessage="{agv_message}"
        strOutputResponse="{agv_response}" />
    
    <PubPrintMessage strPrintMessage="📊 AGV移动结果: 成功={agv_success}, 消息={agv_message}" />
    
    <!-- 详细响应输出（调试用） -->
    <PubPrintMessage strPrintMessage="📄 完整响应: {agv_response}" />
</Sequence>
```

#### 2. 分步测试

```xml
<Sequence name="AGV功能测试">
    <!-- 测试移动到料架 -->
    <AgvGoPoint strGoPointName="ToRacks" boolOutputSuccess="{test1}" />
    <Script code="test1 == true" />
    <PubPrintMessage strPrintMessage="✅ 移动到料架测试通过" />
    
    <!-- 测试返回安全位置 -->
    <AgvGoPoint strGoPointName="ToSafePoint" boolOutputSuccess="{test2}" />
    <Script code="test2 == true" />
    <PubPrintMessage strPrintMessage="✅ 返回安全位置测试通过" />
    
    <PubPrintMessage strPrintMessage="🎉 AGV功能测试全部通过！" />
</Sequence>
```

### 故障排除

#### 常见问题及解决方案

##### 1. 服务不可用

**现象**: 节点返回"服务不可用"错误

**解决方案**:
- 检查AGV控制器节点是否运行
- 确认命名空间配置正确
- 验证ROS2网络连接
- 检查服务名称是否匹配

##### 2. 移动超时

**现象**: AGV移动请求超时

**解决方案**:
- 增加`intTimeoutMs`参数值
- 检查AGV硬件状态
- 确认目标点位是否有效
- 检查路径是否被阻塞

##### 3. 目标点位无效

**现象**: AGV返回"目标点位不存在"错误

**解决方案**:
- 确认目标点位名称正确
- 检查AGV系统中是否已配置该点位
- 使用系统中已定义的点位名称

##### 4. JSON请求格式错误

**现象**: 服务返回格式错误

**解决方案**:
- 检查目标点位名称中是否包含特殊字符
- 确认节点内部JSON构造正确
- 查看服务端日志获取详细错误信息

### 最佳实践

1. **路径规划**: 合理规划AGV移动路径，避免冲突
2. **状态检查**: 移动前确认AGV状态正常
3. **错误恢复**: 实现完善的错误处理和恢复机制
4. **资源协调**: 多AGV环境下注意资源协调
5. **超时设置**: 根据实际环境调整超时参数
6. **日志记录**: 记录关键移动操作便于调试

---

## 与电机控制节点的集成

AGV控制节点通常与电机控制节点配合使用，形成完整的自动化流程：

```xml
<Sequence name="AGV与电机协作">
    <!-- 1. AGV移动到位 -->
    <AgvGoPoint strGoPointName="ToWorkstation" />
    
    <!-- 2. 电机系统回零 -->
    <Parallel success_count="4" failure_count="1">
        <MotorHoming intMotorId="1" />
        <MotorHoming intMotorId="2" />
        <MotorHoming intMotorId="3" />
        <MotorHoming intMotorId="4" />
    </Parallel>
    
    <!-- 3. 执行具体作业 -->
    <MotorVelocityControl 
        intMotorId="1" 
        doubleTargetVelocity="20.0"
        doubleDuration="10.0" />
    
    <!-- 4. AGV离开 -->
    <AgvGoPoint strGoPointName="ToSafePoint" />
</Sequence>
```

---

本指南涵盖了RPCS AGV控制节点的主要使用方法。如需更详细的技术信息，请参考相关的API文档和源代码注释。 