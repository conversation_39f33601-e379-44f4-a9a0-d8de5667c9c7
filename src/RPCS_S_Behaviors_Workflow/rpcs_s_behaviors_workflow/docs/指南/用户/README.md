# RPCS 行为树用户指南

欢迎使用RPCS行为树系统！本指南将帮助您快速上手并掌握系统中各个控制节点的使用方法。

## 🚀 快速开始

### 推荐阅读顺序

1. **[快速参考卡片](电机控制节点快速参考.md)** - 先浏览所有节点的配置模板
2. **[电机控制节点用户指南](电机控制节点用户指南.md)** - 深入学习电机控制的详细用法
3. **[AGV控制节点用户指南](AGV控制节点用户指南.md)** - 了解AGV导航控制
4. **[IO板控制节点用户指南](IO板控制节点用户指南.md)** - 学习IO设备控制和传感器检测

### 常见任务快速链接

| 任务 | 推荐文档 | 关键节点 |
|------|----------|----------|
| 🏠 **电机回零初始化** | [电机控制指南](电机控制节点用户指南.md#1-motorhoming---电机回零控制) | `MotorHoming` |
| 📍 **精确位置移动** | [电机控制指南](电机控制节点用户指南.md#2-motorpositioncontrol---电机位置控制) | `MotorPositionControl` |
| 🔄 **连续运动控制** | [电机控制指南](电机控制节点用户指南.md#3-motorvelocitycontrol---电机速度控制) | `MotorVelocityControl` |
| 💪 **力控制应用** | [电机控制指南](电机控制节点用户指南.md#4-motortorquecontrol---电机转矩控制) | `MotorTorqueControl` |
| 🚗 **AGV导航移动** | [AGV控制指南](AGV控制节点用户指南.md#1-agvgopoint---agv移动控制) | `AgvGoPoint` |
| 💡 **指示灯控制** | [IO控制指南](IO板控制节点用户指南.md#1-dosetiooutput---数字输出控制) | `DoSetIoOutput` |
| 📡 **传感器检测** | [IO控制指南](IO板控制节点用户指南.md#2-digetiostatus---数字输入检测) | `DiGetIoStatus` |
| ⚙️ **多电机协同** | [快速参考](电机控制节点快速参考.md#电机并行回零模板) | `Parallel` + 多个电机节点 |
| 🤝 **AGV与电机协作** | [快速参考](电机控制节点快速参考.md#agv与电机协作模板) | `AgvGoPoint` + 电机节点 |
| 🛡️ **安全检查流程** | [IO控制指南](IO板控制节点用户指南.md#安全检查流程) | `DiGetIoStatus` + 安全逻辑 |

## 📚 详细文档

### 控制节点用户指南

| 文档 | 内容概述 | 适用场景 |
|------|----------|----------|
| **[电机控制节点用户指南](电机控制节点用户指南.md)** | 4个电机控制节点的详细说明、参数配置、应用示例 | 精密运动控制、自动化加工 |
| **[AGV控制节点用户指南](AGV控制节点用户指南.md)** | AGV移动控制、导航功能、多AGV协作 | 物料运输、仓储自动化 |
| **[IO板控制节点用户指南](IO板控制节点用户指南.md)** | 数字输入输出控制、传感器检测、设备状态管理 | 工业IO控制、安全检测 |
| **[控制节点快速参考](电机控制节点快速参考.md)** | 所有节点的配置模板、常用参数、组合应用 | 快速查阅、配置模板 |

### 系统架构文档

| 文档类型 | 位置 | 说明 |
|----------|------|------|
| **设计规范** | `docs/设计/` | 节点设计规范、编程规范 |
| **API文档** | `docs/接口/` | ROS2接口、服务定义 |
| **配置示例** | `config/process_trees/` | 实际工艺流程XML文件 |

## 🛠️ 配置参考

### 参考配置文件

| 配置文件 | 功能描述 | 包含节点 |
|----------|----------|----------|
| `Robot1_test_motor_homing.xml` | 电机回零测试流程 | MotorHoming, MotorVelocityControl |
| `io_control_example.xml` | IO控制示例流程 | DoSetIoOutput, DiGetIoStatus |
| `Robot1_complete_workflow.xml` | 完整工作流程示例 | 全部控制节点组合应用 |

### 核心技术特性

- **🔧 统一接口设计**: 所有控制节点遵循一致的参数命名和端口设计
- **🌐 多设备支持**: 通过命名空间和设备ID支持多机器人系统
- **⏱️ 超时控制**: 所有节点支持自定义超时时间
- **🔄 并行执行**: 支持多设备并行控制，提高执行效率
- **🛡️ 错误处理**: 完善的错误检测和恢复机制
- **📊 实时反馈**: 提供详细的执行状态和进度信息

## 🚨 技术支持

### 常见问题排查

1. **电机控制问题** → 查看 [电机控制指南 - 调试和故障排除](电机控制节点用户指南.md#6-调试和故障排除)
2. **AGV通信问题** → 参考 [AGV控制指南 - 故障排除](AGV控制节点用户指南.md#5-调试和故障排除)
3. **IO设备问题** → 查阅 [IO控制指南 - 调试和故障排除](IO板控制节点用户指南.md#5-调试和故障排除)
4. **配置参数问题** → 使用 [快速参考](电机控制节点快速参考.md) 检查参数格式

### 学习资源

- **示例代码**: `config/process_trees/` 目录下的XML配置文件
- **接口文档**: `src/RPCS_M_Controllers_interfaces/` 目录下的接口定义
- **源码参考**: `rpcs_s_behaviors_workflow/plugins/` 目录下的节点实现

---

**版本**: v1.0.0  
**更新日期**: 2024年12月  
**维护团队**: RPCS开发团队

> 💡 **提示**: 建议先阅读快速参考了解所有节点，再根据具体需求深入学习对应的详细指南。 