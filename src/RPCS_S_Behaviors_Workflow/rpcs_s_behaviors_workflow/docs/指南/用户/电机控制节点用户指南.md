# RPCS 电机控制节点用户指南

## 概述

本指南详细介绍了RPCS行为树系统中四个主要电机控制节点的使用方法，包括参数配置、使用示例和最佳实践。这些节点基于ROS2 Action接口，提供了异步、可取消的电机控制功能。

### 支持的电机控制节点

1. **MotorHoming** - 电机回零控制
2. **MotorPositionControl** - 电机位置控制  
3. **MotorVelocityControl** - 电机速度控制
4. **MotorTorqueControl** - 电机转矩控制

---

## 1. MotorHoming - 电机回零控制

### 功能描述

MotorHoming节点用于执行电机回零操作，建立位置基准。支持多种回零方法，可配置搜索速度、偏移量等参数。

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `intMotorId` | int | 电机ID编号 | `1`, `2`, `3` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strDeviceId` | string | "Robot1" | 设备ID | "Robot1", "Station_A" |
| `intHomingMethod` | int | 17 | 寻找原点方法(1-35) | 17, 1, 7 |
| `floatSpeedSwitch` | float | 20.0 | 开关搜索速度(rpm) | 10.0-50.0 |
| `floatSpeedZero` | float | 20.0 | 零点搜索速度(rpm) | 5.0-20.0 |
| `intHomeOffset` | int | 0 | 原点偏移量(mm) | -100 ~ 100 |
| `intPositionWindow` | int | 100 | 位置窗口(inc) | 10-200 |
| `intPositionWindowTime` | int | 1000 | 位置窗口时间(ms) | 100-2000 |
| `doubleTimeout` | double | 30.0 | 超时时间(s) | 30.0-120.0 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 回零是否成功 |
| `floatFinalPosition` | float | 最终位置(mm) |
| `strOutputMessage` | string | 结果消息 |
| `intCurrentStatus` | int | 当前状态字 |
| `strStatusDescription` | string | 状态描述 |
| `floatCurrentPosition` | float | 当前位置(mm) |

### 使用示例

#### 基本回零操作

```xml
<MotorHoming 
    strDeviceId="Robot1"
    intMotorId="1"
    intHomingMethod="17"
    floatSpeedSwitch="20.0"
    floatSpeedZero="10.0"
    doubleTimeout="60.0"
    boolOutputSuccess="{motor1_homing_success}"
    floatFinalPosition="{motor1_final_position}"
    strOutputMessage="{motor1_message}" />
```

#### 多电机并行回零

```xml
<Parallel success_count="4" failure_count="1" name="并行回零操作">
    <!-- 电机1回零 -->
    <MotorHoming 
        strDeviceId="Robot1"
        intMotorId="1"
        intHomingMethod="17"
        floatSpeedSwitch="20.0"
        floatSpeedZero="20.0"
        boolOutputSuccess="{motor1_homing_success}"
        floatFinalPosition="{motor1_final_position}"
        strOutputMessage="{motor1_message}" />
        
    <!-- 电机2回零 -->
    <MotorHoming 
        strDeviceId="Robot1"
        intMotorId="2"
        intHomingMethod="17"
        floatSpeedSwitch="20.0"
        floatSpeedZero="20.0"
        boolOutputSuccess="{motor2_homing_success}"
        floatFinalPosition="{motor2_final_position}"
        strOutputMessage="{motor2_message}" />
        
    <!-- 更多电机... -->
</Parallel>
```

### 状态监控

回零过程中可以通过以下输出端口监控状态：

- **intCurrentStatus**: 数值状态码
  - 0: 等待开始
  - 1: 正在搜索限位开关
  - 2: 正在搜索零点信号
  - 3: 正在移动到原点偏移位置
  - 4: 回零完成
  - -1~-4: 各种失败状态

### 常见问题

1. **回零超时**: 增加`doubleTimeout`值或检查机械限位
2. **搜索速度过快**: 降低`floatSpeedSwitch`值
3. **精度不足**: 调整`intPositionWindow`参数

---

## 2. MotorPositionControl - 电机位置控制

### 功能描述

MotorPositionControl节点用于精确的位置运动控制，支持绝对位置和相对位置两种模式，可配置运动参数如速度、加减速度等。

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `intMotorId` | int | 电机ID编号 | `1`, `2`, `3` |
| `doubleTargetPosition` | double | 目标位置(mm) | `100.0`, `-50.5` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strDeviceId` | string | "Robot1" | 设备ID | "Robot1", "Station_A" |
| `boolAbsolutePosition` | bool | true | 位置模式：true=绝对，false=相对 | true(推荐) |
| `doubleMaxVelocity` | double | 50.0 | 最大速度(mm/s) | 10.0-100.0 |
| `doubleAcceleration` | double | 100.0 | 加速度(mm/s²) | 50.0-500.0 |
| `doubleDeceleration` | double | 100.0 | 减速度(mm/s²) | 50.0-500.0 |
| `doubleDwellTime` | double | 1.0 | 到达后停留时间(s) | 0.5-5.0 |
| `doubleTimeout` | double | 15.0 | 超时时间(s) | 10.0-60.0 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 操作是否成功 |
| `doubleFinalPosition` | double | 最终位置(mm) |
| `doublePositionError` | double | 位置误差(mm) |
| `strOutputMessage` | string | 结果消息 |
| `intErrorCode` | int | 错误代码 |
| `doubleCurrentPosition` | double | 当前位置(mm) |
| `doubleProgress` | double | 完成进度(0.0-1.0) |

### 使用示例

#### 基本位置控制

```xml
<MotorPositionControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetPosition="100.0"
    boolAbsolutePosition="true"
    doubleMaxVelocity="50.0"
    doubleAcceleration="200.0"
    doubleDeceleration="200.0"
    doubleDwellTime="1.0"
    boolOutputSuccess="{position_success}"
    doubleFinalPosition="{final_position}"
    strOutputMessage="{position_message}" />
```

#### 相对位置移动

```xml
<MotorPositionControl 
    strDeviceId="Robot1"
    intMotorId="2"
    doubleTargetPosition="25.0"
    boolAbsolutePosition="false"
    doubleMaxVelocity="30.0"
    boolOutputSuccess="{relative_move_success}"
    doubleFinalPosition="{new_position}" />
```

#### 多轴协调运动

```xml
<Parallel success_count="3" failure_count="1" name="多轴协调运动">
    <MotorPositionControl 
        intMotorId="1"
        doubleTargetPosition="100.0"
        doubleMaxVelocity="50.0" />
    <MotorPositionControl 
        intMotorId="2"
        doubleTargetPosition="200.0"
        doubleMaxVelocity="40.0" />
    <MotorPositionControl 
        intMotorId="3"
        doubleTargetPosition="150.0"
        doubleMaxVelocity="60.0" />
</Parallel>
```

### 运动模式说明

- **绝对位置模式** (`boolAbsolutePosition="true"`): 移动到指定的绝对坐标位置
- **相对位置模式** (`boolAbsolutePosition="false"`): 从当前位置移动指定的距离

### 最佳实践

1. **速度设置**: 根据负载和精度要求调整`doubleMaxVelocity`
2. **加减速度**: 平滑运动需要适中的加减速度值
3. **停留时间**: 精密应用中设置适当的`doubleDwellTime`确保稳定

---

## 3. MotorVelocityControl - 电机速度控制

### 功能描述

MotorVelocityControl节点用于控制电机以指定速度运行，支持时间限制、电流限制和位置限制，适用于连续运动控制。

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `intMotorId` | int | 电机ID编号 | `1`, `2`, `3` |
| `doubleTargetVelocity` | double | 目标速度(rpm) | `20.0`, `-30.0` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strDeviceId` | string | "Robot1" | 设备ID | "Robot1", "Station_A" |
| `doubleAcceleration` | double | 100.0 | 加速度(rps/s) | 10.0-100.0 |
| `doubleDeceleration` | double | 100.0 | 减速度(rps/s) | 10.0-100.0 |
| `doubleDuration` | double | 0.0 | 运行时间(s)，0表示持续运行 | 5.0-60.0 |
| `doubleTargetCurrentLimit` | double | 20.0 | 目标电流限制(A) | 3.0-25.0 |
| `boolUsePositionLimits` | bool | false | 是否使用位置限制 | false(推荐) |
| `doubleMinPosition` | double | -1000.0 | 最小位置限制(mm) | 根据实际设置 |
| `doubleMaxPosition` | double | 1000.0 | 最大位置限制(mm) | 根据实际设置 |
| `doubleTimeout` | double | 10.0 | 超时时间(s) | 15.0-120.0 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 操作是否成功 |
| `doubleFinalVelocity` | double | 最终速度(rpm) |
| `doubleFinalPosition` | double | 最终位置(mm) |
| `strOutputMessage` | string | 结果消息 |
| `intErrorCode` | int | 错误代码 |
| `doubleCurrentVelocity` | double | 当前速度(rpm) |
| `doubleElapsedTime` | double | 已运行时间(s) |

### 使用示例

#### 基本速度控制

```xml
<MotorVelocityControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetVelocity="20.0"
    doubleAcceleration="50.0"
    doubleDeceleration="50.0"
    doubleDuration="10.0"
    doubleTargetCurrentLimit="4.0"
    boolOutputSuccess="{velocity_success}"
    doubleFinalVelocity="{final_velocity}"
    strOutputMessage="{velocity_message}" />
```

#### 多电机同步速度控制

```xml
<Parallel success_count="4" failure_count="1" name="多电机同步运行">
    <MotorVelocityControl 
        intMotorId="1"
        doubleTargetVelocity="20.0"
        doubleDuration="8.0"
        doubleTargetCurrentLimit="4.0" />
    <MotorVelocityControl 
        intMotorId="2"
        doubleTargetVelocity="20.0"
        doubleDuration="8.0"
        doubleTargetCurrentLimit="4.0" />
    <MotorVelocityControl 
        intMotorId="3"
        doubleTargetVelocity="20.0"
        doubleDuration="8.0"
        doubleTargetCurrentLimit="4.0" />
    <MotorVelocityControl 
        intMotorId="4"
        doubleTargetVelocity="20.0"
        doubleDuration="8.0"
        doubleTargetCurrentLimit="4.0" />
</Parallel>
```

#### 持续运行控制

```xml
<!-- 持续运行，直到被其他条件终止 -->
<MotorVelocityControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetVelocity="15.0"
    doubleDuration="0.0"
    doubleTargetCurrentLimit="5.0"
    boolUsePositionLimits="true"
    doubleMinPosition="-500.0"
    doubleMaxPosition="500.0" />
```

### 重要技术说明

#### 电流限制参数

`doubleTargetCurrentLimit`参数将通过转换系数42.67转换为CANopen协议值：
- 输入4.0A → CANopen值: 4.0 × 42.67 = 170.68 DEC
- 推荐范围：3.0A - 25.0A（根据电机规格）

#### 运行模式

- **定时运行**: 设置`doubleDuration`为正值，运行指定时间后自动停止
- **持续运行**: 设置`doubleDuration`为0.0，持续运行直到手动取消或位置限制

### 安全注意事项

1. **电流限制**: 合理设置`doubleTargetCurrentLimit`防止电机过载
2. **位置限制**: 长时间运行时建议启用位置限制
3. **超时设置**: 设置合适的超时时间避免无限等待

---

## 4. MotorTorqueControl - 电机转矩控制

### 功能描述

MotorTorqueControl节点用于控制电机输出指定的转矩，适用于力控制应用，如恒力加工、压力控制等场景。

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `intMotorId` | int | 电机ID编号 | `1`, `2`, `3` |
| `doubleTargetTorque` | double | 目标转矩(Nm) | `0.5`, `1.2` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strDeviceId` | string | "Robot1" | 设备ID | "Robot1", "Station_A" |
| `doubleVelocityLimit` | double | 30.0 | 最大速度限制(mm/s) | 10.0-50.0 |
| `doubleTorqueSlope` | double | 1.0 | 转矩变化斜率(Nm/s) | 0.5-5.0 |
| `doubleDuration` | double | 0.0 | 运行时间(s)，0表示持续运行 | 5.0-30.0 |
| `boolUsePositionLimits` | bool | false | 是否使用位置限制 | false(推荐) |
| `doubleMinPosition` | double | -1000.0 | 最小位置限制(mm) | 根据实际设置 |
| `doubleMaxPosition` | double | 1000.0 | 最大位置限制(mm) | 根据实际设置 |
| `doubleTimeout` | double | 10.0 | 超时时间(s) | 15.0-60.0 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 操作是否成功 |
| `doubleFinalTorque` | double | 最终转矩(Nm) |
| `doubleFinalVelocity` | double | 最终速度(mm/s) |
| `doubleFinalPosition` | double | 最终位置(mm) |
| `strOutputMessage` | string | 结果消息 |
| `intErrorCode` | int | 错误代码 |
| `doubleCurrentTorque` | double | 当前转矩(Nm) |
| `doubleElapsedTime` | double | 已运行时间(s) |

### 使用示例

#### 基本转矩控制

```xml
<MotorTorqueControl 
    strDeviceId="Robot1"
    intMotorId="1"
    doubleTargetTorque="0.8"
    doubleVelocityLimit="20.0"
    doubleTorqueSlope="2.0"
    doubleDuration="10.0"
    boolUsePositionLimits="true"
    doubleMinPosition="-100.0"
    doubleMaxPosition="100.0"
    boolOutputSuccess="{torque_success}"
    doubleFinalTorque="{final_torque}"
    strOutputMessage="{torque_message}" />
```

#### 恒力压制操作

```xml
<Sequence name="恒力压制">
    <!-- 先移动到起始位置 -->
    <MotorPositionControl 
        intMotorId="1"
        doubleTargetPosition="0.0"
        doubleMaxVelocity="30.0" />
        
    <!-- 执行恒力压制 -->
    <MotorTorqueControl 
        intMotorId="1"
        doubleTargetTorque="1.5"
        doubleVelocityLimit="10.0"
        doubleDuration="15.0"
        boolUsePositionLimits="true"
        doubleMaxPosition="50.0"
        boolOutputSuccess="{press_success}" />
        
    <!-- 返回原位 -->
    <MotorPositionControl 
        intMotorId="1"
        doubleTargetPosition="0.0"
        doubleMaxVelocity="50.0" />
</Sequence>
```

#### 多电机协调转矩控制

```xml
<Parallel success_count="2" failure_count="1" name="双电机协调转矩">
    <MotorTorqueControl 
        intMotorId="1"
        doubleTargetTorque="1.0"
        doubleDuration="8.0"
        doubleVelocityLimit="15.0" />
    <MotorTorqueControl 
        intMotorId="2"
        doubleTargetTorque="1.0"
        doubleDuration="8.0"
        doubleVelocityLimit="15.0" />
</Parallel>
```

### 应用场景

1. **恒力加工**: 维持恒定的加工力
2. **压力控制**: 精确控制压制力度
3. **摩擦检测**: 通过转矩反馈检测摩擦状态
4. **负载测试**: 测试机械系统的承载能力

### 安全考虑

1. **速度限制**: 必须设置`doubleVelocityLimit`防止失控
2. **位置限制**: 强烈建议启用位置限制
3. **转矩斜率**: 适当的`doubleTorqueSlope`确保平滑变化

---

## 通用使用指南

### 设备ID配置

所有节点都支持`strDeviceId`参数来区分不同的设备：

```xml
<!-- Robot1设备 -->
<MotorHoming strDeviceId="Robot1" intMotorId="1" />

<!-- Station_A设备 -->
<MotorHoming strDeviceId="Station_A" intMotorId="1" />

<!-- 默认设备（兼容模式） -->
<MotorHoming intMotorId="1" />
```

### 错误处理模式

#### 基本错误检查

```xml
<Sequence name="带错误处理的电机控制">
    <MotorHoming 
        intMotorId="1"
        boolOutputSuccess="{homing_success}"
        strOutputMessage="{homing_message}" />
    
    <Fallback>
        <!-- 检查回零是否成功 -->
        <Script code="homing_success == true" />
        
        <!-- 失败处理 -->
        <Sequence>
            <LogMessage strMessage="回零失败: {homing_message}" />
            <SetBlackboard strKey="error_occurred" strValue="true" />
        </Sequence>
    </Fallback>
</Sequence>
```

#### 重试机制

```xml
<Retry num_attempts="3" name="重试回零">
    <MotorHoming 
        intMotorId="1"
        boolOutputSuccess="{homing_success}" />
</Retry>
```

### 并行控制模式

#### 全部成功模式

```xml
<Parallel success_count="4" failure_count="1" name="全部成功">
    <!-- 要求所有4个电机都成功 -->
</Parallel>
```

#### 部分成功模式

```xml
<Parallel success_count="3" failure_count="2" name="部分成功">
    <!-- 至少3个成功，最多允许1个失败 -->
</Parallel>
```

### 状态监控和反馈

#### 实时状态监控

```xml
<Sequence name="带状态监控的控制">
    <MotorVelocityControl 
        intMotorId="1"
        doubleTargetVelocity="20.0"
        doubleCurrentVelocity="{current_speed}"
        doubleElapsedTime="{elapsed}" />
    
    <!-- 监控运行状态 -->
    <PubProcessFeedback 
        strMessage="电机运行中，当前速度: {current_speed} rpm，已运行: {elapsed} 秒"
        doubleProgress="50.0" />
</Sequence>
```

### 性能优化建议

1. **并行执行**: 多电机操作使用Parallel节点提高效率
2. **合理超时**: 根据实际操作时间设置超时值
3. **参数调优**: 根据负载特性调整运动参数
4. **错误恢复**: 实现完善的错误处理和恢复机制

### 调试技巧

1. **日志输出**: 使用LogMessage节点记录关键状态
2. **参数验证**: 在关键操作前验证参数范围
3. **分步测试**: 复杂流程分步骤测试验证
4. **状态追踪**: 利用输出端口追踪执行状态

---

## 故障排除

### 常见问题及解决方案

#### 1. Action服务不可用

**现象**: 节点返回"Action服务不可用"错误

**解决方案**:
- 检查电机控制器节点是否运行
- 确认设备ID和电机ID配置正确
- 验证ROS2网络连接

#### 2. 电机响应超时

**现象**: 节点执行超时

**解决方案**:
- 增加`doubleTimeout`参数值
- 检查电机硬件连接
- 确认电机使能状态

#### 3. 位置精度不足

**现象**: 位置控制达不到预期精度

**解决方案**:
- 调整位置窗口参数
- 优化运动参数（速度、加减速度）
- 检查机械传动精度

#### 4. 速度控制不稳定

**现象**: 速度波动较大

**解决方案**:
- 调整加减速度参数
- 检查负载匹配
- 优化PID参数（底层驱动器）

### 参数调试建议

| 控制类型 | 关键参数 | 调试方向 |
|----------|----------|----------|
| 回零控制 | `floatSpeedSwitch`, `floatSpeedZero` | 速度过快时降低，超时时增加 |
| 位置控制 | `doubleMaxVelocity`, `doubleAcceleration` | 根据负载和精度要求平衡 |
| 速度控制 | `doubleAcceleration`, `doubleTargetCurrentLimit` | 平滑性和响应性平衡 |
| 转矩控制 | `doubleVelocityLimit`, `doubleTorqueSlope` | 安全性和响应性平衡 |

---

本指南涵盖了RPCS电机控制节点的主要使用方法。如需更详细的技术信息，请参考相关的API文档和源代码注释。 