## 通用控制节点

### Wait - 等待节点

#### 功能描述
提供简单的延时功能，可以使行为树执行暂停指定的时间。

#### 参数

| 参数名 | 类型 | 必需 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `intMsec` | int | 是 | 等待时间(毫秒) | 1000 |

#### 使用示例

```xml
<!-- 等待1秒 -->
<Wait intMsec="1000" />

<!-- 等待3.5秒 -->
<Wait intMsec="3500" />
```

#### 应用场景

1. **操作间隔**：在两个操作之间添加延时
2. **系统稳定**：等待系统状态稳定后再执行下一步操作
3. **传感器响应**：等待传感器响应或状态变化
4. **动作完成**：等待机械动作完成后再进行下一步

#### 注意事项

- 等待时间单位为毫秒
- 等待过程中会阻塞行为树的执行
- 对于需要长时间等待的场景，建议使用异步节点或状态机设计 