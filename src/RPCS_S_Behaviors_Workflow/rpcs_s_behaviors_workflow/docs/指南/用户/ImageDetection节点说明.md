# ImageDetection 行为树节点说明

## 概述

`ImageDetection`节点是一个用于视觉识别的行为树节点，通过调用视觉算法服务获取目标物体的位置偏差值。该节点主要用于机械臂归零识别、物体定位等场景。

## 功能特性

- **多种识别类型**：支持ROBOT_ALIGN（机械臂归零识别）等多种识别模式
- **灵活的相机配置**：支持指定相机IP或相机组名称
- **异步处理**：使用异步服务调用，不阻塞行为树执行
- **超时控制**：可配置超时时间，防止长时间等待
- **详细输出**：提供偏差坐标、错误信息、结果摘要等多维度输出
- **多机器人支持**：通过命名空间支持多机器人场景

## 端口定义

### 输入端口

| 端口名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `strDetectType` | string | "ROBOT_ALIGN" | 识别类型，ROBOT_ALIGN表示机械臂归零识别 |
| `strCameraIp` | string | "" | 相机IP地址（可选，优先级最高） |
| `strCameraGroup` | string | "" | 相机组名称（可选） |
| `strNamespace` | string | "/Robot1" | 节点命名空间，用于多机器人场景 |
| `intTimeoutMs` | int | 10000 | 超时时间（毫秒） |

### 输出端口

| 端口名 | 类型 | 说明 |
|--------|------|------|
| `boolOutputSuccess` | bool | 操作是否成功 |
| `strOutputErrorCode` | string | 错误代码 |
| `strOutputMessage` | string | 结果消息 |
| `strResultCameraIp` | string | 结果相机IP地址 |
| `doubleResultX` | double | 检测到的X坐标偏差 |
| `doubleResultY` | double | 检测到的Y坐标偏差 |
| `doubleResultRz` | double | 检测到的旋转角度偏差 |
| `strDetectionSummary` | string | 检测结果摘要 |

## 服务接口

该节点调用的ROS2服务：
- **服务名**：`{namespace}/vision/image_detection`
- **服务类型**：`vir_robot_interfaces/srv/ImageDetection`

### 请求参数
```
string detect_type    # 识别类型
string camera_ip      # 相机IP（可选）
string camera_group   # 相机组名称（可选）
```

### 响应结果
```
vir_robot_interfaces/ImageDetectionResult detect_result  # 检测结果
bool success          # 操作是否成功
string error_code     # 错误代码
string msg           # 错误或成功信息
```

其中`ImageDetectionResult`包含：
```
string camera_ip     # 相机IP地址
float64 x           # X坐标偏差
float64 y           # Y坐标偏差  
float64 rz          # 旋转角度偏差
```

## 使用示例

### 基本使用

```xml
<ImageDetection 
    strDetectType="ROBOT_ALIGN"
    strNamespace="/Robot1"
    intTimeoutMs="5000"
    boolOutputSuccess="{detection_success}"
    doubleResultX="{offset_x}"
    doubleResultY="{offset_y}"
    doubleResultRz="{offset_rz}"
    strDetectionSummary="{summary}" />
```

### 指定相机IP

```xml
<ImageDetection 
    strDetectType="ROBOT_ALIGN"
    strCameraIp="*************"
    strNamespace="/Robot1"
    intTimeoutMs="8000"
    boolOutputSuccess="{detection_success}"
    strDetectionSummary="{summary}" />
```

### 使用相机组

```xml
<ImageDetection 
    strDetectType="ROBOT_ALIGN"
    strCameraGroup="arm_cameras"
    strNamespace="/Robot1"
    intTimeoutMs="6000"
    boolOutputSuccess="{detection_success}"
    strDetectionSummary="{summary}" />
```

### 与机械臂控制结合

```xml
<Sequence name="视觉引导的机械臂控制">
    <!-- 1. 进行视觉识别 -->
    <ImageDetection 
        strDetectType="ROBOT_ALIGN"
        strNamespace="/Robot1"
        intTimeoutMs="5000"
        boolOutputSuccess="{detection_success}"
        doubleResultX="{offset_x}"
        doubleResultY="{offset_y}"
        doubleResultRz="{offset_rz}" />
    
    <!-- 2. 检查识别结果 -->
    <Fallback>
        <!-- 识别成功：应用补偿 -->
        <Sequence>
            <Script code="detection_success == true" />
            
            <!-- 使用偏差值进行机械臂控制 -->
            <RobotArmControl 
                strDeviceName="/robot1"
                intProjectId="100"
                intSpeedMultiplier="50"
                floatPositionX="{offset_x}"
                floatPositionY="{offset_y}"
                floatRotationRz="{offset_rz}"
                intTimeoutMs="30000"
                boolOutputSuccess="{arm_success}" />
        </Sequence>
        
        <!-- 识别失败：使用默认位置 -->
        <Sequence>
            <PubPrintMessage strTopicName="识别失败，使用默认位置" />
            <RobotArmControl 
                strDeviceName="/robot1"
                intProjectId="101"
                floatPositionX="0.0"
                floatPositionY="0.0"
                floatRotationRz="0.0" />
        </Sequence>
    </Fallback>
</Sequence>
```

## 错误处理

节点提供详细的错误处理和状态反馈：

### 常见错误代码

| 错误代码 | 说明 | 处理建议 |
|----------|------|----------|
| `SERVICE_UNAVAILABLE` | 视觉服务不可用 | 检查视觉服务是否启动 |
| `TIMEOUT` | 请求超时 | 增加超时时间或检查网络连接 |
| `REQUEST_FAILED` | 请求发送失败 | 检查服务名称和参数 |
| `RESPONSE_ERROR` | 响应处理错误 | 检查服务接口兼容性 |
| `HALTED` | 操作被中断 | 正常中断，无需处理 |

### 错误处理示例

```xml
<Sequence name="带错误处理的视觉识别">
    <ImageDetection 
        strDetectType="ROBOT_ALIGN"
        strNamespace="/Robot1"
        boolOutputSuccess="{success}"
        strOutputErrorCode="{error_code}"
        strOutputMessage="{message}" />
    
    <Fallback>
        <!-- 成功分支 -->
        <Sequence>
            <Script code="success == true" />
            <PubPrintMessage strTopicName="识别成功" />
        </Sequence>
        
        <!-- 错误处理分支 -->
        <Sequence>
            <PubPrintMessage strTopicName="识别失败: {error_code} - {message}" />
            
            <!-- 根据错误类型进行不同处理 -->
            <Fallback>
                <!-- 服务不可用：等待重试 -->
                <Sequence>
                    <Script code="error_code == 'SERVICE_UNAVAILABLE'" />
                    <Wait intMsec="2000" />
                    <PubPrintMessage strTopicName="等待服务恢复..." />
                </Sequence>
                
                <!-- 超时：重新尝试 -->
                <Sequence>
                    <Script code="error_code == 'TIMEOUT'" />
                    <PubPrintMessage strTopicName="超时，准备重试..." />
                </Sequence>
                
                <!-- 其他错误：记录日志 -->
                <PubPrintMessage strTopicName="未知错误: {error_code}" />
            </Fallback>
        </Sequence>
    </Fallback>
</Sequence>
```

## 最佳实践

### 1. 相机配置优先级

按以下优先级配置相机：
1. **相机IP** (`strCameraIp`)：优先级最高，直接指定具体相机
2. **相机组** (`strCameraGroup`)：指定相机组，由系统选择最佳相机
3. **默认相机**：不指定时使用系统默认相机

### 2. 超时时间设置

- **快速识别**：3-5秒（简单场景）
- **复杂识别**：8-15秒（复杂场景或网络较慢）
- **批量处理**：20-30秒（多目标识别）

### 3. 结果验证

```xml
<!-- 检查识别精度 -->
<Sequence name="结果精度验证">
    <ImageDetection strDetectType="ROBOT_ALIGN" 
                   doubleResultX="{x}" 
                   doubleResultY="{y}" 
                   doubleResultRz="{rz}" />
    
    <!-- 验证偏差是否在可接受范围内 -->
    <Script code="abs(x) < 10.0 && abs(y) < 10.0 && abs(rz) < 5.0" />
    
    <PubPrintMessage strTopicName="识别精度验证通过" />
</Sequence>
```

### 4. 多机器人场景

```xml
<!-- Robot1的识别 -->
<ImageDetection strNamespace="/Robot1" strDetectType="ROBOT_ALIGN" />

<!-- Robot2的识别 -->  
<ImageDetection strNamespace="/Robot2" strDetectType="ROBOT_ALIGN" />
```

## 注意事项

1. **服务依赖**：确保视觉识别服务在使用前已启动
2. **网络延迟**：在网络环境较差时适当增加超时时间
3. **资源管理**：避免同时启动过多识别任务
4. **坐标系统**：确保识别结果的坐标系与机械臂坐标系一致
5. **精度要求**：根据实际应用场景设置合适的精度阈值

## 测试建议

1. **单元测试**：测试各种输入参数组合
2. **集成测试**：与机械臂控制节点联合测试
3. **性能测试**：测试不同超时设置下的响应时间
4. **错误测试**：模拟各种错误情况验证错误处理
5. **压力测试**：测试连续大量识别请求的稳定性 