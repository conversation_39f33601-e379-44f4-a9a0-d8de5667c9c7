# RPCS IO板控制节点用户指南

## 概述

本指南详细介绍了RPCS行为树系统中IO板控制节点的使用方法，包含数字输出控制(DO)和数字输入检测(DI)节点的参数配置、使用示例和最佳实践。IO控制节点基于ROS2服务接口，提供了同步的IO设备控制功能。

### 支持的IO控制节点

1. **DoSetIoOutput** - 数字输出控制节点(Action节点)
2. **DiGetIoStatus** - 数字输入检测节点(Condition节点)

---

## 1. DoSetIoOutput - 数字输出控制

### 功能描述

DoSetIoOutput节点用于控制IO板上的数字输出(DO)设备，如灯光、气缸、泵等执行器。该节点通过功能英文命名映射到对应的DO序号，支持开启/关闭操作。

### 技术架构

- **通信方式**: ROS2 Service (`rpcs_s_interfaces_io_board::srv::SetOutput`)
- **服务名称**: `/{namespace}/remote_io/set_output`
- **节点类型**: SyncActionNode (同步执行)
- **超时控制**: 可配置的服务调用超时

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `strFunctionName` | string | IO功能英文命名 | `"RedLight"`, `"VacuumPump1"` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strNamespace` | string | "/remote_io" | IO控制器命名空间 | "/remote_io" |
| `boolOutputState` | bool | true | 输出状态(true=开启, false=关闭) | true/false |
| `intTimeoutMs` | int | 2000 | 服务调用超时时间(毫秒) | 1000-5000 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolSuccess` | bool | 操作是否成功 |
| `strMessage` | string | 操作结果消息 |
| `boolFinalState` | bool | IO的最终状态 |

### 预定义功能映射表

系统预定义了以下DO功能映射：

| 功能名称 | DO序号 | 说明 | 应用场景 |
|----------|--------|------|----------|
| `"StoragePoolCylinder1Action"` | 0 | 料仓气缸1动作 | 料仓控制 |
| `"StoragePoolCylinder1Origin"` | 1 | 料仓气缸1原位 | 料仓复位 |
| `"StoragePoolCylinder2Action"` | 2 | 料仓气缸2动作 | 料仓控制 |
| `"StoragePoolCylinder2Origin"` | 3 | 料仓气缸2原位 | 料仓复位 |
| `"StoragePoolTransferCylinderAction"` | 4 | 传送气缸动作 | 物料传送 |
| `"StoragePoolTransferCylinderOrigin"` | 5 | 传送气缸原位 | 传送复位 |
| `"TransferVacuum"` | 6 | 传送真空 | 真空吸取 |
| `"TransferVacuumSuction"` | 7 | 传送真空吸力 | 真空增强 |
| `"RedLight"` | 8 | 红色指示灯 | 状态指示 |
| `"YellowLight"` | 9 | 黄色指示灯 | 警告指示 |
| `"GreenLight"` | 10 | 绿色指示灯 | 正常指示 |
| `"Buzzer"` | 11 | 蜂鸣器 | 声音报警 |
| `"VacuumPump1"` | 12 | 真空泵1 | 真空系统 |
| `"VacuumPump2"` | 13 | 真空泵2 | 真空系统 |
| `"PressurePump"` | 14 | 压力泵 | 压力系统 |
| `"Undefined15"` | 15 | 未定义输出15 | 备用 |

### 使用示例

#### 基本输出控制

```xml
<!-- 开启红色指示灯 -->
<DoSetIoOutput 
    strFunctionName="RedLight"
    boolOutputState="true"
    boolSuccess="{red_light_success}"
    strMessage="{red_light_message}" />
```

#### 灯光状态控制

```xml
<Sequence name="状态指示灯控制">
    <!-- 关闭所有灯光 -->
    <DoSetIoOutput strFunctionName="RedLight" boolOutputState="false" />
    <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="false" />
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
    
    <!-- 开启绿色指示灯表示正常状态 -->
    <DoSetIoOutput 
        strFunctionName="GreenLight" 
        boolOutputState="true"
        boolSuccess="{green_success}" />
    
    <Script code="green_success == true" />
</Sequence>
```

#### 真空泵控制

```xml
<Sequence name="真空泵控制流程">
    <!-- 启动真空泵1 -->
    <DoSetIoOutput 
        strFunctionName="VacuumPump1"
        boolOutputState="true"
        intTimeoutMs="3000"
        boolSuccess="{pump_start_success}"
        strMessage="{pump_message}" />
    
    <Script code="pump_start_success == true" />
    
    <!-- 等待真空建立 -->
    <Delay delay_msec="2000">
        <AlwaysSuccess/>
    </Delay>
    
    <!-- 启动传送真空 -->
    <DoSetIoOutput 
        strFunctionName="TransferVacuum"
        boolOutputState="true" />
    
    <!-- 工作完成后关闭 -->
    <Delay delay_msec="5000">
        <AlwaysSuccess/>
    </Delay>
    
    <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="false" />
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
</Sequence>
```

#### 报警控制

```xml
<Sequence name="报警控制">
    <!-- 开启红灯和蜂鸣器 -->
    <Parallel success_count="2" failure_count="1">
        <DoSetIoOutput 
            strFunctionName="RedLight" 
            boolOutputState="true" />
        <DoSetIoOutput 
            strFunctionName="Buzzer" 
            boolOutputState="true" />
    </Parallel>
    
    <!-- 报警持续3秒 -->
    <Delay delay_msec="3000">
        <AlwaysSuccess/>
    </Delay>
    
    <!-- 关闭报警 -->
    <Parallel success_count="2" failure_count="1">
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="false" />
    </Parallel>
</Sequence>
```

---

## 2. DiGetIoStatus - 数字输入检测

### 功能描述

DiGetIoStatus节点用于读取IO板上的数字输入(DI)状态，如传感器、开关等检测设备。该节点作为条件节点，根据输入状态返回成功或失败，支持逻辑反转功能。

### 技术架构

- **通信方式**: ROS2 Service (`rpcs_s_interfaces_io_board::srv::ReadRegister`)
- **服务名称**: `/{namespace}/remote_io/read_register`
- **节点类型**: ConditionNode (条件节点)
- **返回逻辑**: 高电平返回SUCCESS，低电平返回FAILURE

### 输入参数

#### 必需参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `strFunctionName` | string | IO功能英文命名 | `"TrayPositionSensor1"` |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `strNamespace` | string | "/remote_io" | IO控制器命名空间 | "/remote_io" |
| `boolInvertLogic` | bool | false | 是否反转逻辑 | false(常用) |
| `intTimeoutMs` | int | 2000 | 服务调用超时时间(毫秒) | 1000-5000 |

### 输出参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `boolCurrentState` | bool | 当前IO状态(true=ON, false=OFF) |
| `strMessage` | string | 操作结果消息 |

### 预定义功能映射表

系统预定义了以下DI功能映射：

| 功能名称 | DI序号 | 说明 | 应用场景 |
|----------|--------|------|----------|
| `"TrayPositionSensor1"` | 0 | 料盘位置传感器1 | 物料检测 |
| `"Undefined1"` | 1 | 未定义输入1 | 备用 |
| `"Undefined2"` | 2 | 未定义输入2 | 备用 |
| `"Undefined3"` | 3 | 未定义输入3 | 备用 |
| `"TrayPositionSensor1_Duplicate"` | 4 | 料盘位置传感器1副本 | 备份检测 |
| `"TrayPositionSensor2"` | 5 | 料盘位置传感器2 | 物料检测 |
| `"TrayPositionSensor3"` | 6 | 料盘位置传感器3 | 物料检测 |
| `"NegativePressureDisplay4"` | 7 | 负压显示4 | 压力监控 |
| `"Undefined8-15"` | 8-15 | 未定义输入8-15 | 备用 |

### 使用示例

#### 基本输入检测

```xml
<!-- 检查料盘1是否到位 -->
<DiGetIoStatus 
    strFunctionName="TrayPositionSensor1"
    boolCurrentState="{tray1_state}"
    strMessage="{tray1_message}" />
```

#### 物料检测流程

```xml
<Sequence name="物料检测流程">
    <!-- 检查料盘1是否到位 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolCurrentState="{tray1_detected}" />
    
    <!-- 料盘到位后的操作 -->
    <PubPrintMessage strPrintMessage="料盘1已检测到，开始处理..." />
    
    <!-- 启动真空泵 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
    
    <!-- 等待处理完成 -->
    <Delay delay_msec="3000">
        <AlwaysSuccess/>
    </Delay>
    
    <!-- 关闭真空泵 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
</Sequence>
```

#### 多传感器检测

```xml
<Parallel success_count="3" failure_count="1" name="多传感器检测">
    <!-- 检查多个料盘位置 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolCurrentState="{tray1_state}" />
    
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor2"
        boolCurrentState="{tray2_state}" />
    
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor3"
        boolCurrentState="{tray3_state}" />
</Parallel>
```

#### 条件分支控制

```xml
<Fallback name="条件检测分支">
    <!-- 检查料盘1 -->
    <Sequence name="料盘1处理">
        <DiGetIoStatus strFunctionName="TrayPositionSensor1" />
        <PubPrintMessage strPrintMessage="处理料盘1..." />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    </Sequence>
    
    <!-- 检查料盘2 -->
    <Sequence name="料盘2处理">
        <DiGetIoStatus strFunctionName="TrayPositionSensor2" />
        <PubPrintMessage strPrintMessage="处理料盘2..." />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
    </Sequence>
    
    <!-- 无料盘检测到 -->
    <Sequence name="无料盘处理">
        <PubPrintMessage strPrintMessage="未检测到料盘，等待中..." />
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="true" />
    </Sequence>
</Fallback>
```

#### 逻辑反转示例

```xml
<Sequence name="逻辑反转示例">
    <!-- 正常逻辑：传感器有信号时返回成功 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolInvertLogic="false" />
    
    <!-- 反转逻辑：传感器无信号时返回成功 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor2"
        boolInvertLogic="true" />
</Sequence>
```

---

## 3. IO控制组合应用

### 完整的物料处理流程

```xml
<Sequence name="完整物料处理流程">
    <!-- 初始化 -->
    <PubProcessFeedback 
        strMessage="开始物料处理流程"
        doubleProgress="0.0" />
    
    <!-- 等待料盘到位 -->
    <Sequence name="等待料盘">
        <PubProcessFeedback 
            strMessage="等待料盘到位..."
            doubleProgress="10.0" />
        
        <!-- 循环检测直到料盘到位 -->
        <Retry num_attempts="10">
            <Sequence>
                <DiGetIoStatus strFunctionName="TrayPositionSensor1" />
                <Delay delay_msec="1000" />
            </Sequence>
        </Retry>
        
        <PubPrintMessage strPrintMessage="✅ 料盘已到位" />
    </Sequence>
    
    <!-- 启动处理设备 -->
    <Sequence name="启动设备">
        <PubProcessFeedback 
            strMessage="启动处理设备..."
            doubleProgress="30.0" />
        
        <!-- 开启绿灯表示正在处理 -->
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
        
        <!-- 启动真空泵 -->
        <DoSetIoOutput 
            strFunctionName="VacuumPump1" 
            boolOutputState="true"
            boolSuccess="{pump_success}" />
        
        <Script code="pump_success == true" />
        
        <!-- 等待真空建立 -->
        <Delay delay_msec="2000">
            <AlwaysSuccess/>
        </Delay>
        
        <!-- 启动传送真空 -->
        <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="true" />
    </Sequence>
    
    <!-- 执行物料处理 -->
    <Sequence name="物料处理">
        <PubProcessFeedback 
            strMessage="正在处理物料..."
            doubleProgress="60.0" />
        
        <!-- 模拟处理时间 -->
        <Delay delay_msec="5000">
            <AlwaysSuccess/>
        </Delay>
        
        <PubPrintMessage strPrintMessage="🔧 物料处理完成" />
    </Sequence>
    
    <!-- 关闭设备 -->
    <Sequence name="关闭设备">
        <PubProcessFeedback 
            strMessage="关闭处理设备..."
            doubleProgress="80.0" />
        
        <!-- 关闭传送真空 -->
        <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="false" />
        
        <!-- 关闭真空泵 -->
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
        
        <!-- 关闭绿灯 -->
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
    </Sequence>
    
    <!-- 完成 -->
    <PubProcessFeedback 
        strMessage="物料处理流程完成"
        doubleProgress="100.0" />
</Sequence>
```

### 安全检查流程

```xml
<Sequence name="安全检查流程">
    <!-- 检查所有安全条件 -->
    <Sequence name="安全条件检查">
        <PubPrintMessage strPrintMessage="🔍 执行安全检查..." />
        
        <!-- 检查料盘是否正确到位 -->
        <DiGetIoStatus 
            strFunctionName="TrayPositionSensor1"
            boolCurrentState="{tray_ok}" />
        
        <!-- 检查负压是否正常 -->
        <DiGetIoStatus 
            strFunctionName="NegativePressureDisplay4"
            boolCurrentState="{pressure_ok}" />
        
        <Script code="tray_ok == true && pressure_ok == true" />
        
        <PubPrintMessage strPrintMessage="✅ 安全检查通过" />
    </Sequence>
    
    <!-- 安全检查通过后的操作 -->
    <Sequence name="安全确认">
        <!-- 开启绿灯表示安全 -->
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
        
        <!-- 关闭警告灯 -->
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="false" />
        
        <PubPrintMessage strPrintMessage="🛡️ 系统处于安全状态" />
    </Sequence>
</Sequence>
```

### 错误处理和报警

```xml
<Fallback name="错误处理流程">
    <!-- 正常操作流程 -->
    <Sequence name="正常操作">
        <!-- 检查料盘位置 -->
        <DiGetIoStatus 
            strFunctionName="TrayPositionSensor1"
            boolCurrentState="{tray_detected}" />
        
        <!-- 启动真空泵 -->
        <DoSetIoOutput 
            strFunctionName="VacuumPump1" 
            boolOutputState="true"
            boolSuccess="{pump_success}" />
        
        <Script code="tray_detected == true && pump_success == true" />
        
        <!-- 正常指示 -->
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    </Sequence>
    
    <!-- 错误处理 -->
    <Sequence name="错误处理">
        <PubPrintMessage strPrintMessage="⚠️ 检测到错误，启动报警..." />
        
        <!-- 关闭所有设备 -->
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="VacuumPump2" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="false" />
        
        <!-- 启动报警 -->
        <Parallel success_count="3" failure_count="1">
            <DoSetIoOutput strFunctionName="RedLight" boolOutputState="true" />
            <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
            <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="true" />
        </Parallel>
        
        <!-- 报警持续时间 -->
        <Delay delay_msec="5000">
            <AlwaysSuccess/>
        </Delay>
        
        <!-- 关闭蜂鸣器但保持灯光警告 -->
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="false" />
        
        <PubPrintMessage strPrintMessage="🚨 系统已进入安全模式" />
    </Sequence>
</Fallback>
```

### 状态机控制

```xml
<Sequence name="状态机控制">
    <!-- 状态1：待机状态 -->
    <Sequence name="待机状态">
        <PubPrintMessage strPrintMessage="系统待机中..." />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="false" />
        
        <!-- 等待料盘到位信号 -->
        <Retry num_attempts="30">
            <Sequence>
                <DiGetIoStatus strFunctionName="TrayPositionSensor1" />
                <Delay delay_msec="1000" />
            </Sequence>
        </Retry>
    </Sequence>
    
    <!-- 状态2：工作状态 -->
    <Sequence name="工作状态">
        <PubPrintMessage strPrintMessage="进入工作状态..." />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
        
        <!-- 启动工作设备 -->
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
        <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="true" />
        
        <!-- 工作循环 -->
        <Delay delay_msec="8000">
            <AlwaysSuccess/>
        </Delay>
    </Sequence>
    
    <!-- 状态3：完成状态 -->
    <Sequence name="完成状态">
        <PubPrintMessage strPrintMessage="工作完成，正在复位..." />
        
        <!-- 关闭所有设备 -->
        <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="TransferVacuum" boolOutputState="false" />
        
        <!-- 状态指示 -->
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
        
        <Delay delay_msec="2000">
            <AlwaysSuccess/>
        </Delay>
        
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="false" />
        <PubPrintMessage strPrintMessage="✅ 状态机循环完成" />
    </Sequence>
</Sequence>
```

---

## 4. 与其他控制节点的集成

### IO控制与电机协作

```xml
<Sequence name="IO与电机协作">
    <!-- 检查安全条件 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolCurrentState="{safety_ok}" />
    
    <Script code="safety_ok == true" />
    
    <!-- 开启安全指示灯 -->
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    
    <!-- 电机回零 -->
    <MotorHoming 
        intMotorId="1"
        boolOutputSuccess="{motor_homing_ok}" />
    
    <Script code="motor_homing_ok == true" />
    
    <!-- 电机运动完成后关闭指示灯 -->
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
    
    <!-- 启动真空泵进行物料吸取 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
    
    <!-- 电机移动到目标位置 -->
    <MotorPositionControl 
        intMotorId="1"
        doubleTargetPosition="100.0"
        boolOutputSuccess="{motor_move_ok}" />
    
    <!-- 到位后关闭真空泵 -->
    <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
</Sequence>
```

### IO控制与AGV协作

```xml
<Sequence name="IO与AGV协作">
    <!-- AGV移动到工作位置 -->
    <AgvGoPoint 
        strGoPointName="ToWorkstation"
        boolOutputSuccess="{agv_arrive_ok}" />
    
    <Script code="agv_arrive_ok == true" />
    
    <!-- 开启工作指示灯 -->
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
    
    <!-- 检查AGV上的物料 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolCurrentState="{material_detected}" />
    
    <!-- 根据检测结果处理 -->
    <Fallback>
        <!-- 有物料：正常处理流程 -->
        <Sequence name="正常处理">
            <Script code="material_detected == true" />
            <PubPrintMessage strPrintMessage="检测到物料，开始处理..." />
            
            <!-- 启动处理设备 -->
            <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="true" />
            <Delay delay_msec="5000" />
            <DoSetIoOutput strFunctionName="VacuumPump1" boolOutputState="false" />
        </Sequence>
        
        <!-- 无物料：报警处理 -->
        <Sequence name="无物料处理">
            <PubPrintMessage strPrintMessage="未检测到物料，启动报警..." />
            <DoSetIoOutput strFunctionName="RedLight" boolOutputState="true" />
            <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="true" />
            <Delay delay_msec="3000" />
            <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="false" />
        </Sequence>
    </Fallback>
    
    <!-- 处理完成，AGV离开 -->
    <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
    <AgvGoPoint strGoPointName="ToSafePoint" />
</Sequence>
```

---

## 5. 调试和故障排除

### 调试技巧

#### 1. IO状态监控

```xml
<Sequence name="IO状态监控">
    <!-- 读取多个输入状态 -->
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor1"
        boolCurrentState="{sensor1_state}"
        strMessage="{sensor1_msg}" />
    
    <DiGetIoStatus 
        strFunctionName="TrayPositionSensor2"
        boolCurrentState="{sensor2_state}"
        strMessage="{sensor2_msg}" />
    
    <!-- 输出状态信息 -->
    <PubPrintMessage strPrintMessage="传感器状态监控:" />
    <PubPrintMessage strPrintMessage="  传感器1: {sensor1_state} - {sensor1_msg}" />
    <PubPrintMessage strPrintMessage="  传感器2: {sensor2_state} - {sensor2_msg}" />
    
    <!-- 测试输出设备 -->
    <DoSetIoOutput 
        strFunctionName="RedLight"
        boolOutputState="true"
        boolSuccess="{light_success}"
        strMessage="{light_msg}" />
    
    <PubPrintMessage strPrintMessage="红灯控制: {light_success} - {light_msg}" />
</Sequence>
```

#### 2. 系统自检流程

```xml
<Sequence name="IO系统自检">
    <PubPrintMessage strPrintMessage="🔍 开始IO系统自检..." />
    
    <!-- 测试所有指示灯 -->
    <Sequence name="指示灯测试">
        <PubPrintMessage strPrintMessage="测试指示灯..." />
        
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="true" />
        <Delay delay_msec="500" />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="true" />
        <Delay delay_msec="500" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="true" />
        <Delay delay_msec="500" />
        
        <!-- 关闭所有灯 -->
        <DoSetIoOutput strFunctionName="RedLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="YellowLight" boolOutputState="false" />
        <DoSetIoOutput strFunctionName="GreenLight" boolOutputState="false" />
        
        <PubPrintMessage strPrintMessage="✅ 指示灯测试完成" />
    </Sequence>
    
    <!-- 测试蜂鸣器 -->
    <Sequence name="蜂鸣器测试">
        <PubPrintMessage strPrintMessage="测试蜂鸣器..." />
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="true" />
        <Delay delay_msec="1000" />
        <DoSetIoOutput strFunctionName="Buzzer" boolOutputState="false" />
        <PubPrintMessage strPrintMessage="✅ 蜂鸣器测试完成" />
    </Sequence>
    
    <!-- 检查传感器状态 -->
    <Sequence name="传感器检查">
        <PubPrintMessage strPrintMessage="检查传感器状态..." />
        
        <DiGetIoStatus 
            strFunctionName="TrayPositionSensor1"
            boolCurrentState="{sensor1}" />
        <DiGetIoStatus 
            strFunctionName="TrayPositionSensor2"
            boolCurrentState="{sensor2}" />
        
        <PubPrintMessage strPrintMessage="传感器1: {sensor1}, 传感器2: {sensor2}" />
        <PubPrintMessage strPrintMessage="✅ 传感器检查完成" />
    </Sequence>
    
    <PubPrintMessage strPrintMessage="🎉 IO系统自检完成！" />
</Sequence>
```

### 常见问题及解决方案

#### 1. 服务不可用

**现象**: 节点返回"IO控制服务不可用"或"IO读取服务不可用"

**解决方案**:
- 检查IO板控制器节点是否运行
- 确认命名空间配置正确 (`/remote_io`)
- 验证ROS2网络连接
- 检查IO板硬件连接

#### 2. 功能名称映射错误

**现象**: 节点返回"未知的功能名称"

**解决方案**:
- 确认功能名称拼写正确
- 检查预定义映射表中是否包含该功能
- 使用系统中已定义的功能名称
- 联系系统管理员添加新的功能映射

#### 3. IO操作超时

**现象**: 服务调用超时

**解决方案**:
- 增加`intTimeoutMs`参数值
- 检查IO板通信状态
- 确认IO板负载不过重
- 检查网络延迟

#### 4. 输出状态不符合预期

**现象**: 设置输出状态后实际状态与期望不符

**解决方案**:
- 检查硬件连接
- 确认DO序号映射正确
- 检查电源供应
- 验证负载设备功能

#### 5. 输入读取异常

**现象**: 传感器状态读取错误或不稳定

**解决方案**:
- 检查传感器供电
- 确认DI序号映射正确
- 检查信号线连接
- 考虑使用`boolInvertLogic`反转逻辑

### 性能优化建议

1. **批量操作**: 相关IO操作尽量批量执行
2. **超时设置**: 根据实际响应时间设置合理的超时值
3. **错误恢复**: 实现完善的错误处理和重试机制
4. **状态缓存**: 避免频繁读取相同的IO状态
5. **并行控制**: 无关联的IO操作可以并行执行

### 最佳实践

1. **功能命名**: 使用清晰、一致的功能命名规范
2. **状态确认**: 重要操作后确认IO状态
3. **安全第一**: 关键安全相关的IO操作要有冗余检查
4. **日志记录**: 记录重要的IO操作便于调试
5. **定期检查**: 建立IO设备的定期自检机制

---

本指南涵盖了RPCS IO板控制节点的主要使用方法。IO控制是自动化系统的重要组成部分，与电机控制、AGV控制等其他子系统紧密配合，共同构成完整的工业自动化解决方案。如需更详细的技术信息，请参考相关的API文档和源代码注释。 