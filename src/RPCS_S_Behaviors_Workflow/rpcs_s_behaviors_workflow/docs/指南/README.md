# RPCS 工艺动作树系统 - 用户指南

欢迎使用 RPCS 工艺动作树系统！本系统基于 ROS2 和 BehaviorTree.CPP，为工业装配机器人提供灵活的工艺流程控制。

## 🚀 快速导航

### 新手入门
- **[5分钟快速开始](快速开始指南.md)** - 最快上手系统 ⚡
- **[安装部署指南](安装部署指南.md)** - 完整环境搭建
- **[基础操作指南](基础操作指南.md)** - 日常使用方法

### 🔧 编译问题快速解决
- **包名重复错误**: `rm -rf RPCS_S_Behaviors_template`
- **Action接口找不到**: 先编译 `rpcs_s_interfaces_behavior_tree`
- **YAML链接错误**: 检查 `libyaml-cpp-dev` 安装
- **详细解决方案**: 查看 [监控调试指南 - 编译问题调试](监控调试指南.md#编译问题调试)

### 开发配置
- **[配置文件指南](配置文件指南.md)** - 系统配置详解
- **[工艺动作树开发指南](工艺动作树开发指南.md)** - XML文件编写
- **[客户端开发指南](客户端开发指南.md)** - 自定义应用开发

### 运维监控
- **[监控调试指南](监控调试指南.md)** - 故障排除与性能优化
- **[多机器人协同指南](多机器人协同指南.md)** - 多机器人系统管理

## 📚 指南目录

### 🚀 快速开始
- [**快速开始指南**](./快速开始指南.md) - 5分钟上手系统
- [**安装部署指南**](./安装部署指南.md) - 详细的安装和配置步骤

### 🔧 基础使用
- [**基础操作指南**](./基础操作指南.md) - 系统的基本操作方法
- [**配置文件指南**](./配置文件指南.md) - 如何配置工艺动作映射
- [**工艺动作树开发指南**](./工艺动作树开发指南.md) - 创建和编辑工艺动作树

### 🎯 高级功能
- [**多机器人协同指南**](./多机器人协同指南.md) - 多机器人系统的配置和使用
- [**监控调试指南**](./监控调试指南.md) - 系统监控、调试和故障排除
- [**客户端开发指南**](./客户端开发指南.md) - 开发自定义客户端应用

### 🛠️ 开发扩展
- [**插件开发指南**](./插件开发指南.md) - 开发自定义行为树节点
- [**系统集成指南**](./系统集成指南.md) - 与其他系统的集成方法
- [**最佳实践指南**](./最佳实践指南.md) - 系统使用的最佳实践

## 🏗️ 系统架构概览

```
┌─────────────────┐    ROS2 Action    ┌─────────────────┐    YAML Config    ┌─────────────────┐
│   客户端应用     │ ─────────────────→ │ProcessActionServ│ ─────────────────→ │  工艺动作树      │
│                 │                    │     er          │                    │   XML 文件      │
│ • Web界面       │                    │ • 动态加载      │                    │ • 自检流程      │
│ • 命令行工具    │                    │ • 状态管理      │                    │ • 装配流程      │
│ • Python脚本    │                    │ • 错误处理      │                    │ • 检测流程      │
└─────────────────┘                    └─────────────────┘                    └─────────────────┘
                                               │
                                               ▼
                                       ┌─────────────────┐
                                       │ BehaviorTree    │
                                       │   执行引擎      │
                                       │                 │
                                       │ • 节点插件      │
                                       │ • 黑板状态      │
                                       │ • Groot2可视化  │
                                       └─────────────────┘
```

## 🎯 核心概念

### 工艺动作类型 (Process Action Type)
系统中定义的标准化工艺操作类型，如：
- `SelfCheck` - 自检流程
- `PickSendStock` - 拾取送料
- `PlaceProduct` - 产品放置
- `Route` - 路径规划

### 工艺动作树 (Process Action Tree)
使用 XML 格式定义的行为树，描述具体的工艺执行流程。每个工艺动作类型对应一个或多个工艺动作树文件。

### 机器人实例 (Robot Instance)
系统中的独立机器人单元，每个机器人有：
- 唯一的机器人 ID (如 `Robot1`, `Robot2`)
- 独立的 Action 服务端点
- 专用的配置和工艺动作树

## 🚦 系统状态

### 执行状态
- `IDLE` - 空闲状态，等待新任务
- `RUNNING` - 正在执行工艺动作
- `SUCCESS` - 执行成功完成
- `FAILURE` - 执行失败
- `ABORTED` - 执行被中止

### 质量状态
- `OK` - 质量合格
- `NG` - 质量不合格
- `UNKNOWN` - 质量状态未知

## 📋 支持的工艺动作类型

### Robot1 (灯板相关工艺)
- `SelfCheck` - 自检
- `PickSendStock` - 拾取送料
- `StackLightBoards` - 灯板堆叠
- `RemoveReleaseFilm` - 撕离型膜
- `BondCarrier` - 载体贴合

### Robot2 (PCBA相关工艺)
- `SelfCheck` - 自检
- `PickSendStock` - 拾取送料
- `MoveWIP` - 移动半成品
- `PlaceWIP` - 放置半成品
- `PlacePCBA` - PCBA放置
- `DoPCAdapterPaste` - PC适配器贴装
- `OpenFPC` - FPC展开
- `TransferFixture` - 夹具转移
- `PlaceProduct` - 产品放置

### Robot3 (保护壳相关工艺)
- `SelfCheck` - 自检
- `Route` - 路径规划
- `PickSendStock` - 拾取送料
- `ScrewPCBA` - PCBA锁螺丝
- `ConnectFPC` - FPC连接
- `FastenProCase` - 保护壳紧固

## 🔗 相关资源

### 技术文档
- [设计文档](../设计/) - 系统架构和接口设计
- [API参考](../API/) - 详细的API文档

### 工具和库
- [BehaviorTree.CPP](https://www.behaviortree.dev/) - 行为树库
- [Groot2](https://github.com/BehaviorTree/Groot2) - 行为树可视化工具
- [ROS2](https://docs.ros.org/en/humble/) - 机器人操作系统

### 社区支持
- 项目仓库: [GitHub链接]
- 问题反馈: [Issues链接]
- 技术讨论: [论坛链接]

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **查看对应的指南文档** - 大部分问题都能在相关指南中找到答案
2. **检查系统日志** - 使用 `ros2 log` 命令查看详细日志
3. **使用调试工具** - 利用 Groot2 可视化调试行为树
4. **联系技术支持** - 通过指定渠道获取技术支持

## 🔄 版本信息

- **当前版本**: v1.0.0
- **更新日期**: 2024年
- **兼容性**: ROS2 Humble, BehaviorTree.CPP 4.x

---

**开始使用**: 推荐从 [快速开始指南](./快速开始指南.md) 开始您的 RPCS 工艺动作树系统之旅！ 