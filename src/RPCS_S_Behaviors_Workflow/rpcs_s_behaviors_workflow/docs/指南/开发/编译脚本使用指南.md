# RPCS_S_Behaviors_workflow 编译脚本使用指南

## 概述

本项目提供了三个编译脚本来简化ROS2项目的编译和管理过程：

1. **`build_project.sh`** - 功能完整的编译脚本
2. **`quick_build.sh`** - 快速编译脚本  
3. **`clean_build.sh`** - 清理脚本

## 🚀 快速开始

### 准备工作
确保ROS2环境已设置：
```bash
source /opt/ros/humble/setup.bash  # 或其他ROS2版本
```

### 1. 快速编译（推荐日常使用）
```bash
./quick_build.sh
```

这是最简单的编译方式，适合日常开发使用。

### 2. 完整编译
```bash
./build_project.sh
```

功能更全面，包含依赖检查、日志记录等。

### 3. 清理后重新编译
```bash
./clean_build.sh          # 清理
./quick_build.sh          # 重新编译
```

或者使用一步完成：
```bash
./build_project.sh -f     # 完整编译（自动清理+编译）
```

## 📋 脚本详细说明

### 1. build_project.sh - 完整编译脚本

#### 功能特点
- 🔍 自动环境检查（ROS2、工作空间结构）
- 📦 依赖关系检查和安装
- 🛠️ 多种编译模式支持
- 📝 详细日志记录
- 🎯 灵活的参数配置
- 📊 编译报告生成

#### 使用方法
```bash
# 基本用法
./build_project.sh                    # 标准编译

# 常用选项
./build_project.sh -h                 # 显示帮助
./build_project.sh -c                 # 清理编译
./build_project.sh -f                 # 完整编译
./build_project.sh -v                 # 详细输出
./build_project.sh -r                 # 发布模式
./build_project.sh -d                 # 调试模式

# 高级用法
./build_project.sh -p RPCS_S_Behaviors_workflow  # 只编译指定包
./build_project.sh -j 8               # 使用8个并行任务
./build_project.sh --cmake-args="-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
```

#### 参数详解

| 参数 | 长选项 | 说明 |
|------|--------|------|
| `-h` | `--help` | 显示帮助信息 |
| `-c` | `--clean` | 清理编译目录 |
| `-f` | `--full` | 完整编译（清理+编译） |
| `-p <包名>` | `--package <包名>` | 只编译指定包 |
| `-j <数量>` | `--jobs <数量>` | 并行任务数 |
| `-v` | `--verbose` | 详细输出模式 |
| `-r` | `--release` | 发布模式编译 |
| `-d` | `--debug` | 调试模式编译 |
| - | `--cmake-args <参数>` | CMAKE额外参数 |
| - | `--no-deps` | 不检查依赖 |
| - | `--continue-on-error` | 遇错继续 |

#### 输出文件
- **日志文件**: `build_logs/build_YYYYMMDD_HHMMSS.log`
- **编译报告**: `build_logs/build_report_YYYYMMDD_HHMMSS.txt`
- **环境脚本**: `setup_env.sh`

### 2. quick_build.sh - 快速编译脚本

#### 功能特点
- ⚡ 简单快速
- 🎯 专注于日常开发
- 🔧 自动环境设置
- ✅ 即用即编译

#### 使用场景
- 日常代码修改后的快速编译
- 不需要复杂配置的场景
- 熟悉项目结构的开发人员

#### 使用方法
```bash
./quick_build.sh
```

### 3. clean_build.sh - 清理脚本

#### 功能特点
- 🧹 彻底清理编译产物
- 🔄 重置编译环境
- 📁 清理多种目录类型

#### 清理内容
- `build/` - CMake编译目录
- `install/` - 安装目录
- `log/` - colcon日志目录
- `build_logs/` - 自定义日志目录
- `setup_env.sh` - 自动生成的环境脚本

#### 使用方法
```bash
./clean_build.sh
```

## 🛠️ 常见使用场景

### 场景1：日常开发
```bash
# 修改代码后
./quick_build.sh

# 测试
source ./setup_env.sh
./start_process_action_server.sh Robot1
```

### 场景2：遇到编译问题
```bash
# 清理重新编译
./clean_build.sh
./build_project.sh -v    # 详细输出查看问题
```

### 场景3：发布版本
```bash
# 发布模式编译
./build_project.sh -f -r
```

### 场景4：只编译特定包
```bash
# 只编译行为树包
./build_project.sh -p RPCS_S_Behaviors_workflow
```

### 场景5：调试编译问题
```bash
# 调试模式+详细输出
./build_project.sh -d -v --continue-on-error
```

## 📝 日志和报告

### 查看编译日志
```bash
# 最新的编译日志
ls -la build_logs/

# 查看具体日志
cat build_logs/build_20240124_143022.log

# 实时查看正在编译的日志
tail -f build_logs/build_20240124_143022.log
```

### 编译报告内容
- 编译时间和参数
- 编译结果和耗时
- 安装的包列表
- 错误信息（如有）

## 🚨 故障排除

### 1. ROS2环境问题
**错误**: `ROS2环境未设置`
```bash
# 解决方法
source /opt/ros/humble/setup.bash
echo 'source /opt/ros/humble/setup.bash' >> ~/.bashrc
```

### 2. 权限问题
**错误**: `Permission denied`
```bash
# 解决方法
chmod +x *.sh
```

### 3. 依赖问题
**错误**: `依赖安装失败`
```bash
# 手动安装依赖
sudo apt update
rosdep update
rosdep install --from-paths src --ignore-src -r -y
```

### 4. 磁盘空间不足
**错误**: `No space left on device`
```bash
# 清理空间
./clean_build.sh
sudo apt autoremove
sudo apt autoclean
```

### 5. 编译超时或卡住
```bash
# 减少并行任务数
./build_project.sh -j 1

# 或强制停止重新开始
Ctrl+C
./clean_build.sh
./build_project.sh
```

## 🔧 自定义配置

### 修改默认并行任务数
编辑 `build_project.sh`，找到：
```bash
JOBS=$(nproc)
```
改为：
```bash
JOBS=4  # 固定使用4个任务
```

### 添加自定义CMAKE参数
```bash
./build_project.sh --cmake-args="-DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DCMAKE_VERBOSE_MAKEFILE=ON"
```

### 修改日志保存位置
编辑 `build_project.sh`，找到：
```bash
LOG_DIR="$PROJECT_ROOT/build_logs"
```
改为你想要的路径。

## 📚 最佳实践

### 1. 开发流程
```bash
# 1. 修改代码
vim src/RPCS_S_Behaviors_workflow/...

# 2. 快速编译
./quick_build.sh

# 3. 测试
source ./setup_env.sh
python3 enhanced_test_client.py

# 4. 如有问题，查看日志
less build_logs/build_*.log
```

### 2. 版本管理
```bash
# 发布前完整测试
./clean_build.sh
./build_project.sh -f -r
```

### 3. 性能优化
```bash
# 根据CPU核心数调整并行任务
./build_project.sh -j $(nproc)

# 只编译必要的包
./build_project.sh -p RPCS_S_Behaviors_workflow
```

## 🎯 总结

- **日常开发**: 使用 `quick_build.sh`
- **完整测试**: 使用 `build_project.sh -f`
- **遇到问题**: 使用 `build_project.sh -v` 查看详细输出
- **重置环境**: 使用 `clean_build.sh`

编译完成后，不要忘记：
```bash
source ./setup_env.sh  # 设置环境
```

然后就可以运行您的ROS2程序了！🎉 