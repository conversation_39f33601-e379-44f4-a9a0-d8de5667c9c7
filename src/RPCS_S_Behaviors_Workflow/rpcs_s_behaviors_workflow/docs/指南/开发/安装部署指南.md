# 安装部署指南

本指南提供 RPCS 工艺动作树系统的详细安装和部署步骤。

## 📋 系统要求

### 操作系统
- **Ubuntu 22.04 LTS** (推荐)
- **Ubuntu 20.04 LTS** (支持)
- **其他 Linux 发行版** (需要适配)

### 硬件要求
- **CPU**: 4核心以上 (推荐8核心)
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 20GB可用空间
- **网络**: 千兆以太网 (多机器人部署)

## 🔧 依赖软件安装

### 1. ROS2 安装

#### Ubuntu 22.04 (Humble)
```bash
# 添加 ROS2 APT 仓库
sudo apt update && sudo apt install curl gnupg lsb-release
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg

echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(source /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null

# 安装 ROS2 Humble
sudo apt update
sudo apt install ros-humble-desktop
sudo apt install ros-humble-ros-base
sudo apt install ros-dev-tools

# 设置环境变量
echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

#### Ubuntu 20.04 (Galactic/Foxy)
```bash
# 类似步骤，替换为对应版本
sudo apt install ros-galactic-desktop ros-dev-tools
echo "source /opt/ros/galactic/setup.bash" >> ~/.bashrc
```

### 2. BehaviorTree.CPP 安装

#### 方法1：从源码编译 (推荐)
```bash
# 安装依赖
sudo apt install libzmq3-dev libboost-dev

# 克隆源码
cd ~/workspace/src
git clone https://github.com/BehaviorTree/BehaviorTree.CPP.git
cd BehaviorTree.CPP
git checkout v4.6  # 或最新稳定版本

# 编译安装
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
sudo make install
```

#### 方法2：使用包管理器
```bash
# Ubuntu 22.04
sudo apt install ros-humble-behaviortree-cpp-v3

# 注意：可能需要从源码安装最新版本
```

### 3. 其他依赖库

```bash
# YAML 处理库
sudo apt install libyaml-cpp-dev

# Action 支持
sudo apt install ros-humble-rclcpp-action

# 可视化工具 (可选)
sudo apt install ros-humble-rqt*

# 编译工具
sudo apt install build-essential cmake git
```

## 🚀 项目编译

### 1. 创建工作空间

```bash
# 创建 ROS2 工作空间
mkdir -p ~/rpcs_ws/src
cd ~/rpcs_ws
```

### 2. 获取源码

```bash
cd ~/rpcs_ws/src

# 克隆项目 (假设从 Git 仓库)
git clone <repository_url> RPCS_S_Behaviors_workflow

# 或者复制现有源码
cp -r /path/to/RPCS_S_Behaviors_workflow .
```

### 3. 安装依赖

```bash
cd ~/rpcs_ws

# 使用 rosdep 安装依赖
rosdep update
rosdep install --from-paths src --ignore-src -r -y
```

### 4. 编译项目

#### 4.1 编译顺序和依赖

**重要提醒**：必须按照以下顺序编译，确保依赖关系正确：

```bash
cd ~/rpcs_ws

# 第一步：编译接口包
colcon build --packages-select rpcs_s_interfaces_behavior_tree

# 第二步：编译行为树框架
colcon build --packages-select behaviortree_ros2

# 第三步：编译主业务包
colcon build --packages-select RPCS_S_Behaviors_workflow

# 或者一次性编译所有包（推荐）
colcon build --packages-select rpcs_s_interfaces_behavior_tree behaviortree_ros2 RPCS_S_Behaviors_workflow
```

#### 4.2 常见编译问题及解决方案

##### 问题1：包名重复错误
```bash
# 错误信息：Duplicate package names not supported
# 解决方案：删除重复的模板包
rm -rf ~/rpcs_ws/RPCS_S_Behaviors_template
```

##### 问题2：Action接口找不到
```bash
# 错误信息：fatal error: rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction.hpp: No such file or directory
# 解决方案：确保 rpcs_s_interfaces_behavior_tree 包正确配置了 Action 文件

# 检查 CMakeLists.txt 是否包含：
# rosidl_generate_interfaces(${PROJECT_NAME}
#   "action/ExecuteProcessAction.action"
#   DEPENDENCIES
#   action_msgs
#   builtin_interfaces
# )

# 检查 package.xml 是否包含：
# <depend>action_msgs</depend>
# <depend>builtin_interfaces</depend>
```

##### 问题3：YAML-CPP链接错误
```bash
# 错误信息：undefined reference to `YAML::LoadFile`
# 解决方案：确保正确链接 yaml-cpp 库

# 检查 CMakeLists.txt 是否包含：
# find_package(yaml-cpp REQUIRED)
# target_link_libraries(process_action_server yaml-cpp)
```

##### 问题4：空文件编译错误
```bash
# 错误信息：undefined reference to `main`
# 解决方案：注释掉空的可执行文件或添加完整代码

# 在 CMakeLists.txt 中注释：
# # ament_auto_add_executable(process_action_client_example
# #     src/process_action_client_example.cpp
# # )
```

#### 4.3 编译优化选项

```bash
# 编译优化版本
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release

# 并行编译 (加速)
colcon build --parallel-workers $(nproc)

# 详细编译输出
colcon build --event-handlers console_direct+

# 只编译修改的包
colcon build --packages-up-to RPCS_S_Behaviors_workflow

# 强制重新编译
colcon build --packages-select RPCS_S_Behaviors_workflow --cmake-force-configure
```

#### 4.4 验证编译结果

```bash
# 检查生成的可执行文件
ls -la install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/
# 应该看到：
# - process_action_server
# - rpcs_s_behaviors_workflow

# 检查生成的插件库
ls -la install/RPCS_S_Behaviors_workflow/lib/lib*.so | head -5
# 应该看到各种 libCheckXXX.so, libDoXXX.so 文件

# 检查 Action 接口
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction
```

### 5. 设置环境

```bash
# 设置工作空间环境
source ~/rpcs_ws/install/setup.bash

# 添加到 bashrc (永久生效)
echo "source ~/rpcs_ws/install/setup.bash" >> ~/.bashrc
```

## 📁 目录结构配置

### 1. 创建配置目录

```bash
# 创建运行时配置目录
mkdir -p ~/rpcs_config/process_trees
mkdir -p ~/rpcs_config/logs
mkdir -p ~/rpcs_config/backup
```

### 2. 复制配置文件

```bash
# 复制默认配置
cp ~/rpcs_ws/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_action_mapping.yaml ~/rpcs_config/

# 复制工艺动作树文件
cp -r ~/rpcs_ws/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_trees/* ~/rpcs_config/process_trees/
```

### 3. 设置权限

```bash
# 设置配置文件权限
chmod -R 755 ~/rpcs_config
chown -R $USER:$USER ~/rpcs_config
```

## 🔧 系统配置

### 1. 网络配置

#### 单机部署
```bash
# 设置 ROS_DOMAIN_ID (避免干扰)
export ROS_DOMAIN_ID=42
echo "export ROS_DOMAIN_ID=42" >> ~/.bashrc
```

#### 多机器人网络部署
```bash
# 主控机器配置
export ROS_DOMAIN_ID=0
export RMW_IMPLEMENTATION=rmw_cyclonedx_cpp

# 各机器人节点配置
# Robot1: ROS_DOMAIN_ID=1
# Robot2: ROS_DOMAIN_ID=2  
# Robot3: ROS_DOMAIN_ID=3
```

### 2. 性能优化配置

```bash
# 增加共享内存大小
echo 'kernel.shmmax = 268435456' | sudo tee -a /etc/sysctl.conf

# 调整网络缓冲区
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf

# 应用配置
sudo sysctl -p
```

### 3. 日志配置

```bash
# 创建日志配置文件
cat > ~/rpcs_config/logging.conf << EOF
[loggers]
keys=root

[handlers]
keys=fileHandler,consoleHandler

[formatters]
keys=fileFormatter,consoleFormatter

[logger_root]
level=INFO
handlers=fileHandler,consoleHandler

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=fileFormatter
args=('~/rpcs_config/logs/rpcs.log', 'a')

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=consoleFormatter
args=(sys.stdout,)

[formatter_fileFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_consoleFormatter]
format=%(levelname)s: %(message)s
EOF
```

## 🧪 验证安装

### 1. 基本功能测试

```bash
# 测试 ROS2 环境
ros2 --version

# 测试节点通信
ros2 run demo_nodes_cpp talker &
ros2 run demo_nodes_cpp listener
# Ctrl+C 停止测试

# 测试编译结果
ros2 pkg list | grep rpcs
```

### 2. 系统组件测试

```bash
# 测试 Action 接口
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction

# 测试配置文件
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1 --dry-run

# 测试客户端
ros2 run rpcs_s_behaviors_workflow process_action_client_example --help
```

### 3. 性能基准测试

```bash
# 启动服务器
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1 &

# 运行基准测试
for i in {1..10}; do
    echo "Test $i:"
    time ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 SelfCheck
    sleep 2
done
```

## 🐳 Docker 部署 (可选)

### 1. 创建 Dockerfile

```dockerfile
FROM ros:humble-desktop

# 安装依赖
RUN apt-get update && apt-get install -y \
    libyaml-cpp-dev \
    libzmq3-dev \
    libboost-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装 BehaviorTree.CPP
RUN git clone https://github.com/BehaviorTree/BehaviorTree.CPP.git /tmp/bt && \
    cd /tmp/bt && mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release && \
    make -j$(nproc) && make install && \
    rm -rf /tmp/bt

# 复制源码
COPY src /workspace/src

# 编译项目
WORKDIR /workspace
RUN . /opt/ros/humble/setup.sh && \
    colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release

# 设置入口点
COPY docker-entrypoint.sh /
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]
```

### 2. 构建和运行

```bash
# 构建镜像
docker build -t rpcs-behavior-tree .

# 运行容器
docker run -it --rm \
    --network host \
    -v ~/rpcs_config:/config \
    rpcs-behavior-tree
```

## 🔄 自动化部署脚本

### 1. 创建部署脚本

```bash
cat > ~/deploy_rpcs.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 RPCS 工艺动作树系统自动部署脚本"

# 检查系统
echo "📋 检查系统要求..."
if ! command -v ros2 &> /dev/null; then
    echo "❌ ROS2 未安装，请先安装 ROS2"
    exit 1
fi

# 创建工作空间
echo "📁 创建工作空间..."
mkdir -p ~/rpcs_ws/src
cd ~/rpcs_ws

# 安装依赖
echo "📦 安装依赖..."
rosdep update
rosdep install --from-paths src --ignore-src -r -y

# 编译项目
echo "🔨 编译项目..."
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release

# 设置环境
echo "⚙️ 配置环境..."
source install/setup.bash
echo "source ~/rpcs_ws/install/setup.bash" >> ~/.bashrc

# 创建配置
echo "📋 创建配置..."
mkdir -p ~/rpcs_config/{process_trees,logs,backup}
cp src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_action_mapping.yaml ~/rpcs_config/
cp -r src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_trees/* ~/rpcs_config/process_trees/

echo "✅ 部署完成！"
echo "🎯 运行以下命令启动系统："
echo "    ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1"
EOF

chmod +x ~/deploy_rpcs.sh
```

### 2. 运行部署

```bash
# 执行自动部署
~/deploy_rpcs.sh
```

## 🚦 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 清理编译缓存
   rm -rf build install log
   colcon build --cmake-clean-cache
   ```

2. **依赖问题**
   ```bash
   # 更新包索引
   sudo apt update
   rosdep update
   ```

3. **权限问题**
   ```bash
   # 修复权限
   sudo chown -R $USER:$USER ~/rpcs_ws
   ```

4. **网络问题**
   ```bash
   # 检查 ROS2 通信
   ros2 doctor
   ```

## 📊 部署验证清单

- [ ] ROS2 环境正常
- [ ] BehaviorTree.CPP 安装成功
- [ ] 项目编译无错误
- [ ] 配置文件正确
- [ ] 网络通信正常
- [ ] 基本功能测试通过
- [ ] 日志系统工作
- [ ] 性能满足要求

---

🎉 **部署完成**！您的 RPCS 工艺动作树系统已经准备就绪，可以开始使用了！ 