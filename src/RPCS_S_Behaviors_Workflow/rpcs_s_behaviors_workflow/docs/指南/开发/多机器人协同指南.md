# 多机器人协同指南

本指南详细介绍 RPCS 系统中多机器人协同工作的配置、协调机制和管理方法。

## 🤖 多机器人系统架构

### 系统概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        多机器人协同系统                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   Robot1    │    │   Robot2    │    │   Robot3    │          │
│  │  灯板工艺   │    │  PCBA工艺   │    │  保护壳工艺  │          │
│  │             │    │             │    │             │          │
│  │ :1670       │    │ :1671       │    │ :1672       │          │
│  └─────────────┘    └─────────────┘    └─────────────┘          │
│         │                   │                   │               │
│         └───────────────────┼───────────────────┘               │
│                             │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  ROS2 通信网络                              │ │
│  │  • Action 服务    • 话题通信    • 状态同步                │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                             │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  协调控制层                                │ │
│  │  • 任务分配      • 状态监控    • 冲突解决                │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 命名空间隔离

为了避免多机器人在同一网络中的服务名称冲突，系统采用命名空间隔离机制：

- **Robot1**: 命名空间 `/Robot1/`，Action 服务 `/Robot1/ExecuteProcessAction`
- **Robot2**: 命名空间 `/Robot2/`，Action 服务 `/Robot2/ExecuteProcessAction`  
- **Robot3**: 命名空间 `/Robot3/`，Action 服务 `/Robot3/ExecuteProcessAction`

## 🚀 多机器人部署

### 1. 启动所有机器人服务器

#### 使用统一启动文件
```bash
# 启动所有机器人的工艺动作服务器
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py

# 选择性启动特定机器人
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py \
    enable_Robot1:=true \
    enable_Robot2:=true \
    enable_Robot3:=false
```

#### 分别启动各机器人
```bash
# 在不同终端中启动各机器人
# 终端1 - Robot1
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1

# 终端2 - Robot2  
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot2

# 终端3 - Robot3
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot3
```

### 2. 网络配置

#### ROS_DOMAIN_ID 配置
```bash
# 为不同机器人设置不同的域ID (可选)
export ROS_DOMAIN_ID=0  # 主控制器
export ROS_DOMAIN_ID=1  # Robot1
export ROS_DOMAIN_ID=2  # Robot2
export ROS_DOMAIN_ID=3  # Robot3

# 或者使用统一域ID
export ROS_DOMAIN_ID=42  # 所有机器人使用同一域
```

#### 网络发现配置
```bash
# 设置 RMW 实现
export RMW_IMPLEMENTATION=rmw_cyclonedx_cpp

# 配置发现超时
export RMW_DISCOVERY_TIMEOUT=30
```

## 🎯 协同工作流程

### 装配线协同模式

#### 1. 顺序协同流程
```
Robot1 (灯板) → Robot2 (PCBA) → Robot3 (保护壳)
     ↓              ↓              ↓
  灯板准备 →    PCBA装配  →   最终组装
```

#### 2. 并行协同流程
```
Robot1 ──┐
         ├─→ 同步点 → Robot3 (最终组装)
Robot2 ──┘
```

### 协同控制脚本

#### 创建协同控制器
```bash
cat > ~/multi_robot_coordinator.py << 'EOF'
#!/usr/bin/env python3

import asyncio
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import threading
import time

class MultiRobotCoordinator(Node):
    def __init__(self):
        super().__init__('multi_robot_coordinator')
        
        # 创建各机器人的 Action 客户端
        self.robot_clients = {
            'Robot1': ActionClient(self, ExecuteProcessAction, '/Robot1/ExecuteProcessAction'),
            'Robot2': ActionClient(self, ExecuteProcessAction, '/Robot2/ExecuteProcessAction'),
            'Robot3': ActionClient(self, ExecuteProcessAction, '/Robot3/ExecuteProcessAction')
        }
        
        # 机器人状态跟踪
        self.robot_status = {
            'Robot1': 'IDLE',
            'Robot2': 'IDLE', 
            'Robot3': 'IDLE'
        }
        
        self.get_logger().info('多机器人协调器已初始化')
    
    async def execute_robot_action(self, robot_id, action_type, timeout=300):
        """执行单个机器人的工艺动作"""
        client = self.robot_clients[robot_id]
        
        # 等待服务可用
        if not client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error(f'{robot_id} 的 Action 服务不可用')
            return False
        
        # 创建目标
        goal = ExecuteProcessAction.Goal()
        goal.process_action_type = action_type
        goal.robot_id = robot_id
        goal.timeout_seconds = timeout
        
        self.get_logger().info(f'发送任务到 {robot_id}: {action_type}')
        self.robot_status[robot_id] = 'RUNNING'
        
        try:
            # 发送目标并等待结果
            future = client.send_goal_async(goal)
            goal_handle = await future
            
            if not goal_handle.accepted:
                self.get_logger().error(f'{robot_id} 拒绝了任务: {action_type}')
                self.robot_status[robot_id] = 'ERROR'
                return False
            
            # 等待执行完成
            result_future = goal_handle.get_result_async()
            result = await result_future
            
            if result.result.success:
                self.get_logger().info(f'{robot_id} 完成任务: {action_type}')
                self.robot_status[robot_id] = 'SUCCESS'
                return True
            else:
                self.get_logger().error(f'{robot_id} 任务失败: {action_type} - {result.result.result_message}')
                self.robot_status[robot_id] = 'FAILURE'
                return False
                
        except Exception as e:
            self.get_logger().error(f'{robot_id} 执行异常: {str(e)}')
            self.robot_status[robot_id] = 'ERROR'
            return False
        finally:
            if self.robot_status[robot_id] in ['RUNNING']:
                self.robot_status[robot_id] = 'IDLE'
    
    async def sequential_workflow(self):
        """顺序协同workflow"""
        self.get_logger().info('开始顺序协同工作流程')
        
        # 所有机器人先自检
        self.get_logger().info('阶段1: 所有机器人自检')
        tasks = []
        for robot_id in ['Robot1', 'Robot2', 'Robot3']:
            task = asyncio.create_task(self.execute_robot_action(robot_id, 'SelfCheck'))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        if not all(results):
            self.get_logger().error('自检阶段失败')
            return False
        
        # 顺序执行装配流程
        self.get_logger().info('阶段2: 顺序装配流程')
        
        # Robot1: 灯板处理
        if not await self.execute_robot_action('Robot1', 'StackLightBoards'):
            return False
        
        # Robot2: PCBA装配
        if not await self.execute_robot_action('Robot2', 'PlacePCBA'):
            return False
        
        # Robot3: 最终组装
        if not await self.execute_robot_action('Robot3', 'FastenProCase'):
            return False
        
        self.get_logger().info('顺序协同工作流程完成')
        return True
    
    async def parallel_workflow(self):
        """并行协同workflow"""
        self.get_logger().info('开始并行协同工作流程')
        
        # 阶段1: 并行准备
        self.get_logger().info('阶段1: 并行准备阶段')
        tasks = [
            asyncio.create_task(self.execute_robot_action('Robot1', 'StackLightBoards')),
            asyncio.create_task(self.execute_robot_action('Robot2', 'PlacePCBA'))
        ]
        
        results = await asyncio.gather(*tasks)
        if not all(results):
            self.get_logger().error('并行准备阶段失败')
            return False
        
        # 阶段2: 最终组装
        self.get_logger().info('阶段2: 最终组装')
        if not await self.execute_robot_action('Robot3', 'FastenProCase'):
            return False
        
        self.get_logger().info('并行协同工作流程完成')
        return True
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            'robot_status': self.robot_status.copy(),
            'timestamp': time.time()
        }

async def main():
    rclpy.init()
    
    coordinator = MultiRobotCoordinator()
    
    try:
        # 选择工作流程模式
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == 'parallel':
            await coordinator.parallel_workflow()
        else:
            await coordinator.sequential_workflow()
            
    except KeyboardInterrupt:
        coordinator.get_logger().info('协调器被中断')
    finally:
        coordinator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    asyncio.run(main())
EOF

chmod +x ~/multi_robot_coordinator.py
```

### 使用协同控制器

```bash
# 启动顺序协同工作流程
python3 ~/multi_robot_coordinator.py sequential

# 启动并行协同工作流程  
python3 ~/multi_robot_coordinator.py parallel
```

## 📊 状态同步与监控

### 创建多机器人监控仪表板

```bash
cat > ~/multi_robot_monitor.py << 'EOF'
#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import time
import threading
from datetime import datetime

class MultiRobotMonitor(Node):
    def __init__(self):
        super().__init__('multi_robot_monitor')
        
        self.robots = ['Robot1', 'Robot2', 'Robot3']
        self.robot_status = {}
        
        # 初始化状态
        for robot_id in self.robots:
            self.robot_status[robot_id] = {
                'action_available': False,
                'current_task': 'UNKNOWN',
                'last_update': None
            }
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.get_logger().info('多机器人监控器已启动')
    
    def check_robot_availability(self, robot_id):
        """检查机器人可用性"""
        try:
            client = ActionClient(self, ExecuteProcessAction, f'/{robot_id}/ExecuteProcessAction')
            available = client.wait_for_server(timeout_sec=2.0)
            return available
        except:
            return False
    
    def monitor_loop(self):
        """监控循环"""
        while rclpy.ok():
            for robot_id in self.robots:
                available = self.check_robot_availability(robot_id)
                self.robot_status[robot_id]['action_available'] = available
                self.robot_status[robot_id]['last_update'] = datetime.now()
            
            time.sleep(5)  # 5秒检查一次
    
    def display_status(self):
        """显示状态"""
        print("\n" + "="*80)
        print(f"🤖 多机器人系统状态监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        for robot_id in self.robots:
            status = self.robot_status[robot_id]
            available_icon = "✅" if status['action_available'] else "❌"
            
            print(f"\n🔧 {robot_id.upper()}:")
            print(f"  Action服务: {available_icon}")
            print(f"  当前任务: {status['current_task']}")
            print(f"  最后更新: {status['last_update']}")
        
        print("\n" + "="*80)
    
    def run_monitor(self):
        """运行监控"""
        try:
            while rclpy.ok():
                rclpy.spin_once(self, timeout_sec=1.0)
                self.display_status()
                time.sleep(10)  # 10秒显示一次
        except KeyboardInterrupt:
            self.get_logger().info('监控器被停止')

def main():
    rclpy.init()
    
    monitor = MultiRobotMonitor()
    
    try:
        monitor.run_monitor()
    finally:
        monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

chmod +x ~/multi_robot_monitor.py
```

### 启动监控

```bash
# 启动多机器人监控
python3 ~/multi_robot_monitor.py
```

## 🔄 任务调度与负载均衡

### 智能任务分配器

```bash
cat > ~/task_scheduler.py << 'EOF'
#!/usr/bin/env python3

import asyncio
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
from queue import Queue
import threading
import time

class TaskScheduler(Node):
    def __init__(self):
        super().__init__('task_scheduler')
        
        # 机器人能力映射
        self.robot_capabilities = {
            'Robot1': ['SelfCheck', 'PickSendStock', 'StackLightBoards', 'RemoveReleaseFilm', 'BondCarrier'],
            'Robot2': ['SelfCheck', 'PickSendStock', 'MoveWIP', 'PlaceWIP', 'PlacePCBA', 'DoPCAdapterPaste', 'OpenFPC', 'TransferFixture', 'PlaceProduct'],
            'Robot3': ['SelfCheck', 'Route', 'PickSendStock', 'ScrewPCBA', 'ConnectFPC', 'FastenProCase']
        }
        
        # 任务队列
        self.task_queue = Queue()
        
        # 机器人状态
        self.robot_status = {
            'Robot1': 'IDLE',
            'Robot2': 'IDLE',
            'Robot3': 'IDLE'
        }
        
        # Action 客户端
        self.clients = {}
        for robot_id in self.robot_capabilities.keys():
            self.clients[robot_id] = ActionClient(
                self, ExecuteProcessAction, f'/{robot_id}/ExecuteProcessAction'
            )
        
        self.get_logger().info('任务调度器已初始化')
    
    def add_task(self, task_type, priority=1, parameters=None):
        """添加任务到队列"""
        task = {
            'type': task_type,
            'priority': priority,
            'parameters': parameters or {},
            'timestamp': time.time()
        }
        
        self.task_queue.put(task)
        self.get_logger().info(f'添加任务: {task_type} (优先级: {priority})')
    
    def find_capable_robots(self, task_type):
        """找到能执行特定任务的机器人"""
        capable_robots = []
        for robot_id, capabilities in self.robot_capabilities.items():
            if task_type in capabilities:
                capable_robots.append(robot_id)
        return capable_robots
    
    def select_best_robot(self, capable_robots):
        """选择最佳机器人执行任务"""
        # 优先选择空闲的机器人
        idle_robots = [r for r in capable_robots if self.robot_status[r] == 'IDLE']
        
        if idle_robots:
            # 简单的负载均衡：选择第一个空闲的机器人
            return idle_robots[0]
        
        # 如果没有空闲机器人，返回None
        return None
    
    async def execute_task(self, robot_id, task):
        """执行任务"""
        client = self.clients[robot_id]
        
        if not client.wait_for_server(timeout_sec=5.0):
            self.get_logger().error(f'{robot_id} 的服务不可用')
            return False
        
        # 创建目标
        goal = ExecuteProcessAction.Goal()
        goal.process_action_type = task['type']
        goal.robot_id = robot_id
        goal.timeout_seconds = 300
        
        # 设置参数
        if task['parameters']:
            goal.process_parameters = [str(v) for v in task['parameters'].values()]
        
        self.get_logger().info(f'{robot_id} 开始执行任务: {task["type"]}')
        self.robot_status[robot_id] = 'RUNNING'
        
        try:
            # 发送目标
            future = client.send_goal_async(goal)
            goal_handle = await future
            
            if not goal_handle.accepted:
                self.get_logger().error(f'{robot_id} 拒绝任务: {task["type"]}')
                return False
            
            # 等待结果
            result_future = goal_handle.get_result_async()
            result = await result_future
            
            success = result.result.success
            if success:
                self.get_logger().info(f'{robot_id} 完成任务: {task["type"]}')
            else:
                self.get_logger().error(f'{robot_id} 任务失败: {task["type"]}')
            
            return success
            
        except Exception as e:
            self.get_logger().error(f'{robot_id} 执行异常: {str(e)}')
            return False
        finally:
            self.robot_status[robot_id] = 'IDLE'
    
    async def process_tasks(self):
        """处理任务队列"""
        while rclpy.ok():
            if not self.task_queue.empty():
                task = self.task_queue.get()
                
                # 找到能执行任务的机器人
                capable_robots = self.find_capable_robots(task['type'])
                
                if not capable_robots:
                    self.get_logger().error(f'没有机器人能执行任务: {task["type"]}')
                    continue
                
                # 选择最佳机器人
                selected_robot = self.select_best_robot(capable_robots)
                
                if selected_robot:
                    # 执行任务
                    await self.execute_task(selected_robot, task)
                else:
                    # 重新放回队列等待
                    self.task_queue.put(task)
                    self.get_logger().info(f'任务 {task["type"]} 等待可用机器人')
            
            await asyncio.sleep(1)

def main():
    rclpy.init()
    
    scheduler = TaskScheduler()
    
    # 添加示例任务
    scheduler.add_task('SelfCheck', priority=1)
    scheduler.add_task('StackLightBoards', priority=2)
    scheduler.add_task('PlacePCBA', priority=2)
    scheduler.add_task('FastenProCase', priority=3)
    
    try:
        asyncio.run(scheduler.process_tasks())
    except KeyboardInterrupt:
        scheduler.get_logger().info('任务调度器被停止')
    finally:
        scheduler.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

chmod +x ~/task_scheduler.py
```

## 🛡️ 冲突解决与安全机制

### 资源锁定机制

```bash
cat > ~/resource_manager.py << 'EOF'
#!/usr/bin/env python3

import threading
import time
from enum import Enum

class ResourceType(Enum):
    WORKSTATION_A = "workstation_a"
    WORKSTATION_B = "workstation_b"
    CONVEYOR_BELT = "conveyor_belt"
    TOOL_STATION = "tool_station"

class ResourceManager:
    def __init__(self):
        self.locks = {}
        self.lock_owners = {}
        self.lock_mutex = threading.Lock()
        
        # 初始化资源锁
        for resource in ResourceType:
            self.locks[resource.value] = threading.Lock()
            self.lock_owners[resource.value] = None
    
    def acquire_resource(self, robot_id, resource_type, timeout=30):
        """获取资源锁"""
        resource_name = resource_type.value
        
        print(f"[{robot_id}] 尝试获取资源: {resource_name}")
        
        try:
            if self.locks[resource_name].acquire(timeout=timeout):
                with self.lock_mutex:
                    self.lock_owners[resource_name] = robot_id
                print(f"[{robot_id}] 成功获取资源: {resource_name}")
                return True
            else:
                print(f"[{robot_id}] 获取资源超时: {resource_name}")
                return False
        except Exception as e:
            print(f"[{robot_id}] 获取资源异常: {resource_name} - {str(e)}")
            return False
    
    def release_resource(self, robot_id, resource_type):
        """释放资源锁"""
        resource_name = resource_type.value
        
        with self.lock_mutex:
            if self.lock_owners[resource_name] == robot_id:
                self.lock_owners[resource_name] = None
                self.locks[resource_name].release()
                print(f"[{robot_id}] 释放资源: {resource_name}")
                return True
            else:
                print(f"[{robot_id}] 无权释放资源: {resource_name}")
                return False
    
    def get_resource_status(self):
        """获取资源状态"""
        with self.lock_mutex:
            return self.lock_owners.copy()

# 全局资源管理器实例
resource_manager = ResourceManager()
EOF
```

### 安全协调机制

```bash
cat > ~/safety_coordinator.py << 'EOF'
#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
import threading
import time

class SafetyCoordinator(Node):
    def __init__(self):
        super().__init__('safety_coordinator')
        
        # 安全区域定义
        self.safety_zones = {
            'zone_a': {'robots': [], 'max_capacity': 1},
            'zone_b': {'robots': [], 'max_capacity': 2},
            'shared_zone': {'robots': [], 'max_capacity': 1}
        }
        
        self.zone_mutex = threading.Lock()
        self.get_logger().info('安全协调器已启动')
    
    def request_zone_access(self, robot_id, zone_name):
        """请求进入安全区域"""
        with self.zone_mutex:
            zone = self.safety_zones.get(zone_name)
            if not zone:
                self.get_logger().error(f'未知安全区域: {zone_name}')
                return False
            
            # 检查容量限制
            if len(zone['robots']) >= zone['max_capacity']:
                self.get_logger().warning(f'安全区域 {zone_name} 已满，{robot_id} 等待')
                return False
            
            # 检查是否已在区域内
            if robot_id in zone['robots']:
                self.get_logger().warning(f'{robot_id} 已在安全区域 {zone_name} 内')
                return True
            
            # 允许进入
            zone['robots'].append(robot_id)
            self.get_logger().info(f'{robot_id} 进入安全区域: {zone_name}')
            return True
    
    def exit_zone(self, robot_id, zone_name):
        """退出安全区域"""
        with self.zone_mutex:
            zone = self.safety_zones.get(zone_name)
            if not zone:
                return False
            
            if robot_id in zone['robots']:
                zone['robots'].remove(robot_id)
                self.get_logger().info(f'{robot_id} 退出安全区域: {zone_name}')
                return True
            
            return False
    
    def emergency_stop_all(self):
        """紧急停止所有机器人"""
        self.get_logger().error('触发紧急停止！')
        
        # 这里应该发送紧急停止指令到所有机器人
        # 实际实现中需要调用各机器人的紧急停止服务
        
        with self.zone_mutex:
            for zone in self.safety_zones.values():
                zone['robots'].clear()

def main():
    rclpy.init()
    
    coordinator = SafetyCoordinator()
    
    try:
        rclpy.spin(coordinator)
    except KeyboardInterrupt:
        coordinator.get_logger().info('安全协调器被停止')
    finally:
        coordinator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

chmod +x ~/safety_coordinator.py
```

## 📋 最佳实践

### 1. 系统启动顺序

```bash
# 1. 启动基础服务
ros2 daemon start

# 2. 启动安全协调器
python3 ~/safety_coordinator.py &

# 3. 启动资源管理器 (如果需要)

# 4. 启动各机器人服务器
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py

# 5. 启动监控系统
python3 ~/multi_robot_monitor.py &

# 6. 启动任务调度器
python3 ~/task_scheduler.py &
```

### 2. 故障恢复策略

```bash
# 检查系统状态脚本
cat > ~/check_system_health.sh << 'EOF'
#!/bin/bash

echo "🔍 检查多机器人系统健康状态"

# 检查各机器人节点
for robot in Robot1 Robot2 Robot3; do
    if ros2 node list | grep -q "${robot}_process_action_server"; then
        echo "✅ $robot 节点运行正常"
    else
        echo "❌ $robot 节点未运行"
    fi
done

# 检查Action服务
for robot in Robot1 Robot2 Robot3; do
    if ros2 action list | grep -q "/${robot}/ExecuteProcessAction"; then
        echo "✅ $robot Action服务可用"
    else
        echo "❌ $robot Action服务不可用"
    fi
done

# 检查网络连通性
ros2 doctor --report | head -10
EOF

chmod +x ~/check_system_health.sh
```

### 3. 性能优化建议

- **网络优化**: 使用有线网络连接，避免WiFi延迟
- **负载均衡**: 合理分配任务，避免单点过载
- **资源管理**: 实现资源锁定机制，避免冲突
- **监控告警**: 实时监控系统状态，及时发现问题
- **容错设计**: 实现故障转移和自动恢复机制

---

🎯 **通过本指南，您可以成功部署和管理多机器人协同系统，实现高效的协同作业！** 