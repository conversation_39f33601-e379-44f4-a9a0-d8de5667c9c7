# 工艺动作树开发指南

本指南详细介绍如何创建、编辑和优化 RPCS 系统的工艺动作树 XML 文件。

## 🌳 行为树基础

### 行为树核心概念

#### 1. 节点类型

| 节点类型 | 说明 | 返回值 | 用途 |
|---------|------|--------|------|
| **Action** | 动作节点 | SUCCESS/FAILURE/RUNNING | 执行具体操作 |
| **Condition** | 条件节点 | SUCCESS/FAILURE | 检查条件状态 |
| **Control** | 控制节点 | 根据子节点决定 | 控制执行流程 |
| **Decorator** | 装饰节点 | 修改子节点行为 | 增强节点功能 |

#### 2. 控制节点类型

- **Sequence**: 顺序执行，全部成功才成功
- **Fallback**: 选择执行，一个成功就成功  
- **Parallel**: 并行执行，根据策略决定结果
- **ReactiveFallback**: 响应式选择，可中断执行

#### 3. 装饰节点类型

- **Retry**: 重试装饰器
- **Timeout**: 超时装饰器
- **ForceSuccess**: 强制成功装饰器
- **Inverter**: 反转结果装饰器

## 📄 XML 文件结构

### 基本模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4" main_tree_to_execute="MainTree">
    
    <!-- 行为树定义 -->
    <BehaviorTree ID="MainTree">
        <!-- 根节点通常是控制节点 -->
        <ReactiveFallback name="MainFlow">
            <!-- 子节点 -->
        </ReactiveFallback>
    </BehaviorTree>
    
    <!-- 子树定义 -->
    <BehaviorTree ID="SubTree">
        <!-- 子树内容 -->
    </BehaviorTree>
    
    <!-- 树黑板端口定义 -->
    <TreeNodesModel>
        <!-- 自定义节点定义 -->
    </TreeNodesModel>
    
</root>
```

### 文件头说明

```xml
<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4" main_tree_to_execute="MainTree">
```

- `BTCPP_format="4"`: BehaviorTree.CPP 版本格式
- `main_tree_to_execute`: 指定主要执行的行为树ID

## 🎯 RPCS 系统专用节点

### 动作节点 (Action Nodes)

#### 1. 机器人控制动作
```xml
<!-- 自检动作 -->
<DoSelfCheck name="执行自检" 
             timeout="30000"
             robot_id="{robot_id}"
             check_type="FULL"/>

<!-- 拾取送料动作 -->
<DoPickSendStock name="拾取送料"
                 timeout="60000" 
                 robot_id="{robot_id}"
                 stock_type="{stock_type}"
                 target_position="{target_pos}"/>

<!-- 产品放置动作 -->
<DoPlaceProduct name="产品放置"
                timeout="45000"
                robot_id="{robot_id}" 
                product_id="{product_id}"
                place_position="{place_pos}"/>
```

#### 2. 状态检查动作
```xml
<!-- 检查机器人状态 -->
<CheckRobotStatus name="检查机器人状态"
                  robot_id="{robot_id}"
                  expected_status="READY"/>

<!-- 检查物料状态 -->
<CheckStockStatus name="检查物料状态"
                  stock_id="{stock_id}"
                  min_quantity="10"/>

<!-- 检查游戏状态 -->
<CheckGameStatus name="检查游戏状态"
                 expected_phase="RUNNING"/>
```

### 通信节点

#### 1. 发布节点 (Publisher)
```xml
<!-- 发布机器人控制指令 -->
<PubRobotControl name="发布控制指令"
                 topic_name="/robot_control"
                 robot_id="{robot_id}"
                 command="{control_cmd}"
                 parameters="{cmd_params}"/>

<!-- 发布状态更新 -->
<PubStatusUpdate name="发布状态更新"
                 topic_name="/status_update"
                 robot_id="{robot_id}"
                 status="{current_status}"/>
```

#### 2. 订阅节点 (Subscriber)
```xml
<!-- 订阅游戏状态 -->
<SubGameStatus name="订阅游戏状态"
               topic_name="/game_status"
               timeout="5000"/>

<!-- 订阅机器人状态 -->
<SubRobotStatus name="订阅机器人状态"
                topic_name="/robot_status"
                robot_id="{robot_id}"
                timeout="3000"/>
```

## 🏗️ 工艺动作树设计模式

### 1. 标准自检流程

```xml
<BehaviorTree ID="SelfCheckTree">
    <Sequence name="自检序列">
        
        <!-- 初始化检查 -->
        <Sequence name="初始化检查">
            <CheckRobotStatus name="检查初始状态" robot_id="{robot_id}" expected_status="IDLE"/>
            <DoInitialize name="初始化机器人" robot_id="{robot_id}"/>
        </Sequence>
        
        <!-- 硬件自检 -->
        <Sequence name="硬件自检">
            <DoHardwareCheck name="检查硬件" robot_id="{robot_id}" check_type="FULL"/>
            <CheckHardwareStatus name="验证硬件状态" robot_id="{robot_id}"/>
        </Sequence>
        
        <!-- 软件自检 -->
        <Sequence name="软件自检">
            <DoSoftwareCheck name="检查软件" robot_id="{robot_id}"/>
            <CheckSoftwareStatus name="验证软件状态" robot_id="{robot_id}"/>
        </Sequence>
        
        <!-- 通信测试 -->
        <Sequence name="通信测试">
            <DoCommTest name="通信测试" robot_id="{robot_id}"/>
            <CheckCommStatus name="验证通信状态" robot_id="{robot_id}"/>
        </Sequence>
        
        <!-- 完成自检 -->
        <DoSetStatus name="设置就绪状态" robot_id="{robot_id}" status="READY"/>
        
    </Sequence>
</BehaviorTree>
```

### 2. 带重试的动作流程

```xml
<BehaviorTree ID="PickSendStockTree">
    <ReactiveFallback name="拾取送料主流程">
        
        <!-- 检查前置条件 -->
        <Sequence name="前置条件检查">
            <CheckRobotStatus name="检查机器人状态" robot_id="{robot_id}" expected_status="READY"/>
            <CheckStockAvailable name="检查物料可用性" stock_type="{stock_type}"/>
        </Sequence>
        
        <!-- 执行拾取送料 (带重试) -->
        <Retry name="重试拾取送料" num_attempts="3">
            <Sequence name="拾取送料序列">
                
                <!-- 移动到拾取位置 -->
                <Timeout name="移动超时控制" msec="30000">
                    <DoMoveToPosition name="移动到拾取位置" 
                                      robot_id="{robot_id}"
                                      target_position="PICK_POSITION"/>
                </Timeout>
                
                <!-- 执行拾取 -->
                <Timeout name="拾取超时控制" msec="20000">
                    <DoPickStock name="拾取物料"
                                 robot_id="{robot_id}"
                                 stock_type="{stock_type}"/>
                </Timeout>
                
                <!-- 移动到送料位置 -->
                <Timeout name="送料移动超时控制" msec="30000">
                    <DoMoveToPosition name="移动到送料位置"
                                      robot_id="{robot_id}" 
                                      target_position="SEND_POSITION"/>
                </Timeout>
                
                <!-- 执行送料 -->
                <Timeout name="送料超时控制" msec="20000">
                    <DoSendStock name="送料"
                                 robot_id="{robot_id}"
                                 target_station="{target_station}"/>
                </Timeout>
                
                <!-- 验证完成 -->
                <CheckOperationComplete name="验证操作完成" 
                                        robot_id="{robot_id}"
                                        operation="PICK_SEND_STOCK"/>
                
            </Sequence>
        </Retry>
        
        <!-- 错误处理 -->
        <Sequence name="错误处理">
            <DoErrorRecovery name="错误恢复" robot_id="{robot_id}"/>
            <DoSetStatus name="设置错误状态" robot_id="{robot_id}" status="ERROR"/>
            <AlwaysFailure name="返回失败"/>
        </Sequence>
        
    </ReactiveFallback>
</BehaviorTree>
```

### 3. 并行处理流程

```xml
<BehaviorTree ID="ParallelProcessTree">
    <Sequence name="并行处理主流程">
        
        <!-- 并行执行多个检查 -->
        <Parallel name="并行检查" 
                  success_threshold="2"
                  failure_threshold="1">
            
            <CheckSystemHealth name="系统健康检查" robot_id="{robot_id}"/>
            <CheckEnvironment name="环境检查" robot_id="{robot_id}"/>
            <CheckSafety name="安全检查" robot_id="{robot_id}"/>
            
        </Parallel>
        
        <!-- 根据检查结果执行后续动作 -->
        <ReactiveFallback name="后续处理">
            
            <!-- 正常流程 -->
            <Sequence name="正常处理流程">
                <IsSystemReady name="系统就绪?" robot_id="{robot_id}"/>
                <DoNormalOperation name="执行正常操作" robot_id="{robot_id}"/>
            </Sequence>
            
            <!-- 异常处理 -->
            <Sequence name="异常处理流程">
                <DoEmergencyStop name="紧急停止" robot_id="{robot_id}"/>
                <DoSafetyProtocol name="执行安全协议" robot_id="{robot_id}"/>
            </Sequence>
            
        </ReactiveFallback>
        
    </Sequence>
</BehaviorTree>
```

## 📝 开发最佳实践

### 1. 命名规范

#### 节点命名
- **动作节点**: 使用 `Do` 前缀，如 `DoSelfCheck`、`DoPickStock`
- **条件节点**: 使用 `Check` 或 `Is` 前缀，如 `CheckStatus`、`IsReady`
- **发布节点**: 使用 `Pub` 前缀，如 `PubRobotControl`
- **订阅节点**: 使用 `Sub` 前缀，如 `SubGameStatus`

#### 树和子树命名
- **主树**: 使用描述性名称，如 `SelfCheckTree`、`PickSendStockTree`
- **子树**: 使用功能性名称，如 `ErrorHandling`、`SafetyCheck`

### 2. 参数使用

#### 黑板变量
```xml
<!-- 使用黑板变量传递数据 -->
<DoPickStock name="拾取物料"
             robot_id="{robot_id}"
             stock_type="{stock_type}"
             pickup_position="{pickup_pos}"
             result="{pick_result}"/>

<!-- 在后续节点中使用结果 -->
<CheckPickResult name="检查拾取结果"
                 pick_result="{pick_result}"
                 expected="SUCCESS"/>
```

#### 默认参数
```xml
<!-- 设置默认超时时间 -->
<DoMoveToPosition name="移动到位置"
                  robot_id="{robot_id}"
                  target_position="{target_pos}"
                  timeout="30000"
                  speed="NORMAL"/>
```

### 3. 错误处理模式

#### 标准错误处理结构
```xml
<ReactiveFallback name="带错误处理的操作">
    
    <!-- 主要操作流程 -->
    <Sequence name="主要流程">
        <!-- 主要操作节点 -->
    </Sequence>
    
    <!-- 错误恢复流程 -->
    <Sequence name="错误恢复">
        <DoErrorRecovery name="尝试恢复" robot_id="{robot_id}"/>
        <CheckRecoverySuccess name="检查恢复结果" robot_id="{robot_id}"/>
    </Sequence>
    
    <!-- 最终错误处理 -->
    <Sequence name="最终错误处理">
        <DoEmergencyStop name="紧急停止" robot_id="{robot_id}"/>
        <DoSetErrorStatus name="设置错误状态" robot_id="{robot_id}"/>
        <AlwaysFailure name="返回失败"/>
    </Sequence>
    
</ReactiveFallback>
```

## 🔧 开发工具和技巧

### 1. XML 验证

#### 使用 xmllint 验证语法
```bash
# 验证 XML 语法
xmllint --noout Robot1_self_check.xml

# 格式化 XML 文件
xmllint --format Robot1_self_check.xml > formatted.xml
```

#### 使用 Groot2 验证
```bash
# 启动 Groot2 并加载文件
groot2

# 在 Groot2 中：
# File -> Load Tree -> 选择 XML 文件
# 检查是否有语法错误或警告
```

### 2. 调试技巧

#### 添加调试节点
```xml
<!-- 添加日志输出节点 -->
<LogMessage name="调试日志" 
            message="执行到检查点1: robot_id={robot_id}, status={current_status}"/>

<!-- 添加断点节点 (开发时使用) -->
<AlwaysSuccess name="调试断点"/>
```

#### 使用条件断点
```xml
<!-- 只在特定条件下执行调试操作 -->
<ReactiveFallback name="条件调试">
    <Sequence name="调试条件">
        <IsDebugMode name="是否调试模式?"/>
        <LogMessage name="调试信息" message="当前状态: {current_status}"/>
        <Delay name="调试延迟" delay_msec="1000"/>
    </Sequence>
    <AlwaysSuccess name="跳过调试"/>
</ReactiveFallback>
```

### 3. 性能优化

#### 减少不必要的检查
```xml
<!-- 避免频繁的状态检查 -->
<Sequence name="优化的状态检查">
    <!-- 只在必要时检查状态 -->
    <CheckRobotStatus name="初始状态检查" robot_id="{robot_id}"/>
    
    <!-- 执行多个操作而不重复检查 -->
    <DoOperation1 name="操作1" robot_id="{robot_id}"/>
    <DoOperation2 name="操作2" robot_id="{robot_id}"/>
    <DoOperation3 name="操作3" robot_id="{robot_id}"/>
    
    <!-- 最后验证结果 -->
    <CheckFinalStatus name="最终状态检查" robot_id="{robot_id}"/>
</Sequence>
```

#### 使用并行处理
```xml
<!-- 并行执行独立的操作 -->
<Parallel name="并行优化" success_threshold="2">
    <DoIndependentTask1 name="独立任务1" robot_id="{robot_id}"/>
    <DoIndependentTask2 name="独立任务2" robot_id="{robot_id}"/>
    <DoIndependentTask3 name="独立任务3" robot_id="{robot_id}"/>
</Parallel>
```

## 📚 实例模板

### 创建新工艺动作树的步骤

1. **复制模板文件**
```bash
cp ~/rpcs_config/process_trees/Robot1_self_check.xml \
   ~/rpcs_config/process_trees/Robot1_new_action.xml
```

2. **修改基本信息**
```xml
<!-- 修改主树名称 -->
<root BTCPP_format="4" main_tree_to_execute="NewActionTree">

<!-- 修改行为树ID -->
<BehaviorTree ID="NewActionTree">
```

3. **设计流程结构**
```xml
<BehaviorTree ID="NewActionTree">
    <ReactiveFallback name="NewAction主流程">
        
        <!-- 前置条件检查 -->
        <Sequence name="前置条件">
            <!-- 添加前置条件检查节点 -->
        </Sequence>
        
        <!-- 主要操作流程 -->
        <Sequence name="主要操作">
            <!-- 添加主要操作节点 -->
        </Sequence>
        
        <!-- 错误处理 -->
        <Sequence name="错误处理">
            <!-- 添加错误处理节点 -->
        </Sequence>
        
    </ReactiveFallback>
</BehaviorTree>
```

4. **测试和调试**
```bash
# 验证 XML 语法
xmllint --noout ~/rpcs_config/process_trees/Robot1_new_action.xml

# 在 Groot2 中测试
groot2  # 加载并测试文件

# 在系统中测试
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 NewAction
```

---

🎯 **通过本指南，您可以熟练开发和优化 RPCS 系统的工艺动作树，创建高效可靠的工艺流程！** 