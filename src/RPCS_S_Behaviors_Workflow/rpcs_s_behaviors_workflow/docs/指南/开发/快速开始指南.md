# 快速开始指南

本指南将帮助您在 5 分钟内快速上手 RPCS 工艺动作树系统。

## 📋 前提条件

在开始之前，请确保您的系统已安装：

- **ROS2 Humble** (或更高版本)
- **BehaviorTree.CPP 4.x**
- **yaml-cpp** 库
- **基本的 C++ 编译环境**

## 🚀 快速开始 (5分钟上手)

### 前置条件检查
```bash
# 检查 ROS2 环境
ros2 --version  # 应该显示 ROS2 版本信息

# 检查必要依赖
dpkg -l | grep -E "(yaml-cpp|behaviortree)"
```

### 1. 编译系统 (2分钟)

```bash
cd /path/to/your/workspace

# 快速编译所有必要包
colcon build --packages-select rpcs_s_interfaces_behavior_tree behaviortree_ros2 RPCS_S_Behaviors_workflow

# 如果遇到编译错误，先清理可能的冲突：
rm -rf RPCS_S_Behaviors_template  # 删除重复包
rm -rf build/ install/  # 清理旧的编译结果

# 重新编译
colcon build --packages-select rpcs_s_interfaces_behavior_tree behaviortree_ros2 RPCS_S_Behaviors_workflow
```

**常见编译问题快速修复**：
- ❌ `Duplicate package names`: 运行 `rm -rf RPCS_S_Behaviors_template`
- ❌ `ExecuteProcessAction.hpp not found`: 先编译 `rpcs_s_interfaces_behavior_tree`
- ❌ `undefined reference to YAML`: 检查 yaml-cpp 安装：`sudo apt install libyaml-cpp-dev`

### 2. 环境设置 (30秒)

```bash
# 设置环境变量
source install/setup.bash

# 验证安装
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction
```

### 3. 启动系统 (1分钟)

```bash
# 启动单个机器人的工艺动作服务器
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1

# 或者使用 Launch 文件 (如果存在)
# ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1
```

### 4. 测试调用 (1分钟)

```bash
# 在新终端中测试 Action 调用
ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction "{process_action_type: 'SelfCheck', robot_id: 'Robot1', process_parameters: [], timeout_seconds: 30, preempt_current: false, process_id: 'test_001'}"
```

### 5. 查看状态 (30秒)

```bash
# 查看活动的 Action 服务器
ros2 action list

# 查看服务器信息
ros2 action info /Robot1/ExecuteProcessAction

# 查看节点信息
ros2 node info /Robot1_process_action_server
```

## 🎯 第二步：启动服务器

### 启动单个机器人服务器

```bash
# 启动 Robot1 的工艺动作服务器
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1
```

您应该看到类似的输出：
```
[INFO] [Robot1_process_action_server]: Initializing ProcessActionServer for robot: Robot1
[INFO] [Robot1_process_action_server]: Successfully loaded 5 process action mappings
[INFO] [Robot1_process_action_server]: ProcessActionServer initialized successfully
```

## 🎮 第三步：执行工艺动作

### 方法1：使用命令行工具

在新终端中执行：

```bash
# 执行自检工艺
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'SelfCheck', robot_id: 'Robot1', timeout_seconds: 60}"
```

### 方法2：使用客户端示例

```bash
# 使用 C++ 客户端
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 SelfCheck

# 使用 Python 客户端
python3 src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/scripts/process_action_client.py Robot1 SelfCheck
```

## 📊 第四步：监控执行状态

### 查看实时反馈

```bash
# 在新终端中监控反馈信息
ros2 topic echo /Robot1/ExecuteProcessAction/_action/feedback
```

您将看到类似的反馈信息：
```yaml
feedback:
  current_process_step: "Executing"
  current_operation: "Running behavior tree tick 45"
  progress_percent: 45.0
  status_message: "Process in progress"
  current_status: "RUNNING"
```

### 查看最终结果

```bash
# 监控结果信息
ros2 topic echo /Robot1/ExecuteProcessAction/_action/result
```

## 🔍 第五步：可视化调试 (可选)

如果您安装了 Groot2，可以实时可视化行为树执行：

```bash
# 启动 Groot2
groot2

# 在 Groot2 中连接到：
# - 地址: localhost
# - 端口: 1670 (Robot1)
```

## 🎉 恭喜！

您已成功完成了基本的工艺动作执行！现在您可以：

### 尝试其他工艺动作类型

```bash
# 执行拾取送料工艺
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot1 PickSendStock

# 执行其他机器人的工艺动作
ros2 run rpcs_s_behaviors_workflow process_action_client_example Robot2 SelfCheck
```

### 启动多机器人系统

```bash
# 启动所有机器人的服务器
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py
```

## 🚦 常见问题快速解决

### 问题1：服务器启动失败
```
ERROR: Config file does not exist
```
**解决方案**: 检查配置文件路径，确保 `process_action_mapping.yaml` 存在

### 问题2：Action 调用被拒绝
```
ERROR: Goal was rejected by server
```
**解决方案**: 
- 检查 `process_action_type` 是否在配置文件中定义
- 确认 `robot_id` 与服务器启动的机器人ID一致

### 问题3：找不到工艺动作树文件
```
ERROR: Process tree file does not exist
```
**解决方案**: 检查 XML 文件是否存在于 `config/process_trees/` 目录中

## 📖 下一步学习

现在您已经掌握了基础操作，建议继续学习：

1. [**基础操作指南**](./基础操作指南.md) - 了解更多操作方法
2. [**配置文件指南**](./配置文件指南.md) - 学习如何自定义配置
3. [**工艺动作树开发指南**](./工艺动作树开发指南.md) - 创建自己的工艺流程

## 💡 小贴士

- **使用Tab补全**: 在输入 ROS2 命令时使用 Tab 键自动补全
- **查看帮助**: 使用 `ros2 action --help` 查看更多 Action 命令
- **日志调试**: 使用 `--ros-args --log-level DEBUG` 获取详细日志
- **快速重启**: 使用 `Ctrl+C` 停止服务器，然后重新启动

---

🎯 **目标达成**: 您现在已经能够启动系统并执行基本的工艺动作了！继续探索更多高级功能吧！ 

## ⚡ 快速故障排除

### 编译失败
```bash
# 1. 清理并重新编译
rm -rf build/ install/
colcon build --packages-select rpcs_s_interfaces_behavior_tree behaviortree_ros2 RPCS_S_Behaviors_workflow

# 2. 检查依赖
rosdep install --from-paths src --ignore-src -r -y

# 3. 查看详细错误
colcon build --event-handlers console_direct+
```

### 运行时错误
```bash
# 1. 检查环境变量
echo $ROS_DOMAIN_ID
source install/setup.bash

# 2. 检查配置文件
ls -la src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/

# 3. 查看日志
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args --log-level debug
```

### Action 调用失败
```bash
# 1. 检查服务器状态
ros2 action list | grep ExecuteProcessAction

# 2. 检查接口定义
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction

# 3. 使用简单测试
ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction "{process_action_type: 'test', robot_id: 'Robot1'}"
``` 