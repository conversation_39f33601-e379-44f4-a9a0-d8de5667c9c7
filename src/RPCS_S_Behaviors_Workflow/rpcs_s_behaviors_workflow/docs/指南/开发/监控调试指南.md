# 监控调试指南

本指南介绍 RPCS 工艺动作树系统的监控、调试方法和故障排除。

## 📋 目录
- [编译问题调试](#编译问题调试)
- [系统监控](#系统监控)
- [调试工具](#调试工具)
- [故障排除](#故障排除)
- [性能分析](#性能分析)
- [日志分析](#日志分析)

## 🔧 编译问题调试

### 常见编译错误及解决方案

#### 1. 包名重复错误
```bash
# 错误信息
ERROR:colcon:colcon build: Duplicate package names not supported:
- rpcs_s_interfaces_behavior_tree:
  - RPCS_S_Behaviors_template/RPCS_S_Behaviors/rpcs_s_interfaces_behavior_tree
  - src/RPCS_S_Behaviors_workflow/rpcs_s_interfaces_behavior_tree

# 解决方案
rm -rf RPCS_S_Behaviors_template
colcon build --packages-select rpcs_s_interfaces_behavior_tree behaviortree_ros2 RPCS_S_Behaviors_workflow
```

#### 2. Action 接口头文件找不到
```bash
# 错误信息
fatal error: rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction.hpp: No such file or directory

# 调试步骤
# 1. 检查 Action 文件是否存在
ls -la src/RPCS_S_Behaviors_workflow/rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction.action

# 2. 检查 CMakeLists.txt 配置
grep -n "ExecuteProcessAction.action" src/RPCS_S_Behaviors_workflow/rpcs_s_interfaces_behavior_tree/CMakeLists.txt

# 3. 检查依赖配置
grep -n "action_msgs" src/RPCS_S_Behaviors_workflow/rpcs_s_interfaces_behavior_tree/package.xml

# 4. 重新编译接口包
colcon build --packages-select rpcs_s_interfaces_behavior_tree
```

#### 3. YAML-CPP 链接错误
```bash
# 错误信息
undefined reference to `YAML::LoadFile(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)'

# 调试步骤
# 1. 检查 yaml-cpp 安装
dpkg -l | grep yaml-cpp
sudo apt list --installed | grep yaml

# 2. 检查 CMakeLists.txt 中的链接配置
grep -n "yaml-cpp" src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/CMakeLists.txt

# 3. 验证链接库
ldd install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/process_action_server | grep yaml

# 解决方案
# 确保 CMakeLists.txt 包含：
# find_package(yaml-cpp REQUIRED)
# target_link_libraries(process_action_server yaml-cpp)
```

#### 4. 空文件编译错误
```bash
# 错误信息
undefined reference to `main'

# 调试步骤
# 1. 检查文件内容
wc -l src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/src/process_action_client_example.cpp

# 2. 检查 CMakeLists.txt 中的可执行文件配置
grep -A3 -B3 "process_action_client_example" src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/CMakeLists.txt

# 解决方案：注释掉空文件的编译配置或添加完整代码
```

### 编译调试工具

#### 详细编译输出
```bash
# 显示详细编译过程
colcon build --event-handlers console_direct+ --packages-select RPCS_S_Behaviors_workflow

# 显示编译警告
colcon build --cmake-args -DCMAKE_CXX_FLAGS="-Wall -Wextra" --packages-select RPCS_S_Behaviors_workflow

# 生成编译数据库 (用于 IDE)
colcon build --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
```

#### 依赖关系检查
```bash
# 检查包依赖
colcon graph --packages-select RPCS_S_Behaviors_workflow

# 检查未满足的依赖
rosdep check --from-paths src --ignore-src

# 安装缺失的依赖
rosdep install --from-paths src --ignore-src -r -y
```

#### 编译缓存管理
```bash
# 清理编译缓存
rm -rf build/ install/

# 强制重新配置
colcon build --cmake-force-configure --packages-select RPCS_S_Behaviors_workflow

# 只重新链接
colcon build --cmake-target install --packages-select RPCS_S_Behaviors_workflow
```

## 📊 系统监控

### 实时状态监控

#### 1. ROS2 节点监控
```bash
# 查看所有活跃节点
ros2 node list

# 查看特定节点信息
ros2 node info /Robot1/Robot1_process_action_server

# 监控节点状态
watch -n 1 'ros2 node list | grep process_action_server'
```

#### 2. Action 服务监控
```bash
# 查看所有 Action 服务
ros2 action list

# 查看特定 Action 信息
ros2 action info /Robot1/ExecuteProcessAction

# 监控 Action 状态
ros2 topic echo /Robot1/ExecuteProcessAction/_action/status
```

#### 3. 话题监控
```bash
# 监控反馈信息
ros2 topic echo /Robot1/ExecuteProcessAction/_action/feedback

# 监控执行结果
ros2 topic echo /Robot1/ExecuteProcessAction/_action/result

# 检查话题频率
ros2 topic hz /Robot1/ExecuteProcessAction/_action/feedback

# 检查话题带宽
ros2 topic bw /Robot1/ExecuteProcessAction/_action/feedback
```

### 系统性能监控

#### 1. 资源使用监控
```bash
# CPU 和内存使用
top -p $(pgrep -f process_action_server)

# 详细进程信息
ps aux | grep process_action_server

# 系统资源概览
htop
```

#### 2. 网络监控
```bash
# 网络连接状态
netstat -tulpn | grep process_action_server

# 网络流量监控
iftop -i eth0

# ROS2 网络诊断
ros2 doctor
```

#### 3. 磁盘和I/O监控
```bash
# 磁盘使用情况
df -h

# I/O 监控
iotop

# 文件系统监控
lsof -p $(pgrep -f process_action_server)
```

## 🔍 调试工具

### Groot2 可视化调试

#### 1. 启动 Groot2
```bash
# 安装 Groot2 (如果未安装)
sudo apt install ros-humble-groot

# 启动 Groot2
groot2
```

#### 2. 连接到机器人
- **Robot1**: `localhost:1670`
- **Robot2**: `localhost:1671`
- **Robot3**: `localhost:1672`

#### 3. Groot2 调试功能
- **实时行为树可视化**: 查看节点执行状态
- **黑板监控**: 监控共享数据状态
- **执行跟踪**: 跟踪节点执行路径
- **性能分析**: 分析节点执行时间

### 命令行调试

#### 1. 详细日志模式
```bash
# 启动时开启 DEBUG 日志
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 \
    --ros-args --log-level DEBUG

# 设置环境变量
export RCUTILS_LOGGING_SEVERITY=DEBUG
export RCUTILS_COLORIZED_OUTPUT=1
```

#### 2. 调试工具脚本
```bash
cat > ~/debug_rpcs.sh << 'EOF'
#!/bin/bash

ROBOT_ID=${1:-Robot1}

echo "🔍 RPCS 系统调试工具 - $ROBOT_ID"
echo "================================"

# 检查节点状态
echo "📱 节点状态:"
if ros2 node list | grep -q "${ROBOT_ID}_process_action_server"; then
    echo "  ✅ 节点运行中"
    ros2 node info /${ROBOT_ID}/${ROBOT_ID}_process_action_server
else
    echo "  ❌ 节点未运行"
fi

echo ""

# 检查 Action 服务
echo "🎯 Action 服务状态:"
if ros2 action list | grep -q "/${ROBOT_ID}/ExecuteProcessAction"; then
    echo "  ✅ Action 服务可用"
    ros2 action info /${ROBOT_ID}/ExecuteProcessAction
else
    echo "  ❌ Action 服务不可用"
fi

echo ""

# 检查配置文件
echo "📋 配置文件检查:"
CONFIG_FILE=~/rpcs_config/process_action_mapping.yaml
if [ -f "$CONFIG_FILE" ]; then
    echo "  ✅ 配置文件存在"
    if grep -q "$ROBOT_ID:" "$CONFIG_FILE"; then
        echo "  ✅ 机器人配置存在"
        echo "  📝 配置的工艺动作:"
        grep -A 10 "$ROBOT_ID:" "$CONFIG_FILE" | grep "  " | sed 's/^/    /'
    else
        echo "  ❌ 机器人配置不存在"
    fi
else
    echo "  ❌ 配置文件不存在"
fi

echo ""

# 检查工艺动作树文件
echo "📄 工艺动作树文件检查:"
TREES_DIR=~/rpcs_config/process_trees
if [ -d "$TREES_DIR" ]; then
    echo "  ✅ 工艺动作树目录存在"
    XML_FILES=$(ls ${TREES_DIR}/${ROBOT_ID}_*.xml 2>/dev/null | wc -l)
    echo "  📁 找到 $XML_FILES 个工艺动作树文件"
    ls ${TREES_DIR}/${ROBOT_ID}_*.xml 2>/dev/null | sed 's/^/    /'
else
    echo "  ❌ 工艺动作树目录不存在"
fi

echo ""

# 网络诊断
echo "🌐 网络诊断:"
ros2 doctor --report | head -20

EOF

chmod +x ~/debug_rpcs.sh
```

#### 3. 使用调试工具
```bash
# 调试特定机器人
~/debug_rpcs.sh Robot1

# 调试所有机器人
for robot in Robot1 Robot2 Robot3; do
    echo "调试 $robot:"
    ~/debug_rpcs.sh $robot
    echo "------------------------"
done
```

### RQT 调试工具

#### 1. 启动 RQT
```bash
# 启动 RQT 图形界面
rqt
```

#### 2. 有用的 RQT 插件
- **Node Graph**: 可视化节点关系图
- **Topic Monitor**: 监控话题数据
- **Service Caller**: 调用 ROS2 服务
- **Action Type Browser**: 浏览 Action 类型
- **Parameter Reconfigure**: 动态调整参数
- **Console**: 查看日志输出

## 🚨 故障排除

### 常见问题诊断

#### 1. 服务器启动失败

**症状**: 服务器无法启动或立即退出

**诊断步骤**:
```bash
# 检查配置文件
ls -la ~/rpcs_config/process_action_mapping.yaml

# 验证配置文件格式
yamllint ~/rpcs_config/process_action_mapping.yaml

# 检查依赖
ros2 pkg list | grep rpcs

# 查看详细错误信息
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 --ros-args --log-level DEBUG
```

**解决方案**:
```bash
# 重新编译项目
cd ~/rpcs_ws
colcon build --packages-select rpcs_s_behaviors_workflow

# 重置配置文件
cp src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_action_mapping.yaml \
   ~/rpcs_config/

# 检查文件权限
chmod 644 ~/rpcs_config/process_action_mapping.yaml
```

#### 2. Action 调用被拒绝

**症状**: 客户端调用返回 "Goal was rejected"

**诊断步骤**:
```bash
# 检查 Action 服务状态
ros2 action info /Robot1/ExecuteProcessAction

# 验证工艺动作类型
grep -r "SelfCheck" ~/rpcs_config/process_action_mapping.yaml

# 检查服务器日志
ros2 log echo | grep process_action_server
```

**解决方案**:
```bash
# 确认工艺动作类型正确
cat ~/rpcs_config/process_action_mapping.yaml

# 重启服务器
pkill -f process_action_server
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1
```

#### 3. 工艺动作执行失败

**症状**: Action 接受但执行失败

**诊断步骤**:
```bash
# 检查工艺动作树文件
ls -la ~/rpcs_config/process_trees/Robot1_self_check.xml

# 验证 XML 格式
xmllint --noout ~/rpcs_config/process_trees/Robot1_self_check.xml

# 使用 Groot2 调试
groot2  # 连接到 localhost:1670
```

**解决方案**:
```bash
# 修复 XML 文件
xmllint --format ~/rpcs_config/process_trees/Robot1_self_check.xml > /tmp/fixed.xml
mv /tmp/fixed.xml ~/rpcs_config/process_trees/Robot1_self_check.xml

# 重载配置
ros2 service call /Robot1/reload_config std_srvs/srv/Empty
```

### 网络问题排除

#### 1. 节点发现问题

**症状**: 节点无法被发现或通信失败

**诊断**:
```bash
# 检查 ROS_DOMAIN_ID
echo $ROS_DOMAIN_ID

# 检查网络接口
ip addr show

# 测试多播
ping *********
```

**解决方案**:
```bash
# 设置正确的域 ID
export ROS_DOMAIN_ID=42

# 重启网络服务
sudo systemctl restart networking

# 清除 ROS2 缓存
rm -rf ~/.ros/log/*
```

#### 2. 防火墙问题

**诊断**:
```bash
# 检查防火墙状态
sudo ufw status

# 检查端口占用
netstat -tulpn | grep 1670
```

**解决方案**:
```bash
# 开放必要端口
sudo ufw allow 1670:1672/tcp
sudo ufw allow 7400:7500/udp

# 或临时关闭防火墙
sudo ufw disable
```

## 📈 性能分析

### 执行时间分析

#### 1. 创建性能分析脚本
```bash
cat > ~/performance_analysis.py << 'EOF'
#!/usr/bin/env python3

import time
import statistics
import subprocess
import sys

def run_action(robot_id, action_type, iterations=10):
    """运行工艺动作并测量性能"""
    times = []
    success_count = 0
    
    print(f"🚀 性能测试: {robot_id} - {action_type}")
    print(f"📊 测试次数: {iterations}")
    print("-" * 50)
    
    for i in range(iterations):
        print(f"测试 {i+1}/{iterations}...", end=" ")
        
        start_time = time.time()
        
        # 执行工艺动作
        result = subprocess.run([
            'ros2', 'run', 'rpcs_s_behaviors_workflow', 'process_action_client_example',
            robot_id, action_type
        ], capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            times.append(duration)
            success_count += 1
            print(f"✅ {duration:.2f}s")
        else:
            print(f"❌ 失败")
        
        time.sleep(1)  # 间隔
    
    if times:
        print("\n" + "=" * 50)
        print("📈 性能统计:")
        print(f"成功率: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
        print(f"平均时间: {statistics.mean(times):.2f}s")
        print(f"最短时间: {min(times):.2f}s")
        print(f"最长时间: {max(times):.2f}s")
        if len(times) > 1:
            print(f"标准差: {statistics.stdev(times):.2f}s")
    else:
        print("\n❌ 所有测试都失败了")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python3 performance_analysis.py <robot_id> <action_type> [iterations]")
        sys.exit(1)
    
    robot_id = sys.argv[1]
    action_type = sys.argv[2]
    iterations = int(sys.argv[3]) if len(sys.argv) > 3 else 10
    
    run_action(robot_id, action_type, iterations)
EOF

chmod +x ~/performance_analysis.py
```

#### 2. 运行性能分析
```bash
# 分析自检工艺性能
python3 ~/performance_analysis.py Robot1 SelfCheck 10

# 分析拾取送料性能
python3 ~/performance_analysis.py Robot1 PickSendStock 5
```

### 内存使用分析

#### 1. 内存监控脚本
```bash
cat > ~/memory_monitor.sh << 'EOF'
#!/bin/bash

ROBOT_ID=${1:-Robot1}
INTERVAL=${2:-5}
DURATION=${3:-60}

echo "🧠 内存监控 - $ROBOT_ID (间隔: ${INTERVAL}s, 持续: ${DURATION}s)"
echo "时间,RSS(MB),VSZ(MB),CPU%"

END_TIME=$(($(date +%s) + DURATION))

while [ $(date +%s) -lt $END_TIME ]; do
    PID=$(pgrep -f "${ROBOT_ID}_process_action_server")
    
    if [ -n "$PID" ]; then
        STATS=$(ps -p $PID -o rss,vsz,pcpu --no-headers)
        RSS=$(echo $STATS | awk '{print $1/1024}')
        VSZ=$(echo $STATS | awk '{print $2/1024}')
        CPU=$(echo $STATS | awk '{print $3}')
        
        echo "$(date '+%H:%M:%S'),${RSS},${VSZ},${CPU}"
    else
        echo "$(date '+%H:%M:%S'),N/A,N/A,N/A"
    fi
    
    sleep $INTERVAL
done
EOF

chmod +x ~/memory_monitor.sh
```

#### 2. 使用内存监控
```bash
# 监控 Robot1 内存使用 (5秒间隔，持续60秒)
~/memory_monitor.sh Robot1 5 60

# 保存监控结果到文件
~/memory_monitor.sh Robot1 5 300 > memory_usage.csv
```

## 📋 监控仪表板

### 创建简单的监控仪表板

```bash
cat > ~/monitoring_dashboard.py << 'EOF'
#!/usr/bin/env python3

import subprocess
import time
import json
from datetime import datetime

class RPCSMonitor:
    def __init__(self):
        self.robots = ['Robot1', 'Robot2', 'Robot3']
    
    def get_node_status(self, robot_id):
        """检查节点状态"""
        try:
            result = subprocess.run([
                'ros2', 'node', 'list'
            ], capture_output=True, text=True, timeout=5)
            
            node_name = f"/{robot_id}/{robot_id}_process_action_server"
            return node_name in result.stdout
        except:
            return False
    
    def get_action_status(self, robot_id):
        """检查 Action 服务状态"""
        try:
            result = subprocess.run([
                'ros2', 'action', 'list'
            ], capture_output=True, text=True, timeout=5)
            
            action_name = f"/{robot_id}/ExecuteProcessAction"
            return action_name in result.stdout
        except:
            return False
    
    def get_system_stats(self):
        """获取系统统计信息"""
        try:
            # CPU 使用率
            cpu_result = subprocess.run([
                'top', '-bn1'
            ], capture_output=True, text=True, timeout=5)
            
            # 内存使用率
            mem_result = subprocess.run([
                'free', '-m'
            ], capture_output=True, text=True, timeout=5)
            
            return {
                'cpu_info': cpu_result.stdout.split('\n')[2],
                'memory_info': mem_result.stdout.split('\n')[1]
            }
        except:
            return {'cpu_info': 'N/A', 'memory_info': 'N/A'}
    
    def generate_report(self):
        """生成监控报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'robots': {},
            'system': self.get_system_stats()
        }
        
        for robot_id in self.robots:
            report['robots'][robot_id] = {
                'node_status': self.get_node_status(robot_id),
                'action_status': self.get_action_status(robot_id)
            }
        
        return report
    
    def display_status(self):
        """显示状态"""
        report = self.generate_report()
        
        print("\n" + "="*60)
        print(f"🖥️  RPCS 系统监控仪表板 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        # 机器人状态
        print("\n🤖 机器人状态:")
        for robot_id, status in report['robots'].items():
            node_icon = "✅" if status['node_status'] else "❌"
            action_icon = "✅" if status['action_status'] else "❌"
            print(f"  {robot_id}: 节点 {node_icon} | Action {action_icon}")
        
        # 系统状态
        print(f"\n💻 系统状态:")
        print(f"  CPU: {report['system']['cpu_info']}")
        print(f"  内存: {report['system']['memory_info']}")
        
        return report
    
    def run_continuous(self, interval=30):
        """持续监控"""
        print("🚀 启动持续监控模式 (Ctrl+C 退出)")
        
        try:
            while True:
                self.display_status()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")

if __name__ == "__main__":
    import sys
    
    monitor = RPCSMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
        monitor.run_continuous(interval)
    else:
        monitor.display_status()
EOF

chmod +x ~/monitoring_dashboard.py
```

### 使用监控仪表板

```bash
# 单次状态检查
python3 ~/monitoring_dashboard.py

# 持续监控 (30秒间隔)
python3 ~/monitoring_dashboard.py --continuous 30

# 持续监控 (10秒间隔)
python3 ~/monitoring_dashboard.py --continuous 10
```

## 🔧 高级调试技巧

### 1. 使用 GDB 调试 C++ 代码

```bash
# 编译 Debug 版本
cd ~/rpcs_ws
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Debug

# 使用 GDB 启动
gdb --args ros2 run rpcs_s_behaviors_workflow process_action_server_main Robot1

# GDB 命令
# (gdb) run
# (gdb) bt  # 查看调用栈
# (gdb) info threads  # 查看线程
```

### 2. 使用 Valgrind 检测内存问题

```bash
# 安装 Valgrind
sudo apt install valgrind

# 使用 Valgrind 检测内存泄漏
valgrind --leak-check=full --show-leak-kinds=all \
    ros2 run rpcs_s_behaviors_workflow process_action_server_main Robot1
```

### 3. 使用 strace 跟踪系统调用

```bash
# 跟踪系统调用
strace -p $(pgrep -f process_action_server) -o trace.log

# 分析 trace 日志
grep -E "(open|read|write|close)" trace.log
```

---

🎯 **通过本指南，您可以全面掌握 RPCS 系统的监控和调试技能，快速定位和解决问题！** 