# 基础操作指南

本指南介绍 RPCS 工艺动作树系统的日常使用方法和基本操作。

## 🎯 系统启动与关闭

### 启动系统

#### 1. 直接启动 (当前可用方式)
```bash
# 启动单个机器人的工艺动作服务器
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1

# 启动不同机器人
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot2
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot3

# 自定义配置启动
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args \
    -p robot_id:=Robot1 \
    -p config_file:=/path/to/config.yaml \
    -p tree_file_path:=/path/to/trees \
    -p groot2_port:=1670
```

#### 2. Launch 文件启动 (如果存在)
```bash
# 启动单个机器人
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1

# 自定义配置启动
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 \
    config_file:=/path/to/custom_config.yaml \
    tree_file_path:=/path/to/trees \
    groot2_port:=1670
```

#### 3. 启动多个机器人
```bash
# 在不同终端中启动多个机器人
# 终端1
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1

# 终端2  
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot2

# 终端3
ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot3

# 或使用 Launch 文件 (如果存在)
ros2 launch rpcs_s_behaviors_workflow process_action_servers.launch.py
```

#### 4. 后台启动
```bash
# 使用 nohup 后台运行
nohup ros2 run RPCS_S_Behaviors_workflow process_action_server --ros-args -p robot_id:=Robot1 > ~/rpcs_logs/Robot1.log 2>&1 &

# 查看后台进程
ps aux | grep process_action_server

# 使用 systemd 服务 (高级)
sudo systemctl start rpcs-Robot1.service
```

#### 5. 启动验证
```bash
# 检查服务器是否启动成功
ros2 action list | grep ExecuteProcessAction

# 检查节点状态
ros2 node list | grep process_action_server

# 检查接口
ros2 interface show rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction
```

### 关闭系统

```bash
# 优雅关闭 (推荐)
Ctrl+C

# 强制关闭进程
pkill -f process_action_server

# 关闭所有相关进程
pkill -f rpcs_s_behaviors_workflow
```

## 🎮 工艺动作执行

### 命令行执行

#### 1. 基本语法
```bash
ros2 action send_goal /<robot_id>/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: '<action_type>', robot_id: '<robot_id>', timeout_seconds: <timeout>}"
```

#### 2. 常用示例
```bash
# 自检工艺
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'SelfCheck', robot_id: 'Robot1', timeout_seconds: 60}"

# 拾取送料工艺
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'PickSendStock', robot_id: 'Robot1', timeout_seconds: 120}"

# 带参数的工艺动作
ros2 action send_goal /Robot2/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'PlaceProduct', robot_id: 'Robot2', process_parameters: ['PRODUCT_001', 'STATION_A'], timeout_seconds: 180, process_id: 'TASK_001'}"
```

### 客户端工具执行

#### 1. C++ 客户端 (开发中)
```bash
# 注意：process_action_client_example 当前还未实现
# 可以通过以下方式检查客户端是否可用：
ls -la install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/process_action_client_example

# 如果文件存在，使用方法：
# ros2 run RPCS_S_Behaviors_workflow process_action_client_example <robot_id> <action_type>
```

#### 2. Python 客户端 (开发中)
```bash
# 注意：Python 客户端脚本当前还未创建
# 可以通过以下方式检查是否存在：
ls -la src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/scripts/process_action_client.py

# 如果文件存在，使用方法：
# python3 scripts/process_action_client.py <robot_id> <action_type> [process_id] [timeout]
```

#### 3. 当前推荐的调用方式
由于客户端工具还在开发中，建议使用以下方式调用工艺动作：

```bash
# 使用 ros2 action 命令行工具 (推荐)
ros2 action send_goal /<robot_id>/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: '<action_type>', robot_id: '<robot_id>', timeout_seconds: <timeout>}"

# 带反馈的调用
ros2 action send_goal /<robot_id>/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: '<action_type>', robot_id: '<robot_id>', timeout_seconds: <timeout>}" \
    --feedback
```

### 批量执行

#### 1. 创建批量执行脚本
```bash
cat > ~/batch_execute.sh << 'EOF'
#!/bin/bash

ROBOT_ID=$1
ACTIONS=("SelfCheck" "PickSendStock" "PlaceProduct")

for action in "${ACTIONS[@]}"; do
    echo "执行工艺动作: $action"
    ros2 run rpcs_s_behaviors_workflow process_action_client_example $ROBOT_ID $action
    
    if [ $? -eq 0 ]; then
        echo "✅ $action 执行成功"
    else
        echo "❌ $action 执行失败"
        break
    fi
    
    sleep 2
done
EOF

chmod +x ~/batch_execute.sh
```

#### 2. 运行批量执行
```bash
# 执行 Robot1 的所有工艺动作
~/batch_execute.sh Robot1
```

## 📊 状态监控

### 实时监控

#### 1. 监控反馈信息
```bash
# 监控特定机器人的反馈
ros2 topic echo /Robot1/ExecuteProcessAction/_action/feedback

# 监控所有机器人的反馈
ros2 topic echo /*/ExecuteProcessAction/_action/feedback
```

#### 2. 监控执行结果
```bash
# 监控执行结果
ros2 topic echo /Robot1/ExecuteProcessAction/_action/result

# 监控状态变化
ros2 topic echo /Robot1/ExecuteProcessAction/_action/status
```

#### 3. 监控系统状态
```bash
# 查看活跃的节点
ros2 node list | grep process_action_server

# 查看节点信息
ros2 node info /Robot1/Robot1_process_action_server

# 查看话题列表
ros2 topic list | grep ExecuteProcessAction
```

### 日志查看

#### 1. 实时日志
```bash
# 查看实时日志
ros2 log echo

# 过滤特定节点日志
ros2 log echo | grep Robot1_process_action_server
```

#### 2. 历史日志
```bash
# 查看日志文件 (如果配置了文件输出)
tail -f ~/rpcs_config/logs/rpcs.log

# 查看系统日志
journalctl -u rpcs-Robot1.service -f
```

## 🔧 任务管理

### 任务取消

#### 1. 取消当前任务
```bash
# 获取当前任务ID
ros2 action list -t

# 取消特定任务
ros2 action send_goal --cancel /Robot1/ExecuteProcessAction
```

#### 2. 抢占式执行
```bash
# 强制执行新任务 (抢占当前任务)
ros2 action send_goal /Robot1/ExecuteProcessAction \
    rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \
    "{process_action_type: 'SelfCheck', robot_id: 'Robot1', preempt_current: true}"
```

### 任务队列管理

#### 1. 查看任务状态
```bash
# 查看 Action 服务状态
ros2 action info /Robot1/ExecuteProcessAction

# 查看当前执行状态
ros2 service call /Robot1/get_status std_srvs/srv/Empty
```

#### 2. 任务调度脚本
```bash
cat > ~/task_scheduler.sh << 'EOF'
#!/bin/bash

ROBOT_ID=$1
shift
TASKS=("$@")

echo "开始任务调度，机器人: $ROBOT_ID"
echo "任务列表: ${TASKS[*]}"

for task in "${TASKS[@]}"; do
    echo "正在执行: $task"
    
    # 等待机器人空闲
    while ros2 topic echo /Robot1/ExecuteProcessAction/_action/status --once | grep -q "ACTIVE"; do
        echo "等待机器人空闲..."
        sleep 1
    done
    
    # 执行任务
    ros2 run rpcs_s_behaviors_workflow process_action_client_example $ROBOT_ID $task
    
    echo "任务 $task 完成"
done

echo "所有任务执行完成"
EOF

chmod +x ~/task_scheduler.sh
```

#### 3. 使用任务调度
```bash
# 调度多个任务
~/task_scheduler.sh Robot1 SelfCheck PickSendStock PlaceProduct
```

## 🎛️ 参数配置

### 运行时参数调整

#### 1. 查看当前参数
```bash
# 查看节点参数
ros2 param list /Robot1/Robot1_process_action_server

# 获取特定参数值
ros2 param get /Robot1/Robot1_process_action_server default_timeout
```

#### 2. 动态修改参数
```bash
# 修改默认超时时间
ros2 param set /Robot1/Robot1_process_action_server default_timeout 600

# 修改最大并发数
ros2 param set /Robot1/Robot1_process_action_server max_concurrent_actions 3
```

### 配置文件热重载

#### 1. 重载配置文件
```bash
# 发送重载信号 (需要系统支持)
ros2 service call /Robot1/reload_config std_srvs/srv/Empty

# 或者重启服务
sudo systemctl reload rpcs-Robot1.service
```

## 🔍 调试工具

### 命令行调试

#### 1. 详细日志模式
```bash
# 启动时开启详细日志
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 \
    --ros-args --log-level DEBUG
```

#### 2. 网络诊断
```bash
# ROS2 网络诊断
ros2 doctor

# 检查节点通信
ros2 topic hz /Robot1/ExecuteProcessAction/_action/feedback

# 测试 Action 连接
ros2 action info /Robot1/ExecuteProcessAction
```

### 可视化调试

#### 1. Groot2 可视化
```bash
# 启动 Groot2
groot2

# 连接到机器人
# Robot1: localhost:1670
# Robot2: localhost:1671
# Robot3: localhost:1672
```

#### 2. RQT 工具
```bash
# 启动 RQT
rqt

# 使用 Action 插件监控
# Plugins -> Actions -> Action Type Browser
```

## 📈 性能监控

### 系统性能

#### 1. 资源使用监控
```bash
# CPU 和内存使用
top -p $(pgrep -f process_action_server)

# 网络流量监控
iftop -i eth0

# 磁盘I/O监控
iotop
```

#### 2. ROS2 性能监控
```bash
# 话题频率监控
ros2 topic hz /Robot1/ExecuteProcessAction/_action/feedback

# 话题带宽监控
ros2 topic bw /Robot1/ExecuteProcessAction/_action/feedback

# 延迟测试
ros2 topic delay /Robot1/ExecuteProcessAction/_action/feedback
```

### 性能基准测试

#### 1. 执行时间测试
```bash
cat > ~/performance_test.sh << 'EOF'
#!/bin/bash

ROBOT_ID=$1
ACTION_TYPE=$2
ITERATIONS=${3:-10}

echo "性能测试: $ROBOT_ID - $ACTION_TYPE ($ITERATIONS 次)"

total_time=0
success_count=0

for i in $(seq 1 $ITERATIONS); do
    echo "测试 $i/$ITERATIONS"
    
    start_time=$(date +%s.%N)
    if ros2 run rpcs_s_behaviors_workflow process_action_client_example $ROBOT_ID $ACTION_TYPE; then
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        total_time=$(echo "$total_time + $duration" | bc)
        success_count=$((success_count + 1))
        echo "执行时间: ${duration}s"
    else
        echo "执行失败"
    fi
    
    sleep 2
done

if [ $success_count -gt 0 ]; then
    avg_time=$(echo "scale=2; $total_time / $success_count" | bc)
    success_rate=$(echo "scale=2; $success_count * 100 / $ITERATIONS" | bc)
    
    echo "===================="
    echo "测试结果:"
    echo "成功率: ${success_rate}%"
    echo "平均执行时间: ${avg_time}s"
    echo "总执行时间: ${total_time}s"
    echo "===================="
else
    echo "所有测试都失败了"
fi
EOF

chmod +x ~/performance_test.sh
```

#### 2. 运行性能测试
```bash
# 测试自检工艺性能
~/performance_test.sh Robot1 SelfCheck 10

# 测试拾取送料性能
~/performance_test.sh Robot1 PickSendStock 5
```

## 🚦 故障处理

### 常见问题处理

#### 1. 服务器无响应
```bash
# 检查进程状态
ps aux | grep process_action_server

# 重启服务
sudo systemctl restart rpcs-Robot1.service

# 或者手动重启
pkill -f process_action_server
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py robot_id:=Robot1
```

#### 2. 工艺动作执行失败
```bash
# 检查配置文件
cat ~/rpcs_config/process_action_mapping.yaml

# 检查工艺动作树文件
ls -la ~/rpcs_config/process_trees/

# 验证文件语法
xmllint --noout ~/rpcs_config/process_trees/Robot1_self_check.xml
```

#### 3. 网络通信问题
```bash
# 检查 ROS2 域设置
echo $ROS_DOMAIN_ID

# 测试节点发现
ros2 node list

# 重置网络
sudo systemctl restart networking
```

## 📝 操作记录

### 创建操作日志
```bash
# 创建操作记录脚本
cat > ~/log_operation.sh << 'EOF'
#!/bin/bash

OPERATION="$1"
ROBOT_ID="$2"
RESULT="$3"

TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
LOG_FILE="~/rpcs_config/logs/operations.log"

echo "[$TIMESTAMP] $ROBOT_ID - $OPERATION - $RESULT" >> $LOG_FILE
EOF

chmod +x ~/log_operation.sh
```

### 查看操作历史
```bash
# 查看最近的操作
tail -20 ~/rpcs_config/logs/operations.log

# 搜索特定机器人的操作
grep "Robot1" ~/rpcs_config/logs/operations.log

# 统计操作成功率
grep -c "SUCCESS" ~/rpcs_config/logs/operations.log
```

---

🎯 **掌握这些基础操作，您就能熟练使用 RPCS 工艺动作树系统了！** 如需了解更高级的功能，请参考其他专题指南。 