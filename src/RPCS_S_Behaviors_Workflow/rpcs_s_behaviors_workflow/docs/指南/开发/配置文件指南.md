# 配置文件指南

本指南详细介绍 RPCS 工艺动作树系统的配置文件结构和自定义方法。

## 📁 配置文件概览

### 主要配置文件

```
~/rpcs_config/
├── process_action_mapping.yaml    # 工艺动作映射配置 (核心)
├── system_config.yaml             # 系统配置 (可选)
├── logging.conf                   # 日志配置
├── process_trees/                 # 工艺动作树目录
│   ├── Robot1_*.xml              # Robot1 工艺动作树
│   ├── Robot2_*.xml              # Robot2 工艺动作树
│   └── Robot3_*.xml              # Robot3 工艺动作树
├── logs/                          # 日志文件目录
└── backup/                        # 配置备份目录
```

## 🎯 工艺动作映射配置

### 基本结构

`process_action_mapping.yaml` 是系统的核心配置文件，定义了工艺动作类型到具体XML文件的映射关系。

```yaml
# 工艺动作类型到工艺动作树文件的映射配置
Robot1:
  SelfCheck: "Robot1_self_check.xml"
  PickSendStock: "Robot1_pick_send_stock.xml"
  StackLightBoards: "Robot1_stack_light_boards.xml"
  RemoveReleaseFilm: "Robot1_remove_release_film.xml"
  BondCarrier: "Robot1_bond_carrier.xml"

Robot2:
  SelfCheck: "robot2_self_check.xml"
  PickSendStock: "Robot2_pick_send_stock.xml"
  MoveWIP: "Robot2_move_wip.xml"
  PlaceWIP: "Robot2_place_wip.xml"
  PlacePCBA: "Robot2_place_pcba.xml"
  DoPCAdapterPaste: "Robot2_pc_adapter_paste.xml"
  OpenFPC: "Robot2_open_fpc.xml"
  TransferFixture: "Robot2_transfer_fixture.xml"
  PlaceProduct: "Robot2_place_product.xml"

Robot3:
  SelfCheck: "robot3_self_check.xml"
  Route: "Robot3_route.xml"
  PickSendStock: "Robot3_pick_send_stock.xml"
  ScrewPCBA: "Robot3_screw_pcba.xml"
  ConnectFPC: "Robot3_connect_fpc.xml"
  FastenProCase: "Robot3_fasten_pro_case.xml"
```

### 配置字段说明

| 字段层级 | 说明 | 示例 |
|---------|------|------|
| `Robot1/2/3` | 机器人ID，作为顶级键 | `Robot1` |
| `工艺动作类型` | 标准化的工艺动作名称 | `SelfCheck` |
| `XML文件名` | 对应的工艺动作树文件 | `Robot1_self_check.xml` |

## ⚙️ 系统配置

### 创建系统配置文件

```bash
cat > ~/rpcs_config/system_config.yaml << 'EOF'
# RPCS 系统配置文件

# 网络配置
network:
  ros_domain_id: 42
  rmw_implementation: "rmw_cyclonedx_cpp"
  discovery_timeout: 30
  
# 性能配置
performance:
  max_concurrent_actions: 2
  default_timeout: 300
  tick_frequency: 10  # Hz
  memory_limit: 1024  # MB

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARN, ERROR
  file_output: true
  console_output: true
  max_file_size: 100  # MB
  max_files: 10

# Groot2 配置
groot2:
  enabled: true
  base_port: 1670
  port_increment: 1

# 机器人特定配置
robots:
  Robot1:
    groot2_port: 1670
    max_concurrent_actions: 2
    default_timeout: 300
    
  Robot2:
    groot2_port: 1671
    max_concurrent_actions: 3
    default_timeout: 600
    
  Robot3:
    groot2_port: 1672
    max_concurrent_actions: 2
    default_timeout: 450

# 安全配置
security:
  enable_authentication: false
  max_retry_attempts: 3
  blacklist_timeout: 300  # 秒

# 监控配置
monitoring:
  enable_metrics: true
  metrics_port: 8080
  health_check_interval: 30  # 秒
EOF
```

### 使用系统配置

在启动时指定系统配置文件：

```bash
ros2 launch rpcs_s_behaviors_workflow single_robot_process_action_server.launch.py \
    robot_id:=Robot1 \
    config_file:=~/rpcs_config/process_action_mapping.yaml \
    system_config:=~/rpcs_config/system_config.yaml
```

## 📋 配置验证

### 验证配置文件格式

```bash
# 使用 yamllint 验证 YAML 格式
yamllint ~/rpcs_config/process_action_mapping.yaml

# 验证 XML 文件格式
xmllint --noout ~/rpcs_config/process_trees/*.xml
```

### 配置文件完整性检查

```bash
# 检查配置文件和对应的 XML 文件是否匹配
python3 -c "
import yaml
import os

config_file = '~/rpcs_config/process_action_mapping.yaml'
trees_dir = '~/rpcs_config/process_trees'

with open(config_file) as f:
    config = yaml.safe_load(f)

for robot_id, actions in config.items():
    for action_type, xml_file in actions.items():
        xml_path = os.path.join(trees_dir, xml_file)
        if os.path.exists(xml_path):
            print(f'✅ {robot_id}/{action_type}: {xml_file}')
        else:
            print(f'❌ {robot_id}/{action_type}: {xml_file} 文件不存在')
"
```

## 🔄 配置管理

### 添加新的工艺动作类型

1. **编辑配置文件**
```bash
nano ~/rpcs_config/process_action_mapping.yaml
```

2. **添加新的映射**
```yaml
Robot1:
  # 现有配置...
  NewProcessAction: "Robot1_new_process.xml"
```

3. **创建对应的XML文件**
```bash
cp ~/rpcs_config/process_trees/Robot1_self_check.xml \
   ~/rpcs_config/process_trees/Robot1_new_process.xml
```

### 添加新机器人

1. **在配置文件中添加新机器人部分**
```yaml
robot4:
  SelfCheck: "robot4_self_check.xml"
  PickSendStock: "robot4_pick_send_stock.xml"
```

2. **创建对应的工艺动作树文件**
```bash
cp ~/rpcs_config/process_trees/Robot1_self_check.xml \
   ~/rpcs_config/process_trees/robot4_self_check.xml
```

## 🛡️ 配置安全

### 设置文件权限

```bash
# 设置适当的文件权限
chmod 644 ~/rpcs_config/process_action_mapping.yaml
chmod 644 ~/rpcs_config/system_config.yaml
chmod -R 644 ~/rpcs_config/process_trees/*.xml

# 设置目录权限
chmod 755 ~/rpcs_config
chmod 755 ~/rpcs_config/process_trees
```

### 配置备份

```bash
# 创建配置备份
cp ~/rpcs_config/process_action_mapping.yaml \
   ~/rpcs_config/backup/mapping_$(date +%Y%m%d_%H%M%S).yaml

# 自动备份脚本
cat > ~/backup_config.sh << 'EOF'
#!/bin/bash
BACKUP_DIR=~/rpcs_config/backup/$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
cp ~/rpcs_config/*.yaml $BACKUP_DIR/
cp -r ~/rpcs_config/process_trees $BACKUP_DIR/
echo "配置已备份到: $BACKUP_DIR"
EOF

chmod +x ~/backup_config.sh
```

## 📊 配置监控

### 监控配置文件变更

```bash
# 使用 inotify 监控配置文件变更
inotifywait -m ~/rpcs_config/process_action_mapping.yaml -e modify | \
while read path action file; do
    echo "配置文件已修改: $file"
    # 可以在这里添加验证和重载逻辑
done
```

## 🔧 高级配置

### 环境相关配置

```yaml
# 开发环境配置
development:
  debug_mode: true
  log_level: "DEBUG"
  enable_simulation: true

# 生产环境配置  
production:
  debug_mode: false
  log_level: "INFO"
  enable_simulation: false
  
# 测试环境配置
testing:
  debug_mode: true
  log_level: "WARN"
  enable_simulation: true
```

### 条件配置

```yaml
# 根据机器人类型的条件配置
robot_types:
  assembly_robot:
    default_timeout: 300
    max_retries: 3
    
  inspection_robot:
    default_timeout: 600
    max_retries: 1
    
  transport_robot:
    default_timeout: 120
    max_retries: 5
```

---

🎯 **通过本指南，您可以完全掌握 RPCS 系统的配置管理，实现灵活的工艺动作定制！** 