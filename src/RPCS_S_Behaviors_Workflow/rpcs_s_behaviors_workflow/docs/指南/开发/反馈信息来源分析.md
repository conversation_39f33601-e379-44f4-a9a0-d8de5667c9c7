# ProcessActionServer 反馈信息来源分析

## 📍 反馈信息生成位置

反馈信息在 `ProcessActionServer::publish_feedback()` 函数中生成：

**文件位置**: `src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/src/process_action_server.cpp`
**函数位置**: 第419-432行

```cpp
void ProcessActionServer::publish_feedback(
    const std::shared_ptr<GoalHandleExecuteProcessAction> goal_handle,
    const std::string& current_step,
    const std::string& current_operation,
    double progress_percent,
    const std::string& status_message)
{
    auto feedback = std::make_shared<ExecuteProcessAction::Feedback>();
    feedback->current_process_step = current_step;
    feedback->current_operation = current_operation;
    feedback->progress_percent = progress_percent;
    feedback->status_message = status_message;
    feedback->current_status = "RUNNING";  // 固定值
    feedback->timestamp = this->now();     // ROS2节点时间戳
    
    goal_handle->publish_feedback(feedback);
}
```

## 🔍 各字段详细来源分析

### 1. `current_process_step` (当前工艺步骤)
**来源**: 函数调用时的参数传入
**调用位置**:
- 第315行: `"Initializing"` - 初始化阶段
- 第329行: `"Tree Loaded"` - 树加载完成
- 第350行: `"Executing"` - 执行阶段
- 第385行: `"Completed"` - 完成阶段

### 2. `current_operation` (当前操作描述)
**来源**: 函数调用时的参数传入
**具体内容**:
- `"Loading process tree"` - 加载工艺树
- `"Executing process tree"` - 执行工艺树
- `"Running behavior tree tick X"` - 执行行为树tick (X为tick计数)
- `"Process finished successfully"` - 流程成功完成

### 3. `progress_percent` (执行进度)
**来源**: 基于tick计数的计算
**计算逻辑**:
```cpp
// 第346行
double progress = 10.0 + (static_cast<double>(tick_count) / max_ticks) * 80.0;
```
- 初始化: `0.0%`
- 树加载: `10.0%`
- 执行过程: `10.0% + (tick_count/max_ticks) * 80.0%`
- 完成: `100.0%`

### 4. `status_message` (状态描述)
**来源**: 函数调用时的参数传入
**具体内容**:
- `"Starting process execution"` - 开始执行
- `"Process tree loaded successfully"` - 树加载成功
- `"Process in progress"` - 执行中
- `"Success"` - 成功完成

### 5. `current_status` (当前状态)
**来源**: 固定值 `"RUNNING"`
**位置**: 第428行
**说明**: 在执行过程中始终为"RUNNING"，只有在最终结果中才会变为其他状态

### 6. `timestamp` (时间戳)
**来源**: ROS2节点的当前时间
**生成方式**: `this->now()` - ROS2的内置时间函数
**位置**: 第429行

## 📈 反馈信息发送时机

### 执行流程中的反馈发送点:

1. **初始化阶段** (第315行)
   ```cpp
   publish_feedback(goal_handle, "Initializing", "Loading process tree", 0.0, "Starting process execution");
   ```

2. **树加载完成** (第329行)
   ```cpp
   publish_feedback(goal_handle, "Tree Loaded", "Executing process tree", 10.0, "Process tree loaded successfully");
   ```

3. **执行循环中** (第350行) - 每100ms发送一次
   ```cpp
   publish_feedback(goal_handle, current_step, current_operation, progress, "Process in progress");
   ```

4. **成功完成** (第385行)
   ```cpp
   publish_feedback(goal_handle, "Completed", "Process finished successfully", 100.0, "Success");
   ```

## 🔄 反馈发送频率

- **固定频率**: 每100ms发送一次 (第354行: `std::this_thread::sleep_for(std::chrono::milliseconds(100))`)
- **总反馈数量**: 取决于执行时间和超时设置
  - 默认超时: 300秒 → 最大3000次反馈
  - 实际执行: 约4-5秒 → 约40-50次反馈

## 🎯 行为树执行与反馈的关系

### 行为树执行循环 (第335-355行):
```cpp
while (rclcpp::ok() && tree_status == BT::NodeStatus::RUNNING && tick_count < max_ticks) {
    // 1. 检查取消请求
    // 2. 执行行为树tick
    tree_status = current_process_tree_->tickOnce();
    // 3. 计算进度
    // 4. 发送反馈
    // 5. 增加计数和延时
}
```

### 重要说明:
- **反馈信息与行为树节点无直接关系**: 反馈是基于整体执行进度，不是基于具体的行为树节点状态
- **PubPrintMessage消息不会出现在反馈中**: 这些是行为树内部的输出，不会传递到Action反馈
- **进度计算是估算的**: 基于tick计数而非实际工艺完成度

## 📝 总结

反馈信息完全由 `ProcessActionServer` 生成，包含：
- **执行阶段信息**: 基于代码逻辑的固定步骤
- **进度信息**: 基于时间和tick计数的估算
- **操作描述**: 描述当前正在执行的操作
- **时间戳**: ROS2系统时间

这些反馈信息与XML中的具体消息内容（如PubPrintMessage的内容）是分离的，主要用于监控整体执行状态而非具体工艺细节。 