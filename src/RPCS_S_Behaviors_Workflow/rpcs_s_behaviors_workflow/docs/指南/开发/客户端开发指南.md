# 客户端开发指南

本指南介绍如何开发自定义客户端应用来调用 RPCS 工艺动作树系统的服务。

## 🎯 客户端开发概览

### 支持的开发语言

- **C++**: 高性能，适合实时控制应用
- **Python**: 快速开发，适合原型和脚本
- **命令行**: 简单测试和调试

### Action 接口定义

```
rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction

Goal:
  - process_action_type: string    # 工艺动作类型
  - robot_id: string              # 机器人ID
  - process_parameters: string[]   # 工艺参数
  - timeout_seconds: int32        # 超时时间
  - preempt_current: bool         # 是否抢占当前任务
  - process_id: string            # 工艺流程ID

Result:
  - success: bool                 # 执行成功状态
  - result_message: string        # 结果描述
  - final_status: string          # 最终状态
  - execution_time: float64       # 执行时间
  - process_output_data: string[] # 工艺输出数据
  - quality_status: string        # 质量状态

Feedback:
  - current_process_step: string  # 当前工艺步骤
  - current_status: string        # 当前状态
  - progress_percent: float64     # 进度百分比
  - status_message: string        # 状态描述
  - current_operation: string     # 当前操作描述
```

## 🔧 C++ 客户端开发

### 基本 C++ 客户端

```cpp
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction.hpp>

class ProcessActionClient : public rclcpp::Node
{
public:
    using ExecuteProcessAction = rpcs_s_interfaces_behavior_tree::action::ExecuteProcessAction;
    using GoalHandle = rclcpp_action::ClientGoalHandle<ExecuteProcessAction>;

    ProcessActionClient(const std::string& robot_id) 
        : Node("process_action_client"), robot_id_(robot_id)
    {
        // 创建 Action 客户端
        std::string action_name = "/" + robot_id + "/ExecuteProcessAction";
        client_ = rclcpp_action::create_client<ExecuteProcessAction>(this, action_name);
        
        RCLCPP_INFO(this->get_logger(), "客户端已初始化，目标机器人: %s", robot_id.c_str());
    }

    bool ExecuteProcessAction(const std::string& action_type, 
                               const std::vector<std::string>& parameters = {},
                               int timeout_seconds = 300)
    {
        // 等待服务可用
        if (!client_->wait_for_action_server(std::chrono::seconds(10))) {
            RCLCPP_ERROR(this->get_logger(), "Action 服务不可用");
            return false;
        }

        // 创建目标
        auto goal = ExecuteProcessAction::Goal();
        goal.process_action_type = action_type;
        goal.robot_id = robot_id_;
        goal.process_parameters = parameters;
        goal.timeout_seconds = timeout_seconds;

        // 设置回调函数
        auto send_goal_options = rclcpp_action::Client<ExecuteProcessAction>::SendGoalOptions();
        
        send_goal_options.goal_response_callback = 
            [this](std::shared_ptr<GoalHandle> goal_handle) {
                if (!goal_handle) {
                    RCLCPP_ERROR(this->get_logger(), "目标被服务器拒绝");
                } else {
                    RCLCPP_INFO(this->get_logger(), "目标被服务器接受");
                }
            };

        send_goal_options.feedback_callback = 
            [this](std::shared_ptr<GoalHandle> goal_handle, 
                   const std::shared_ptr<const ExecuteProcessAction::Feedback> feedback) {
                RCLCPP_INFO(this->get_logger(), 
                           "进度: %.1f%% - %s", 
                           feedback->progress_percent,
                           feedback->status_message.c_str());
            };

        send_goal_options.result_callback = 
            [this](const GoalHandle::WrappedResult& result) {
                switch (result.code) {
                    case rclcpp_action::ResultCode::SUCCEEDED:
                        RCLCPP_INFO(this->get_logger(), "执行成功: %s", 
                                   result.result->result_message.c_str());
                        result_received_ = true;
                        success_ = result.result->success;
                        break;
                    case rclcpp_action::ResultCode::ABORTED:
                        RCLCPP_ERROR(this->get_logger(), "执行被中止");
                        result_received_ = true;
                        success_ = false;
                        break;
                    case rclcpp_action::ResultCode::CANCELED:
                        RCLCPP_WARN(this->get_logger(), "执行被取消");
                        result_received_ = true;
                        success_ = false;
                        break;
                    default:
                        RCLCPP_ERROR(this->get_logger(), "未知结果代码");
                        result_received_ = true;
                        success_ = false;
                        break;
                }
            };

        // 发送目标
        RCLCPP_INFO(this->get_logger(), "发送工艺动作: %s", action_type.c_str());
        auto goal_handle_future = client_->async_send_goal(goal, send_goal_options);

        // 等待结果
        result_received_ = false;
        while (rclcpp::ok() && !result_received_) {
            rclcpp::spin_some(this->shared_from_this());
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        return success_;
    }

private:
    std::string robot_id_;
    rclcpp_action::Client<ExecuteProcessAction>::SharedPtr client_;
    bool result_received_ = false;
    bool success_ = false;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);

    if (argc < 3) {
        std::cout << "用法: " << argv[0] << " <robot_id> <action_type> [param1] [param2] ..." << std::endl;
        return 1;
    }

    std::string robot_id = argv[1];
    std::string action_type = argv[2];
    
    std::vector<std::string> parameters;
    for (int i = 3; i < argc; ++i) {
        parameters.push_back(argv[i]);
    }

    auto client = std::make_shared<ProcessActionClient>(robot_id);
    
    bool success = client->ExecuteProcessAction(action_type, parameters);
    
    rclcpp::shutdown();
    return success ? 0 : 1;
}
```

### 编译 C++ 客户端

```cmake
# CMakeLists.txt
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(rpcs_s_interfaces_behavior_tree REQUIRED)

add_executable(my_process_client src/my_process_client.cpp)
ament_target_dependencies(my_process_client 
    rclcpp 
    rclcpp_action 
    rpcs_s_interfaces_behavior_tree)

install(TARGETS my_process_client DESTINATION lib/${PROJECT_NAME})
```

## 🐍 Python 客户端开发

### 基本 Python 客户端

```python
#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import sys
import time

class ProcessActionClient(Node):
    def __init__(self, robot_id):
        super().__init__('process_action_client')
        self.robot_id = robot_id
        
        # 创建 Action 客户端
        action_name = f'/{robot_id}/ExecuteProcessAction'
        self.client = ActionClient(self, ExecuteProcessAction, action_name)
        
        self.get_logger().info(f'客户端已初始化，目标机器人: {robot_id}')

    def ExecuteProcessAction(self, action_type, parameters=None, timeout_seconds=300):
        """执行工艺动作"""
        
        # 等待服务可用
        if not self.client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error('Action 服务不可用')
            return False

        # 创建目标
        goal = ExecuteProcessAction.Goal()
        goal.process_action_type = action_type
        goal.robot_id = self.robot_id
        goal.process_parameters = parameters or []
        goal.timeout_seconds = timeout_seconds

        self.get_logger().info(f'发送工艺动作: {action_type}')

        # 发送目标
        future = self.client.send_goal_async(
            goal, 
            feedback_callback=self.feedback_callback
        )
        
        rclpy.spin_until_future_complete(self, future)
        goal_handle = future.result()

        if not goal_handle.accepted:
            self.get_logger().error('目标被服务器拒绝')
            return False

        self.get_logger().info('目标被服务器接受')

        # 等待结果
        result_future = goal_handle.get_result_async()
        rclpy.spin_until_future_complete(self, result_future)
        result = result_future.result()

        if result.status == 4:  # SUCCEEDED
            self.get_logger().info(f'执行成功: {result.result.result_message}')
            return result.result.success
        else:
            self.get_logger().error(f'执行失败，状态码: {result.status}')
            return False

    def feedback_callback(self, feedback_msg):
        """反馈回调函数"""
        feedback = feedback_msg.feedback
        self.get_logger().info(
            f'进度: {feedback.progress_percent:.1f}% - {feedback.status_message}'
        )

def main():
    rclpy.init()

    if len(sys.argv) < 3:
        print(f"用法: {sys.argv[0]} <robot_id> <action_type> [param1] [param2] ...")
        return 1

    robot_id = sys.argv[1]
    action_type = sys.argv[2]
    parameters = sys.argv[3:] if len(sys.argv) > 3 else []

    client = ProcessActionClient(robot_id)
    
    try:
        success = client.ExecuteProcessAction(action_type, parameters)
        return 0 if success else 1
    except KeyboardInterrupt:
        client.get_logger().info('客户端被中断')
        return 1
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    sys.exit(main())
```

### 异步 Python 客户端

```python
#!/usr/bin/env python3

import asyncio
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction

class AsyncProcessActionClient(Node):
    def __init__(self, robot_id):
        super().__init__('async_process_action_client')
        self.robot_id = robot_id
        
        action_name = f'/{robot_id}/ExecuteProcessAction'
        self.client = ActionClient(self, ExecuteProcessAction, action_name)

    async def ExecuteProcessAction_async(self, action_type, parameters=None):
        """异步执行工艺动作"""
        
        # 等待服务可用
        if not self.client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error('Action 服务不可用')
            return False

        # 创建目标
        goal = ExecuteProcessAction.Goal()
        goal.process_action_type = action_type
        goal.robot_id = self.robot_id
        goal.process_parameters = parameters or []

        # 异步发送目标
        future = self.client.send_goal_async(goal)
        goal_handle = await self._wait_for_future(future)

        if not goal_handle.accepted:
            return False

        # 异步等待结果
        result_future = goal_handle.get_result_async()
        result = await self._wait_for_future(result_future)

        return result.result.success

    async def _wait_for_future(self, future):
        """等待 future 完成"""
        while not future.done():
            rclpy.spin_once(self, timeout_sec=0.1)
            await asyncio.sleep(0.01)
        return future.result()

    async def execute_multiple_actions(self, action_list):
        """并行执行多个工艺动作"""
        tasks = []
        for action_type, parameters in action_list:
            task = asyncio.create_task(
                self.ExecuteProcessAction_async(action_type, parameters)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return results

async def main():
    rclpy.init()
    
    # 创建多个机器人客户端
    clients = {
        'Robot1': AsyncProcessActionClient('Robot1'),
        'Robot2': AsyncProcessActionClient('Robot2'),
        'Robot3': AsyncProcessActionClient('Robot3')
    }
    
    try:
        # 并行执行所有机器人的自检
        tasks = []
        for robot_id, client in clients.items():
            task = asyncio.create_task(
                client.ExecuteProcessAction_async('SelfCheck')
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        print(f"自检结果: {results}")
        
    finally:
        for client in clients.values():
            client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    asyncio.run(main())
```

## 📱 Web 客户端开发

### 使用 roslibjs 的 Web 客户端

```html
<!DOCTYPE html>
<html>
<head>
    <title>RPCS 工艺动作控制面板</title>
    <script src="https://cdn.jsdelivr.net/npm/roslib@1/build/roslib.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .robot-panel { border: 1px solid #ccc; margin: 10px; padding: 15px; }
        .button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .status { margin: 10px 0; font-weight: bold; }
    </style>
</head>
<body>
    <h1>RPCS 工艺动作控制面板</h1>
    
    <div id="connection-status">连接状态: 未连接</div>
    
    <div class="robot-panel">
        <h3>Robot1 - 灯板工艺</h3>
        <button class="button" onclick="executeAction('Robot1', 'SelfCheck')">自检</button>
        <button class="button" onclick="executeAction('Robot1', 'StackLightBoards')">灯板堆叠</button>
        <div id="Robot1-status" class="status">状态: 空闲</div>
        <div id="Robot1-progress">进度: 0%</div>
    </div>

    <div class="robot-panel">
        <h3>Robot2 - PCBA工艺</h3>
        <button class="button" onclick="executeAction('Robot2', 'SelfCheck')">自检</button>
        <button class="button" onclick="executeAction('Robot2', 'PlacePCBA')">PCBA放置</button>
        <div id="Robot2-status" class="status">状态: 空闲</div>
        <div id="Robot2-progress">进度: 0%</div>
    </div>

    <script>
        // 连接到 ROS
        var ros = new ROSLIB.Ros({
            url: 'ws://localhost:9090'  // rosbridge_server 地址
        });

        ros.on('connection', function() {
            document.getElementById('connection-status').innerHTML = '连接状态: 已连接';
            console.log('已连接到 ROS');
        });

        ros.on('error', function(error) {
            document.getElementById('connection-status').innerHTML = '连接状态: 错误';
            console.log('连接错误:', error);
        });

        ros.on('close', function() {
            document.getElementById('connection-status').innerHTML = '连接状态: 已断开';
            console.log('连接已断开');
        });

        // Action 客户端
        var actionClients = {};

        function createActionClient(robotId) {
            if (!actionClients[robotId]) {
                actionClients[robotId] = new ROSLIB.ActionClient({
                    ros: ros,
                    serverName: `/${robotId}/ExecuteProcessAction`,
                    actionName: 'rpcs_s_interfaces_behavior_tree/ExecuteProcessAction'
                });
            }
            return actionClients[robotId];
        }

        function executeAction(robotId, actionType) {
            var client = createActionClient(robotId);
            
            var goal = new ROSLIB.Goal({
                actionClient: client,
                goalMessage: {
                    process_action_type: actionType,
                    robot_id: robotId,
                    timeout_seconds: 300
                }
            });

            goal.on('feedback', function(feedback) {
                document.getElementById(`${robotId}-progress`).innerHTML = 
                    `进度: ${feedback.progress_percent.toFixed(1)}%`;
                document.getElementById(`${robotId}-status`).innerHTML = 
                    `状态: ${feedback.status_message}`;
            });

            goal.on('result', function(result) {
                if (result.success) {
                    document.getElementById(`${robotId}-status`).innerHTML = 
                        `状态: 完成 - ${result.result_message}`;
                } else {
                    document.getElementById(`${robotId}-status`).innerHTML = 
                        `状态: 失败 - ${result.result_message}`;
                }
                document.getElementById(`${robotId}-progress`).innerHTML = 
                    `进度: ${result.success ? '100%' : '0%'}`;
            });

            document.getElementById(`${robotId}-status`).innerHTML = 
                `状态: 执行中 - ${actionType}`;
            goal.send();
        }
    </script>
</body>
</html>
```

## 🔧 客户端工具库

### 创建可复用的客户端库

```python
# rpcs_client_lib.py
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import threading
import time

class RPCSClient:
    """RPCS 系统客户端库"""
    
    def __init__(self):
        if not rclpy.ok():
            rclpy.init()
        
        self.node = Node('rpcs_client_lib')
        self.clients = {}
        self.spin_thread = threading.Thread(target=self._spin_node)
        self.spin_thread.daemon = True
        self.spin_thread.start()
    
    def _spin_node(self):
        """后台运行节点"""
        while rclpy.ok():
            rclpy.spin_once(self.node, timeout_sec=0.1)
    
    def get_client(self, robot_id):
        """获取或创建机器人客户端"""
        if robot_id not in self.clients:
            action_name = f'/{robot_id}/ExecuteProcessAction'
            self.clients[robot_id] = ActionClient(
                self.node, ExecuteProcessAction, action_name
            )
        return self.clients[robot_id]
    
    def execute_action(self, robot_id, action_type, parameters=None, timeout=300):
        """执行工艺动作"""
        client = self.get_client(robot_id)
        
        if not client.wait_for_server(timeout_sec=10.0):
            raise Exception(f'{robot_id} 的 Action 服务不可用')
        
        goal = ExecuteProcessAction.Goal()
        goal.process_action_type = action_type
        goal.robot_id = robot_id
        goal.process_parameters = parameters or []
        goal.timeout_seconds = timeout
        
        future = client.send_goal_async(goal)
        
        # 等待目标接受
        start_time = time.time()
        while not future.done() and time.time() - start_time < 10:
            time.sleep(0.1)
        
        if not future.done():
            raise Exception('发送目标超时')
        
        goal_handle = future.result()
        if not goal_handle.accepted:
            raise Exception('目标被服务器拒绝')
        
        # 等待结果
        result_future = goal_handle.get_result_async()
        start_time = time.time()
        while not result_future.done() and time.time() - start_time < timeout:
            time.sleep(0.1)
        
        if not result_future.done():
            raise Exception('执行超时')
        
        result = result_future.result()
        if result.status != 4:  # SUCCEEDED
            raise Exception(f'执行失败，状态码: {result.status}')
        
        return result.result
    
    def shutdown(self):
        """关闭客户端"""
        self.node.destroy_node()

# 使用示例
if __name__ == '__main__':
    client = RPCSClient()
    
    try:
        # 执行自检
        result = client.execute_action('Robot1', 'SelfCheck')
        print(f"自检结果: {result.success} - {result.result_message}")
        
        # 执行工艺动作
        result = client.execute_action('Robot1', 'StackLightBoards')
        print(f"灯板堆叠结果: {result.success} - {result.result_message}")
        
    except Exception as e:
        print(f"执行异常: {e}")
    finally:
        client.shutdown()
        rclpy.shutdown()
```

## 📋 开发最佳实践

### 1. 错误处理

```python
def safe_execute_action(client, robot_id, action_type, max_retries=3):
    """带重试的安全执行"""
    for attempt in range(max_retries):
        try:
            result = client.execute_action(robot_id, action_type)
            if result.success:
                return result
            else:
                print(f"执行失败 (尝试 {attempt + 1}): {result.result_message}")
        except Exception as e:
            print(f"执行异常 (尝试 {attempt + 1}): {e}")
        
        if attempt < max_retries - 1:
            time.sleep(2)  # 等待后重试
    
    raise Exception(f"执行失败，已重试 {max_retries} 次")
```

### 2. 超时管理

```python
import signal

class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("操作超时")

def execute_with_timeout(func, timeout_seconds):
    """带超时的执行"""
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_seconds)
    
    try:
        result = func()
        signal.alarm(0)  # 取消超时
        return result
    except TimeoutException:
        print(f"操作超时 ({timeout_seconds}s)")
        raise
```

### 3. 批量操作

```python
def batch_execute(client, robot_actions):
    """批量执行工艺动作"""
    results = {}
    
    for robot_id, actions in robot_actions.items():
        results[robot_id] = []
        
        for action_type in actions:
            try:
                result = client.execute_action(robot_id, action_type)
                results[robot_id].append({
                    'action': action_type,
                    'success': result.success,
                    'message': result.result_message
                })
            except Exception as e:
                results[robot_id].append({
                    'action': action_type,
                    'success': False,
                    'message': str(e)
                })
    
    return results

# 使用示例
robot_actions = {
    'Robot1': ['SelfCheck', 'StackLightBoards'],
    'Robot2': ['SelfCheck', 'PlacePCBA'],
    'Robot3': ['SelfCheck', 'FastenProCase']
}

results = batch_execute(client, robot_actions)
```

---

🎯 **通过本指南，您可以开发各种类型的客户端应用，灵活调用 RPCS 工艺动作树系统！** 