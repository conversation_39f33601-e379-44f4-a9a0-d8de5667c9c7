# LightBoardSelfCheck动作增强测试客户端使用说明

## 概述

`enhanced_test_client.py` 是一个专门用于测试LightBoardSelfCheck动作的增强客户端，能够显示完整的反馈数据和结果信息。

## 功能特点

### 📊 详细的反馈显示
- **实时进度监控**: 显示执行进度百分比和变化趋势
- **步骤追踪**: 显示当前执行的工艺步骤
- **操作详情**: 显示当前具体操作内容
- **状态信息**: 显示详细的状态消息
- **时间戳**: 显示本地和服务器时间戳

### 📈 完整的结果信息
- **执行状态**: 成功/失败状态
- **执行时间**: 精确的执行时间统计
- **质量状态**: OK/NG/UNKNOWN质量评估
- **工艺输出数据**: 显示所有工艺输出数据
- **反馈统计**: 反馈消息总数统计

### 🎨 美观的界面显示
- 使用emoji图标增强可读性
- 清晰的分段显示
- 进度变化可视化指示器
- 彩色状态提示

## 使用方法

### 1. 启动Action服务器
首先确保ProcessActionServer正在运行：
```bash
./start_process_action_server.sh Robot1
```

### 2. 运行增强测试客户端
```bash
python3 enhanced_test_client.py
```

或者使用直接执行：
```bash
./enhanced_test_client.py
```

### 3. 观察输出
客户端将显示以下信息：

#### 启动信息
```
🤖 启动增强测试客户端 - 2024-01-24 10:30:15
📡 连接到Action服务器: Robot1/ExecuteProcessAction
```

#### 目标信息
```
📤 发送目标信息:
  🎯 工艺动作类型: LightBoardSelfCheck
  🤖 机器人ID: Robot1
  ⏱️  超时时间: 120秒
  🆔 流程ID: ENHANCED_TEST_LightBoardSelfCheck_Robot1_1706076615
  📋 工艺参数: []
  🔄 抢占当前: 否
```

#### 实时反馈
```
🔄 反馈 #1 | 10:30:16.123
  📍 当前步骤: Initializing
  🔧 当前操作: Loading process tree
  📊 执行进度: 0.0% ⬆️
  🎯 当前状态: RUNNING
  💬 状态信息: Starting process execution
  🕒 服务器时间戳: 1706076616.123
```

#### 执行结果
```
📊 执行结果详情
✅ 执行成功: 是
📝 结果信息: Process action completed successfully
🏁 最终状态: SUCCESS
⏱️  执行时间: 8.50秒
🕐 总耗时: 8.72秒
✅ 质量状态: OK
📦 工艺输出数据: 无
📈 反馈消息总数: 42
🎉 执行成功!
```

## 配置选项

### 修改超时时间
在`enhanced_test_client.py`文件中的main函数里修改：
```python
success = client.send_goal('LightBoardSelfCheck', 'Robot1', 120)  # 超时时间120秒
```

### 测试不同动作类型
修改第一个参数：
```python
success = client.send_goal('LightBoardPickSendStock', 'Robot1', 120)
```

### 测试不同机器人
修改第二个参数：
```python
success = client.send_goal('LightBoardSelfCheck', 'Robot2', 120)
```

## 故障排除

### 1. Action服务器未响应
**错误**: `❌ Action服务器 Robot1/ExecuteProcessAction 未响应`

**解决**: 
- 确保ProcessActionServer正在运行
- 检查机器人ID是否正确
- 验证ROS2环境是否正确设置

### 2. 目标被拒绝
**错误**: `❌ 目标被拒绝!`

**可能原因**:
- 动作类型不存在或拼写错误
- 机器人ID不匹配
- 服务器正在执行其他任务且未设置抢占

### 3. 执行超时
**警告**: `Process action 'LightBoardSelfCheck' timed out`

**解决**:
- 增加超时时间
- 检查行为树文件是否存在死循环
- 查看服务器日志获取详细错误信息

### 4. Python依赖问题
**错误**: 导入模块失败

**解决**:
```bash
# 确保ROS2环境已source
source install/setup.bash

# 检查包是否正确安装
ros2 pkg list | grep rpcs_s_interfaces_behavior_tree
```

## 日志记录

增强测试客户端会同时输出到：
- **控制台**: 美观的格式化显示
- **ROS2日志**: 标准的ROS2日志格式，可用ros2 topic查看

查看ROS2日志：
```bash
ros2 topic echo /rosout
```

## 性能监控

### 反馈频率
- 默认每100ms发送一次反馈
- 可以观察反馈消息的数量来评估性能

### 进度跟踪
- 进度百分比从0%到100%
- 箭头指示器显示进度变化趋势：
  - ⬆️ 进度增加
  - ➡️ 进度不变  
  - ⬇️ 进度减少（异常情况）

## 扩展功能

### 添加自定义工艺参数
修改goal_msg的process_parameters：
```python
goal_msg.process_parameters = ["param1=value1", "param2=value2"]
```

### 启用抢占功能
设置preempt_current为True：
```python
goal_msg.preempt_current = True
```

这将中断当前正在执行的任务并开始新任务。

## 技术细节

### 反馈字段说明
- `current_process_step`: 当前工艺步骤名称
- `current_operation`: 当前具体操作描述  
- `progress_percent`: 执行进度(0.0-100.0)
- `current_status`: 当前状态(RUNNING/SUCCESS/FAILURE)
- `status_message`: 状态描述信息
- `timestamp`: 服务器时间戳

### 结果字段说明
- `success`: 布尔值，执行是否成功
- `result_message`: 结果描述信息
- `final_status`: 最终状态(SUCCESS/FAILURE/ABORTED)
- `execution_time`: 执行时间(秒)
- `process_output_data`: 工艺输出数据数组
- `quality_status`: 质量状态(OK/NG/UNKNOWN)

## 示例输出

完整的测试会话示例请参考项目根目录下的`test_output_example.log`文件。

## 🆕 执行时间优化 (2024-01-24更新)

### 5秒执行时间配置
LightBoardSelfCheck动作现已优化为约5秒完成：

**修改内容:**
- 每个检查步骤的延迟从2000ms调整为1200ms
- 总共4个步骤：机械臂、传感器、工具、通信状态检查
- 预期总执行时间：4.8秒 + 系统开销 ≈ 5秒

**测试工具:**
使用快速测试脚本验证执行时间：
```bash
python3 quick_test_5sec.py
```

**预期结果:**
```
📊 执行结果:
⏱️  总执行时间: 5.12秒
⏱️  服务器报告时间: 4.85秒
✅ 执行成功: 是
🎯 时间控制良好！在目标5秒范围内
```

**时间评估标准:**
- 🎯 4.0-6.0秒：时间控制良好
- ⚡ <4.0秒：执行较快，可能需要增加延迟
- 🐌 >6.0秒：执行较慢，可能需要减少延迟

**配置文件位置:**
`src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_trees/Robot1_self_check.xml`

如果需要进一步调整时间，可以修改XML文件中的`delay_msec`参数。 