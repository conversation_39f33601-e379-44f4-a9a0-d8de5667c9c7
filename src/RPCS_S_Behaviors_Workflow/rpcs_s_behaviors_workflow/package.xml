<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rpcs_s_behaviors_workflow</name>
  <version>0.1.0</version>
  <description>RPCS Behavior Tree Package</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_auto</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>behaviortree_cpp</depend>
  <depend>behaviortree_ros2</depend>
  <depend>rpcs_s_interfaces_behavior_tree</depend>
  <depend>rpcs_s_interfaces_agv</depend>
  <depend>rpcs_s_interfaces_io_board</depend>
  <depend>rpcs_interfaces_motor</depend>
  <depend>vir_robot_interfaces</depend>
  <depend>algorithm_interface</depend>
  <depend>hikvision_interface</depend>
  <depend>sensor_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>opencv2</depend>
  <depend>nlohmann_json</depend>
  <depend>std_srvs</depend>
  <depend>yaml_cpp_vendor</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
