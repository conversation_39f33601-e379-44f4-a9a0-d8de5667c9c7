<?xml version="1.0"?>
<root BTCPP_format="4" project_name="RPCS_Behavior_Tree_Project">
    <!-- Auto-generated Node Models for Groot2 Editor -->
    <TreeNodesModel>
        <!-- Action Nodes -->
        <Action ID="AgvGoPoint" editable="true">
            <input_port name="intTimeoutMs" default="" />
            <input_port name="strGoPointName" default="" />
            <input_port name="strNamespace" default="" />
            <output_port name="boolOutputSuccess" default="" />
            <output_port name="strOutputMessage" default="" />
            <output_port name="strOutputResponse" default="" />
        </Action>
        <Action ID="CleanStatus" editable="true">
            <output_port name="strAllstatus" default="" />
        </Action>
                 <Action ID="CorrectionPosition" editable="true">
             <input_port name="doubleAbsX" default="" />
             <input_port name="doubleAbsY" default="" />
             <input_port name="doubleAbsZ" default="" />
             <input_port name="doubleAbsR" default="" />
             <input_port name="doubleTimeout" default="30.0" />
             <output_port name="boolOutputSuccess" default="" />
             <output_port name="strErrorCode" default="" />
             <output_port name="strOutputMessage" default="" />
             <output_port name="strPlcFeedback" default="" />
         </Action>
         <Action ID="BoardAlign" editable="true">
             <input_port name="strImageAPath" default="" />
             <input_port name="strImageBPath" default="" />
             <input_port name="strImageATopic" default="" />
             <input_port name="strImageBTopic" default="" />
             <input_port name="intBoardNum" default="" />
             <input_port name="doubleTimeout" default="10.0" />
             <output_port name="boolOutputSuccess" default="" />
             <output_port name="strOutputMessage" default="" />
             <output_port name="boolIsFinish" default="" />
             <output_port name="strMoveCommands" default="" />
             <output_port name="intMoveCommandsCount" default="" />
         </Action>
         <Action ID="CameraCapture" editable="true">
             <input_port name="strNamespace" default="HJGC" />
             <input_port name="boolTriggerImage" default="true" />
             <input_port name="strImageTopic" default="" />
             <output_port name="boolSuccess" default="" />
             <output_port name="stringMessage" default="" />
         </Action>
        <Action ID="MotorHoming" editable="true">
            <input_port name="doubleTimeout" default="" />
            <input_port name="floatSpeedSwitch" default="" />
            <input_port name="floatSpeedZero" default="" />
            <input_port name="intHomeOffset" default="" />
            <input_port name="intHomingMethod" default="" />
            <input_port name="intMotorId" default="" />
            <input_port name="intPositionWindow" default="" />
            <input_port name="intPositionWindowTime" default="" />
            <input_port name="strDeviceId" default="" />
            <output_port name="boolOutputSuccess" default="" />
            <output_port name="floatCurrentPosition" default="" />
            <output_port name="floatFinalPosition" default="" />
            <output_port name="intCurrentStatus" default="" />
            <output_port name="strOutputMessage" default="" />
            <output_port name="strStatusDescription" default="" />
        </Action>
        <Action ID="MotorPositionControl" editable="true">
            <input_port name="boolAbsolutePosition" default="" />
            <input_port name="doubleAcceleration" default="" />
            <input_port name="doubleDeceleration" default="" />
            <input_port name="doubleDwellTime" default="" />
            <input_port name="doubleMaxVelocity" default="" />
            <input_port name="doubleTargetPosition" default="" />
            <input_port name="doubleTimeout" default="" />
            <input_port name="intMotorId" default="" />
            <input_port name="strDeviceId" default="" />
            <output_port name="boolOutputSuccess" default="" />
            <output_port name="doubleCurrentPosition" default="" />
            <output_port name="doubleFinalPosition" default="" />
            <output_port name="doublePositionError" default="" />
            <output_port name="doubleProgress" default="" />
            <output_port name="intErrorCode" default="" />
            <output_port name="strOutputMessage" default="" />
        </Action>
        <Action ID="MotorTorqueControl" editable="true">
            <input_port name="boolUsePositionLimits" default="" />
            <input_port name="doubleDuration" default="" />
            <input_port name="doubleMaxPosition" default="" />
            <input_port name="doubleMinPosition" default="" />
            <input_port name="doubleTargetTorque" default="" />
            <input_port name="doubleTimeout" default="" />
            <input_port name="doubleTorqueSlope" default="" />
            <input_port name="doubleVelocityLimit" default="" />
            <input_port name="intMotorId" default="" />
            <input_port name="strDeviceId" default="" />
            <output_port name="boolOutputSuccess" default="" />
            <output_port name="doubleCurrentTorque" default="" />
            <output_port name="doubleElapsedTime" default="" />
            <output_port name="doubleFinalPosition" default="" />
            <output_port name="doubleFinalTorque" default="" />
            <output_port name="doubleFinalVelocity" default="" />
            <output_port name="intErrorCode" default="" />
            <output_port name="strOutputMessage" default="" />
        </Action>
        <Action ID="MotorVelocityControl" editable="true">
            <input_port name="boolUsePositionLimits" default="" />
            <input_port name="doubleAcceleration" default="" />
            <input_port name="doubleDeceleration" default="" />
            <input_port name="doubleDuration" default="" />
            <input_port name="doubleMaxPosition" default="" />
            <input_port name="doubleMinPosition" default="" />
            <input_port name="doubleTargetCurrentLimit" default="" />
            <input_port name="doubleTargetVelocity" default="" />
            <input_port name="doubleTimeout" default="" />
            <input_port name="intMotorId" default="" />
            <input_port name="strDeviceId" default="" />
            <output_port name="boolOutputSuccess" default="" />
            <output_port name="doubleCurrentVelocity" default="" />
            <output_port name="doubleElapsedTime" default="" />
            <output_port name="doubleFinalPosition" default="" />
            <output_port name="doubleFinalVelocity" default="" />
            <output_port name="intErrorCode" default="" />
            <output_port name="strOutputMessage" default="" />
        </Action>
        <Action ID="PubPrintMessage" editable="true">
            <input_port name="strPrintMessage" default="" />
            <input_port name="strTopicName" default="" />
        </Action>
        <Action ID="PubProcessFeedback" editable="true">
            <input_port name="doubleProgress" default="" />
            <input_port name="strMessage" default="" />
            <input_port name="strOperation" default="" />
            <input_port name="strProcessStep" default="" />
            <input_port name="strStatus" default="" />
        </Action>
        <Action ID="SetProgress" editable="true">
            <input_port name="progress" default="" />
        </Action>
        <!-- Condition Nodes -->
        <Condition ID="DiGetIoStatus" editable="true">
            <input_port name="boolInvertLogic" default="" />
            <input_port name="intTimeoutMs" default="" />
            <input_port name="strFunctionName" default="" />
            <input_port name="strNamespace" default="" />
            <output_port name="boolCurrentState" default="" />
            <output_port name="strMessage" default="" />
        </Condition>
    </TreeNodesModel>
</root>