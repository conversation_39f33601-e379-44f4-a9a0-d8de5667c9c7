#!/bin/bash

# RPCS ProcessActionServer 启动脚本
# 使用方法: ./start_process_action_server.sh [robot_id]

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"

# 默认机器人ID
ROBOT_ID=${1:-Robot1}

echo "🚀 启动 RPCS ProcessActionServer"
echo "   工作空间: $WORKSPACE_DIR"
echo "   机器人ID: $ROBOT_ID"

# 检查可执行文件是否存在
EXECUTABLE="$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/process_action_server"
if [ ! -f "$EXECUTABLE" ]; then
    echo "❌ 错误: 可执行文件不存在: $EXECUTABLE"
    echo "   请先运行: colcon build --packages-select RPCS_S_Behaviors_workflow"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="$WORKSPACE_DIR/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_action_mapping.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 检查工艺树目录是否存在
TREE_PATH="$WORKSPACE_DIR/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow/config/process_trees"
if [ ! -d "$TREE_PATH" ]; then
    echo "❌ 错误: 工艺树目录不存在: $TREE_PATH"
    exit 1
fi

# 设置环境变量
export AMENT_PREFIX_PATH="$WORKSPACE_DIR/install:$AMENT_PREFIX_PATH"
export CMAKE_PREFIX_PATH="$WORKSPACE_DIR/install:$CMAKE_PREFIX_PATH"
export LD_LIBRARY_PATH="$WORKSPACE_DIR/install/rpcs_s_interfaces_behavior_tree/lib:$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib:$WORKSPACE_DIR/install/behaviortree_ros2/lib:$LD_LIBRARY_PATH"
export PYTHONPATH="$WORKSPACE_DIR/install/rpcs_s_interfaces_behavior_tree/local/lib/python3.10/dist-packages:$PYTHONPATH"

echo "✅ 环境变量设置完成"
echo "   AMENT_PREFIX_PATH: $AMENT_PREFIX_PATH"
echo "   LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

# 启动服务器
echo "🎯 启动 ProcessActionServer..."
exec "$EXECUTABLE" \
  --ros-args \
  -p robot_id:="$ROBOT_ID" \
  -p config_file:="$CONFIG_FILE" \
  -p tree_file_path:="$TREE_PATH" \
  -p max_concurrent_actions:=2 \
  -p default_timeout:=300 \
  -p groot2_port:=1670 