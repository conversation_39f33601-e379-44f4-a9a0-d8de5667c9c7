#include <memory>
#include <chrono>
#include <string>
#include <iostream>

#include "rclcpp/rclcpp.hpp"
#include "rpcs_s_interfaces_agv/srv/agv_go_point.hpp"
#include <nlohmann/json.hpp>

using namespace std::chrono_literals;

class AgvServiceClient : public rclcpp::Node
{
public:
  AgvServiceClient()
  : Node("agv_service_client")
  {
    // 创建客户端
    client_ = create_client<rpcs_s_interfaces_agv::srv::AgvGoPoint>("/Robot1/AgvGoPointService");
  }

  bool send_request(const std::string& go_point_name)
  {
    // 等待服务可用
    if (!client_->wait_for_service(5s)) {
      RCLCPP_ERROR(get_logger(), "服务不可用: %s", client_->get_service_name());
      return false;
    }

    // 创建请求
    auto request = std::make_shared<rpcs_s_interfaces_agv::srv::AgvGoPoint::Request>();
    
    // 设置请求JSON
    nlohmann::json req_json;
    req_json["go_point_name"] = go_point_name;
    request->request = req_json.dump();
    
    RCLCPP_INFO(get_logger(), "发送AGV移动请求到: %s", go_point_name.c_str());
    RCLCPP_INFO(get_logger(), "请求内容: %s", request->request.c_str());

    // 发送异步请求
    auto future = client_->async_send_request(request);
    
    // 等待响应
    if (rclcpp::spin_until_future_complete(shared_from_this(), future) != rclcpp::FutureReturnCode::SUCCESS) {
      RCLCPP_ERROR(get_logger(), "调用服务失败");
      return false;
    }

    // 获取响应
    auto response = future.get();
    
    // 输出响应信息
    RCLCPP_INFO(get_logger(), "响应状态: %s", response->success ? "成功" : "失败");
    RCLCPP_INFO(get_logger(), "响应消息: %s", response->message.c_str());
    
    if (!response->response.empty()) {
      RCLCPP_INFO(get_logger(), "额外响应数据: %s", response->response.c_str());
    }
    
    return response->success;
  }

private:
  rclcpp::Client<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedPtr client_;
};

// 帮助信息
void print_usage()
{
  std::cout << "用法: test_agv_service <point_name>" << std::endl;
  std::cout << "  <point_name>: AGV目标点名称，例如 ToSafePoint, ToRacks" << std::endl;
}

int main(int argc, char** argv)
{
  // 检查命令行参数
  if (argc < 2) {
    std::cout << "错误: 缺少目标点名称参数" << std::endl;
    print_usage();
    return 1;
  }
  
  std::string point_name = argv[1];
  
  // 初始化ROS2
  rclcpp::init(argc, argv);
  
  // 创建客户端节点
  auto client_node = std::make_shared<AgvServiceClient>();
  
  // 发送请求
  bool success = client_node->send_request(point_name);
  
  // 清理
  rclcpp::shutdown();
  
  return success ? 0 : 1;
} 