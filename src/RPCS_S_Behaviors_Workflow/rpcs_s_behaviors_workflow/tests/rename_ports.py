#!/usr/bin/env python3

import os
import re
import glob

def camel_case(s):
    """将下划线格式转换为驼峰格式，首字母也大写"""
    parts = s.split('_')
    return ''.join(x.title() for x in parts)

def get_type_prefix(type_str):
    """根据类型字符串确定前缀"""
    if 'std::string' in type_str:
        return 'str'
    elif 'int' in type_str:
        return 'int'
    elif 'bool' in type_str:
        return 'bool'
    elif 'double' in type_str or 'float' in type_str:
        return 'flt'
    else:
        return 'obj'

def process_file(file_path):
    """处理单个文件，找出并替换所有端口名称"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 保存原始内容，用于比较是否有修改
    original_content = content
    
    # 找到所有InputPort和OutputPort定义
    input_port_pattern = r'BT::InputPort<([^>]+)>\s*\("([^"]+)"\)'
    output_port_pattern = r'BT::OutputPort<([^>]+)>\s*\("([^"]+)"\)'
    
    # 记录所有端口名称的新旧映射
    port_mapping = {}
    
    # 处理InputPort
    for match in re.finditer(input_port_pattern, content):
        type_str = match.group(1)
        old_name = match.group(2)
        
        # 已经有类型前缀的情况
        prefixes = ['str', 'int', 'bool', 'flt', 'obj']
        has_prefix = False
        for prefix in prefixes:
            if old_name.startswith(prefix) and len(old_name) > len(prefix) and old_name[len(prefix)].isupper():
                has_prefix = True
                break
        
        if has_prefix:
            continue
            
        type_prefix = get_type_prefix(type_str)
        
        # 检查是否是下划线格式，如果是则转换为驼峰
        if '_' in old_name:
            new_name = type_prefix + camel_case(old_name)
        else:
            # 如果不是下划线格式，则首字母大写
            new_name = type_prefix + old_name[0].upper() + old_name[1:]
            
        port_mapping[old_name] = new_name
    
    # 处理OutputPort
    for match in re.finditer(output_port_pattern, content):
        type_str = match.group(1)
        old_name = match.group(2)
        
        # 已经有类型前缀的情况
        prefixes = ['str', 'int', 'bool', 'flt', 'obj']
        has_prefix = False
        for prefix in prefixes:
            if old_name.startswith(prefix) and len(old_name) > len(prefix) and old_name[len(prefix)].isupper():
                has_prefix = True
                break
        
        if has_prefix:
            continue
            
        type_prefix = get_type_prefix(type_str)
        
        # 检查是否是下划线格式，如果是则转换为驼峰
        if '_' in old_name:
            new_name = type_prefix + camel_case(old_name)
        else:
            # 如果不是下划线格式，则首字母大写
            new_name = type_prefix + old_name[0].upper() + old_name[1:]
            
        port_mapping[old_name] = new_name
    
    # 替换所有端口名称
    modified_content = content
    for old_name, new_name in port_mapping.items():
        # 替换所有引号中的端口名称
        modified_content = re.sub(f'"{old_name}"', f'"{new_name}"', modified_content)
        
        # 替换代码中的变量名（更加复杂，需要小心处理）
        # 避免替换掉变量名中包含的子字符串
        modified_content = re.sub(r'\b' + old_name + r'\b', new_name, modified_content)
    
    # 检查是否有修改
    if modified_content != original_content:
        with open(file_path, 'w') as f:
            f.write(modified_content)
        print(f"已修改文件: {file_path}")
        print(f"  端口名称映射: {port_mapping}")
        return True
    return False

def main():
    # 设置基础目录
    base_dir = "/home/<USER>/workspaces/home_git/AutoCreateRPS_home/src/RPCS_S_Behaviors_workflow/rpcs_s_behaviors_workflow"
    
    # 处理include目录下的头文件
    include_pattern = os.path.join(base_dir, "include/rpcs_s_behaviors_workflow/plugins/**/*.h*")
    include_files = glob.glob(include_pattern, recursive=True)
    
    # 处理plugins目录下的实现文件
    plugins_pattern = os.path.join(base_dir, "plugins/**/*.cpp")
    plugins_files = glob.glob(plugins_pattern, recursive=True)
    
    # 合并所有文件
    all_files = include_files + plugins_files
    
    print(f"找到 {len(all_files)} 个文件")
    
    # 处理所有文件
    modified_count = 0
    for file_path in all_files:
        if process_file(file_path):
            modified_count += 1
    
    print(f"共修改了 {modified_count} 个文件")

if __name__ == "__main__":
    main() 