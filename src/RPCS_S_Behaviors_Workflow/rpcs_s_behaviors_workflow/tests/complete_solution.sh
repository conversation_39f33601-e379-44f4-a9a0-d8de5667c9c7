#!/bin/bash

echo "🔧 RPCS工作空间完整解决方案"
echo "=========================================="

# 获取工作空间目录
WORKSPACE_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "📂 工作空间目录: $WORKSPACE_DIR"

# 1. 编译包
echo ""
echo "1️⃣ 编译必要的包..."
cd "$WORKSPACE_DIR"

echo "   编译 rpcs_s_interfaces_behavior_tree..."
colcon build --packages-select rpcs_s_interfaces_behavior_tree

echo "   编译 behaviortree_ros2..."
colcon build --packages-select behaviortree_ros2

echo "   编译 RPCS_S_Behaviors_workflow..."
colcon build --packages-select RPCS_S_Behaviors_workflow

# 2. 配置环境变量
echo ""
echo "2️⃣ 配置环境变量..."
export AMENT_PREFIX_PATH="$WORKSPACE_DIR/install:/opt/ros/humble"
export CMAKE_PREFIX_PATH="$WORKSPACE_DIR/install:/opt/ros/humble"
export LD_LIBRARY_PATH="$WORKSPACE_DIR/install/rpcs_s_interfaces_behavior_tree/lib:$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib:$WORKSPACE_DIR/install/behaviortree_ros2/lib:$LD_LIBRARY_PATH"
export PYTHONPATH="$WORKSPACE_DIR/install/rpcs_s_interfaces_behavior_tree/local/lib/python3.10/dist-packages:$PYTHONPATH"

echo "✅ 环境变量已配置"

# 3. 验证安装
echo ""
echo "3️⃣ 验证包安装..."
if [ -f "$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/process_action_server" ]; then
    echo "✅ process_action_server 可执行文件存在"
else
    echo "❌ process_action_server 可执行文件缺失"
    exit 1
fi

if [ -f "$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/share/RPCS_S_Behaviors_workflow/launch/single_robot_process_action_server.launch.py" ]; then
    echo "✅ launch 文件存在"
else
    echo "❌ launch 文件缺失"
    exit 1
fi

# 4. 提供使用方法
echo ""
echo "4️⃣ 可用的启动方法:"
echo ""
echo "方法1: 使用启动脚本 (推荐)"
echo "   ./start_process_action_server.sh Robot1"
echo ""
echo "方法2: 直接运行可执行文件"
echo "   export AMENT_PREFIX_PATH=\"$WORKSPACE_DIR/install:/opt/ros/humble\""
echo "   export LD_LIBRARY_PATH=\"$WORKSPACE_DIR/install/rpcs_s_interfaces_behavior_tree/lib:$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib:\$LD_LIBRARY_PATH\""
echo "   $WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/lib/RPCS_S_Behaviors_workflow/process_action_server Robot1"
echo ""
echo "方法3: 使用绝对路径launch"
echo "   export AMENT_PREFIX_PATH=\"$WORKSPACE_DIR/install:/opt/ros/humble\""
echo "   ros2 launch $WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow/share/RPCS_S_Behaviors_workflow/launch/single_robot_process_action_server.launch.py robot_id:=Robot1"
echo ""
echo "5️⃣ 测试新的动作类型:"
echo "   # Robot1 动作类型:"
echo "   ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \"{process_action_type: 'LightBoardSelfCheck', robot_id: 'Robot1', timeout_seconds: 30}\""
echo "   ros2 action send_goal /Robot1/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \"{process_action_type: 'LightBoardPickSendStock', robot_id: 'Robot1', timeout_seconds: 60}\""
echo ""
echo "   # Robot2 动作类型:"
echo "   ros2 action send_goal /Robot2/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \"{process_action_type: 'PcbaSelfCheck', robot_id: 'Robot2', timeout_seconds: 30}\""
echo ""
echo "   # Robot3 动作类型:"
echo "   ros2 action send_goal /Robot3/ExecuteProcessAction rpcs_s_interfaces_behavior_tree/action/ExecuteProcessAction \"{process_action_type: 'ProCaseSelfCheck', robot_id: 'Robot3', timeout_seconds: 30}\""
echo ""
echo "🎉 解决方案配置完成！" 