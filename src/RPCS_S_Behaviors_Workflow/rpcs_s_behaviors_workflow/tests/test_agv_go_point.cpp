#include <memory>
#include <string>
#include <chrono>
#include <thread>

#include "rclcpp/rclcpp.hpp"
#include "behaviortree_cpp/bt_factory.h"
#include "behaviortree_cpp/loggers/bt_cout_logger.h"

#include "rpcs_s_interfaces_agv/srv/agv_go_point.hpp"
#include "rpcs_s_behaviors_workflow/plugins/action/AgvGoPoint.hpp"

using namespace std::chrono_literals;

// 模拟AGV服务的节点类
class MockAgvServiceNode : public rclcpp::Node
{
public:
    MockAgvServiceNode(const std::string& name)
    : Node(name)
    {
        // 创建模拟AGV服务
        auto service_callback = [this](
            const std::shared_ptr<rpcs_s_interfaces_agv::srv::AgvGoPoint::Request> request,
            std::shared_ptr<rpcs_s_interfaces_agv::srv::AgvGoPoint::Response> response) -> void
        {
            RCLCPP_INFO(this->get_logger(), "收到AGV移动请求: %s", request->request.c_str());

            // 解析请求
            try {
                nlohmann::json req_json = nlohmann::json::parse(request->request);
                std::string point_name = req_json["go_point_name"];
                
                // 模拟处理时间
                std::this_thread::sleep_for(500ms);
                
                // 生成响应
                nlohmann::json resp_json;
                resp_json["success"] = true;
                resp_json["message"] = "成功移动到" + point_name;
                response->response = resp_json.dump();
                
                RCLCPP_INFO(this->get_logger(), "AGV已移动到: %s", point_name.c_str());
            } catch (const std::exception& e) {
                nlohmann::json resp_json;
                resp_json["success"] = false;
                resp_json["message"] = std::string("解析请求失败: ") + e.what();
                response->response = resp_json.dump();
                
                RCLCPP_ERROR(this->get_logger(), "处理AGV请求失败: %s", e.what());
            }
        };
        
        // 创建服务
        agv_service_ = create_service<rpcs_s_interfaces_agv::srv::AgvGoPoint>(
            "/Robot1/AgvGoPointService", service_callback);
        
        RCLCPP_INFO(this->get_logger(), "模拟AGV服务已启动: %s", agv_service_->get_service_name());
    }

private:
    rclcpp::Service<rpcs_s_interfaces_agv::srv::AgvGoPoint>::SharedPtr agv_service_;
};

// 行为树节点类
class BehaviorTreeTestNode : public rclcpp::Node
{
public:
    BehaviorTreeTestNode()
    : Node("behavior_tree_test")
    {
        // 设置行为树工厂和黑板
        factory_ = std::make_shared<BT::BehaviorTreeFactory>();
        blackboard_ = BT::Blackboard::create();
        
        // 将节点实例添加到黑板，以便AgvGoPoint可以访问
        blackboard_->set("node", std::static_pointer_cast<rclcpp::Node>(shared_from_this()));
        
        // 注册行为树节点
        BT::NodeBuilder builder = [](const std::string& name, const BT::NodeConfiguration& config) {
            auto node = config.blackboard->get<rclcpp::Node::SharedPtr>("node");
            return std::make_unique<rpcs_s_behaviors_workflow::AgvGoPoint>(name, config, node);
        };
        
        factory_->registerBuilder<rpcs_s_behaviors_workflow::AgvGoPoint>("AgvGoPoint", builder);
        
        // 创建简单的测试树
        std::string xml_text = R"(
        <root BTCPP_format="4">
            <BehaviorTree ID="TestAgvGoPoint">
                <Sequence>
                    <AgvGoPoint point_name="TestPoint" namespace="/Robot1" timeout_ms="5000"/>
                </Sequence>
            </BehaviorTree>
        </root>
        )";
        
        // 创建行为树
        tree_ = factory_->createTreeFromText(xml_text, blackboard_);
        
        // 添加日志记录器
        logger_ = std::make_unique<BT::StdCoutLogger>(tree_);
        
        // 创建定时器执行行为树
        timer_ = this->create_wall_timer(
            1s, std::bind(&BehaviorTreeTestNode::executeTree, this));
        
        RCLCPP_INFO(this->get_logger(), "行为树测试节点已启动");
    }
    
private:
    void executeTree()
    {
        // 停止定时器，避免重复执行
        timer_->cancel();
        
        RCLCPP_INFO(this->get_logger(), "开始执行行为树...");
        
        // 执行行为树
        BT::NodeStatus status = tree_.tickOnce();
        
        RCLCPP_INFO(this->get_logger(), "行为树执行结果: %s", 
                   status == BT::NodeStatus::SUCCESS ? "成功" : 
                   status == BT::NodeStatus::FAILURE ? "失败" : "运行中");
        
        // 如果仍在运行，继续执行
        if (status == BT::NodeStatus::RUNNING) {
            timer_ = this->create_wall_timer(1s, std::bind(&BehaviorTreeTestNode::executeTree, this));
        }
    }
    
    std::shared_ptr<BT::BehaviorTreeFactory> factory_;
    BT::Tree tree_;
    BT::Blackboard::Ptr blackboard_;
    std::unique_ptr<BT::StdCoutLogger> logger_;
    rclcpp::TimerBase::SharedPtr timer_;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    
    // 创建执行器
    rclcpp::executors::MultiThreadedExecutor executor;
    
    // 创建模拟AGV服务节点
    auto mock_service_node = std::make_shared<MockAgvServiceNode>("mock_agv_service");
    executor.add_node(mock_service_node);
    
    // 创建行为树测试节点
    auto bt_node = std::make_shared<BehaviorTreeTestNode>();
    executor.add_node(bt_node);
    
    // 运行执行器
    executor.spin();
    
    rclcpp::shutdown();
    return 0;
} 