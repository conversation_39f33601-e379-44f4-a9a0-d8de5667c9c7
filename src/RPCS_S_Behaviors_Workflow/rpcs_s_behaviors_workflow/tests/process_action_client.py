#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.action import Action<PERSON>lient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import sys
import argparse

class ProcessActionClientPy(Node):
    def __init__(self, robot_id):
        super().__init__(f'process_action_client_{robot_id}')
        self.robot_id = robot_id
        
        # 创建 Action 客户端
        action_name = f'{robot_id}/ExecuteProcessAction'
        self.action_client = ActionClient(self, ExecuteProcessAction, action_name)
        
        self.get_logger().info(f'ProcessActionClient initialized for robot: {robot_id}')
        self.get_logger().info(f'Action server name: {action_name}')
        
        self.goal_done = False

    def wait_for_action_server(self, timeout_sec=10.0):
        """等待 Action 服务器可用"""
        self.get_logger().info('Waiting for action server...')
        return self.action_client.wait_for_server(timeout_sec=timeout_sec)

    def send_goal(self, process_action_type, process_parameters=None, timeout_seconds=300, process_id=''):
        """发送目标到 Action 服务器"""
        if not self.action_client.server_is_ready():
            self.get_logger().error('Action server not ready')
            return False

        # 创建目标消息
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = process_action_type
        goal_msg.robot_id = self.robot_id
        goal_msg.process_parameters = process_parameters if process_parameters else []
        goal_msg.timeout_seconds = timeout_seconds
        goal_msg.preempt_current = False
        goal_msg.process_id = process_id if process_id else f'PROCESS_{int(self.get_clock().now().nanoseconds / 1e9)}'

        self.get_logger().info(f'Sending goal: {process_action_type} (ID: {goal_msg.process_id})')

        # 发送目标
        send_goal_future = self.action_client.send_goal_async(
            goal_msg,
            feedback_callback=self.feedback_callback
        )
        
        send_goal_future.add_done_callback(self.goal_response_callback)
        return True

    def goal_response_callback(self, future):
        """处理目标响应"""
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().error('Goal was rejected by server')
            self.goal_done = True
            return

        self.get_logger().info('Goal accepted by server, waiting for result')
        
        # 获取结果
        get_result_future = goal_handle.get_result_async()
        get_result_future.add_done_callback(self.result_callback)

    def feedback_callback(self, feedback_msg):
        """处理反馈消息"""
        feedback = feedback_msg.feedback
        self.get_logger().info(
            f'Feedback - Step: {feedback.current_process_step}, '
            f'Operation: {feedback.current_operation}, '
            f'Progress: {feedback.progress_percent:.1f}%, '
            f'Status: {feedback.status_message}'
        )

    def result_callback(self, future):
        """处理结果消息"""
        result = future.result().result
        status = future.result().status
        
        if status == 4:  # SUCCEEDED
            self.get_logger().info(
                f'Goal succeeded! Message: {result.result_message}, '
                f'Status: {result.final_status}, '
                f'Quality: {result.quality_status}, '
                f'Time: {result.execution_time:.2f}s'
            )
        elif status == 5:  # ABORTED
            self.get_logger().error(f'Goal was aborted: {result.result_message}')
        elif status == 6:  # CANCELED
            self.get_logger().warn('Goal was canceled')
        else:
            self.get_logger().error(f'Unknown result status: {status}')
        
        self.goal_done = True

    def wait_for_result(self):
        """等待结果"""
        while rclpy.ok() and not self.goal_done:
            rclpy.spin_once(self, timeout_sec=0.1)

def print_usage():
    """打印使用说明"""
    print("Usage: python3 process_action_client.py <robot_id> <process_action_type> [process_id] [timeout]")
    print("Examples:")
    print("  python3 process_action_client.py Robot1 LightBoardSelfCheck")
    print("  python3 process_action_client.py Robot1 LightBoardPickSendStock LIGHTBOARD_001 60")
    print("  python3 process_action_client.py Robot2 PlaceProduct PRODUCT_001 120")

def main():
    parser = argparse.ArgumentParser(description='Process Action Client')
    parser.add_argument('robot_id', type=str, help='Robot ID (Robot1, Robot2, Robot3)')
    parser.add_argument('process_action_type', type=str, help='Process action type')
    parser.add_argument('process_parameters', nargs='*', default=[], help='Process parameters')
    parser.add_argument('--timeout', type=int, default=300, help='Timeout in seconds')
    parser.add_argument('--process-id', type=str, default='', help='Process ID for tracking')
    parser.add_argument('--preempt', action='store_true', help='Preempt current action')
    
    args = parser.parse_args()
    
    if not args.robot_id or not args.process_action_type:
        print("使用方法:")
        print("  python3 process_action_client.py Robot1 LightBoardSelfCheck")
        print("  python3 process_action_client.py Robot1 LightBoardPickSendStock LIGHTBOARD_001 60")
        return

    rclpy.init()

    try:
        client = ProcessActionClientPy(args.robot_id)

        if not client.wait_for_action_server():
            client.get_logger().error('Action server not available after waiting')
            return 1

        # 发送目标
        if not client.send_goal(args.process_action_type, args.process_parameters, args.timeout, args.process_id):
            return 1

        # 等待结果
        client.wait_for_result()

    except KeyboardInterrupt:
        print('\nInterrupted by user')
    finally:
        rclpy.shutdown()

    return 0

if __name__ == '__main__':
    sys.exit(main()) 