#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import time
from datetime import datetime

class TimingTestClient(Node):
    def __init__(self):
        super().__init__('timing_test_client')
        
    def test_fixed_timing(self):
        """验证修复后的5秒执行时间"""
        action_name = 'Robot1/ExecuteProcessAction'
        self.action_client = ActionClient(self, ExecuteProcessAction, action_name)
        
        print("🧪 验证修复后的执行时间")
        print("=" * 50)
        
        if not self.action_client.wait_for_server(timeout_sec=10.0):
            print("❌ Action服务器未响应")
            return False
            
        print("✅ 已连接到Action服务器")
        
        # 创建目标
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = 'LightBoardSelfCheck'
        goal_msg.robot_id = 'Robot1'
        goal_msg.timeout_seconds = 30
        goal_msg.preempt_current = False
        goal_msg.process_id = f"TIMING_TEST_{int(time.time())}"
        
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        start_time = time.time()
        
        # 发送目标
        future = self.action_client.send_goal_async(goal_msg, feedback_callback=self.timing_feedback)
        rclpy.spin_until_future_complete(self, future)
        
        goal_handle = future.result()
        if not goal_handle.accepted:
            print("❌ 目标被拒绝")
            return False
            
        print("✅ 目标已接受，正在执行...")
        
        # 等待结果
        result_future = goal_handle.get_result_async()
        rclpy.spin_until_future_complete(self, result_future)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        result = result_future.result().result
        
        print("=" * 50)
        print("📊 修复验证结果:")
        print(f"⏱️  实际执行时间: {total_time:.2f}秒")
        print(f"⏱️  服务器报告时间: {result.execution_time:.2f}秒")
        print(f"✅ 执行成功: {'是' if result.success else '否'}")
        print(f"🏁 最终状态: {result.final_status}")
        print(f"⏰ 结束时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        
        # 时间评估
        if 3.0 <= total_time <= 7.0:
            print("🎯 ✅ 修复成功！执行时间在期望范围内(3-7秒)")
            status = "SUCCESS"
        elif total_time < 3.0:
            print("⚡ ⚠️  执行过快，可能有问题")
            status = "TOO_FAST"
        elif 7.0 < total_time <= 15.0:
            print("🐌 ⚠️  仍然偏慢，但比之前好")
            status = "IMPROVED"
        else:
            print("💥 ❌ 修复失败，执行时间仍然过长")
            status = "FAILED"
            
        print("=" * 50)
        
        return status == "SUCCESS", total_time
    
    def timing_feedback(self, feedback_msg):
        """简化的反馈显示"""
        feedback = feedback_msg.feedback
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        print(f"📍 {timestamp} | {feedback.current_process_step} ({feedback.progress_percent:.1f}%)")

def main():
    rclpy.init()
    
    client = TimingTestClient()
    
    try:
        print("🔧 测试修复后的LightBoardSelfCheck执行时间...")
        print("预期：约5秒（3-7秒范围内）")
        print("修复：减少了DEFAULT_DELAY_TIME和简化了XML结构\n")
        
        success, execution_time = client.test_fixed_timing()
        
        print("\n📝 修复总结:")
        print("1. 将DEFAULT_DELAY_TIME从3秒改为1秒")
        print("2. 简化XML结构，减少PubPrintMessage节点数量")
        print("3. 移除了CleanStatus节点")
        print(f"4. 实际执行时间: {execution_time:.2f}秒")
        
        if success:
            print("\n🎉 修复验证成功！")
        else:
            print("\n😞 需要进一步调整")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被中断")
    except Exception as e:
        print(f"\n💥 测试错误: {str(e)}")
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 