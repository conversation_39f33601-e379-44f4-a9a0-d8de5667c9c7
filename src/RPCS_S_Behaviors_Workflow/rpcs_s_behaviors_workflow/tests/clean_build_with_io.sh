#!/bin/bash

# 获取当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
WORKSPACE_DIR="$(cd "$SCRIPT_DIR/../../" && pwd)"

# 配置IO接口路径
IO_BOARD_PATH="/home/<USER>/workspaces/home_git/RPCS_M_Controllers_interfaces"

echo "=== 环境配置信息 ==="
echo "当前工作空间: $WORKSPACE_DIR"
echo "IO接口路径: $IO_BOARD_PATH"

# 确认IO接口安装目录存在
if [ ! -d "$IO_BOARD_PATH/install/rpcs_s_interfaces_io_board" ]; then
  echo "错误: IO接口安装目录不存在，请确认路径: $IO_BOARD_PATH/install/rpcs_s_interfaces_io_board"
  exit 1
fi

# 确保ROS环境已加载
if [ -z "$ROS_VERSION" ]; then
  echo "加载ROS环境..."
  if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
  else
    echo "错误: 无法找到ROS环境脚本，请先手动加载ROS环境"
    exit 1
  fi
fi

# 清理构建缓存
echo "清理构建缓存..."
rm -rf "$WORKSPACE_DIR/build/RPCS_S_Behaviors_workflow"
rm -rf "$WORKSPACE_DIR/install/RPCS_S_Behaviors_workflow"
rm -rf "$WORKSPACE_DIR/log/latest_build/RPCS_S_Behaviors_workflow"

# 加载IO接口包
if [ -f "$IO_BOARD_PATH/install/setup.bash" ]; then
  echo "加载IO接口包..."
  source "$IO_BOARD_PATH/install/setup.bash"
fi

# 验证包是否可用
if ros2 pkg list | grep -q "rpcs_s_interfaces_io_board"; then
  echo "✅ rpcs_s_interfaces_io_board包已找到并加载"
else
  echo "⚠️ 警告: rpcs_s_interfaces_io_board包未加载，将尝试设置环境变量"
  export CMAKE_PREFIX_PATH="$IO_BOARD_PATH/install/rpcs_s_interfaces_io_board:$CMAKE_PREFIX_PATH"
  export LD_LIBRARY_PATH="$IO_BOARD_PATH/install/rpcs_s_interfaces_io_board/lib:$LD_LIBRARY_PATH"
  export AMENT_PREFIX_PATH="$IO_BOARD_PATH/install/rpcs_s_interfaces_io_board:$AMENT_PREFIX_PATH"
fi

# 输出环境变量信息
echo "环境变量:"
echo "- CMAKE_PREFIX_PATH: $(echo $CMAKE_PREFIX_PATH | cut -c1-100)..."
echo "- LD_LIBRARY_PATH: $(echo $LD_LIBRARY_PATH | cut -c1-100)..."
echo "- AMENT_PREFIX_PATH: $(echo $AMENT_PREFIX_PATH | cut -c1-100)..."
echo "====================="

# 返回工作空间根目录
cd "$WORKSPACE_DIR"

# 使用普通模式编译，不使用symlink
echo "开始编译..."
colcon build --packages-select RPCS_S_Behaviors_workflow \
  --cmake-args \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

# 检查编译结果
if [ $? -eq 0 ]; then
  echo "✅ 编译成功！"
else
  echo "❌ 编译失败，请查看上面的错误信息。"
fi 