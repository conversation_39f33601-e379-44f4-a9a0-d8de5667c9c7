#!/bin/bash

# 设置环境变量
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/home/<USER>/workspace/RPCS_M_Controllers_interfaces/install/rpcs_s_interfaces_agv/lib
export CMAKE_PREFIX_PATH=$CMAKE_PREFIX_PATH:/home/<USER>/workspace/RPCS_M_Controllers_interfaces/install/rpcs_s_interfaces_agv
export AMENT_PREFIX_PATH=$AMENT_PREFIX_PATH:/home/<USER>/workspace/RPCS_M_Controllers_interfaces/install/rpcs_s_interfaces_agv

# 检查命令行参数
if [ $# -lt 1 ]; then
    echo "用法: $0 <目标点名称>"
    echo "例如: $0 ToSafePoint"
    exit 1
fi

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}测试AGV移动到: $1${NC}"

# 执行测试程序
./build/RPCS_S_Behaviors_workflow/test_agv_service "$1"

# 检查返回值
if [ $? -eq 0 ]; then
    echo -e "${GREEN}测试成功!${NC}"
else
    echo -e "${RED}测试失败!${NC}"
    exit 1
fi 