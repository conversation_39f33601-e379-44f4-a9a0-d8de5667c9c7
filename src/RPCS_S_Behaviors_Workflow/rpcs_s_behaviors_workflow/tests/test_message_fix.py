#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rpcs_s_interfaces_behavior_tree.action import ExecuteProcessAction
import time
from datetime import datetime
import argparse

class MessageFixTest(Node):
    def __init__(self, robot_id, process_action_type):
        super().__init__('message_fix_test')
        self.feedback_count = 0
        self.goal_handle = None
        self.result_future = None
        self.robot_id = robot_id
        self.process_action_type = process_action_type
        
    def test_message_display(self):
        """验证PubPrintMessage现在能正确显示消息内容，并捕捉完整的反馈信息"""
        action_name = f'{self.robot_id}/ExecuteProcessAction'
        self.action_client = ActionClient(self, ExecuteProcessAction, action_name)
        
        print(f"🔧 验证{self.robot_id}执行{self.process_action_type}工艺 + 完整反馈信息")
        print("=" * 70)
        print("📋 期望在服务器终端看到:")
        print(f"   📢 各种{self.process_action_type}工艺消息...")
        print("\n📋 期望在客户端看到完整反馈信息:")
        print("   - current_process_step: 当前执行的工艺步骤名称")
        print("   - current_status: 当前状态 (RUNNING, SUCCESS, FAILURE)")
        print("   - progress_percent: 执行进度百分比")
        print("   - status_message: 状态描述信息")
        print("   - current_operation: 当前操作描述")
        print("   - timestamp: 时间戳")
        print("=" * 70)
        
        if not self.action_client.wait_for_server(timeout_sec=10.0):
            print(f"❌ {self.robot_id}的Action服务器未响应")
            return False
            
        print("✅ 已连接到Action服务器")
        
        # 创建目标
        goal_msg = ExecuteProcessAction.Goal()
        goal_msg.process_action_type = self.process_action_type
        goal_msg.robot_id = self.robot_id
        goal_msg.timeout_seconds = 60  # 增加超时时间到60秒
        goal_msg.preempt_current = False
        goal_msg.process_id = f"MESSAGE_FIX_TEST_{int(time.time())}"
        goal_msg.process_parameters = []
        
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        start_time = time.time()
        
        # 发送目标，注册反馈回调
        send_goal_future = self.action_client.send_goal_async(goal_msg, feedback_callback=self.detailed_feedback_callback)
        
        # 等待目标被接受
        rclpy.spin_until_future_complete(self, send_goal_future)
        self.goal_handle = send_goal_future.result()
        
        if not self.goal_handle:
            print("❌ 目标发送失败")
            return False
            
        if not self.goal_handle.accepted:
            print("❌ 目标被拒绝")
            return False
            
        print("✅ 目标已接受，正在执行...")
        print("👀 请观察服务器终端的消息输出...")
        print("📡 开始接收反馈信息...")
        print("-" * 70)
        
        # 等待结果
        self.result_future = self.goal_handle.get_result_async()
        
        try:
            # 持续接收反馈，直到动作完成
            timeout_time = time.time() + 70  # 70秒总超时
            while rclpy.ok() and not self.result_future.done():
                rclpy.spin_once(self, timeout_sec=0.1)
                
                # 检查总超时
                if time.time() > timeout_time:
                    print("\n⚠️ 等待结果超时")
                    if self.goal_handle:
                        print("正在取消目标...")
                        cancel_future = self.goal_handle.cancel_goal_async()
                        rclpy.spin_until_future_complete(self, cancel_future)
                    return False
                    
            if not rclpy.ok():
                print("\n⚠️ ROS系统已关闭")
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断执行")
            if self.goal_handle:
                print("正在取消目标...")
                cancel_future = self.goal_handle.cancel_goal_async()
                rclpy.spin_until_future_complete(self, cancel_future)
            return False
            
        if not self.result_future.done():
            print("\n⚠️ 等待结果失败")
            return False
            
        end_time = time.time()
        total_time = end_time - start_time
        
        result = self.result_future.result().result
        
        print("-" * 70)
        print(f"🎊 {self.robot_id}执行{self.process_action_type}验证结果:")
        print(f"⏱️  执行时间: {total_time:.2f}秒")
        print(f"📊 反馈消息总数: {self.feedback_count}")
        print(f"✅ 执行成功: {'是' if result.success else '否'}")
        print(f"💬 结果消息: {result.result_message}")
        print(f"🏁 最终状态: {result.final_status}")
        print(f"⌚ 服务器报告执行时间: {result.execution_time:.2f}秒")
        print(f"🔍 质量状态: {result.quality_status}")
        
        if result.process_output_data:
            print(f"📦 工艺输出数据: {result.process_output_data}")
        else:
            print("📦 工艺输出数据: 无")
        
        print("\n📝 Action消息格式验证:")
        print("✅ Goal消息: process_action_type, robot_id, process_parameters等")
        print("✅ Result消息: success, result_message, final_status等")
        print("✅ Feedback消息: current_process_step, current_status, progress_percent等")
        print("✅ 完整的反馈信息捕捉和显示")
        
        return True
    
    def detailed_feedback_callback(self, feedback_msg):
        """详细的反馈回调函数，显示所有反馈信息字段"""
        self.feedback_count += 1
        feedback = feedback_msg.feedback
        
        # 转换时间戳
        timestamp_sec = feedback.timestamp.sec + feedback.timestamp.nanosec / 1e9
        timestamp_str = datetime.fromtimestamp(timestamp_sec).strftime('%H:%M:%S.%f')[:-3]
        
        # 显示完整的反馈信息
        print(f"📡 反馈 #{self.feedback_count:03d} | {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        print(f"   📍 工艺步骤: {feedback.current_process_step}")
        print(f"   🔄 当前状态: {feedback.current_status}")
        print(f"   📊 执行进度: {feedback.progress_percent:.1f}%")
        print(f"   💬 状态信息: {feedback.status_message}")
        print(f"   🔧 当前操作: {feedback.current_operation}")
        print(f"   ⏰ 服务器时间戳: {timestamp_str}")
        
        # 根据进度显示进度条
        progress = int(feedback.progress_percent / 10)
        progress_bar = "█" * progress + "░" * (10 - progress)
        print(f"   📈 进度条: [{progress_bar}] {feedback.progress_percent:.1f}%")
        print()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='测试工艺动作执行和反馈信息')
    parser.add_argument('-r', '--robot', type=str, default='Robot1',
                        help='机器人ID (默认: Robot1)')
    parser.add_argument('-a', '--action', type=str, default='LightBoardSelfCheck',
                        help='工艺动作类型 (默认: LightBoardSelfCheck)')
    args = parser.parse_args()
    
    # 初始化ROS
    rclpy.init()
    
    # 创建测试客户端对象
    client = MessageFixTest(args.robot, args.action)
    
    try:
        print(f"🔍 测试{args.robot}执行{args.action}工艺...")
        print("目标1：验证能看到XML中配置的具体消息内容")
        print("目标2：捕捉和显示ExecuteProcessAction的完整反馈信息\n")
        
        success = client.test_message_display()
        
        if success:
            print("\n🎉 测试完成！")
            print("✅ 请检查服务器终端的消息输出（应该看到📢前缀的消息）")
            print("✅ 请检查客户端的完整反馈信息显示")
        else:
            print("\n😞 测试失败")
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被中断")
    except Exception as e:
        print(f"\n💥 测试错误: {str(e)}")
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 