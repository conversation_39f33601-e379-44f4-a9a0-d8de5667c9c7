<package format="3">
  <name>behaviortree_ros2</name>
  <version>0.2.0</version>
  <description>
  This package provides a ROS2 wrapper, on top of BehaviorTree.CPP.
  </description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>MIT</license>
  <author><PERSON><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>libboost-dev</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>behaviortree_cpp</depend>

<export>
  <build_type>ament_cmake</build_type>
</export>

</package>
